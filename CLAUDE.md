# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Task Management Workflow
For task organization, memory management, and cross-task coordination, see AI_WORKFLOW.md. This workflow establishes:

- Task directory structure (tasks/TMS-XXX/)
- Requirement change tracking
- AI agent prompt templates
- Cross-task dependency management

Use this workflow for maintaining context across Jira tasks and AI agent sessions.

## Development Commands

### Full Development Environment
```bash
composer dev                  # Starts all services: server, queue, logs, vite (recommended)
composer dev:ssr             # Development with SSR support
```

### Frontend Development
```bash
npm run dev                  # Vite development server
npm run build               # Production build
npm run build:ssr          # SSR production build
npm run lint               # ESLint with auto-fix
npm run format             # Format code with Prettier
npm run format:check       # Check formatting without changes  
npm run types              # TypeScript type checking
```

### Backend Development  
```bash
php artisan serve           # Laravel development server
php artisan queue:listen    # Process background jobs
php artisan pail           # Real-time log monitoring
php artisan migrate        # Run database migrations
./vendor/bin/pint          # Laravel Pint code formatting
```

### Testing
```bash
php artisan test           # Run all tests (Pest framework)
php artisan test --filter=DriverController  # Run specific test class
php artisan test tests/Feature/Drivers/     # Run tests in directory
```

## Architecture Overview

**Ride4** is a Laravel 12 + React 19 fleet management application using Inertia.js for SPA functionality.

### Backend Architecture (Laravel)
- **Actions/** - Single-purpose command classes (preferred over controllers for complex logic)
- **Services/** - Business logic layer with interface contracts
- **Data/** - Laravel Data DTOs for type-safe API responses  
- **Enums/** - PHP 8.4 enums for constants and state management
- **Models/** - Eloquent models with defined relationships and states
- **Policies/** - Authorization rules for each model

### Frontend Architecture (React + TypeScript)
- **components/** - shadcn/ui components organized by feature
- **pages/** - Inertia.js page components (entry points)
- **layouts/** - Shared layout components
- **hooks/** - Custom React hooks for shared logic
- **types/** - TypeScript definitions generated from Laravel Data
- **lib/** - Utility functions and configurations

### Key Design Patterns

#### Service Layer Pattern
Business logic lives in Services with interface contracts:
```php
interface TelematicServiceInterface {
    public function sync(Provider $provider): SyncResult;
}
```

#### Action Pattern  
Single-responsibility classes for complex operations:
```php
class SyncTelematicDataAction {
    public function execute(Provider $provider): void
}
```

#### Laravel Data DTOs
Type-safe data transfer between backend and frontend:
```php
class DriverData extends Data {
    public function __construct(
        public string $name,
        public ?string $license_number,
    ) {}
}
```

#### Component Architecture
shadcn/ui components with consistent patterns:
- Use `clsx` and `cn()` for conditional classes
- Implement proper TypeScript interfaces
- Follow compound component patterns for complex UI

### Database Architecture
- **Migrations** drive schema changes (never modify existing migrations)
- **States** track entity lifecycle using Spatie Laravel Model States
- **Activity Logging** provides audit trails via Spatie Activity Log
- **Relationships** are properly defined on both sides with inverse relationships

### Integration Architecture
- **Multi-provider Telematics** - Extensible system supporting multiple providers (Samsara primary)
- **Import/Export System** - Excel/CSV processing with validation and rollback capabilities  
- **Queue System** - Background job processing for heavy operations
- **WorkOS Integration** - SSO and user management

## Key Technologies
- **Laravel 12** with PHP 8.4 features
- **React 19** with TypeScript and strict mode
- **Inertia.js 2.0** for SPA functionality without API layer
- **Tailwind CSS 4** with shadcn/ui components
- **Pest** testing framework with Laravel plugin
- **Vite 6** for asset building and HMR
- **Laravel Data** for type-safe DTOs
- **Laravel Model States** for entity state management

## File Upload Architecture
Uses Uppy.js for file uploads with Laravel backend processing:
- Drag & drop interface with progress tracking
- Server-side validation and processing
- Import rollback capabilities for data integrity

## Testing Strategy
- **Feature Tests** test full HTTP request/response cycles
- **Unit Tests** focus on isolated business logic (Services, Actions)
- **Pest Framework** with descriptive test syntax
- Tests organized by feature matching controller structure
