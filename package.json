{"private": true, "type": "module", "scripts": {"build": "vite build", "build:ssr": "vite build && vite build --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix", "types": "tsc --noEmit"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/node": "^24.0.4", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.6.1", "prettier-plugin-blade": "^2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.13", "typescript-eslint": "^8.35.0"}, "dependencies": {"@headlessui/react": "^2.2.4", "@inertiajs/react": "^2.0.13", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.10", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-table": "^8.21.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@uppy/core": "^4.4.6", "@uppy/dashboard": "^4.3.4", "@uppy/drag-drop": "^4.1.3", "@uppy/file-input": "^4.1.3", "@uppy/progress-bar": "^4.2.1", "@uppy/react": "^4.3.0", "@uppy/xhr-upload": "^4.3.3", "@vis.gl/react-google-maps": "^1.5.3", "@vitejs/plugin-react": "^4.6.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "concurrently": "^9.2.0", "date-fns": "^4.1.0", "globals": "^16.2.0", "laravel-vite-plugin": "^1.3", "lucide-react": "^0.523.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "9.7.0", "react-dom": "^19.1.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3", "vite": "^6.3"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.44.0", "@tailwindcss/oxide-linux-x64-gnu": "^4.1.10", "lightningcss-linux-x64-gnu": "^1.30.1"}}