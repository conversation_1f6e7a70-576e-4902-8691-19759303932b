import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { ChevronDown } from 'lucide-react';
import { useState, useEffect } from 'react';

export function NavMain({ items = [] }: { items: NavItem[] }) {
  const page = usePage();
  const [openSubmenus, setOpenSubmenus] = useState<Record<string, boolean>>({});

  // Initialize open states based on current URL
  useEffect(() => {
    const newOpenStates: Record<string, boolean> = {};
    items.forEach((item) => {
      if (item.submenu) {
        // Check if any submenu item matches the current URL
        const isSubmenuActive = item.submenu.some((subItem) => page.url.startsWith(subItem.href));
        newOpenStates[item.title] = isSubmenuActive;
      }
    });
    setOpenSubmenus(newOpenStates);
  }, [page.url, items]);

  const toggleSubmenu = (title: string) => {
    setOpenSubmenus((prev) => ({
      ...prev,
      [title]: !prev[title],
    }));
  };

  return (
    <SidebarGroup className="px-2 py-0">
      <SidebarGroupLabel>Platform</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => {
          const isParentActive = page.url.startsWith(item.href);
          const isSubmenuActive = item.submenu && item.submenu.some((subItem) => page.url.startsWith(subItem.href));
          const isActive = isParentActive && (!item.submenu || !isSubmenuActive);

          return (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton
                onClick={() => item.submenu && toggleSubmenu(item.title)}
                isActive={isActive}
                className="cursor-pointer hover:bg-transparent"
                tooltip={{ children: item.title }}
                asChild={!item.submenu}
              >
                {!item.submenu ? (
                  <Link href={item.href} prefetch>
                    {item.icon && <item.icon />}
                    <span>{item.title}</span>
                  </Link>
                ) : (
                  <>
                    {item.icon && <item.icon />}
                    <span>{item.title}</span>
                    <ChevronDown
                      className={`ml-auto h-4 w-4 transition-transform ${openSubmenus[item.title] ? 'rotate-180' : ''}`}
                    />
                  </>
                )}
              </SidebarMenuButton>
              {item.submenu && openSubmenus[item.title] && (
                <SidebarMenuSub>
                  {item.submenu.map((subItem) => (
                    <SidebarMenuSubItem key={subItem.title}>
                      <SidebarMenuSubButton
                        asChild
                        isActive={page.url.startsWith(subItem.href)}
                        className="cursor-pointer hover:bg-transparent"
                      >
                        <Link href={subItem.href} prefetch>
                          <span>{subItem.title}</span>
                        </Link>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  ))}
                </SidebarMenuSub>
              )}
            </SidebarMenuItem>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
