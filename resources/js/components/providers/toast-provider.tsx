import { Toaster } from '@/components/ui/sonner';

export function ToastProvider() {
  return (
    <Toaster
      position="top-right"
      expand={true}
      richColors
      closeButton
      theme="system"
      className="dark:bg-background dark:text-foreground"
      toastOptions={{
        classNames: {
          title: 'dark:text-foreground',
          description: 'dark:text-muted-foreground',
          actionButton: 'dark:bg-primary dark:text-primary-foreground',
          cancelButton: 'dark:bg-muted dark:text-muted-foreground',
          error: 'dark:bg-destructive/10 dark:text-destructive-foreground dark:border-destructive/20',
          success: 'dark:bg-success/10 dark:text-success-foreground dark:border-success/20',
          warning: 'dark:bg-warning/10 dark:text-warning-foreground dark:border-warning/20',
          info: 'dark:bg-info/10 dark:text-info-foreground dark:border-info/20',
        },
      }}
    />
  );
}
