import * as React from 'react';
import { cn } from '@/lib/utils';
import { Label } from './label';

interface FormFieldProps extends React.HTMLAttributes<HTMLDivElement> {
  label?: string;
  error?: string;
  required?: boolean;
  id?: string;
  children: React.ReactElement<React.InputHTMLAttributes<HTMLInputElement>>;
}

export function FormField({ className, children, label, error, required, id, ...props }: FormFieldProps) {
  return (
    <div className={cn('space-y-2', className)} {...props}>
      {label && (
        <Label htmlFor={id}>
          {label}
          {required && <span className="text-destructive dark:text-muted-foreground ml-1">*</span>}
        </Label>
      )}
      {React.cloneElement(children, {
        id,
        'aria-describedby': error ? `${id}-error` : undefined,
      })}
      {error && (
        <p id={`${id}-error`} className="text-destructive dark:text-muted-foreground text-sm" role="alert">
          {error}
        </p>
      )}
    </div>
  );
}
