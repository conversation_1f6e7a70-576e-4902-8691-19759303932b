import * as React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";

interface DatePickerProps {
  value?: string;
  onChange: (date: string | undefined) => void;
  placeholder?: string;
}

export function DatePicker({ value, onChange, placeholder = "Pick a date" }: DatePickerProps) {
  const [viewedMonth, setViewedMonth] = React.useState<Date>(
    value ? new Date(value) : new Date()
  );

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full pl-3 text-left font-normal",
            !value && "text-muted-foreground"
          )}
        >
          {value ? (
            format(new Date(value), "PPP")
          ) : (
            <span>{placeholder}</span>
          )}
          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <div className="p-3 border-b">
          <Select
            value={value?.split('-')[0] || new Date().getFullYear().toString()}
            onValueChange={(year) => {
              const newDate = value ? new Date(value) : new Date();
              newDate.setFullYear(parseInt(year));
              onChange(format(newDate, 'yyyy-MM-dd'));
              setViewedMonth(newDate);
            }}
          >
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Year" />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: 51 }, (_, i) => 2000 + i).map((year) => (
                <SelectItem key={year} value={year.toString()}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <Calendar
            mode="single"
            selected={value ? new Date(value) : undefined}
            month={viewedMonth}
            onMonthChange={(month) => {
              setViewedMonth(month);
              const currentDate = value ? new Date(value) : new Date();
              currentDate.setFullYear(month.getFullYear());
              onChange(format(currentDate, 'yyyy-MM-dd'));
            }}
            onSelect={(date) => {
              if (!date) return;
              onChange(format(date, 'yyyy-MM-dd'));
              setViewedMonth(date);
            }}
          />
      </PopoverContent>
    </Popover>
  );
} 