import React from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { FormField } from '@/components/ui/form-field';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { TelematicsFormProps } from '@/types/telematics';
import { AlertCircle, CheckCircle } from 'lucide-react';

export default function TelematicsForm({
  data,
  setData,
  errors,
  processing,
  onSubmit,
  provider,
  buttonLabel,
}: TelematicsFormProps) {
  const handleCredentialChange = (key: string, value: string) => {
    const newCredentials = { ...data.credentials, [key]: value };
    setData('credentials', newCredentials);
  };

  const handleCheckboxChange = (checked: boolean) => {
    setData('enabled', checked);
  };

  return (
    <form onSubmit={onSubmit} className="space-y-6">
      {/* Provider Info */}
      <div className="bg-muted/50 rounded-lg border p-3">
        <div className="flex items-center gap-2">
          <CheckCircle className="text-primary h-4 w-4 flex-shrink-0" />
          <div>
            <span className="text-sm font-medium">{provider.name}</span>
            <Badge variant={provider.enabled ? "default" : "secondary"} className="ml-2">
              {provider.enabled ? "Enabled" : "Disabled"}
            </Badge>
          </div>
        </div>
      </div>

      {/* Configuration Fields */}
      <div className="space-y-6 rounded-lg border p-6">
        <h3 className="mb-2 text-lg font-semibold">Configuration</h3>
        <p className="text-muted-foreground mb-4 text-sm">
          Enter your {provider.name} credentials and settings
        </p>
        <div className="grid gap-4 md:grid-cols-2">
          {provider.fields.map((field) => (
            <FormField
              key={field.key}
              label={field.label}
              error={errors[`credentials.${field.key}` as keyof typeof errors]}
              required={field.required}
              id={field.key}
            >
              <div className="space-y-2">
                <Input
                  type={field.type}
                  value={data.credentials[field.key] || ''}
                  onChange={(e) => handleCredentialChange(field.key, e.target.value)}
                  placeholder={field.placeholder}
                  required={field.required}
                  aria-invalid={!!errors[`credentials.${field.key}` as keyof typeof errors]}
                />
                {field.description && (
                  <p className="text-muted-foreground flex items-start gap-2 text-sm">
                    <AlertCircle className="mt-0.5 h-4 w-4 flex-shrink-0" />
                    {field.description}
                  </p>
                )}
              </div>
            </FormField>
          ))}
        </div>
      </div>

      {/* Integration Status */}
      <div className="space-y-6 rounded-lg border p-6">
        <h3 className="mb-2 text-lg font-semibold">Integration Status</h3>
        <div className="flex items-start space-x-3">
          <Checkbox id="enabled" checked={data.enabled} onCheckedChange={handleCheckboxChange} />
          <div className="flex-1 space-y-1">
            <label
              htmlFor="enabled"
              className="block text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Enable Integration
            </label>
            <p className="text-muted-foreground text-sm">
              When enabled, data will be automatically synchronized from your telematics provider
            </p>
          </div>
          {data.enabled && <Badge variant="secondary">Active</Badge>}
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button type="submit" disabled={processing}>
          {processing ? 'Saving...' : buttonLabel}
        </Button>
      </div>
    </form>
  );
}
