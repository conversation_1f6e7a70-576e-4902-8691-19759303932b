import React from 'react';
import { TruckMaintenanceData, MaintenanceAlert } from '@/types/truck';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { AlertTriangle, CheckCircle, Clock, Wrench, Gauge, Calendar, Shield } from 'lucide-react';
import { format } from 'date-fns';

interface TruckMaintenanceViewProps {
  data: TruckMaintenanceData;
}

const getStatusColor = (status: TruckMaintenanceData['maintenanceStatus']) => {
  switch (status) {
    case 'current':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'due_soon':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'overdue':
      return 'bg-red-100 text-red-800 border-red-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getStatusIcon = (status: TruckMaintenanceData['maintenanceStatus']) => {
  switch (status) {
    case 'current':
      return <CheckCircle className="h-4 w-4" />;
    case 'due_soon':
      return <Clock className="h-4 w-4" />;
    case 'overdue':
      return <AlertTriangle className="h-4 w-4" />;
    default:
      return <Wrench className="h-4 w-4" />;
  }
};

const getAlertIcon = (type: MaintenanceAlert['type']) => {
  switch (type) {
    case 'critical':
      return <AlertTriangle className="h-4 w-4 text-red-500" />;
    case 'warning':
      return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    case 'info':
      return <CheckCircle className="h-4 w-4 text-blue-500" />;
    default:
      return <AlertTriangle className="h-4 w-4 text-gray-500" />;
  }
};

const getAlertBadgeVariant = (type: MaintenanceAlert['type']) => {
  switch (type) {
    case 'critical':
      return 'destructive';
    case 'warning':
      return 'secondary';
    case 'info':
      return 'outline';
    default:
      return 'secondary';
  }
};

export function TruckMaintenanceView({ data }: TruckMaintenanceViewProps) {
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MM/dd/yyyy');
    } catch {
      return dateString;
    }
  };

  const formatMileage = (mileage: number) => {
    return mileage.toLocaleString() + ' mi';
  };

  return (
    <div className="border-t bg-slate-50/30 p-4">
      {/* Main Status Bar */}
      <div className="mb-4 flex flex-wrap items-center justify-between gap-4 rounded-lg bg-white p-4 shadow-sm border">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Gauge className="h-4 w-4 text-slate-600" />
            <span className="text-sm font-medium text-slate-900">Current: {formatMileage(data.currentMileage)}</span>
          </div>
          <div className="h-4 w-px bg-slate-300"></div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-slate-600">Status:</span>
            <Badge className={`${getStatusColor(data.maintenanceStatus)} flex items-center gap-1 text-xs`}>
              {getStatusIcon(data.maintenanceStatus)}
              {data.maintenanceStatus.replace('_', ' ')}
            </Badge>
          </div>
        </div>
        
        {data.alerts.length > 0 && (
          <div className="flex items-center gap-2 text-sm text-amber-600">
            <AlertTriangle className="h-4 w-4" />
            <span>{data.alerts.length} alert{data.alerts.length > 1 ? 's' : ''}</span>
          </div>
        )}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
        {/* Service Information */}
        <div className="lg:col-span-2">
          <div className="rounded-lg bg-white border shadow-sm h-full flex flex-col">
            <div className="border-b bg-slate-50/50 px-4 py-3">
              <h3 className="text-sm font-semibold text-slate-900 flex items-center gap-2">
                <Wrench className="h-4 w-4" />
                Service Schedule
              </h3>
            </div>
            <div className="p-4 space-y-4 flex-1">
              {/* Next Service */}
              <div className="flex items-center justify-between py-2 border-b border-slate-100">
                <div>
                  <p className="text-sm font-medium text-slate-900">Next Service Due</p>
                  <p className="text-xs text-slate-500">{formatDate(data.nextServiceDue.date)}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold text-slate-900">{formatMileage(data.nextServiceDue.mileage)}</p>
                </div>
              </div>

              {/* Oil Change */}
              <div className="flex items-center justify-between py-2 border-b border-slate-100">
                <div className="flex items-center gap-2">
                  <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium text-slate-900">Oil Change</p>
                    <p className="text-xs text-slate-500">Due {formatDate(data.oilChange.nextDue)}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold text-slate-900">{formatMileage(data.oilChange.nextDueMileage)}</p>
                </div>
              </div>

              {/* Tire Rotation */}
              <div className="flex items-center justify-between py-2">
                <div className="flex items-center gap-2">
                  <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium text-slate-900">Tire Rotation</p>
                    <p className="text-xs text-slate-500">Due {formatDate(data.tireRotation.nextDue)}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold text-slate-900">{formatMileage(data.tireRotation.nextDueMileage)}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Inspections */}
        <div>
          <div className="rounded-lg bg-white border shadow-sm h-full flex flex-col">
            <div className="border-b bg-slate-50/50 px-4 py-3">
              <h3 className="text-sm font-semibold text-slate-900 flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Inspections
              </h3>
            </div>
            <div className="p-4 space-y-3 flex-1">
              <div>
                <p className="text-xs text-slate-500 uppercase tracking-wide">DOT Expiry</p>
                <p className="text-sm font-medium text-slate-900">{formatDate(data.inspection.dotExpiry)}</p>
              </div>
              <div>
                <p className="text-xs text-slate-500 uppercase tracking-wide">Annual Due</p>
                <p className="text-sm font-medium text-slate-900">{formatDate(data.inspection.annualInspectionDue)}</p>
              </div>
              <div>
                <p className="text-xs text-slate-500 uppercase tracking-wide">Last Inspection</p>
                <p className="text-sm font-medium text-slate-900">{formatDate(data.inspection.lastInspectionDate)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Alerts */}
        <div>
          <div className="rounded-lg bg-white border shadow-sm h-full flex flex-col">
            <div className="border-b bg-slate-50/50 px-4 py-3">
              <h3 className="text-sm font-semibold text-slate-900 flex items-center gap-2">
                <AlertTriangle className="h-4 w-4" />
                Alerts {data.alerts.length === 0 && <span className="text-xs text-slate-400 font-normal">(None)</span>}
              </h3>
            </div>
            <div className="p-4 space-y-3 flex-1">
              {data.alerts.length > 0 ? (
                data.alerts.map((alert) => (
                  <div key={alert.id} className="flex items-start gap-2 p-2 rounded-md bg-slate-50/50">
                    {getAlertIcon(alert.type)}
                    <div className="flex-1 min-w-0">
                      <Badge variant={getAlertBadgeVariant(alert.type)} className="text-xs mb-1">
                        {alert.type.toUpperCase()}
                      </Badge>
                      <p className="text-sm text-slate-900 leading-tight">{alert.message}</p>
                      {(alert.dueDate || alert.dueMileage) && (
                        <div className="mt-1 flex items-center gap-2 text-xs text-slate-500">
                          {alert.dueDate && (
                            <span className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {formatDate(alert.dueDate)}
                            </span>
                          )}
                          {alert.dueMileage && (
                            <span className="flex items-center gap-1">
                              <Gauge className="h-3 w-3" />
                              {formatMileage(alert.dueMileage)}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="flex items-center justify-center py-8 text-slate-400">
                  <div className="text-center">
                    <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-400" />
                    <p className="text-sm">No active alerts</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}