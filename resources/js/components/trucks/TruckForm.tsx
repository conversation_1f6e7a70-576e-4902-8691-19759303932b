import { FormEventHandler, ChangeEvent } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FormField } from '@/components/ui/form-field';
import { FleetStatus } from '@/types/fleetStatus';
import { OwnershipType } from '@/types/ownershipType';
import { Checkbox } from '@/components/ui/checkbox';
import { DatePicker } from '@/components/ui/date-picker';
import { DriverData } from '@/types/driver';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Check, ChevronsUpDown, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { TruckStatus } from '@/types/truckStatus';

interface TruckFormProps {
  data: any;
  setData: (field: string, value: any) => void;
  errors: Record<string, string>;
  processing: boolean;
  onSubmit: FormEventHandler;
  regions: { value: string; label: string }[];
  fleetStatuses: { value: string; label: string }[];
  ownershipType: { value: string; label: string }[];
  truckStatuses: { value: string; label: string }[];
  drivers: DriverData[];
  buttonLabel: string;
}

export default function TruckForm({
  data,
  setData,
  errors,
  processing,
  onSubmit,
  regions,
  fleetStatuses,
  ownershipType,
  truckStatuses,
  drivers,
  buttonLabel,
}: TruckFormProps) {
  const handleCheckboxChange = (field: string) => (checked: boolean) => {
    setData(field, checked);
  };

  const handleDateChange = (field: string) => (date: string | undefined) => {
    setData(field, date || null);
  };

  return (
    <form onSubmit={onSubmit} className="mt-8 max-w-5xl grid gap-4 lg:grid-cols-2">
      <div className="space-y-6 border rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-2">Truck Details</h3>
        <div className="grid grid-cols-2 gap-4">
          <FormField label="Unit Number" error={errors.unit_number} required id="unit_number">
            <Input
              type="text"
              required
              value={data.unit_number}
              onChange={(e) => setData('unit_number', e.target.value)}
              placeholder="Unit number"
              aria-invalid={!!errors.unit_number}
            />
          </FormField>
          <FormField label="Make" error={errors.make} id="make">
            <Input
              type="text"
              value={data.make || ''}
              onChange={(e) => setData('make', e.target.value)}
              placeholder="Make"
              aria-invalid={!!errors.make}
            />
          </FormField>
          <FormField label="Model" error={errors.model} id="model">
            <Input
              type="text"
              value={data.model || ''}
              onChange={(e) => setData('model', e.target.value)}
              placeholder="Model"
              aria-invalid={!!errors.model}
            />
          </FormField>
          <FormField label="Year" error={errors.year} id="year">
            <Input
              type="number"
              value={data.year || ''}
              onChange={(e) => setData('year', parseInt(e.target.value) || null)}
              placeholder="Year"
              aria-invalid={!!errors.year}
            />
          </FormField>
          <FormField label="VIN" error={errors.vin} id="vin">
            <Input
              type="text"
              value={data.vin || ''}
              onChange={(e) => setData('vin', e.target.value)}
              placeholder="VIN"
              aria-invalid={!!errors.vin}
            />
          </FormField>
          <FormField label="Status" error={errors.status} id="status">
            <Select
              value={data.status || ''}
              onValueChange={(value) => setData('status', value as TruckStatus)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select truck status" />
              </SelectTrigger>
              <SelectContent>
                {truckStatuses.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormField>
        </div>
      </div>

      <div className="space-y-6 border rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-2">Registration</h3>
        <div className="grid grid-cols-2 gap-4">
          <FormField label="Plate" error={errors.plate} id="plate">
            <Input
              type="text"
              value={data.plate || ''}
              onChange={(e) => setData('plate', e.target.value)}
              placeholder="License plate"
              aria-invalid={!!errors.plate}
            />
          </FormField>
          <FormField label="Plate Region" error={errors.plate_region} id="plate_region">
            <Select value={data.plate_region || ''} onValueChange={(value) => setData('plate_region', value)}>
              <SelectTrigger aria-invalid={!!errors.plate_region}>
                <SelectValue placeholder="Select region" />
              </SelectTrigger>
              <SelectContent>
                {regions.map((region) => (
                  <SelectItem key={region.value} value={region.value}>
                    {region.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormField>
          <FormField label="DOT Expiry" error={errors.regulatory_compliance_expiry || errors.dot_expiry} id="regulatory_compliance_expiry">
            <DatePicker
              value={data.regulatory_compliance_expiry || undefined}
              onChange={handleDateChange('regulatory_compliance_expiry')}
            />
          </FormField>
          <FormField label="Plate Expiry" error={errors.plate_expiration_date} id="plate_expiration_date">
            <DatePicker
              value={data.plate_expiration_date || undefined}
              onChange={handleDateChange('plate_expiration_date')}
            />
          </FormField>
        </div>
      </div>

      <div className="space-y-6 border rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-2">Ownership</h3>
        <div className="grid grid-cols-2 gap-4">
          <FormField label="Fleet Status" error={errors.fleet_status} id="fleet_status">
            <Select
              value={data.fleet_status || ''}
              onValueChange={(value) => setData('fleet_status', value as FleetStatus)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select fleet status" />
              </SelectTrigger>
              <SelectContent>
                {fleetStatuses.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormField>
          <FormField label="Ownership Type" error={errors.ownership_type} id="ownership_type">
            <Select
              value={data.ownership_type || ''}
              onValueChange={(value) => setData('ownership_type', value as OwnershipType)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select ownership type" />
              </SelectTrigger>
              <SelectContent>
                {ownershipType.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormField>
          <FormField label="Owner Company" error={errors.owner_company} id="owner_company">
            <Input
              type="text"
              value={data.owner_company || ''}
              onChange={(e) => setData('owner_company', e.target.value)}
              placeholder="Owner company"
              aria-invalid={!!errors.owner_company}
            />
          </FormField>
          <FormField label="Lease Termination Date" error={errors.lease_termination_date} id="lease_termination_date">
            <DatePicker
              value={data.lease_termination_date || undefined}
              onChange={handleDateChange('lease_termination_date')}
            />
          </FormField>
        </div>
      </div>

      <div className="space-y-6 border rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-2">Drivers</h3>
        <div className="grid grid-cols-2 gap-4">
          <FormField label="Driver 1" error={errors.driver_1_id} id="driver_1_id">
            {(() => {
              const selectedDriver = drivers.find((d) => d.id === data.driver_1_id);
              return (
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={false}
                      className={cn('w-full justify-between', errors.driver_1_id && 'border-destructive', 'relative')}
                    >
                      {selectedDriver ? (
                        <span className="flex items-center gap-2">
                          {`${selectedDriver.first_name} ${selectedDriver.last_name}`}
                          <span
                            className="ml-2 text-muted-foreground hover:text-destructive cursor-pointer"
                            onClick={e => {
                              e.stopPropagation();
                              setData('driver_1_id', null);
                            }}
                            tabIndex={0}
                            role="button"
                            aria-label="Clear selection"
                          >
                            <X className="h-4 w-4" />
                          </span>
                        </span>
                      ) : (
                        <span className="placeholder:text-muted-foreground text-muted-foreground">Select driver</span>
                      )}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0">
                    <Command>
                      <CommandInput placeholder="Search driver..." />
                      <CommandList>
                        <CommandEmpty>No driver found.</CommandEmpty>
                        <CommandGroup>
                          {drivers.map((driver) => (
                            <CommandItem
                              key={driver.id}
                              value={`${driver.first_name} ${driver.last_name}`}
                              onSelect={() => setData('driver_1_id', driver.id)}
                            >
                              <Check
                                className={cn(
                                  'mr-2 h-4 w-4',
                                  data.driver_1_id === driver.id ? 'opacity-100' : 'opacity-0'
                                )}
                              />
                              {driver.first_name} {driver.last_name}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              );
            })()}
          </FormField>
          <FormField label="Driver 2" error={errors.driver_2_id} id="driver_2_id">
            {(() => {
              const selectedDriver2 = drivers.find((d) => d.id === data.driver_2_id);
              return (
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={false}
                      className={cn('w-full justify-between', errors.driver_2_id && 'border-destructive', 'relative')}
                    >
                      {selectedDriver2 ? (
                        <span className="flex items-center gap-2">
                          {`${selectedDriver2.first_name} ${selectedDriver2.last_name}`}
                          <span
                            className="ml-2 text-muted-foreground hover:text-destructive cursor-pointer"
                            onClick={e => {
                              e.stopPropagation();
                              setData('driver_2_id', null);
                            }}
                            tabIndex={0}
                            role="button"
                            aria-label="Clear selection"
                          >
                            <X className="h-4 w-4" />
                          </span>
                        </span>
                      ) : (
                        <span className="placeholder:text-muted-foreground text-muted-foreground">Select driver</span>
                      )}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0">
                    <Command>
                      <CommandInput placeholder="Search driver..." />
                      <CommandList>
                        <CommandEmpty>No driver found.</CommandEmpty>
                        <CommandGroup>
                          {drivers.map((driver) => (
                            <CommandItem
                              key={driver.id}
                              value={`${driver.first_name} ${driver.last_name}`}
                              onSelect={() => setData('driver_2_id', driver.id)}
                            >
                              <Check
                                className={cn(
                                  'mr-2 h-4 w-4',
                                  data.driver_2_id === driver.id ? 'opacity-100' : 'opacity-0'
                                )}
                              />
                              {driver.first_name} {driver.last_name}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              );
            })()}
          </FormField>
        </div>
      </div>

      <div className="space-y-6 border rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-2">Equipment</h3>
        <div className="grid grid-cols-2 gap-4">
          <FormField label="GPS Note" error={errors.gps_note} id="gps_note">
            <Input
              type="text"
              value={data.gps_note || ''}
              onChange={(e: ChangeEvent<HTMLInputElement>) => setData('gps_note', e.target.value)}
              placeholder="GPS note"
              aria-invalid={!!errors.gps_note}
            />
          </FormField>
          <FormField label="Gateway Serial" error={errors.gateway_serial} id="gateway_serial">
            <Input
              type="text"
              value={data.gateway_serial || ''}
              onChange={(e) => setData('gateway_serial', e.target.value)}
              placeholder="Gateway Serial"
              aria-invalid={!!errors.gateway_serial}
            />
          </FormField>
        </div>
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="deer_guards"
              checked={data.deer_guards}
              onCheckedChange={handleCheckboxChange('deer_guards')}
            />
            <label
              htmlFor="deer_guards"
              className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Deer Guards
            </label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="dash_camera"
              checked={data.dash_camera}
              onCheckedChange={handleCheckboxChange('dash_camera')}
            />
            <label
              htmlFor="dash_camera"
              className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Dash Camera
            </label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="has_headrack"
              checked={data.has_headrack}
              onCheckedChange={handleCheckboxChange('has_headrack')}
            />
            <label
              htmlFor="has_headrack"
              className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Has Headrack
            </label>
          </div>
        </div>
      </div>

      <div className="space-y-6 border rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-2">Dates</h3>
        <div className="grid grid-cols-2 gap-4">
          <FormField label="Activation Date" error={errors.activation_date} id="activation_date">
            <DatePicker value={data.activation_date || undefined} onChange={handleDateChange('activation_date')} />
          </FormField>
          <FormField label="Deactivation Date" error={errors.deactivation_date} id="deactivation_date">
            <div className="flex items-center gap-2">
              <DatePicker
                value={data.deactivation_date || undefined}
                onChange={handleDateChange('deactivation_date')}
              />
              {data.deactivation_date && (
                <Button type="button" variant="ghost" size="icon" onClick={() => { setData('deactivation_date', null); setData('deactivation_reason', null); }} aria-label="Clear deactivation date">
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </FormField>
          {data.deactivation_date && (
            <FormField label="Deactivation Reason" error={errors.deactivation_reason} id="deactivation_reason" className="col-span-2">
              <Input
                type="text"
                value={data.deactivation_reason || ''}
                onChange={e => setData('deactivation_reason', e.target.value)}
                placeholder="Enter reason for deactivation"
                aria-invalid={!!errors.deactivation_reason}
              />
            </FormField>
          )}
        </div>
      </div>

      <div className="lg:col-span-2 flex justify-end">
        <Button type="submit" disabled={processing}>
          {buttonLabel}
        </Button>
      </div>
    </form>
  );
}
