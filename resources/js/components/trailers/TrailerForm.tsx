import { FormEventHandler } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FormField } from '@/components/ui/form-field';
import { FleetStatus } from '@/types/fleetStatus';
import { DatePicker } from '@/components/ui/date-picker';

interface TrailerFormProps {
  data: any;
  setData: (field: string, value: any) => void;
  errors: Record<string, string>;
  processing: boolean;
  onSubmit: FormEventHandler;
  regions: { value: string; label: string }[];
  fleetStatuses: { value: string; label: string }[];
  ownershipTypes: { value: string; label: string }[];
  buttonLabel: string;
}

export default function TrailerForm({
  data,
  setData,
  errors,
  processing,
  onSubmit,
  regions,
  fleetStatuses,
  buttonLabel,
}: TrailerFormProps) {
  const handleDateChange = (field: string) => (date: string | undefined) => {
    setData(field, date || null);
  };

  return (
    <form onSubmit={onSubmit} className="mt-8 max-w-5xl grid gap-4 lg:grid-cols-2">
      <div className="space-y-6 border rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-2">Trailer Details</h3>
        <div className="grid grid-cols-2 gap-4">
          <FormField label="Unit Number" error={errors.unit_number} required id="unit_number">
            <Input
              type="text"
              required
              value={data.unit_number}
              onChange={(e) => setData('unit_number', e.target.value)}
              placeholder="Unit number"
              aria-invalid={!!errors.unit_number}
            />
          </FormField>
          <FormField label="Make" error={errors.make} id="make">
            <Input
              type="text"
              value={data.make || ''}
              onChange={(e) => setData('make', e.target.value)}
              placeholder="Make"
              aria-invalid={!!errors.make}
            />
          </FormField>
          <FormField label="Model" error={errors.model} id="model">
            <Input
              type="text"
              value={data.model || ''}
              onChange={(e) => setData('model', e.target.value)}
              placeholder="Model"
              aria-invalid={!!errors.model}
            />
          </FormField>
          <FormField label="Year" error={errors.year} id="year">
            <Input
              type="number"
              value={data.year || ''}
              onChange={(e) => setData('year', parseInt(e.target.value) || null)}
              placeholder="Year"
              aria-invalid={!!errors.year}
            />
          </FormField>
          <FormField label="VIN" error={errors.vin} id="vin">
            <Input
              type="text"
              value={data.vin || ''}
              onChange={(e) => setData('vin', e.target.value)}
              placeholder="VIN"
              aria-invalid={!!errors.vin}
            />
          </FormField>
          <FormField label="Location" error={errors.location} id="location">
            <Input
              type="text"
              value={data.location || ''}
              onChange={(e) => setData('location', e.target.value)}
              placeholder="Location"
              aria-invalid={!!errors.location}
            />
          </FormField>
        </div>
      </div>

      <div className="space-y-6 border rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-2">Registration</h3>
        <div className="grid grid-cols-2 gap-4">
          <FormField label="Plate Number" error={errors.plate_number} id="plate_number">
            <Input
              type="text"
              value={data.plate_number || ''}
              onChange={(e) => setData('plate_number', e.target.value)}
              placeholder="License plate"
              aria-invalid={!!errors.plate_number}
            />
          </FormField>
          <FormField label="Plate Region" error={errors.plate_region} id="plate_region">
            <Select value={data.plate_region || ''} onValueChange={(value) => setData('plate_region', value)}>
              <SelectTrigger aria-invalid={!!errors.plate_region}>
                <SelectValue placeholder="Select region" />
              </SelectTrigger>
              <SelectContent>
                {regions.map((region) => (
                  <SelectItem key={region.value} value={region.value}>
                    {region.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormField>
          <FormField label="DOT Expiry" error={errors.dot_expiry} id="dot_expiry">
            <DatePicker
              value={data.dot_expiry || undefined}
              onChange={handleDateChange('dot_expiry')}
            />
          </FormField>
        </div>
      </div>

      <div className="space-y-6 border rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-2">Ownership</h3>
        <div className="grid grid-cols-2 gap-4">
          <FormField label="Fleet Status" error={errors.fleet_status} id="fleet_status">
            <Select
              value={data.fleet_status || ''}
              onValueChange={(value) => setData('fleet_status', value as FleetStatus)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select fleet status" />
              </SelectTrigger>
              <SelectContent>
                {fleetStatuses.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormField>
          <FormField label="Owner Company" error={errors.owner_company} id="owner_company">
            <Input
              type="text"
              value={data.owner_company || ''}
              onChange={(e) => setData('owner_company', e.target.value)}
              placeholder="Owner company"
              aria-invalid={!!errors.owner_company}
            />
          </FormField>
        </div>
      </div>

      <div className="space-y-6 border rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-2">Equipment</h3>
        <div className="grid grid-cols-1 gap-4">
          <FormField label="GPS Note" error={errors.gps_note} id="gps_note">
            <Input
              type="text"
              value={data.gps_note || ''}
              onChange={(e) => setData('gps_note', e.target.value)}
              placeholder="GPS note"
              aria-invalid={!!errors.gps_note}
            />
          </FormField>
        </div>
      </div>

      <div className="space-y-6 border rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-2">Dates</h3>
        <div className="grid grid-cols-2 gap-4">
          <FormField label="Activation Date" error={errors.activation_date} id="activation_date">
            <DatePicker value={data.activation_date || undefined} onChange={handleDateChange('activation_date')} />
          </FormField>
          <FormField label="Deactivation Date" error={errors.deactivation_date} id="deactivation_date">
            <DatePicker
              value={data.deactivation_date || undefined}
              onChange={handleDateChange('deactivation_date')}
            />
          </FormField>
        </div>
      </div>

      <div className="lg:col-span-2 flex justify-end">
        <Button type="submit" disabled={processing}>
          {buttonLabel}
        </Button>
      </div>
    </form>
  );
}
