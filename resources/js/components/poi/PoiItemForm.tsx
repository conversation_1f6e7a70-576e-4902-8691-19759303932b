import { FormEventHandler } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { FormField } from '@/components/ui/form-field';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { X, Plus } from 'lucide-react';

interface PoiItemFormProps {
  data: any;
  setData: (field: string, value: any) => void;
  errors: Record<string, string>;
  processing: boolean;
  onSubmit: FormEventHandler;
  buttonLabel: string;
  cancelHref: string;
}

const DAYS_OF_WEEK = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

const DAY_LABELS: Record<string, string> = {
  monday: 'Monday',
  tuesday: 'Tuesday',
  wednesday: 'Wednesday',
  thursday: 'Thursday',
  friday: 'Friday',
  saturday: 'Saturday',
  sunday: 'Sunday',
};

export default function PoiItemForm({
  data,
  setData,
  errors,
  processing,
  onSubmit,
  buttonLabel,
  cancelHref,
}: PoiItemFormProps) {
  const openingHours = data.opening_hours || {};

  const updateOpeningHours = (day: string, hours: string[]) => {
    const newOpeningHours = { ...openingHours, [day]: hours };
    setData('opening_hours', newOpeningHours);
  };

  const addTimeSlot = (day: string) => {
    const currentHours = openingHours[day] || [];
    updateOpeningHours(day, [...currentHours, '09:00-17:00']);
  };

  const removeTimeSlot = (day: string, index: number) => {
    const currentHours = openingHours[day] || [];
    const newHours = currentHours.filter((_: string, i: number) => i !== index);
    updateOpeningHours(day, newHours);
  };

  const updateTimeSlot = (day: string, index: number, value: string) => {
    const currentHours = openingHours[day] || [];
    const newHours = [...currentHours];
    newHours[index] = value;
    updateOpeningHours(day, newHours);
  };

  const copyFromPrevious = (day: string) => {
    const dayIndex = DAYS_OF_WEEK.indexOf(day);
    if (dayIndex === 0) return;

    const previousDay = DAYS_OF_WEEK[dayIndex - 1];
    const previousHours = openingHours[previousDay] || [];
    updateOpeningHours(day, [...previousHours]);
  };

  return (
    <form onSubmit={onSubmit} className="mt-8 max-w-4xl space-y-6 lg:max-w-3xl">
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField label="Name" error={errors.name} required>
              <Input
                id="name"
                value={data.name || ''}
                onChange={(e) => setData('name', e.target.value)}
                required
                autoFocus
                placeholder="POI name"
              />
            </FormField>

            <div className="grid gap-4 md:grid-cols-2">
              <FormField label="Phone" error={errors.phone}>
                <Input
                  id="phone"
                  value={data.phone || ''}
                  onChange={(e) => setData('phone', e.target.value)}
                  placeholder="+****************"
                />
              </FormField>

              <FormField label="Email" error={errors.email}>
                <Input
                  id="email"
                  type="email"
                  value={data.email || ''}
                  onChange={(e) => setData('email', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </FormField>
            </div>

            <FormField label="Website" error={errors.website}>
              <Input
                id="website"
                value={data.website || ''}
                onChange={(e) => setData('website', e.target.value)}
                placeholder="https://example.com"
              />
            </FormField>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Address</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <FormField label="Address Line 1" error={errors.address_line1}>
                <Input
                  id="address_line1"
                  value={data.address_line1 || ''}
                  onChange={(e) => setData('address_line1', e.target.value)}
                  placeholder="123 Main Street"
                />
              </FormField>

              <FormField label="Address Line 2" error={errors.address_line2}>
                <Input
                  id="address_line2"
                  value={data.address_line2 || ''}
                  onChange={(e) => setData('address_line2', e.target.value)}
                  placeholder="Suite 100"
                />
              </FormField>
            </div>

            <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
              <FormField label="City" error={errors.city}>
                <Input
                  id="city"
                  value={data.city || ''}
                  onChange={(e) => setData('city', e.target.value)}
                  placeholder="City"
                />
              </FormField>

              <FormField label="State" error={errors.state}>
                <Input
                  id="state"
                  value={data.state || ''}
                  onChange={(e) => setData('state', e.target.value)}
                  placeholder="CA"
                />
              </FormField>

              <FormField label="Postal Code" error={errors.postal_code}>
                <Input
                  id="postal_code"
                  value={data.postal_code || ''}
                  onChange={(e) => setData('postal_code', e.target.value)}
                  placeholder="90210"
                />
              </FormField>

              <FormField label="Country" error={errors.country}>
                <Input
                  id="country"
                  value={data.country || ''}
                  onChange={(e) => setData('country', e.target.value)}
                  placeholder="USA"
                />
              </FormField>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <FormField label="Latitude" error={errors.latitude}>
                <Input
                  id="latitude"
                  type="number"
                  step="any"
                  value={data.latitude || ''}
                  onChange={(e) => setData('latitude', e.target.value ? parseFloat(e.target.value) : null)}
                  placeholder="34.0522"
                />
              </FormField>

              <FormField label="Longitude" error={errors.longitude}>
                <Input
                  id="longitude"
                  type="number"
                  step="any"
                  value={data.longitude || ''}
                  onChange={(e) => setData('longitude', e.target.value ? parseFloat(e.target.value) : null)}
                  placeholder="-118.2437"
                />
              </FormField>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Opening Hours</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {DAYS_OF_WEEK.map((day, dayIndex) => {
            const dayHours = openingHours[day] || [];
            const isClosed = dayHours.length === 0;

            return (
              <div key={day} className="space-y-2 rounded-lg border p-3">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium">{DAY_LABELS[day]}</h4>
                  <div className="flex items-center gap-2">
                    {dayIndex > 0 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => copyFromPrevious(day)}
                        className="h-7 text-xs"
                      >
                        Copy Previous
                      </Button>
                    )}
                    {isClosed ? (
                      <Badge variant="secondary" className="text-xs">
                        Closed
                      </Badge>
                    ) : (
                      <Badge variant="default" className="text-xs">
                        {dayHours.length} slot{dayHours.length !== 1 ? 's' : ''}
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  {dayHours.map((timeSlot: string, index: number) => (
                    <div key={index} className="flex items-center gap-2">
                      <Input
                        value={timeSlot}
                        onChange={(e) => updateTimeSlot(day, index, e.target.value)}
                        placeholder="09:00-17:00"
                        className="h-8 flex-1"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => removeTimeSlot(day, index)}
                        className="h-8 w-8"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}

                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => addTimeSlot(day)}
                    className="h-8 w-full text-xs"
                  >
                    <Plus className="mr-1 h-3 w-3" />
                    Add Time Slot
                  </Button>
                </div>
              </div>
            );
          })}
        </CardContent>
      </Card>

      <div className="flex items-center justify-end gap-4 px-6">
        <Button type="button" variant="outline" asChild>
          <a href={cancelHref}>Cancel</a>
        </Button>
        <Button type="submit" disabled={processing}>
          {processing ? 'Saving...' : buttonLabel}
        </Button>
      </div>
    </form>
  );
}
