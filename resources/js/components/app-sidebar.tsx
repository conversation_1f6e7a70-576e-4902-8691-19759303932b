import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/react';
import {
  LayoutGrid,
  UsersIcon,
  Truck,
  Caravan,
  PlugZap,
  ContactRoundIcon,
  HardDriveIcon,
  Settings,
} from 'lucide-react';
import AppLogo from './app-logo';

const mainNavItems: NavItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: LayoutGrid,
  },
  {
    title: 'Users',
    href: '/users/office',
    icon: UsersIcon,
    submenu: [
      {
        title: 'Office Users',
        href: '/users/office',
      },
      {
        title: 'Dispatchers',
        href: '/users/dispatchers',
      },
    ],
  },
  {
    title: 'Drivers',
    href: '/drivers',
    icon: ContactRoundIcon,
    submenu: [
      {
        title: 'Drivers',
        href: '/drivers',
      },
    ],
  },
  {
    title: 'Trucks',
    href: '/trucks',
    icon: Truck,
    submenu: [
      {
        title: 'Trucks',
        href: '/trucks',
      },
      {
        title: 'Notifications',
        href: '/trucks/notifications',
      },
      {
        title: 'Availability',
        href: '/trucks/availability',
      },
      {
        title: 'Maintenance',
        href: '/trucks/maintenance',
      },
      {
        title: 'Ownerships',
        href: '/trucks/ownerships',
      },
    ],
  },
  {
    title: 'Trailers',
    href: '/trailers',
    icon: Caravan,
    submenu: [
      {
        title: 'Trailers',
        href: '/trailers',
      },
      {
        title: 'Maintenance',
        href: '/trailers/maintenance',
      },
      {
        title: 'Notifications',
        href: '/trailers/notifications',
      },
    ],
  },
  {
    title: 'Devices',
    href: '/devices',
    icon: HardDriveIcon,
    submenu: [
      {
        title: 'Devices',
        href: '/devices',
      },
      {
        title: 'Import',
        href: '/devices/import',
      },
    ],
  },
  {
    title: 'Integrations',
    href: '/integrations',
    icon: PlugZap,
    submenu: [
      {
        title: 'Telematics',
        href: '/integrations/telematics',
      },
    ],
  },
  {
    title: 'Settings',
    href: '/settings/poi',
    icon: Settings,
    submenu: [
      {
        title: 'POI',
        href: '/settings/poi',
      },
    ],
  },
];

const footerNavItems: NavItem[] = [];

export function AppSidebar() {
  return (
    <Sidebar collapsible="icon" variant="inset">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <Link href="/dashboard" prefetch>
                <AppLogo />
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <NavMain items={mainNavItems} />
      </SidebarContent>

      <SidebarFooter>
        <NavFooter items={footerNavItems} className="mt-auto" />
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
