import { BreadcrumbItem } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm, usePage } from '@inertiajs/react';
import HeadingSmall from '@/components/heading-small';
import { FormEventHandler } from 'react';
import { toast } from 'sonner';
import { TruckFormData } from '@/types/truck';
import { fleetStatusLabels } from '@/types/fleetStatus';
import { ownershipTypeLabels } from '@/types/ownershipType';
import TruckForm from '@/components/trucks/TruckForm';
import { DriverData } from '@/types/driver';
import { truckStatusLabels } from '@/types/truckStatus';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Trucks',
    href: route('trucks.index'),
  },
  {
    title: 'Create Truck',
    href: route('trucks.create'),
  },
];

interface CreateTruckPageProps {
  errors: Record<string, string>;
  regions: { value: string; label: string }[];
  drivers: DriverData[];
  [key: string]: unknown;
}

export default function CreateTruck() {
  const { errors, regions, drivers } = usePage<CreateTruckPageProps>().props;
  const { data, setData, post, processing } = useForm<TruckFormData & Record<string, any>>({
    unit_number: '',
    make: null,
    model: null,
    year: null,
    vin: null,
    status: 'active',
    regulatory_compliance_expiry: null,
    owner_company: null,
    fleet_status: null,
    activation_date: null,
    deactivation_date: null,
    lease_termination_date: null,
    gps_note: null,
    gateway_serial: null,
    deer_guards: false,
    dash_camera: false,
    has_headrack: false,
    ownership_type: null,
    plate: null,
    plate_region: 'il',
    driver_1_id: null,
    driver_2_id: null,
    plate_expiration_date: null,
    deactivation_reason: null,
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();

    post(route('trucks.store'), {
      onSuccess: () => {
        toast.success('Truck created successfully');
      },
      onError: (errors) => {
        const errorMessage = Object.values(errors)[0] as string;
        toast.error(errorMessage);
      },
    });
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Create Truck" />

      <div className="px-4 py-6">
        <HeadingSmall title="Create Truck" description="Create a new truck in the fleet" />
        <TruckForm
          data={data}
          setData={setData}
          errors={errors}
          processing={processing}
          onSubmit={submit}
          regions={regions}
          fleetStatuses={Object.entries(fleetStatusLabels).map(([value, label]) => ({ value, label }))}
          ownershipType={Object.entries(ownershipTypeLabels).map(([value, label]) => ({ value, label }))}
          truckStatuses={Object.entries(truckStatusLabels).map(([value, label]) => ({ value, label }))}
          buttonLabel="Save"
          drivers={drivers}
        />
      </div>
    </AppLayout>
  );
}
