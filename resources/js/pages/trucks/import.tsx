import { BreadcrumbItem, SharedData } from '@/types';
import { Head, router, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  UploadIcon,
  FileIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  DownloadIcon,
  RotateCcwIcon,
  InfoIcon,
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useRef, useState, useEffect } from 'react';
import Uppy from '@uppy/core';
import XHRUpload from '@uppy/xhr-upload';
import Dashboard from '@uppy/dashboard';

import '@uppy/core/dist/style.min.css';
import '@uppy/dashboard/dist/style.min.css';

interface ImportRecord {
  id: number;
  filename: string;
  imported_at: string;
  total_records: number;
  imported_records: number;
  updated_records: number;
  failed_records: number;
  status: 'completed' | 'completed_with_errors' | 'processing' | 'failed';
  download_url?: string;
}

interface ImportPageProps extends SharedData {
  recentImports: ImportRecord[];
}

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Trucks',
    href: '/trucks',
  },
  {
    title: 'Import',
    href: '/trucks/import',
  },
];

const getStatusBadge = (status: string) => {
  const statusName = status.includes('\\') ? status.split('\\').pop() || status : status;

  switch (statusName.toLowerCase()) {
    case 'completed':
      return (
        <Badge variant="outline" className="border-green-200 bg-green-50 text-green-700">
          <CheckCircleIcon className="mr-1 h-3 w-3" />
          Completed
        </Badge>
      );
    case 'completedwitherrors':
      return (
        <Badge variant="outline" className="border-yellow-200 bg-yellow-50 text-yellow-700">
          <ClockIcon className="mr-1 h-3 w-3" />
          Completed with Errors
        </Badge>
      );
    case 'processing':
      return (
        <Badge variant="outline" className="border-blue-200 bg-blue-50 text-blue-700">
          <ClockIcon className="mr-1 h-3 w-3" />
          Processing
        </Badge>
      );
    case 'pending':
      return (
        <Badge variant="outline" className="border-gray-200 bg-gray-50 text-gray-700">
          <ClockIcon className="mr-1 h-3 w-3" />
          Pending
        </Badge>
      );
    case 'failed':
      return (
        <Badge variant="outline" className="border-red-200 bg-red-50 text-red-700">
          <XCircleIcon className="mr-1 h-3 w-3" />
          Failed
        </Badge>
      );
    default:
      return (
        <Badge variant="outline" className="border-gray-200 bg-gray-50 text-gray-700">
          {statusName}
        </Badge>
      );
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString();
};

export default function TrucksImport() {
  const { recentImports } = usePage<ImportPageProps>().props;
  const [uploadStatus, setUploadStatus] = useState<string>('');
  const [repeatingImport, setRepeatingImport] = useState<number | null>(null);
  const [showRepeatConfirm, setShowRepeatConfirm] = useState<boolean>(false);
  const [selectedImportId, setSelectedImportId] = useState<number | null>(null);
  const uppyRef = useRef<Uppy | null>(null);

  const handleRepeatImportClick = (importId: number) => {
    setSelectedImportId(importId);
    setShowRepeatConfirm(true);
  };

  const handleConfirmRepeat = async () => {
    if (!selectedImportId) return;

    setShowRepeatConfirm(false);
    setRepeatingImport(selectedImportId);
    setUploadStatus('Starting repeat import...');

    try {
      const response = await fetch(route('trucks.import.repeat', selectedImportId), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
          Accept: 'application/json',
        },
      });

      const result = await response.json();

      if (result.success) {
        setUploadStatus('Import repeated successfully!');
        setTimeout(() => {
          router.reload({ only: ['recentImports'] });
          setUploadStatus('');
        }, 2000);
      } else {
        setUploadStatus(`Failed to repeat import: ${result.message}`);
        setTimeout(() => setUploadStatus(''), 5000);
      }
    } catch (error) {
      console.error('Repeat import error:', error);
      setUploadStatus('Failed to repeat import. Please try again.');
      setTimeout(() => setUploadStatus(''), 5000);
    } finally {
      setRepeatingImport(null);
      setSelectedImportId(null);
    }
  };

  const handleCancelRepeat = () => {
    setShowRepeatConfirm(false);
    setSelectedImportId(null);
  };

  useEffect(() => {
    if (!uppyRef.current) {
      const csrfToken =
        document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
        (window as any).Laravel?.csrfToken ||
        '';

      uppyRef.current = new Uppy({
        restrictions: {
          maxNumberOfFiles: 1,
          allowedFileTypes: ['.csv', '.xlsx'],
          maxFileSize: 2 * 1024 * 1024, // 2MB
        },
        autoProceed: false,
      })
        .use(Dashboard, {
          target: '#uppy-dashboard',
          inline: true,
          width: '100%',
          height: 400,
          showProgressDetails: true,
          note: 'CSV and Excel files only, up to 2 MB',
          proudlyDisplayPoweredByUppy: false,
          showRemoveButtonAfterComplete: true,
        })
        .use(XHRUpload, {
          endpoint: route('trucks.import.process'),
          method: 'POST',
          formData: true,
          fieldName: 'file',
          headers: {
            'X-CSRF-TOKEN': csrfToken,
            Accept: 'application/json',
          },
        })
        .on('upload', () => {
          setUploadStatus('Uploading file...');
        })
        .on('upload-progress', (_file, progress) => {
          setUploadStatus(`Uploading: ${Math.round(progress.percentage || 0)}%`);
        })
        .on('complete', (result) => {
          if (result.successful && result.successful.length > 0) {
            setUploadStatus('Upload completed successfully!');
            setTimeout(() => {
              router.reload({ only: ['recentImports'] });
              setUploadStatus('');
            }, 2000);
          } else {
            setUploadStatus('Upload failed. Please try again.');
            console.error('Upload failed:', result);
          }
        })
        .on('error', (error) => {
          setUploadStatus('Upload failed. Please try again.');
          console.error('Upload error:', error);
        });
    }

    return () => {
      if (uppyRef.current) {
        uppyRef.current.destroy();
        uppyRef.current = null;
      }
    };
  }, []);

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Import Trucks" />

      <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-4">
        {/* Upload Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UploadIcon className="h-5 w-5" />
              Upload Trucks File
            </CardTitle>
            <CardDescription>
              Import trucks data from CSV or Excel files. The file should contain columns for unit number, make, model,
              year, and other truck details.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Upload Guidelines */}
            <div className="rounded-lg border bg-gray-50 p-4">
              <h4 className="mb-2 font-medium text-gray-900">File Format Guidelines:</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Supported formats: CSV (.csv) and Excel (.xlsx)</li>
                <li>• Maximum file size: 2MB</li>
                <li>• Required columns: unit_number, make, model, year</li>
                <li>• Optional columns: fleet_status, ownership_type, region, driver_1_id, driver_2_id</li>
                <li>• Existing trucks will be updated based on unit_number</li>
              </ul>
            </div>

            <div
              id="uppy-dashboard"
              className="w-full"
              style={
                {
                  '--uppy-c-white': '#ffffff',
                  '--uppy-c-gray-50': '#f9fafb',
                  '--uppy-c-gray-100': '#f3f4f6',
                  '--uppy-border-radius-card': '0.5rem',
                } as React.CSSProperties
              }
            ></div>
            {uploadStatus && (
              <div className="mt-4 text-center">
                <div
                  className={`rounded-md px-3 py-2 text-sm ${
                    uploadStatus.includes('successfully') || uploadStatus.includes('completed')
                      ? 'border border-green-200 bg-green-50 text-green-700'
                      : uploadStatus.includes('Failed') || uploadStatus.includes('failed')
                        ? 'border border-red-200 bg-red-50 text-red-700'
                        : 'border border-blue-200 bg-blue-50 text-blue-700'
                  }`}
                >
                  {uploadStatus}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Imports Section */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Imports</CardTitle>
            <CardDescription>View the status and details of your recent truck import operations.</CardDescription>
          </CardHeader>
          <CardContent>
            {recentImports.length > 0 ? (
              <div className="space-y-4">
                {recentImports.map((importRecord) => (
                  <div
                    key={importRecord.id}
                    className="flex items-center justify-between rounded-lg border p-4 transition-colors hover:bg-gray-50"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                          <FileIcon className="h-5 w-5 text-blue-600" />
                        </div>
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="flex items-center gap-3">
                          <h4 className="truncate text-sm font-medium text-gray-900">{importRecord.filename}</h4>
                          {getStatusBadge(importRecord.status)}
                        </div>
                        <div className="mt-1 flex items-center gap-4 text-sm text-gray-500">
                          <span>{formatDate(importRecord.imported_at)}</span>
                          <span>•</span>
                          <span>{importRecord.total_records} total records</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6">
                      {/* Stats */}
                      <div className="hidden items-center space-x-6 text-sm md:flex">
                        <div className="text-center">
                          <div className="font-medium text-green-600">{importRecord.imported_records}</div>
                          <div className="text-xs text-gray-500">Imported</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium text-blue-600">{importRecord.updated_records}</div>
                          <div className="text-xs text-gray-500">Updated</div>
                        </div>
                        {importRecord.failed_records > 0 && (
                          <div className="text-center">
                            <div className="font-medium text-red-600">{importRecord.failed_records}</div>
                            <div className="text-xs text-gray-500">Failed</div>
                          </div>
                        )}
                      </div>

                      {/* Actions */}
                      <div className="flex items-center space-x-2">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleRepeatImportClick(importRecord.id)}
                                disabled={repeatingImport === importRecord.id}
                                className="h-8 w-8 p-0"
                              >
                                <RotateCcwIcon
                                  className={`h-3 w-3 ${repeatingImport === importRecord.id ? 'animate-spin' : ''}`}
                                />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Repeat this import with the same file</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  router.visit(route('trucks.import.details', importRecord.id));
                                }}
                                className="h-8 w-8 p-0"
                              >
                                <InfoIcon className="h-3 w-3" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>View import details</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  if (importRecord.download_url) {
                                    window.open(importRecord.download_url, '_blank');
                                  } else {
                                    console.log('Download URL not available for import:', importRecord.id);
                                  }
                                }}
                                className="h-8 w-8 p-0"
                              >
                                <DownloadIcon className="h-3 w-3" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Download import file</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-16 text-center">
                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
                  <FileIcon className="h-8 w-8 text-gray-400" />
                </div>
                <h3 className="mt-4 text-lg font-medium text-gray-900">No imports yet</h3>
                <p className="mt-2 max-w-sm text-sm text-gray-500">
                  Upload your first truck data file to see import history and track progress here.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Repeat Import Confirmation Dialog */}
      <Dialog open={showRepeatConfirm} onOpenChange={setShowRepeatConfirm}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Repeat Import</DialogTitle>
            <DialogDescription>
              Are you sure you want to repeat this import? This will process the same file again and may create
              duplicate records or update existing ones.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelRepeat}>
              Cancel
            </Button>
            <Button onClick={handleConfirmRepeat}>Repeat Import</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AppLayout>
  );
}
