import { BreadcrumbItem, SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { ArrowLeftIcon, FileIcon, CheckCircleIcon, XCircleIcon, AlertTriangleIcon, ClockIcon } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface ImportRecord {
  id: number;
  filename: string;
  imported_at: string;
  total_records: number;
  imported_records: number;
  updated_records: number;
  failed_records: number;
  status: string;
  errors: string[];
  success_rate: number;
  has_errors: boolean;
}

interface ImportDetailsPageProps extends SharedData {
  importRecord: ImportRecord;
}

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Trucks',
    href: '/trucks',
  },
  {
    title: 'Import',
    href: '/trucks/import',
  },
  {
    title: 'Details',
    href: '',
  },
];

const getStatusBadge = (status: string) => {
  const statusName = status.includes('\\') ? status.split('\\').pop() || status : status;

  switch (statusName.toLowerCase()) {
    case 'completed':
      return (
        <Badge variant="outline" className="border-green-200 bg-green-50 text-green-700">
          <CheckCircleIcon className="mr-1 h-3 w-3" />
          Completed
        </Badge>
      );
    case 'completedwitherrors':
      return (
        <Badge variant="outline" className="border-yellow-200 bg-yellow-50 text-yellow-700">
          <AlertTriangleIcon className="mr-1 h-3 w-3" />
          Completed with Errors
        </Badge>
      );
    case 'processing':
      return (
        <Badge variant="outline" className="border-blue-200 bg-blue-50 text-blue-700">
          <ClockIcon className="mr-1 h-3 w-3" />
          Processing
        </Badge>
      );
    case 'pending':
      return (
        <Badge variant="outline" className="border-gray-200 bg-gray-50 text-gray-700">
          <ClockIcon className="mr-1 h-3 w-3" />
          Pending
        </Badge>
      );
    case 'failed':
      return (
        <Badge variant="outline" className="border-red-200 bg-red-50 text-red-700">
          <XCircleIcon className="mr-1 h-3 w-3" />
          Failed
        </Badge>
      );
    default:
      return (
        <Badge variant="outline" className="border-gray-200 bg-gray-50 text-gray-700">
          {statusName}
        </Badge>
      );
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString();
};

export default function ImportDetails() {
  const { importRecord } = usePage<ImportDetailsPageProps>().props;

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`Import Details - ${importRecord.filename}`} />

      <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/trucks/import">
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <ArrowLeftIcon className="h-4 w-4" />
                Back to Imports
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Import Details</h1>
            </div>
          </div>
          {getStatusBadge(importRecord.status)}
        </div>

        {/* Import Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileIcon className="h-5 w-5" />
              Import Summary
            </CardTitle>
            <CardDescription>Overview of the import process for {importRecord.filename}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* File Information */}
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <h3 className="mb-3 text-sm font-medium text-gray-900">File Information</h3>
                <dl className="space-y-2">
                  <div>
                    <dt className="text-xs text-gray-500">Filename</dt>
                    <dd className="text-sm font-medium text-gray-900">{importRecord.filename}</dd>
                  </div>
                  <div>
                    <dt className="text-xs text-gray-500">Import Date</dt>
                    <dd className="text-sm text-gray-700">{formatDate(importRecord.imported_at)}</dd>
                  </div>
                  <div>
                    <dt className="text-xs text-gray-500">Status</dt>
                    <dd className="text-sm">{getStatusBadge(importRecord.status)}</dd>
                  </div>
                </dl>
              </div>

              <div>
                <h3 className="mb-3 text-sm font-medium text-gray-900">Performance Metrics</h3>
                <dl className="space-y-2">
                  <div>
                    <dt className="text-xs text-gray-500">Success Rate</dt>
                    <dd className="text-sm font-medium text-gray-900">{importRecord.success_rate.toFixed(2)}%</dd>
                  </div>
                  <div>
                    <dt className="text-xs text-gray-500">Total Records</dt>
                    <dd className="text-sm text-gray-700">{importRecord.total_records}</dd>
                  </div>
                </dl>
              </div>
            </div>

            <div className="border-t border-gray-200"></div>

            {/* Statistics */}
            <div>
              <h3 className="mb-4 text-sm font-medium text-gray-900">Import Statistics</h3>
              <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                <div className="rounded-lg border border-green-200 bg-green-50 p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">{importRecord.imported_records}</div>
                  <div className="text-xs text-green-700">New Records</div>
                </div>
                <div className="rounded-lg border border-blue-200 bg-blue-50 p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">{importRecord.updated_records}</div>
                  <div className="text-xs text-blue-700">Updated Records</div>
                </div>
                <div className="rounded-lg border border-red-200 bg-red-50 p-4 text-center">
                  <div className="text-2xl font-bold text-red-600">{importRecord.failed_records}</div>
                  <div className="text-xs text-red-700">Failed Records</div>
                </div>
                <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 text-center">
                  <div className="text-2xl font-bold text-gray-600">{importRecord.total_records}</div>
                  <div className="text-xs text-gray-700">Total Records</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error Details */}
        {importRecord.has_errors && importRecord.errors.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-700">
                <XCircleIcon className="h-5 w-5" />
                Error Details
              </CardTitle>
              <CardDescription>
                {importRecord.errors.length} error{importRecord.errors.length !== 1 ? 's' : ''} occurred during the
                import process
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {importRecord.errors.map((error, index) => (
                  <div key={index} className="rounded-md border border-red-200 bg-red-50 p-3">
                    <div className="flex items-start gap-3">
                      <XCircleIcon className="mt-0.5 h-4 w-4 flex-shrink-0 text-red-500" />
                      <div className="flex-1">
                        <p className="font-mono text-sm text-red-800">{error}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Success Message */}
        {!importRecord.has_errors && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-700">
                <CheckCircleIcon className="h-5 w-5" />
                Import Successful
              </CardTitle>
              <CardDescription>All records were processed successfully without any errors</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border border-green-200 bg-green-50 p-4">
                <div className="flex items-center gap-3">
                  <CheckCircleIcon className="h-5 w-5 text-green-500" />
                  <p className="text-sm text-green-800">
                    Successfully processed {importRecord.total_records} records with no errors.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}
