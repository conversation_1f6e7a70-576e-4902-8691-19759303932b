import { BreadcrumbItem } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm, usePage } from '@inertiajs/react';
import HeadingSmall from '@/components/heading-small';
import { FormEventHandler } from 'react';
import { toast } from 'sonner';
import TruckForm from '@/components/trucks/TruckForm';
import { Truck, TruckFormData } from '@/types/truck';
import { DriverData } from '@/types/driver';
interface EditTruckPageProps {
  truck: Truck;
  fleetStatuses: { value: string; label: string }[];
  ownershipType: { value: string; label: string }[];
  status: { value: string; label: string }[];
  regions: { value: string; label: string }[];
  drivers: DriverData[];
  errors: Record<string, string>;
  [key: string]: unknown;
}

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Trucks',
    href: route('trucks.index'),
  },
  {
    title: 'Edit Truck',
    href: '#',
  },
];

export default function EditTruck() {
  const { truck, fleetStatuses, ownershipType, errors, regions, drivers, status } = usePage<EditTruckPageProps>().props;

  const { data, setData, patch, processing } = useForm<TruckFormData & Record<string, any>>({
    unit_number: truck.unit_number,
    make: truck.make || null,
    model: truck.model || null,
    year: truck.year || null,
    vin: truck.vin || null,
    status: truck.status || 'active',
    regulatory_compliance_expiry: truck.regulatory_compliance_expiry || null,
    owner_company: truck.owner_company || null,
    fleet_status: truck.fleet_status || null,
    activation_date: truck.activation_date || null,
    deactivation_date: truck.deactivation_date || null,
    lease_termination_date: truck.lease_termination_date || null,
    gps_note: truck.gps_note || null,
    gateway_serial: truck.gateway_serial || null,
    deer_guards: truck.deer_guards,
    dash_camera: truck.dash_camera,
    has_headrack: truck.has_headrack,
    ownership_type: truck.ownership_type || null,
    plate: truck.plate || null,
    plate_region: truck.plate_region || 'il',
    driver_1_id: truck.driver_1_id || null,
    driver_2_id: truck.driver_2_id || null,
    plate_expiration_date: truck.plate_expiration_date || null,
    deactivation_reason: truck.deactivation_reason || null,
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();

    patch(route('trucks.update', { truck: truck.id }), {
      onSuccess: () => {
        toast.success('Truck updated successfully');
      },
      onError: (errors) => {
        const errorMessage = Object.values(errors)[0] as string;
        toast.error(errorMessage);
      },
    });
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Edit Truck" />

      <div className="px-4 py-6">
        <HeadingSmall title="Edit Truck" description="Update truck information" />
        <TruckForm
          data={data}
          setData={setData}
          errors={errors}
          processing={processing}
          onSubmit={submit}
          regions={regions}
          fleetStatuses={fleetStatuses}
          ownershipType={ownershipType}
          drivers={drivers}
          truckStatuses={status}
          buttonLabel="Update Truck"
        />
      </div>
    </AppLayout>
  );
}
