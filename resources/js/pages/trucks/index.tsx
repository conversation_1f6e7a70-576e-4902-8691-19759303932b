import { BreadcrumbItem } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { BellIcon, PlusIcon, UploadIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/datatable';
import { columns } from './columns';
import { TruckPageProps, TruckMaintenanceData, Truck } from '@/types/truck';
import { TruckMaintenanceView } from '@/components/trucks/TruckMaintenanceView';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Trucks',
    href: '/trucks',
  },
];

// Hardcoded maintenance data for demo purposes
// TODO: Replace with API data from Samsara TMS in future iterations
const getMaintenanceData = (truckId: number): TruckMaintenanceData => {
  const baseData = {
    truckId,
    currentMileage: 125000 + (truckId * 1500),
    lastServiceDate: '2024-11-15',
    lastServiceMileage: 120000 + (truckId * 1500),
    nextServiceDue: {
      date: '2025-02-15',
      mileage: 130000 + (truckId * 1500),
    },
    oilChange: {
      lastDate: '2024-12-01',
      lastMileage: 123000 + (truckId * 1500),
      nextDue: '2025-01-15',
      nextDueMileage: 128000 + (truckId * 1500),
    },
    tireRotation: {
      lastDate: '2024-10-20',
      lastMileage: 118000 + (truckId * 1500),
      nextDue: '2025-01-20',
      nextDueMileage: 133000 + (truckId * 1500),
    },
    inspection: {
      dotExpiry: '2025-06-30',
      annualInspectionDue: '2025-03-15',
      lastInspectionDate: '2024-03-15',
    },
  };

  // Generate different maintenance statuses and alerts based on truck ID
  const statusOptions: TruckMaintenanceData['maintenanceStatus'][] = ['current', 'due_soon', 'overdue'];
  const status = statusOptions[truckId % 3];

  const alerts = [];
  
  // Add conditional alerts based on status
  if (status === 'due_soon') {
    alerts.push({
      id: `oil-${truckId}`,
      type: 'warning' as const,
      message: 'Oil change due within 500 miles',
      dueDate: baseData.oilChange.nextDue,
      dueMileage: baseData.oilChange.nextDueMileage,
    });
  }
  
  if (status === 'overdue') {
    alerts.push({
      id: `service-${truckId}`,
      type: 'critical' as const,
      message: 'Scheduled maintenance overdue',
      dueDate: baseData.nextServiceDue.date,
      dueMileage: baseData.nextServiceDue.mileage,
    });
  }

  // Add DOT inspection alert for some trucks
  if (truckId % 4 === 0) {
    alerts.push({
      id: `dot-${truckId}`,
      type: 'info' as const,
      message: 'DOT inspection expires in 6 months',
      dueDate: baseData.inspection.dotExpiry,
    });
  }

  return {
    ...baseData,
    maintenanceStatus: status,
    alerts,
  };
};

// Function to get row styling based on maintenance status
const getTruckRowClassName = (truck: Truck): string => {
  const maintenanceData = getMaintenanceData(truck.id);
  
  switch (maintenanceData.maintenanceStatus) {
    case 'overdue':
      return 'bg-red-50/50 hover:bg-red-50/80 border-l-2 border-l-red-500';
    case 'due_soon':
      return 'bg-yellow-50/50 hover:bg-yellow-50/80 border-l-2 border-l-yellow-500';
    case 'current':
    default:
      return 'hover:bg-slate-50/50';
  }
};

export default function Trucks() {
  const { trucks, filters } = usePage<TruckPageProps>().props;

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Trucks" />
      <div className="mt-4 flex items-center justify-end gap-2 px-4 lg:px-4">
        <Button asChild>
          <Link href={route('trucks.create')} className="flex items-center">
            <PlusIcon className="mr-2 h-4 w-4" />
            Add
          </Link>
        </Button>
        <Button asChild>
          <Link href="#" className="flex items-center">
            <BellIcon className="mr-2 h-4 w-4" />
            Notifications
          </Link>
        </Button>
        <Button asChild>
          <Link href={route('trucks.import.index')} className="flex items-center">
            <UploadIcon className="mr-2 h-4 w-4" />
            Import
          </Link>
        </Button>
      </div>
      <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
        <DataTable
          columns={columns}
          data={trucks}
          config={{
            enableGlobalFilter: true,
            enableSorting: true,
            enableExpanding: true,
            initialSorting: {
              id: 'unit_number',
              desc: false,
            },
            persistSort: true,
            filters: filters,
            getRowCanExpand: () => true, // All trucks can be expanded to show maintenance details
            getRowClassName: getTruckRowClassName, // Color-code rows based on maintenance status
          }}
          renderSubComponent={({ row }) => {
            const maintenanceData = getMaintenanceData(row.original.id);
            return <TruckMaintenanceView data={maintenanceData} />;
          }}
        />
      </div>
    </AppLayout>
  );
}
