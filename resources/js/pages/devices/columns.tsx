import { ColumnDef } from '@/types/table';
import { MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { router } from '@inertiajs/react';

export const columns: ColumnDef<any>[] = [
  {
    id: 'name',
    accessorKey: 'name',
    header: 'Name',
    meta: { initiallyVisible: true },
  },
  {
    id: 'device_type',
    accessorKey: 'device_type',
    header: 'Device Type',
    meta: { initiallyVisible: true },
  },
  {
    id: 'status',
    accessorKey: 'status',
    header: 'Status',
    meta: { initiallyVisible: true },
  },
  {
    id: 'ip_address',
    accessorKey: 'ip_address',
    header: 'IP Address',
    meta: { initiallyVisible: true },
  },
  {
    id: 'mac_address',
    accessorKey: 'mac_address',
    header: 'MAC Address',
    meta: { initiallyVisible: false },
  },
  {
    id: 'last_communication_at',
    accessorKey: 'last_communication_at',
    header: 'Last Communication',
    meta: { initiallyVisible: true },
  },
  {
    id: 'actions',
    header: 'Actions',
    enableHiding: false,
    cell: ({ row }) => {
      const device = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => router.visit(route('devices.edit', { device: device.id }))}>
              Edit Device
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => {
                if (confirm('Are you sure you want to delete this truck?')) {
                  router.delete(route('devices.destroy', { device: device.id }));
                }
              }}
            >
              Delete Device
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
