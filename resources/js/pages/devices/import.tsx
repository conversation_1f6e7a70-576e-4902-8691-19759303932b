import { BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { PlusIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Devices',
    href: '/devices',
  },
  {
    title: 'Import',
    href: '/devices/import',
  },
];

export default function Devices() {
  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Devices Import" />
    </AppLayout>
  );
}
