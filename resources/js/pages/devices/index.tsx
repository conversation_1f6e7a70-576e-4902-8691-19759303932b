import { BreadcrumbItem } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { PlusIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/datatable';
import { columns } from '@/pages/devices/columns';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Devices',
    href: '/devices',
  },
];

export default function Devices() {
  const { devices } = usePage<any>().props;

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Devices" />
      <div className="mt-4 flex items-center justify-end gap-2 px-4 lg:px-4">
        <Button asChild>
          <Link href={`/devices/import`} className="flex items-center">
            <PlusIcon className="mr-2 h-4 w-4" />
            Import
          </Link>
        </Button>
      </div>
      <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
        <DataTable columns={columns} data={devices} />
      </div>
    </AppLayout>
  );
}
