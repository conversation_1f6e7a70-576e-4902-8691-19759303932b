import { BreadcrumbItem } from '@/types';
import { Head, useForm, usePage } from '@inertiajs/react';
import { FormEventHandler, useState } from 'react';
import { toast } from 'sonner';
import AppLayout from '@/layouts/app-layout';
import HeadingSmall from '@/components/heading-small';
import TelematicsForm from '@/components/integrations/TelematicsForm';
import { TelematicsFormData, TelematicsSettingsPageProps } from '@/types/telematics';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FormField } from '@/components/ui/form-field';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Integrations',
    href: '/integrations',
  },
  {
    title: 'Telematics Settings',
    href: '/integrations/telematics',
  },
];

export default function TelematicsSettings() {
  const { settings } = usePage<TelematicsSettingsPageProps>().props;
  const [selectedProviderId, setSelectedProviderId] = useState<string>(
    settings.length > 0 ? settings[0].id : ''
  );

  const selectedProvider = settings.find(provider => provider.id === selectedProviderId);

  const initialFormData: TelematicsFormData = {
    enabled: selectedProvider?.enabled || false,
    credentials: selectedProvider?.fields.reduce((acc, field) => {
      acc[field.key] = field.value || '';
      return acc;
    }, {} as Record<string, string>) || {},
  };

  const { data, setData, put, processing, errors } = useForm<TelematicsFormData>(initialFormData);

  const handleProviderChange = (providerId: string) => {
    setSelectedProviderId(providerId);
    const provider = settings.find(p => p.id === providerId);
    if (provider) {
      setData({
        enabled: provider.enabled,
        credentials: provider.fields.reduce((acc, field) => {
          acc[field.key] = field.value || '';
          return acc;
        }, {} as Record<string, string>),
      });
    }
  };

  const submit: FormEventHandler = (e) => {
    e.preventDefault();

    if (!selectedProvider) {
      toast.error('Please select a provider');
      return;
    }

    put(route('integrations.telematics.update', { provider: selectedProvider.id }), {
      onSuccess: () => {
        toast.success('Telematics settings saved successfully');
      },
      onError: (errors) => {
        const errorMessage = Object.values(errors)[0] as string;
        toast.error(errorMessage);
      },
    });
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Telematics Settings" />

      <div className="py-6">
        <HeadingSmall
          title="Telematics Integration"
          description="Configure your telematics provider to sync fleet data automatically"
        />

        <div className="px-4">
          {settings.length > 0 ? (
            <div className="mt-8 max-w-3xl space-y-6">
              {/* Provider Selection */}
              <div className="space-y-6 rounded-lg border p-6">
                <h3 className="mb-2 text-lg font-semibold">Provider Selection</h3>
                <div className="grid gap-6 md:grid-cols-2">
                  <FormField label="Telematics Provider" required id="provider">
                    <Select value={selectedProviderId} onValueChange={handleProviderChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select provider" />
                      </SelectTrigger>
                      <SelectContent>
                        {settings.map((provider) => (
                          <SelectItem key={provider.id} value={provider.id}>
                            {provider.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormField>
                </div>
              </div>

              {/* Provider Form */}
              {selectedProvider && (
                <TelematicsForm
                  data={data}
                  setData={setData}
                  errors={errors}
                  processing={processing}
                  onSubmit={submit}
                  provider={selectedProvider}
                  buttonLabel="Save Settings"
                />
              )}
            </div>
          ) : (
            <div className="mt-8 text-center">
              <p className="text-muted-foreground">No telematics providers are configured.</p>
            </div>
          )}
        </div>
      </div>
    </AppLayout>
  );
}
