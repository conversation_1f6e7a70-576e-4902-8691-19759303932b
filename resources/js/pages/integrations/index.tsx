import { BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Integrations',
    href: '/integrations',
  },
];

export default function Trucks() {
  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Integrations" />
      <div className="mt-4 flex items-center justify-end gap-2 px-4 lg:px-4"></div>
    </AppLayout>
  );
}
