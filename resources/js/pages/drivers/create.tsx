import { BreadcrumbItem } from '@/types';
import { Head, useForm, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import HeadingSmall from '@/components/heading-small';
import { FormEventHandler } from 'react';
import { toast } from 'sonner';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FormField } from '@/components/ui/form-field';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { CreateDriverPageProps, DriverFormData, DriverContractType } from '@/types/driver';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Drivers',
    href: route('drivers.index'),
  },
  {
    title: 'Create Driver',
    href: route('drivers.create'),
  },
];

export default function CreateDriver() {
  const { errors, contractTypes } = usePage<CreateDriverPageProps>().props;
  const { data, setData, post, processing } = useForm<DriverFormData>({
    first_name: '',
    last_name: '',
    phone_number: '',
    contract_type: undefined,
    email: '',
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();

    post(route('drivers.store'), {
      onSuccess: () => {
        toast.success('Driver created successfully');
      },
      onError: (errors) => {
        const errorMessage = Object.values(errors)[0] as string;
        toast.error(errorMessage);
      },
    });
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Create Driver" />

      <div className="px-4 py-6">
        <HeadingSmall title="Create Driver" description="Create a new driver" />

        <form onSubmit={submit} className="mt-8 max-w-xl space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <FormField label="First name" error={errors.first_name} id="first_name">
              <Input
                id="first_name"
                type="text"
                value={data.first_name}
                onChange={e => setData('first_name', e.target.value)}
                placeholder="First name"
                aria-invalid={!!errors.first_name}
                autoComplete="given-name"
              />
            </FormField>
            <FormField label="Last name" error={errors.last_name} id="last_name">
              <Input
                id="last_name"
                type="text"
                value={data.last_name}
                onChange={e => setData('last_name', e.target.value)}
                placeholder="Last name"
                aria-invalid={!!errors.last_name}
                autoComplete="family-name"
              />
            </FormField>
            <FormField label="Phone number" error={errors.phone_number} id="phone_number">
              <Input
                id="phone_number"
                type="tel"
                value={data.phone_number || ''}
                onChange={e => setData('phone_number', e.target.value)}
                placeholder="Phone number"
                aria-invalid={!!errors.phone_number}
                autoComplete="tel"
              />
            </FormField>
            <FormField label="Email address" error={errors.email} id="email">
              <Input
                type="email"
                onChange={(e) => setData('email', e.target.value)}
                aria-invalid={!!errors.email}
                autoComplete="email"
                placeholder="Email"
              />
            </FormField>
            <FormField label="Contract type" error={errors.contract_type} id="contract_type">
              <Select
                value={data.contract_type || ''}
                onValueChange={value => setData('contract_type', value as DriverContractType['value'])}
              >
                <SelectTrigger aria-invalid={!!errors.contract_type}>
                  <SelectValue placeholder="Select contract type" />
                </SelectTrigger>
                <SelectContent>
                  {contractTypes.map(region => (
                    <SelectItem key={region.value} value={region.value}>
                      {region.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormField>
          </div>
          <div className="flex items-center justify-end gap-4">
            <Button type="submit" disabled={processing}>
              Save
            </Button>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}
