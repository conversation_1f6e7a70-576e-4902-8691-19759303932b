import { ColumnDef } from '@/types/table';
import { MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { router } from '@inertiajs/react';

export const columns: ColumnDef<any>[] = [
  {
    id: 'first_name',
    accessorKey: 'first_name',
    header: 'First Name',
    meta: { initiallyVisible: true },
    enableSorting: true,
  },
  {
    id: 'last_name',
    accessorKey: 'last_name',
    header: 'Last Name',
    meta: { initiallyVisible: true },
    enableSorting: true,
  },
  {
    id: 'phone_number',
    accessorKey: 'phone_number',
    header: 'Phone number',
    meta: { initiallyVisible: true },
    enableSorting: true,
  },
  {
    id: 'contract_type',
    accessorKey: 'contract_type',
    header: 'Contract type',
    meta: { initiallyVisible: true },
    enableSorting: true,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    enableHiding: false,
    cell: ({ row }) => {
      const driver = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => router.visit(route('drivers.edit', { driver: driver.id }))}>
              Edit Driver
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => {
                if (confirm('Are you sure you want to delete this truck?')) {
                  router.delete(route('drivers.destroy', { driver: driver.id }));
                }
              }}
            >
              Delete Driver
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
