import { BreadcrumbItem } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { PlusIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/datatable';
import { columns } from '@/pages/drivers/columns';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Drivers',
    href: '/drivers',
  },
];

export default function Drivers() {
  const { drivers } = usePage<any>().props;

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Drivers" />
      <div className="mt-4 flex items-center justify-end gap-2 px-4 lg:px-4">
        <Button asChild>
          <Link href={route('drivers.create')} className="flex items-center">
            <PlusIcon className="mr-2 h-4 w-4" />
            Add
          </Link>
        </Button>
      </div>
      <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
        <DataTable
          columns={columns}
          data={drivers}
          config={{
            enableGlobalFilter: true,
            enableSorting: true,
            initialSorting: {
              id: 'first_name',
              desc: false,
            },
            persistSort: true,
            filters: [
              {
                columnId: 'contract_type',
                title: 'Contract Type',
                type: 'faceted',
                options: [
                  { label: 'Owner', value: 'owner' },
                  { label: 'Owner/O', value: 'owner_o' },
                  { label: 'Lease', value: 'lease' },
                  { label: 'CT/mile', value: 'ct_mile' },
                  { label: '%/mile', value: 'percent_mile' },
                ],
              },
            ],
          }}
        />
      </div>
    </AppLayout>
  );
}
