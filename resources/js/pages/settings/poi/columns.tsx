import { ColumnDef } from '@/types/table';
import { MoreHorizon<PERSON>, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { router } from '@inertiajs/react';
import { PoiCategory } from '@/types/poi';

export const columns: ColumnDef<PoiCategory>[] = [
  {
    id: 'name',
    accessorKey: 'name',
    header: 'Name',
    enableHiding: false,
  },
  {
    id: 'description',
    accessorKey: 'description',
    header: 'Description',
    meta: { initiallyVisible: true },
    cell: ({ row }) => {
      const description = row.getValue('description') as string;
      return description || '-';
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    enableHiding: false,
    cell: ({ row }) => {
      const category = row.original;

      return (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.visit(route('poi.categories.items.index', { category: category.id }))}
            className="h-8 w-8 p-0"
            title="View Items"
          >
            <Eye className="h-4 w-4" />
            <span className="sr-only">View Items</span>
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => router.visit(route('poi.categories.edit', { category: category.id }))}>
                Edit Category
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  if (confirm('Are you sure you want to delete this category?')) {
                    router.delete(route('poi.categories.destroy', { category: category.id }));
                  }
                }}
              >
                Delete Category
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    },
  },
];