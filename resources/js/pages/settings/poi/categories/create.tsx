import { BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import HeadingSmall from '@/components/heading-small';
import { FormEventHandler } from 'react';
import { toast } from 'sonner';
import { FormField } from '@/components/ui/form-field';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { PoiCategoryFormData } from '@/types/poi';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Settings',
    href: '/settings/poi',
  },
  {
    title: 'POI Categories',
    href: '/settings/poi',
  },
  {
    title: 'Create Category',
    href: route('poi.categories.create'),
  },
];

export default function CreatePoiCategory() {
  const { data, setData, post, processing } = useForm<PoiCategoryFormData>({
    name: '',
    description: null,
    icon: null,
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();

    post(route('poi.categories.store'), {
      onSuccess: () => {
        toast.success('POI Category created successfully');
      },
    });
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Create POI Category" />

      <div className="px-4 py-6">
        <HeadingSmall title="Create POI Category" />

        <form onSubmit={submit} className="mt-8 max-w-2xl space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <FormField label="Name" error="" required>
              <Input
                id="name"
                value={data.name}
                onChange={(e) => setData('name', e.target.value)}
                required
                autoFocus
              />
            </FormField>

            <FormField label="Icon" error="">
              <Input
                id="icon"
                value={data.icon || ''}
                onChange={(e) => setData('icon', e.target.value || null)}
                placeholder="e.g., map-pin, building, etc."
              />
            </FormField>
          </div>

          <FormField label="Description" error="">
            <Input
              id="description"
              value={data.description || ''}
              onChange={(e) => setData('description', e.target.value || null)}
              placeholder="Optional description for this category"
            />
          </FormField>

          <div className="flex items-center justify-end gap-4">
            <Button type="button" variant="outline" asChild>
              <a href={route('poi.index')}>Cancel</a>
            </Button>
            <Button type="submit" disabled={processing}>
              {processing ? 'Creating...' : 'Create Category'}
            </Button>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}