import { BreadcrumbItem } from '@/types';
import { Head, useForm, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import HeadingSmall from '@/components/heading-small';
import { FormEventHandler } from 'react';
import { toast } from 'sonner';
import { FormField } from '@/components/ui/form-field';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { PoiCategory, PoiCategoryFormData, EditPoiCategoryPageProps } from '@/types/poi';

export default function EditPoiCategory() {
  const { category } = usePage<EditPoiCategoryPageProps>().props;

  const breadcrumbs: BreadcrumbItem[] = [
    {
      title: 'Settings',
      href: '/settings/poi',
    },
    {
      title: 'POI Categories',
      href: '/settings/poi',
    },
    {
      title: category.name,
      href: route('poi.categories.edit', { category: category.id }),
    },
  ];

  const { data, setData, patch, processing } = useForm<PoiCategoryFormData>({
    name: category.name,
    description: category.description || '',
    icon: category.icon || '',
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();

    patch(route('poi.categories.update', { category: category.id }), {
      onSuccess: () => {
        toast.success('POI Category updated successfully');
      },
    });
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`Edit ${category.name}`} />

      <div className="px-4 py-6">
        <HeadingSmall title={`Edit ${category.name}`} />

        <form onSubmit={submit} className="mt-8 max-w-2xl space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <FormField label="Name" error="" required>
              <Input id="name" value={data.name} onChange={(e) => setData('name', e.target.value)} required autoFocus />
            </FormField>

            <FormField label="Icon" error="">
              <Input
                id="icon"
                value={data.icon || ''}
                onChange={(e) => setData('icon', e.target.value)}
                placeholder="e.g., map-pin, building, etc."
              />
            </FormField>
          </div>

          <FormField label="Description" error="">
            <Input
              id="description"
              value={data.description || ''}
              onChange={(e) => setData('description', e.target.value)}
              placeholder="Optional description for this category"
            />
          </FormField>

          <div className="flex items-center justify-end gap-4">
            <Button type="button" variant="outline" asChild>
              <a href={route('poi.index')}>Cancel</a>
            </Button>
            <Button type="submit" disabled={processing}>
              {processing ? 'Updating...' : 'Update Category'}
            </Button>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}
