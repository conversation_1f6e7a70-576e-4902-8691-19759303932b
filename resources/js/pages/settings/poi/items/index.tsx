import { BreadcrumbItem } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { PlusIcon, UploadIcon, ArrowLeftIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/datatable';
import { columns } from './columns';
import { PoiItemsPageProps } from '@/types/poi';

export default function PoiItems() {
  const { category, items } = usePage<PoiItemsPageProps>().props;

  const breadcrumbs: BreadcrumbItem[] = [
    {
      title: 'Settings',
      href: '/settings/poi',
    },
    {
      title: 'POI Categories',
      href: '/settings/poi',
    },
    {
      title: category.name,
      href: route('poi.categories.items.index', { category: category.id }),
    },
  ];

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`${category.name} - POI Items`} />
      <div className="mt-4 flex items-center justify-between px-4 lg:px-4">
        <Button asChild variant="outline">
          <Link href={route('poi.index')} className="flex items-center">
            <ArrowLeftIcon className="mr-2 h-4 w-4" />
            Back to Categories
          </Link>
        </Button>
        <div className="flex items-center gap-2">
          <Button asChild>
            <Link href={route('poi.items.create', { category: category.id })} className="flex items-center">
              <PlusIcon className="mr-2 h-4 w-4" />
              Add Item
            </Link>
          </Button>
          <Button asChild variant="secondary">
            <Link href="#" className="flex items-center">
              <UploadIcon className="mr-2 h-4 w-4" />
              Import Items
            </Link>
          </Button>
        </div>
      </div>
      <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
        <div className="mb-4">
          <h1 className="text-2xl font-semibold">{category.name}</h1>
          {category.description && (
            <p className="text-muted-foreground">{category.description}</p>
          )}
        </div>
        <DataTable columns={columns} data={items || []} />
      </div>
    </AppLayout>
  );
}