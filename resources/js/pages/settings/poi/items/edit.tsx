import { BreadcrumbItem } from '@/types';
import { Head, useForm, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import HeadingSmall from '@/components/heading-small';
import { FormEventHandler } from 'react';
import { toast } from 'sonner';
import { PoiItemFormData, EditPoiItemPageProps } from '@/types/poi';
import PoiItemForm from '@/components/poi/PoiItemForm';

export default function EditPoiItem() {
  const { item, category } = usePage<EditPoiItemPageProps>().props;
  
  const breadcrumbs: BreadcrumbItem[] = [
    {
      title: 'Settings',
      href: '/settings/poi',
    },
    {
      title: 'POI Categories',
      href: '/settings/poi',
    },
    {
      title: category.name,
      href: route('poi.categories.items.index', { category: category.id }),
    },
    {
      title: item.name,
      href: route('poi.items.edit', { item: item.id }),
    },
  ];

  const { data, setData, patch, processing, errors } = useForm<PoiItemFormData>({
    name: item.name,
    address_line1: item.address_line1 || '',
    address_line2: item.address_line2 || '',
    city: item.city || '',
    state: item.state || '',
    postal_code: item.postal_code || '',
    country: item.country || '',
    latitude: item.latitude,
    longitude: item.longitude,
    phone: item.phone || '',
    email: item.email || '',
    website: item.website || '',
    opening_hours: item.opening_hours,
    metadata: item.metadata,
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();

    patch(route('poi.items.update', { item: item.id }), {
      onSuccess: () => {
        toast.success('POI Item updated successfully');
      },
    });
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`Edit ${item.name}`} />

      <div className="px-4 py-6">
        <HeadingSmall title={`Edit ${item.name}`} />

        <PoiItemForm
          data={data}
          setData={setData}
          errors={errors}
          processing={processing}
          onSubmit={submit}
          buttonLabel="Update Item"
          cancelHref={route('poi.categories.items.index', { category: category.id })}
        />
      </div>
    </AppLayout>
  );
}