import { ColumnDef } from '@/types/table';
import { MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { router } from '@inertiajs/react';
import { PoiItem } from '@/types/poi';

export const columns: ColumnDef<PoiItem>[] = [
  {
    id: 'name',
    accessorKey: 'name',
    header: 'Name',
    enableHiding: false,
  },
  {
    id: 'address',
    header: 'Address',
    meta: { initiallyVisible: true },
    cell: ({ row }) => {
      const item = row.original;
      const addressParts = [
        item.address_line1,
        item.address_line2,
        item.city,
        item.state,
        item.postal_code,
      ].filter(Boolean);
      return addressParts.length > 0 ? addressParts.join(', ') : '-';
    },
  },
  {
    id: 'coordinates',
    header: 'Coordinates',
    meta: { initiallyVisible: true },
    cell: ({ row }) => {
      const item = row.original;
      if (item.latitude && item.longitude) {
        return `${item.latitude}, ${item.longitude}`;
      }
      return '-';
    },
  },
  {
    id: 'contact',
    header: 'Contact',
    meta: { initiallyVisible: false },
    cell: ({ row }) => {
      const item = row.original;
      const contact = [];
      if (item.phone) contact.push(item.phone);
      if (item.email) contact.push(item.email);
      return contact.length > 0 ? contact.join(', ') : '-';
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    enableHiding: false,
    cell: ({ row }) => {
      const item = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => router.visit(route('poi.items.edit', { item: item.id }))}>
              Edit Item
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => {
                if (confirm('Are you sure you want to delete this POI item?')) {
                  router.delete(route('poi.items.destroy', { item: item.id }));
                }
              }}
            >
              Delete Item
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];