import { BreadcrumbItem } from '@/types';
import { Head, useForm, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import HeadingSmall from '@/components/heading-small';
import { FormEventHandler } from 'react';
import { toast } from 'sonner';
import { PoiItemFormData, CreatePoiItemPageProps } from '@/types/poi';
import PoiItemForm from '@/components/poi/PoiItemForm';

export default function CreatePoiItem() {
  const { category } = usePage<CreatePoiItemPageProps>().props;
  
  const breadcrumbs: BreadcrumbItem[] = [
    {
      title: 'Settings',
      href: '/settings/poi',
    },
    {
      title: 'POI Categories',
      href: '/settings/poi',
    },
    {
      title: category.name,
      href: route('poi.categories.items.index', { category: category.id }),
    },
    {
      title: 'Create Item',
      href: route('poi.items.create', { category: category.id }),
    },
  ];

  const { data, setData, post, processing, errors } = useForm<PoiItemFormData>({
    name: '',
    address_line1: '',
    address_line2: '',
    city: '',
    state: '',
    postal_code: '',
    country: '',
    latitude: null,
    longitude: null,
    phone: '',
    email: '',
    website: '',
    opening_hours: null,
    metadata: null,
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();

    post(route('poi.items.store', { category: category.id }), {
      onSuccess: () => {
        toast.success('POI Item created successfully');
      },
    });
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`Create POI Item in ${category.name}`} />

      <div className="px-4 py-6">
        <HeadingSmall title={`Create POI Item in ${category.name}`} />

        <PoiItemForm
          data={data}
          setData={setData}
          errors={errors}
          processing={processing}
          onSubmit={submit}
          buttonLabel="Create Item"
          cancelHref={route('poi.categories.items.index', { category: category.id })}
        />
      </div>
    </AppLayout>
  );
}