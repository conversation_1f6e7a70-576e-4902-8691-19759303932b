import { BreadcrumbItem } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { PlusIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/datatable';
import { columns } from './poi/columns';
import { PoiCategoryPageProps } from '@/types/poi';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Settings',
    href: '/settings/poi',
  },
  {
    title: 'POI Categories',
    href: '/settings/poi',
  },
];

export default function POI() {
  const { categories } = usePage<PoiCategoryPageProps>().props;

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="POI Categories" />
      <div className="mt-4 flex items-center justify-end gap-2 px-4 lg:px-4">
        <Button asChild>
          <Link href={route('poi.categories.create')} className="flex items-center">
            <PlusIcon className="mr-2 h-4 w-4" />
            Add Category
          </Link>
        </Button>
      </div>
      <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
        <DataTable columns={columns} data={categories || []} />
      </div>
    </AppLayout>
  );
}