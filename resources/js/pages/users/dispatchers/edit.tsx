import AppLayout from '@/layouts/app-layout';
import { BreadcrumbItem } from '@/types';
import { Head, useForm, usePage, Link } from '@inertiajs/react';
import HeadingSmall from '@/components/heading-small';
import { FormEventHandler } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { FormField } from '@/components/ui/form-field';
import { DispatcherUserEditPageProps, DispatcherUserFormData, DispatcherUserStatus } from '@/types/dispatcher-user';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dispatchers',
    href: '/users/dispatchers',
  },
  {
    title: 'Edit User',
    href: '/users/dispatchers/edit',
  },
];

export default function EditDispatcherUser() {
  const { user, errors } = usePage<DispatcherUserEditPageProps>().props;

  const { data, setData, patch, processing } = useForm<DispatcherUserFormData & Record<string, any>>({
    first_name: user.first_name || '',
    last_name: user.last_name || '',
    email: user.email || '',
    phone: user.phone || '',
    alias: user.alias || '',
    status: user.status || ('active' as DispatcherUserStatus),
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();

    patch(route('users.dispatchers.update', { user: user.id }), {
      onSuccess: () => {
        toast.success('User updated successfully');
      },
      onError: (errors) => {
        const errorMessage = Object.values(errors)[0] as string;
        toast.error(errorMessage);
      },
    });
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Edit Dispatcher User" />

      <div className="px-4 py-6">
        <HeadingSmall title="Edit Dispatcher User" description="Update dispatcher user information" />

        <form onSubmit={submit} className="mt-8 max-w-xl space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <FormField label="First Name" error={errors.first_name} required id="first_name">
              <Input
                className="mt-1 block w-full"
                type="text"
                required
                value={data.first_name}
                onChange={(e) => setData('first_name', e.target.value)}
                autoComplete="name"
                placeholder="First name"
                aria-invalid={!!errors.first_name}
              />
            </FormField>

            <FormField label="Last Name" error={errors.last_name} required id="last_name">
              <Input
                className="mt-1 block w-full"
                type="text"
                required
                value={data.last_name}
                onChange={(e) => setData('last_name', e.target.value)}
                autoComplete="name"
                placeholder="Last name"
                aria-invalid={!!errors.last_name}
              />
            </FormField>
          </div>

          <FormField label="Email" error={errors.email} required id="email">
            <Input
              className="mt-1 block w-full bg-gray-100"
              type="email"
              required
              value={data.email}
              disabled
              readOnly
              placeholder="Email"
              aria-invalid={!!errors.email}
            />
          </FormField>

          <FormField label="Phone" error={errors.phone} id="phone">
            <Input
              className="mt-1 block w-full"
              type="tel"
              value={data.phone}
              onChange={(e) => setData('phone', e.target.value)}
              autoComplete="tel"
              placeholder="Phone"
              aria-invalid={!!errors.phone}
            />
          </FormField>

          <FormField label="Alias" error={errors.alias} id="alias">
            <Input
              className="mt-1 block w-full"
              type="text"
              value={data.alias}
              onChange={(e) => setData('alias', e.target.value)}
              placeholder="Alias"
              aria-invalid={!!errors.alias}
            />
          </FormField>

          <FormField label="Status" error={errors.status} required id="status">
            <Select value={data.status} onValueChange={(value: DispatcherUserStatus) => setData('status', value)}>
              <SelectTrigger className="w-1/2" aria-invalid={!!errors.status}>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent className="min-w-[200px]">
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </FormField>

          <div className="flex items-center justify-end gap-4">
            <Button variant="outline" asChild>
              <Link href={route('users.dispatchers.index')}>Back</Link>
            </Button>
            <Button type="submit" disabled={processing}>
              Save
            </Button>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}
