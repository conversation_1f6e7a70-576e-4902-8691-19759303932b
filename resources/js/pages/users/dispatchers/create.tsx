import { BreadcrumbItem } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm, usePage } from '@inertiajs/react';
import HeadingSmall from '@/components/heading-small';
import { FormEventHandler } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { FormField } from '@/components/ui/form-field';
import { DispatcherUserCreatePageProps, DispatcherUserFormData } from '@/types/dispatcher-user';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dispatchers',
    href: route('users.dispatchers.index'),
  },
  {
    title: 'Create Dispatcher',
    href: route('users.dispatchers.create'),
  },
];

export default function CreateDispatcher() {
  const { errors } = usePage<DispatcherUserCreatePageProps>().props;
  const { setData, post, processing } = useForm<DispatcherUserFormData & Record<string, any>>({
    first_name: '',
    last_name: '',
    alias: '',
    email: '',
    phone: '',
    status: 'active',
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();

    post(route('users.dispatchers.store'), {
      onSuccess: () => {
        toast.success('User created successfully');
      },
      onError: (errors) => {
        const errorMessage = Object.values(errors)[0] as string;
        toast.error(errorMessage);
      },
    });
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Create Dispatcher" />

      <div className="px-4 py-6">
        <HeadingSmall title="Create Dispatcher" description="Create a new dispatcher user" />

        <form onSubmit={submit} className="mt-8 max-w-xl space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <FormField label="First Name" error={errors.first_name} required id="first_name">
              <Input
                className="mt-1 block w-full"
                type="text"
                required
                onChange={(e) => setData('first_name', e.target.value)}
                autoComplete="name"
                placeholder="First name"
                aria-invalid={!!errors.first_name}
              />
            </FormField>

            <FormField label="Last Name" error={errors.last_name} required id="last_name">
              <Input
                className="mt-1 block w-full"
                type="text"
                required
                onChange={(e) => setData('last_name', e.target.value)}
                autoComplete="name"
                placeholder="Last name"
                aria-invalid={!!errors.last_name}
              />
            </FormField>
          </div>

          <FormField label="Email" error={errors.email} required id="email">
            <Input
              className="mt-1 block w-full"
              type="email"
              required
              onChange={(e) => setData('email', e.target.value)}
              autoComplete="email"
              placeholder="Email"
              aria-invalid={!!errors.email}
            />
          </FormField>

          <FormField label="Phone" error={errors.phone} id="phone">
            <Input
              className="mt-1 block w-full"
              type="tel"
              onChange={(e) => setData('phone', e.target.value)}
              autoComplete="tel"
              placeholder="Phone"
              aria-invalid={!!errors.phone}
            />
          </FormField>

          <FormField label="Alias" error={errors.alias} id="alias">
            <Input
              className="mt-1 block w-full"
              type="text"
              onChange={(e) => setData('alias', e.target.value)}
              autoComplete="nickname"
              placeholder="Alias"
              aria-invalid={!!errors.alias}
            />
          </FormField>

          <FormField label="Status" error={errors.status} required id="status">
            <Select defaultValue="active" onValueChange={(value) => setData('status', value as 'active' | 'inactive')}>
              <SelectTrigger className="w-1/2" aria-invalid={!!errors.status}>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent className="min-w-[200px]">
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </FormField>

          <div className="flex items-center justify-end gap-4">
            <Button type="submit" disabled={processing}>
              Save
            </Button>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}
