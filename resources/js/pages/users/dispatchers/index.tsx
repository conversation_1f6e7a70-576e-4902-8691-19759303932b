import { BreadcrumbItem } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { PlusIcon } from 'lucide-react';
import { columns } from '@/pages/users/dispatchers/columns';
import { DataTable } from '@/components/datatable';
import { DispatcherUserIndexPageProps } from '@/types/dispatcher-user';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dispatchers',
    href: route('users.dispatchers.index'),
  },
];

export default function Dispatchers() {
  const { users } = usePage<DispatcherUserIndexPageProps>().props;

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Dispatchers" />
      <div className="mt-4 flex items-center justify-end px-4 lg:px-4">
        <Button asChild>
          <Link href={route('users.dispatchers.create')} className="flex items-center">
            <PlusIcon className="mr-2 h-4 w-4" />
            Add User
          </Link>
        </Button>
      </div>
      <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
        <DataTable columns={columns} data={users} />
      </div>
    </AppLayout>
  );
}
