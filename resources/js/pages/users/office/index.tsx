import { BreadcrumbItem } from '@/types';
import { Head, usePage, Link } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { columns } from './columns';
import { Button } from '@/components/ui/button';
import { PlusIcon } from 'lucide-react';
import { DataTable } from '@/components/datatable';
import { OfficeUserIndexPageProps } from '@/types/office-user';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Office Users',
    href: '/users/office',
  },
];

export default function OfficeUsers() {
  const { users } = usePage<OfficeUserIndexPageProps>().props;

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Office Users" />
      <div className="mt-4 flex items-center justify-end px-4 lg:px-4">
        <Button asChild>
          <Link href={route('users.office.create')} className="flex items-center">
            <PlusIcon className="mr-2 h-4 w-4" />
            Add User
          </Link>
        </Button>
      </div>
      <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
        <DataTable columns={columns} data={users} />
      </div>
    </AppLayout>
  );
}
