import { ColumnDef } from '@tanstack/react-table';
import { MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { router, usePage } from '@inertiajs/react';
import { Badge } from '@/components/ui/badge';
import { OfficeUser } from '@/types/office-user';
import { OfficeUserPageProps } from '@/types/office-user';

export const columns: ColumnDef<OfficeUser>[] = [
  {
    accessorKey: 'first_name',
    header: 'First Name',
  },
  {
    accessorKey: 'last_name',
    header: 'Last Name',
  },
  {
    accessorKey: 'email',
    header: 'Email',
  },
  {
    accessorKey: 'phone',
    header: 'Phone',
    cell: ({ row }) => {
      const phone = row.getValue('phone') as string;
      return phone || '-';
    },
  },
  {
    accessorKey: 'alias',
    header: 'Alias',
    cell: ({ row }) => {
      const alias = row.getValue('alias') as string;
      return alias || '-';
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status') as string;
      const variant = status === 'active' ? 'default' : status === 'inactive' ? 'destructive' : 'secondary';
      return <Badge variant={variant}>{status}</Badge>;
    },
  },
  {
    accessorKey: 'role',
    header: 'Role',
    cell: ({ row }) => {
      const role = row.getValue('role') as OfficeUser['role'];
      return role ? <Badge variant="outline">{role.name}</Badge> : '-';
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const user = row.original;
      const { auth } = usePage<OfficeUserPageProps>().props;
      const isSelf = Boolean(user.local_id && auth?.user?.id && user.local_id === auth.user.id);

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => router.visit(route('users.office.edit', { user: user.id }))}>
              Edit User
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>Impersonate User</DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                if (confirm('Are you sure you want to delete this user?')) {
                  router.delete(route('users.office.destroy', { user: user.workos_id }));
                }
              }}
              disabled={isSelf}
              className={isSelf ? 'cursor-not-allowed opacity-50' : ''}
            >
              Delete User
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
