import { BreadcrumbItem } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm, usePage } from '@inertiajs/react';
import HeadingSmall from '@/components/heading-small';
import { FormEventHandler } from 'react';
import { toast } from 'sonner';
import { TrailerFormData, CreateTrailerPageProps } from '@/types/trailer';
import TrailerForm from '@/components/trailers/TrailerForm';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Trailers',
    href: route('trailers.index'),
  },
  {
    title: 'Create Trailer',
    href: route('trailers.create'),
  },
];

export default function CreateTrailer() {
  const { errors, regions, fleetStatuses, ownershipTypes } = usePage<CreateTrailerPageProps>().props;
  const { data, setData, post, processing } = useForm<TrailerFormData>({
    unit_number: '',
    make: null,
    model: null,
    year: null,
    vin: null,
    dot_expiry: null,
    plate_number: null,
    plate_region: 'il',
    owner_company: null,
    fleet_status: null,
    activation_date: null,
    deactivation_date: null,
    location: null,
    gps_note: null,
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();

    post(route('trailers.store'), {
      onSuccess: () => {
        toast.success('Trailer created successfully');
      },
      onError: (errors) => {
        const errorMessage = Object.values(errors)[0] as string;
        toast.error(errorMessage);
      },
    });
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Create Trailer" />

      <div className="px-4 py-6">
        <HeadingSmall title="Create Trailer" description="Create a new trailer in the fleet" />
        <TrailerForm
          data={data}
          setData={setData}
          errors={errors}
          processing={processing}
          onSubmit={submit}
          regions={regions}
          fleetStatuses={fleetStatuses}
          ownershipTypes={ownershipTypes}
          buttonLabel="Save"
        />
      </div>
    </AppLayout>
  );
}
