import { BreadcrumbItem } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm, usePage } from '@inertiajs/react';
import HeadingSmall from '@/components/heading-small';
import { FormEventHandler } from 'react';
import { toast } from 'sonner';
import TrailerForm from '@/components/trailers/TrailerForm';
import { Trailer, TrailerFormData } from '@/types/trailer';

interface EditTrailerPageProps {
  trailer: Trailer;
  fleetStatuses: { value: string; label: string }[];
  ownershipTypes: { value: string; label: string }[];
  regions: { value: string; label: string }[];
  errors: Record<string, string>;
  [key: string]: unknown;
}

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Trailers',
    href: route('trailers.index'),
  },
  {
    title: 'Edit Trailer',
    href: '#',
  },
];

export default function EditTrailer() {
  const { trailer, fleetStatuses, ownershipTypes, errors, regions } = usePage<EditTrailerPageProps>().props;

  const { data, setData, patch, processing } = useForm<TrailerFormData>({
    unit_number: trailer.unit_number,
    make: trailer.make || null,
    model: trailer.model || null,
    year: trailer.year || null,
    vin: trailer.vin || null,
    dot_expiry: trailer.dot_expiry || null,
    plate_number: trailer.plate_number || null,
    plate_region: trailer.plate_region || 'il',
    owner_company: trailer.owner_company || null,
    fleet_status: trailer.fleet_status || null,
    activation_date: trailer.activation_date || null,
    deactivation_date: trailer.deactivation_date || null,
    location: trailer.location || null,
    gps_note: trailer.gps_note || null,
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();

    patch(route('trailers.update', { trailer: trailer.id }), {
      onSuccess: () => {
        toast.success('Trailer updated successfully');
      },
      onError: (errors) => {
        const errorMessage = Object.values(errors)[0] as string;
        toast.error(errorMessage);
      },
    });
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Edit Trailer" />
      <div className="px-4 py-6">
        <HeadingSmall title="Edit Trailer" description="Update trailer information" />
        <TrailerForm
          data={data}
          setData={setData}
          errors={errors}
          processing={processing}
          onSubmit={submit}
          regions={regions}
          fleetStatuses={fleetStatuses}
          ownershipTypes={ownershipTypes}
          buttonLabel="Update Trailer"
        />
      </div>
    </AppLayout>
  );
}
