import { ColumnDef } from '@/types/table';
import { MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { router } from '@inertiajs/react';
import { Trailer } from '@/types/trailer';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';

export const columns: ColumnDef<Trailer>[] = [
  {
    id: 'unit_number',
    accessorKey: 'unit_number',
    header: 'Unit Number',
    enableHiding: false,
    enableSorting: true,
  },
  {
    id: 'make',
    accessorKey: 'make',
    header: 'Make',
    meta: { initiallyVisible: true },
    enableSorting: true,
  },
  {
    id: 'model',
    accessorKey: 'model',
    header: 'Model',
    meta: { initiallyVisible: true },
    enableSorting: true,
  },
  {
    id: 'year',
    accessorKey: 'year',
    header: 'Year',
    meta: { initiallyVisible: true },
    enableSorting: true,
  },
  {
    id: 'fleet_status',
    accessorKey: 'fleet_status',
    header: 'Status',
    meta: { initiallyVisible: true },
    enableSorting: true,
    cell: ({ row }) => {
      const status = row.getValue('fleet_status') as string;
      return status ? <Badge>{status}</Badge> : '-';
    },
  },
  {
    id: 'plate_number',
    accessorKey: 'plate_number',
    header: 'License Plate',
    meta: { initiallyVisible: true },
    enableSorting: true,
  },
  {
    id: 'vin',
    accessorKey: 'vin',
    header: 'VIN',
    meta: { initiallyVisible: false },
  },
  {
    id: 'dot_expiry',
    accessorKey: 'dot_expiry',
    header: 'DOT Expiry',
    meta: { initiallyVisible: true },
    enableSorting: true,
    cell: ({ row }) => {
      const date = row.getValue('dot_expiry') as string;
      return date ? format(new Date(date), 'MM/dd/yyyy') : '-';
    },
  },
  {
    id: 'owner_company',
    accessorKey: 'owner_company',
    header: 'Owner Company',
    meta: { initiallyVisible: false },
  },
  {
    id: 'activation_date',
    accessorKey: 'activation_date',
    header: 'Activation Date',
    meta: { initiallyVisible: false },
    cell: ({ row }) => {
      const date = row.getValue('activation_date') as string;
      return date ? format(new Date(date), 'MM/dd/yyyy') : '-';
    },
  },
  {
    id: 'location',
    accessorKey: 'location',
    header: 'Location',
    meta: { initiallyVisible: false },
  },
  {
    id: 'actions',
    header: 'Actions',
    enableHiding: false,
    cell: ({ row }) => {
      const trailer = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => router.visit(route('trailers.edit', { trailer: trailer.id }))}>
              Edit Trailer
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => {
                if (confirm('Are you sure you want to delete this trailer?')) {
                  router.delete(route('trailers.destroy', { trailer: trailer.id }));
                }
              }}
            >
              Delete Trailer
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
