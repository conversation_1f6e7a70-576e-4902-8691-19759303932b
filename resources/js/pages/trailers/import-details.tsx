import { BreadcrumbItem, SharedData } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { Head, usePage } from '@inertiajs/react';
import HeadingSmall from '@/components/heading-small';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  AlertCircleIcon,
  CheckCircleIcon,
  ClockIcon,
  FileTextIcon,
  XCircleIcon
} from 'lucide-react';

interface ImportRecord {
  id: number;
  filename: string;
  imported_at: string;
  total_records: number;
  imported_records: number;
  updated_records: number;
  failed_records: number;
  status: string;
  errors: string[];
  success_rate: number;
  has_errors: boolean;
}

interface ImportDetailsPageProps extends SharedData {
  importRecord: ImportRecord;
}

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Trailers',
    href: '/trailers',
  },
  {
    title: 'Import',
    href: '/trailers/import',
  },
  {
    title: 'Details',
    href: '#',
  },
];

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'Completed':
      return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
    case 'CompletedWithErrors':
      return <AlertCircleIcon className="h-5 w-5 text-yellow-500" />;
    case 'Processing':
      return <ClockIcon className="h-5 w-5 text-blue-500" />;
    case 'Failed':
      return <XCircleIcon className="h-5 w-5 text-red-500" />;
    default:
      return <ClockIcon className="h-5 w-5 text-gray-500" />;
  }
};

const getStatusBadge = (status: string) => {
  switch (status) {
    case 'Completed':
      return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>;
    case 'CompletedWithErrors':
      return <Badge variant="destructive" className="bg-yellow-100 text-yellow-800">Completed with Errors</Badge>;
    case 'Processing':
      return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Processing</Badge>;
    case 'Failed':
      return <Badge variant="destructive">Failed</Badge>;
    default:
      return <Badge variant="secondary">Unknown</Badge>;
  }
};

export default function TrailerImportDetails() {
  const { importRecord } = usePage<ImportDetailsPageProps>().props;

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title={`Import Details - ${importRecord.filename}`} />

      <div className="space-y-6">
        <HeadingSmall 
          title="Import Details" 
          description={`Details for ${importRecord.filename}`}
        />

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Import Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileTextIcon className="h-5 w-5" />
                Import Summary
              </CardTitle>
              <CardDescription>
                Overview of the import process and results
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Status</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(importRecord.status)}
                  {getStatusBadge(importRecord.status)}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">File</span>
                <span className="text-sm text-gray-600">{importRecord.filename}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Import Date</span>
                <span className="text-sm text-gray-600">
                  {new Date(importRecord.imported_at).toLocaleString()}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Success Rate</span>
                <span className="text-sm text-gray-600">{importRecord.success_rate.toFixed(1)}%</span>
              </div>
            </CardContent>
          </Card>

          {/* Record Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>Record Statistics</CardTitle>
              <CardDescription>
                Breakdown of processed records
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{importRecord.total_records}</div>
                  <div className="text-sm text-blue-600">Total Records</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{importRecord.imported_records}</div>
                  <div className="text-sm text-green-600">New Records</div>
                </div>
                <div className="text-center p-3 bg-yellow-50 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">{importRecord.updated_records}</div>
                  <div className="text-sm text-yellow-600">Updated Records</div>
                </div>
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">{importRecord.failed_records}</div>
                  <div className="text-sm text-red-600">Failed Records</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Errors Section */}
        {importRecord.has_errors && importRecord.errors.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircleIcon className="h-5 w-5 text-red-500" />
                Import Errors
              </CardTitle>
              <CardDescription>
                Errors encountered during the import process
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {importRecord.errors.map((error, index) => (
                  <div key={index} className="p-3 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-sm text-red-800">{error}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Success Message */}
        {!importRecord.has_errors && importRecord.status === 'Completed' && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircleIcon className="h-5 w-5 text-green-500" />
                Import Completed Successfully
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                All records were processed successfully without any errors.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}
