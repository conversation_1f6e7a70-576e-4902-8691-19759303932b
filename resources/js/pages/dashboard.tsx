import { PlaceholderPattern } from '@/components/ui/placeholder-pattern';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { type PoiCategory, type PoiItem } from '@/types/poi';
import { Head } from '@inertiajs/react';
import { APIProvider, Map, AdvancedMarker, useMap } from '@vis.gl/react-google-maps';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useState, useEffect, useMemo } from 'react';
import { CheckIcon, FilterIcon, PlusIcon, XIcon } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
  },
];

interface DashboardProps {
  poiCategories: PoiCategory[];
  poiItems: PoiItem[];
  googleMapsApiKey: string;
}

function MapBoundsController({ bounds }: { bounds: google.maps.LatLngBoundsLiteral | null }) {
  const map = useMap();

  useEffect(() => {
    if (!map || !bounds) return;

    const googleBounds = new google.maps.LatLngBounds(
      { lat: bounds.south, lng: bounds.west },
      { lat: bounds.north, lng: bounds.east },
    );

    map.fitBounds(googleBounds, { top: 50, bottom: 50, left: 50, right: 50 });
  }, [map, bounds]);

  return null;
}

export default function Dashboard({ poiCategories, poiItems, googleMapsApiKey }: DashboardProps) {
  const [selectedCategories, setSelectedCategories] = useState<number[]>([]);
  const [isFilterOpen, setIsFilterOpen] = useState(true);
  const [isComboboxOpen, setIsComboboxOpen] = useState(false);

  const toggleCategory = (categoryId: number) => {
    setSelectedCategories((prev) =>
      prev.includes(categoryId) ? prev.filter((id) => id !== categoryId) : [...prev, categoryId],
    );
  };

  const removeCategory = (categoryId: number) => {
    setSelectedCategories((prev) => prev.filter((id) => id !== categoryId));
  };

  const filteredPoiItems = poiItems.filter((item) => {
    if (selectedCategories.length === 0) return false;
    return selectedCategories.includes(item.poi_category_id) && item.latitude !== null && item.longitude !== null;
  });

  const mapBounds = useMemo(() => {
    if (filteredPoiItems.length === 0) {
      return null;
    }

    const lats = filteredPoiItems.map((item) => item.latitude!);
    const lngs = filteredPoiItems.map((item) => item.longitude!);

    const minLat = Math.min(...lats);
    const maxLat = Math.max(...lats);
    const minLng = Math.min(...lngs);
    const maxLng = Math.max(...lngs);

    return {
      north: maxLat,
      south: minLat,
      east: maxLng,
      west: minLng,
    };
  }, [filteredPoiItems]);

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Dashboard" />
      <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
        <div className="grid auto-rows-min gap-4 md:grid-cols-3">
          <div className="border-sidebar-border/70 dark:border-sidebar-border relative aspect-video overflow-hidden rounded-xl border">
            <PlaceholderPattern className="absolute inset-0 size-full stroke-neutral-900/20 dark:stroke-neutral-100/20" />
          </div>
          <div className="border-sidebar-border/70 dark:border-sidebar-border relative aspect-video overflow-hidden rounded-xl border">
            <PlaceholderPattern className="absolute inset-0 size-full stroke-neutral-900/20 dark:stroke-neutral-100/20" />
          </div>
          <div className="border-sidebar-border/70 dark:border-sidebar-border relative aspect-video overflow-hidden rounded-xl border">
            <PlaceholderPattern className="absolute inset-0 size-full stroke-neutral-900/20 dark:stroke-neutral-100/20" />
          </div>
        </div>
        <div className="border-sidebar-border/70 dark:border-sidebar-border relative min-h-[100vh] flex-1 overflow-hidden rounded-xl border md:min-h-min">
          <APIProvider apiKey={googleMapsApiKey}>
            <Map
              mapId="ride4-fleet-map"
              defaultCenter={{ lat: 39.8283, lng: -98.5795 }}
              defaultZoom={4}
              style={{ width: '100%', height: '100%' }}
              gestureHandling="greedy"
              disableDefaultUI={false}
            >
              <MapBoundsController bounds={mapBounds} />
              {filteredPoiItems.map((item) => (
                <AdvancedMarker
                  key={item.id}
                  position={{
                    lat: item.latitude!,
                    lng: item.longitude!,
                  }}
                  title={item.name}
                />
              ))}
            </Map>
          </APIProvider>

          {isFilterOpen && (
            <div className="absolute top-16 z-10" style={{ left: '10px' }}>
              <Card className="w-80">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-base">
                    <FilterIcon className="size-4" />
                    Map Filters
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="mb-3 text-sm font-medium">POI Categories</h4>

                    <Popover open={isComboboxOpen} onOpenChange={setIsComboboxOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={isComboboxOpen}
                          className="w-full justify-start"
                        >
                          <PlusIcon className="size-4" />
                          Add categories...
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-80 p-0" align="start">
                        <Command>
                          <CommandInput placeholder="Search categories..." />
                          <CommandList>
                            <CommandEmpty>No categories found.</CommandEmpty>
                            <CommandGroup>
                              {poiCategories.map((category) => (
                                <CommandItem
                                  key={category.id}
                                  value={category.name}
                                  onSelect={() => {
                                    toggleCategory(category.id);
                                    // Don't close the popover to allow multiple selections
                                    setIsComboboxOpen(true);
                                  }}
                                >
                                  <CheckIcon
                                    className={`mr-2 size-4 ${
                                      selectedCategories.includes(category.id) ? 'opacity-100' : 'opacity-0'
                                    }`}
                                  />
                                  <span className="truncate">{category.name}</span>
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>

                    {selectedCategories.length > 0 && (
                      <div className="mt-3 space-y-2">
                        <div className="flex flex-wrap gap-1">
                          {selectedCategories.map((categoryId) => {
                            const category = poiCategories.find((cat) => cat.id === categoryId);
                            return category ? (
                              <Badge
                                key={categoryId}
                                variant="secondary"
                                className="flex max-w-[200px] items-center gap-1 pr-1"
                              >
                                <span className="truncate">{category.name}</span>
                                <button
                                  type="button"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    removeCategory(categoryId);
                                  }}
                                  className="hover:text-destructive ml-1 transition-colors"
                                >
                                  <XIcon className="size-3" />
                                </button>
                              </Badge>
                            ) : null;
                          })}
                        </div>
                        <p className="text-muted-foreground text-xs">{selectedCategories.length} categories selected</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          <button
            onClick={() => setIsFilterOpen(!isFilterOpen)}
            className="bg-background border-border hover:bg-accent absolute top-16 right-4 z-10 rounded-lg border p-2 shadow-sm transition-colors"
            title={isFilterOpen ? 'Hide filters' : 'Show filters'}
          >
            <FilterIcon className="size-4" />
          </button>
        </div>
      </div>
    </AppLayout>
  );
}
