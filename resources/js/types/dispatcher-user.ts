import { User } from '@/types';

export type DispatcherUserStatus = 'active' | 'inactive';

export interface DispatcherUser {
  id: string;
  local_id: number | null;
  first_name: string;
  last_name: string;
  email: string;
  phone: string | null;
  alias: string | null;
  status: DispatcherUserStatus;
  role_slug: string | null;
}

export interface DispatcherUserFormData {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  alias: string;
  status: DispatcherUserStatus;
}

export type DispatcherUserFormDataKeys = keyof DispatcherUserFormData;

export type DispatcherUserFormErrors = {
  [K in DispatcherUserFormDataKeys]?: string;
};

export interface DispatcherUserPageProps {
  user?: DispatcherUser;
  users?: DispatcherUser[];
  auth: {
    user: Pick<User, 'id' | 'first_name' | 'last_name'> | null;
  };
  errors: DispatcherUserFormErrors;
  [key: string]: unknown;
}

export interface DispatcherUserCreatePageProps extends DispatcherUserPageProps {}

export interface DispatcherUserEditPageProps extends DispatcherUserPageProps {
  user: DispatcherUser;
}

export interface DispatcherUserIndexPageProps extends DispatcherUserPageProps {
  users: DispatcherUser[];
}
