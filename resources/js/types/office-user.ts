import { Role } from '@/types/role';

export interface OfficeUser {
  id: number;
  local_id?: number;
  workos_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string | null;
  alias?: string | null;
  status: 'active' | 'inactive';
  role: Role;
  created_at: string;
  updated_at: string;
}

export interface OfficeUserFormData {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  alias: string;
  status: 'active' | 'inactive';
  role_id: string;
}

export type OfficeUserFormDataKeys = keyof OfficeUserFormData;

export type OfficeUserFormErrors = {
  [K in OfficeUserFormDataKeys]?: string;
};

export interface OfficeUserPageProps {
  user?: OfficeUser;
  users?: OfficeUser[];
  roles: Role[];
  currentRole?: string;
  auth: {
    user: {
      id: number;
    };
  };
  errors: {
    first_name?: string;
    last_name?: string;
    email?: string;
    phone?: string;
    alias?: string;
    status?: string;
    role_id?: string;
  };
  [key: string]: unknown;
}

export interface OfficeUserCreatePageProps extends OfficeUserPageProps {
  roles: Role[];
}

export interface OfficeUserEditPageProps extends OfficeUserPageProps {
  user: OfficeUser;
  currentRole: string;
}

export interface OfficeUserIndexPageProps extends OfficeUserPageProps {
  users: OfficeUser[];
}
