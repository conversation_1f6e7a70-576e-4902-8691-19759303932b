export type FleetStatus = 
    | 'repair'
    | 'yard_without_driver'
    | 'yard_with_driver'
    | 'active'
    | 'inactive'
    | 'dropped'
    | 'accident'
    | 'ryder'
    | 'dealer'
    | 'vacation'
    | 'stolen'
    | 'for_sale';

export const fleetStatusLabels: Record<FleetStatus, string> = {
    repair: 'Repair',
    yard_without_driver: 'Yard, without driver',
    yard_with_driver: 'Yard, with driver',
    active: 'Active',
    inactive: 'Inactive',
    dropped: 'Dropped',
    accident: 'Accident',
    ryder: 'Ryder',
    dealer: 'Dealer',
    vacation: 'Vacation',
    stolen: 'Stolen',
    for_sale: 'For Sale'
}; 