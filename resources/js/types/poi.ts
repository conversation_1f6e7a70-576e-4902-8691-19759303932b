export interface PoiCategory {
  id: number;
  name: string;
  description: string | null;
  icon: string | null;
  created_at: string;
  updated_at: string;
}

export interface PoiItem {
  id: number;
  poi_category_id: number;
  name: string;
  address_line1: string | null;
  address_line2: string | null;
  city: string | null;
  state: string | null;
  postal_code: string | null;
  country: string | null;
  latitude: number | null;
  longitude: number | null;
  phone: string | null;
  email: string | null;
  website: string | null;
  opening_hours: Record<string, string[]> | null;
  metadata: Record<string, any> | null;
  created_at: string;
  updated_at: string;
  category?: PoiCategory;
}

export interface PoiCategoryPageProps {
  categories: PoiCategory[];
  [key: string]: unknown;
}

export interface PoiItemsPageProps {
  category: PoiCategory;
  items: PoiItem[];
  [key: string]: unknown;
}

export type PoiCategoryFormData = {
  name: string;
  description?: string | null;
  icon?: string | null;
};

export type PoiItemFormData = {
  name: string;
  address_line1?: string | null;
  address_line2?: string | null;
  city?: string | null;
  state?: string | null;
  postal_code?: string | null;
  country?: string | null;
  latitude?: number | null;
  longitude?: number | null;
  phone?: string | null;
  email?: string | null;
  website?: string | null;
  opening_hours?: Record<string, string[]> | null;
  metadata?: Record<string, any> | null;
};

export interface EditPoiCategoryPageProps {
  category: PoiCategory;
  [key: string]: unknown;
}

export interface CreatePoiItemPageProps {
  category: PoiCategory;
  [key: string]: unknown;
}

export interface EditPoiItemPageProps {
  item: PoiItem;
  category: PoiCategory;
  [key: string]: unknown;
}