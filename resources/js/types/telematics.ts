export interface TelematicsField {
  key: string;
  value: string | null;
  label: string;
  type: 'text' | 'password' | 'email' | 'number';
  required: boolean;
  placeholder?: string;
  description?: string;
}

export interface TelematicsProvider {
  id: string;
  name: string;
  enabled: boolean;
  fields: TelematicsField[];
}

export interface TelematicsFormData {
  enabled: boolean;
  credentials: Record<string, string>;
  [key: string]: any;
}

export interface TelematicsFormProps {
  data: TelematicsFormData;
  setData: (field: string, value: any) => void;
  errors: Partial<Record<keyof TelematicsFormData | `credentials.${string}`, string>>;
  processing: boolean;
  onSubmit: (e: React.FormEvent) => void;
  provider: TelematicsProvider;
  buttonLabel: string;
}

export interface TelematicsSettingsPageProps {
  settings: TelematicsProvider[];
  [key: string]: any;
}
