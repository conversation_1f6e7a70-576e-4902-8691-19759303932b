export type DriverContractType = {
    value: 'owner' | 'owner_o' | 'lease' | 'ct_mile' | 'percent_mile';
    label: string;
  };

export type CreateDriverPageProps = {
  errors: Record<string, string>;
  contractTypes: DriverContractType[];
};

export type DriverFormData = {
  first_name: string;
  last_name: string;
  phone_number?: string;
  contract_type?: DriverContractType['value'];
  email?: string;
};

export type DriverData = {
  id: number;
  first_name: string;
  last_name: string;
  phone_number: string;
  contract_type: DriverContractType['value'];
  email: string;
};
