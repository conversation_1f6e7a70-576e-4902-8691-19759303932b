import { FleetStatus } from './fleetStatus';
import { PaginatedData } from './pagination';

export interface FilterOption {
  label: string;
  value: string;
  icon?: string;
  count?: number;
}

export interface FilterConfig {
  columnId: string;
  title: string;
  type: 'faceted' | 'search' | 'range' | 'date';
  options?: FilterOption[];
  placeholder?: string;
  multiple?: boolean;
}

export interface Trailer {
  id: number;
  unit_number: string;
  make?: string | null;
  model?: string | null;
  year?: number | null;
  vin?: string | null;
  dot_expiry?: string | null;
  plate_number?: string | null;
  plate_region?: string | null;
  owner_company?: string | null;
  fleet_status?: FleetStatus | null;
  activation_date?: string | null;
  deactivation_date?: string | null;
  location?: string | null;
  gps_note?: string | null;
  created_at?: string;
  updated_at?: string;
}

export interface TrailerFormData {
  unit_number: string;
  make: string | null;
  model: string | null;
  year: number | null;
  vin: string | null;
  dot_expiry: string | null;
  plate_number: string | null;
  plate_region: string | null;
  owner_company: string | null;
  fleet_status: FleetStatus | null;
  activation_date: string | null;
  deactivation_date: string | null;
  location: string | null;
  gps_note: string | null;
  [key: string]: string | number | boolean | null | undefined;
}

export interface TrailerPageProps {
  trailers: PaginatedData<Trailer>;
  filters: FilterConfig[];
  [key: string]: unknown;
}

export interface CreateTrailerPageProps {
  errors: Record<string, string>;
  regions: { value: string; label: string }[];
  fleetStatuses: { value: string; label: string }[];
  ownershipTypes: { value: string; label: string }[];
  [key: string]: unknown;
}
