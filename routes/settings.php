<?php

use App\Http\Controllers\Settings\PoiController;
use App\Http\Controllers\Settings\PoiItemController;
use App\Http\Controllers\Settings\ProfileController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Lara<PERSON>\WorkOS\Http\Middleware\ValidateSessionWithWorkOS;

Route::middleware([
    'auth',
    ValidateSessionWithWorkOS::class,
])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Route::get('settings/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('settings/profile', [ProfileController::class, 'update'])->name('profile.update');

    Route::get('settings/appearance', function () {
        return Inertia::render('settings/appearance');
    })->name('appearance');

    Route::get('settings/poi', [PoiController::class, 'index'])->name('poi.index');

    // POI Categories
    Route::get('settings/poi/categories/create', [PoiController::class, 'create'])->name('poi.categories.create');
    Route::post('settings/poi/categories', [PoiController::class, 'store'])->name('poi.categories.store');
    Route::get('settings/poi/categories/{category}/edit', [PoiController::class, 'edit'])->name('poi.categories.edit');
    Route::patch('settings/poi/categories/{category}', [PoiController::class, 'update'])->name('poi.categories.update');
    Route::delete('settings/poi/categories/{category}', [PoiController::class, 'destroy'])->name('poi.categories.destroy');

    // POI Category Items
    Route::get('settings/poi/categories/{category}/items', [PoiItemController::class, 'index'])->name('poi.categories.items.index');
    Route::get('settings/poi/categories/{category}/items/create', [PoiItemController::class, 'create'])->name('poi.items.create');
    Route::post('settings/poi/categories/{category}/items', [PoiItemController::class, 'store'])->name('poi.items.store');
    Route::get('settings/poi/items/{item}/edit', [PoiItemController::class, 'edit'])->name('poi.items.edit');
    Route::patch('settings/poi/items/{item}', [PoiItemController::class, 'update'])->name('poi.items.update');
    Route::delete('settings/poi/items/{item}', [PoiItemController::class, 'destroy'])->name('poi.items.destroy');
});
