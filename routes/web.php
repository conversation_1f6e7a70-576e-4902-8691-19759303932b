<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Devices\DeviceController;
use App\Http\Controllers\Drivers\DriverController;
use App\Http\Controllers\Integrations\IntegrationController;
use App\Http\Controllers\Integrations\TelematicSettingController;
use App\Http\Controllers\Trailers\TrailerController;
use App\Http\Controllers\Trailers\TrailerImportController;
use App\Http\Controllers\Trucks\TruckController;
use App\Http\Controllers\Trucks\TruckImportController;
use App\Http\Controllers\Users\DispatcherUserController;
use App\Http\Controllers\Users\OfficeUserController;
use Illuminate\Support\Facades\Route;
use Laravel\WorkOS\Http\Middleware\ValidateSessionWithWorkOS;

Route::get('/', fn () => redirect()->route('login'))->name('home');

Route::middleware([
    'auth',
    ValidateSessionWithWorkOS::class,
])->group(function () {
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');

    Route::group(['prefix' => 'users', 'as' => 'users.'], function () {
        Route::group(['prefix' => 'office', 'as' => 'office.'], function () {
            Route::get('/', [OfficeUserController::class, 'index'])->name('index');
            Route::get('/edit/{user}', [OfficeUserController::class, 'edit'])->name('edit');
            Route::patch('/update/{user}', [OfficeUserController::class, 'update'])->name('update');
            Route::get('/create', [OfficeUserController::class, 'create'])->name('create');
            Route::post('/store', [OfficeUserController::class, 'store'])->name('store');
            Route::delete('/{user}', [OfficeUserController::class, 'destroy'])->name('destroy');
        });
        Route::group(['prefix' => 'dispatchers', 'as' => 'dispatchers.'], function () {
            Route::get('/', [DispatcherUserController::class, 'index'])->name('index');
            Route::get('/edit/{user}', [DispatcherUserController::class, 'edit'])->name('edit');
            Route::patch('/update/{user}', [DispatcherUserController::class, 'update'])->name('update');
            Route::get('/create', [DispatcherUserController::class, 'create'])->name('create');
            Route::post('/store', [DispatcherUserController::class, 'store'])->name('store');
        });
    });

    Route::group(['prefix' => 'drivers', 'as' => 'drivers.'], function () {
        Route::get('/', [DriverController::class, 'index'])->name('index');
        Route::get('/edit/{driver}', [DriverController::class, 'edit'])->name('edit');
        Route::get('/create', [DriverController::class, 'create'])->name('create');
        Route::post('/store', [DriverController::class, 'store'])->name('store');
        Route::patch('/{driver}', [DriverController::class, 'update'])->name('update');
        Route::delete('/{driver}', [DriverController::class, 'destroy'])->name('destroy');
    });

    Route::group(['prefix' => 'trucks', 'as' => 'trucks.'], function () {
        Route::get('/', [TruckController::class, 'index'])->name('index');
        Route::get('/create', [TruckController::class, 'create'])->name('create');
        Route::post('/', [TruckController::class, 'store'])->name('store');
        Route::get('/{truck}/edit', [TruckController::class, 'edit'])->name('edit');
        Route::patch('/{truck}', [TruckController::class, 'update'])->name('update');
        Route::delete('/{truck}', [TruckController::class, 'destroy'])->name('destroy');

        Route::group(['prefix' => 'import', 'as' => 'import.'], function () {
            Route::get('/', [TruckImportController::class, 'import'])->name('index');
            Route::post('/process', [TruckImportController::class, 'process'])->name('process');
            Route::post('/repeat/{importRecord}', [TruckImportController::class, 'repeat'])->name('repeat');
            Route::get('/{importRecord}/details', [TruckImportController::class, 'details'])->name('details');
            Route::get('/{importRecord}/api-details', [TruckImportController::class, 'apiDetails'])->name('api-details');
            Route::get('/download/{importRecord}', [TruckImportController::class, 'download'])->name('download');
        });

        Route::get('/notifications', [TruckController::class, 'index'])->name('notifications');
        Route::get('/availability', [TruckController::class, 'index'])->name('availability');
        Route::get('/ownerships', [TruckController::class, 'index'])->name('ownerships');
        Route::get('/maintenance', [TruckController::class, 'index'])->name('maintenance');
    });

    Route::group(['prefix' => 'trailers', 'as' => 'trailers.'], function () {
        Route::get('/', [TrailerController::class, 'index'])->name('index');
        Route::get('/create', [TrailerController::class, 'create'])->name('create');
        Route::post('/', [TrailerController::class, 'store'])->name('store');
        Route::get('/{trailer}/edit', [TrailerController::class, 'edit'])->name('edit');
        Route::patch('/{trailer}', [TrailerController::class, 'update'])->name('update');
        Route::delete('/{trailer}', [TrailerController::class, 'destroy'])->name('destroy');

        Route::group(['prefix' => 'import', 'as' => 'import.'], function () {
            Route::get('/', [TrailerImportController::class, 'import'])->name('index');
            Route::post('/process', [TrailerImportController::class, 'process'])->name('process');
            Route::post('/repeat/{importRecord}', [TrailerImportController::class, 'repeat'])->name('repeat');
            Route::get('/{importRecord}/details', [TrailerImportController::class, 'details'])->name('details');
            Route::get('/{importRecord}/api-details', [TrailerImportController::class, 'apiDetails'])->name('api-details');
            Route::get('/download/{importRecord}', [TrailerImportController::class, 'download'])->name('download');
        });

        Route::get('/maintenance', [TrailerController::class, 'index'])->name('maintenance');
        Route::get('/notifications', [TrailerController::class, 'index'])->name('notifications');
    });

    Route::group(['prefix' => 'devices', 'as' => 'devices.'], function () {
        Route::get('/', [DeviceController::class, 'index'])->name('index');
        Route::get('/import', [DeviceController::class, 'import'])->name('import');
    });

    Route::group(['prefix' => 'integrations', 'as' => 'integrations.'], function () {
        Route::get('/', [IntegrationController::class, 'index'])->name('index');

        Route::group(['prefix' => 'telematics', 'as' => 'telematics.'], function () {
            Route::get('/', [TelematicSettingController::class, 'index'])->name('index');
            Route::put('/{provider}', [TelematicSettingController::class, 'update'])->name('update');
        });
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
