# AI Agent Task Management Workflow

## Overview
This document establishes a standardized workflow for AI agents to maintain context, track changes, and create persistent memory across related tasks.

## Task Structure

### 1. Task Initialization
When starting a new task, create the following directory structure:

```
tasks/
├── templates/
│   ├── task-init-prompt.md
│   ├── requirement-change-prompt.md
│   ├── task-review-prompt.md
│   └── cross-task-analysis-prompt.md
├── TMS-001/
│   ├── description.md
│   └── [task-specific files]
├── TMS-002/
│   ├── description.md
│   └── [task-specific files]
└── changelog.md
```

### 2. Task Description File (`tasks/[TMS-XXX]/description.md`)

Each task must have a `description.md` file with the following structure:

```markdown
# Task TMS-XXX: [Task Title]

## Jira Reference
- **Ticket ID**: TMS-XXX
- **Priority**: [High/Medium/Low]
- **Assignee**: [Name/AI Agent]
- **Created**: [Date]

## Description
[Detailed task description from <PERSON><PERSON>]

## Technical Considerations
- [Key technical points]
- [Dependencies on other tasks]
- [Potential challenges]
- [Related components/systems]

## Requirements Log
### Initial Requirements ([Date])
- [Original requirement 1]
- [Original requirement 2]

### Change Log
#### [Date] - Requirement Update
- **Changed**: [What changed]
- **Reason**: [Why it changed]
- **Impact**: [Effect on implementation]
- **Updated by**: [Person/AI Agent]

#### [Date] - Scope Addition
- **Added**: [New requirement]
- **Reason**: [Business justification]
- **Dependencies**: [What this affects]
- **Updated by**: [Person/AI Agent]

## Related Tasks
- [TMS-YYY]: [Brief description of relationship]
- [TMS-ZZZ]: [Brief description of relationship]

## Implementation Notes
- [Key decisions made]
- [Approaches considered]
- [Final implementation strategy]

## Status
- **Current Status**: [Not Started/In Progress/Review/Complete]
- **Last Updated**: [Date]
- **Completion Date**: [Date if completed]
```

### 3. Global Changelog (`changelog.md`)

Maintain a project-wide changelog at the root level:

```markdown
# Project Changelog

## [Date] - TMS-XXX
### Added
- [New features/requirements]

### Changed
- [Modified requirements/scope]

### Dependencies
- [New task dependencies created]

### Notes
- [Important context for future tasks]

---

## [Date] - TMS-YYY
### Added
- [New features/requirements]

### Fixed
- [Issues resolved]

### Dependencies
- [Tasks this affects]

---
```

## Workflow Process

### Starting a New Task
1. Create task directory: `tasks/TMS-XXX/`
2. Create `description.md` with initial requirements
3. Update global `changelog.md`
4. Review related tasks for context
5. Begin implementation

### During Task Execution
1. Update `description.md` with any requirement changes
2. Log all significant decisions in Implementation Notes
3. Update Related Tasks section if new dependencies emerge
4. Update Status regularly

### Task Completion
1. Update Status to "Complete" with completion date
2. Add final entry to global `changelog.md`
3. Document any lessons learned
4. Note impact on future tasks

## AI Agent Prompt Templates

### Template Files Structure
Create these template files within the tasks directory:

```
tasks/
├── templates/
│   ├── task-init-prompt.md
│   ├── start-task-prompt.md
│   ├── requirement-change-prompt.md
│   ├── task-review-prompt.md
│   └── cross-task-analysis-prompt.md
├── TMS-001/
│   └── description.md
└── changelog.md
```

### Task Initialization Prompt
Use template file: `templates/task-init-prompt.md`

```markdown
# Task Initialization Prompt Template

I'm starting work on Jira task TMS-XXX. Please help me:

1. Create the task directory structure: tasks/TMS-XXX/
2. Generate the description.md file based on this task information: [paste Jira details]
3. Review existing tasks in the tasks/ directory to identify related work
4. Update the global changelog.md with this new task
5. Suggest potential technical considerations and dependencies

Context: This is part of [project name] and relates to [brief project context].

## Usage Instructions
1. Replace TMS-XXX with actual task number
2. Replace [paste Jira details] with actual task information
3. Replace [project name] and [brief project context] with relevant details
4. Copy this prompt and use with your AI agent
```

### Requirement Change Prompt
Use template file: `templates/requirement-change-prompt.md`

### Task Review Prompt
Use template file: `templates/task-review-prompt.md`

### Cross-Task Analysis Prompt
Use template file: `templates/cross-task-analysis-prompt.md`

### Quick Start
To use any template:
1. Copy content from the appropriate template file in `tasks/templates/`
2. Replace placeholder values with actual task details
3. Use with Claude Code or your preferred AI agent

## Best Practices

### For AI Agents
- Always read existing task descriptions before starting new work
- Update documentation immediately when requirements change
- Cross-reference related tasks to maintain consistency
- Use the changelog to understand project evolution
- Document decisions and rationale for future reference

### For Requirement Changes
- Always log the reason for changes
- Note who requested the change
- Assess impact on related tasks
- Update global changelog for visibility
- Consider backwards compatibility

### For Documentation
- Keep descriptions specific and actionable
- Use consistent formatting across all tasks
- Include enough detail for future AI agents to understand context
- Link related tasks explicitly
- Date all entries for temporal context

## Memory Retrieval
When working on new tasks:
1. Search for similar patterns in existing task descriptions
2. Review changelog for recent project evolution
3. Check Related Tasks sections for dependencies
4. Look for recurring technical considerations
5. Use implementation notes from similar tasks as guidance

This system creates a persistent knowledge base that allows AI agents to build upon previous work and maintain project consistency across time.
