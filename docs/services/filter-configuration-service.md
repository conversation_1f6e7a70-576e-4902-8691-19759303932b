# FilterConfigurationService

The `FilterConfigurationService` provides a reusable way to generate filter configurations for datatables across different controllers. This service follows SOLID principles and eliminates the need for duplicate filter logic in controllers.

## Features

- **SOLID Principles** - Follows Single Responsibility, Open/Closed, and Dependency Inversion principles
- **Reusable filter configurations** - Define filter logic once, use across multiple controllers
- **Automatic count generation** - Automatically queries the database to get counts for filter options
- **Enum support** - Built-in support for Laravel enum classes with labels
- **Flexible configuration** - Support for custom sorting, ordering, and field mapping
- **Type safety** - Consistent filter structure across the application

## Architecture

The service uses a configuration-based approach where each model has its own filter configuration class:

- `FilterConfigurationService` - Core service that processes configurations
- `FilterConfigurationInterface` - Contract for configuration classes
- `TruckFilterConfiguration` - Truck-specific filter configuration
- `TrailerFilterConfiguration` - Trailer-specific filter configuration

## Basic Usage

### 1. Create a Filter Configuration Class

```php
<?php

namespace App\Services\FilterConfigurations;

use App\Enums\MyModelStatusEnum;
use App\Models\MyModel;

class MyModelFilterConfiguration implements FilterConfigurationInterface
{
    public static function getConfiguration(): array
    {
        return [
            [
                'type' => 'enum',
                'field' => 'status',
                'title' => 'Status',
                'column_id' => 'status',
                'enum_class' => MyModelStatusEnum::class,
                'model_class' => MyModel::class,
            ],
            // ... more filters
        ];
    }

    public static function getModelClass(): string
    {
        return MyModel::class;
    }
}
```

### 2. Use in Controllers

```php
<?php

namespace App\Http\Controllers;

use App\Services\FilterConfigurationService;
use App\Services\FilterConfigurations\TruckFilterConfiguration;

class TruckController extends Controller
{
    public function __construct(
        protected FilterConfigurationService $filterService
    ) {}

    public function index()
    {
        // ... query logic ...

        return Inertia::render('trucks/index', [
            'trucks' => $trucks,
            'filters' => $this->filterService->generateFilters(
                TruckFilterConfiguration::getModelClass(),
                TruckFilterConfiguration::getConfiguration()
            ),
        ]);
    }
}
```

### Custom Filter Configuration

For more control, use the `generateFilters` method with custom configuration:

```php
$filters = $filterService->generateFilters(MyModel::class, [
    [
        'type' => 'enum',
        'field' => 'status',
        'title' => 'Status',
        'column_id' => 'status',
        'enum_class' => MyStatusEnum::class,
        'model_class' => MyModel::class,
    ],
    [
        'type' => 'faceted',
        'field' => 'category',
        'title' => 'Category',
        'column_id' => 'category',
        'sort_by' => 'label',
    ],
    [
        'type' => 'faceted',
        'field' => 'year',
        'title' => 'Year',
        'column_id' => 'year',
        'order_by' => 'year',
        'order_direction' => 'desc',
    ],
]);
```

## Filter Types

### Faceted Filters

Faceted filters query the database to get unique values and their counts:

```php
[
    'type' => 'faceted',
    'field' => 'make',           // Database column name
    'title' => 'Make',           // Display title
    'column_id' => 'make',       // Frontend column identifier
    'sort_by' => 'label',        // Optional: sort options by 'label' or 'count'
    'order_by' => 'make',        // Optional: database ordering column
    'order_direction' => 'asc',  // Optional: 'asc' or 'desc'
    'multiple' => true,          // Optional: allow multiple selections
]
```

### Enum Filters

Enum filters use Laravel enum classes and get counts from the database:

```php
[
    'type' => 'enum',
    'field' => 'status',                    // Database column name
    'title' => 'Status',                    // Display title
    'column_id' => 'status',                // Frontend column identifier
    'enum_class' => TruckStatusEnum::class, // Enum class with labels() method
    'model_class' => Truck::class,          // Model class for counting
    'multiple' => true,                     // Optional: allow multiple selections
]
```

## Configuration Options

| Option | Type | Description | Default |
|--------|------|-------------|---------|
| `type` | string | Filter type: 'faceted' or 'enum' | 'faceted' |
| `field` | string | Database column name | Required |
| `title` | string | Display title for the filter | Required |
| `column_id` | string | Frontend column identifier | Same as `field` |
| `multiple` | boolean | Allow multiple selections | `true` |
| `sort_by` | string | Sort options by 'label' or 'count' | None |
| `order_by` | string | Database column for ordering | None |
| `order_direction` | string | 'asc' or 'desc' | 'asc' |
| `enum_class` | string | Enum class (for enum type) | Required for enum |
| `model_class` | string | Model class (for enum type) | Required for enum |

## Output Format

The service returns an array of filter configurations in this format:

```php
[
    [
        'columnId' => 'status',
        'title' => 'Truck Status',
        'type' => 'faceted',
        'multiple' => true,
        'options' => [
            [
                'value' => 'in_pit_stop',
                'label' => 'In pit stop',
                'count' => 15,
            ],
            [
                'value' => 'on_the_road',
                'label' => 'On the road',
                'count' => 42,
            ],
            // ...
        ],
    ],
    // ... more filters
]
```

## Adding New Models

To add filter support for a new model:

1. **Create a filter configuration class**:
```php
<?php

namespace App\Services\FilterConfigurations;

use App\Enums\DispatcherStatusEnum;
use App\Models\Dispatcher;

class DispatcherFilterConfiguration implements FilterConfigurationInterface
{
    public static function getConfiguration(): array
    {
        return [
            [
                'type' => 'enum',
                'field' => 'status',
                'title' => 'Status',
                'column_id' => 'status',
                'enum_class' => DispatcherStatusEnum::class,
                'model_class' => Dispatcher::class,
            ],
            // ... more filters
        ];
    }

    public static function getModelClass(): string
    {
        return Dispatcher::class;
    }
}
```

2. **Use in your controller**:
```php
use App\Services\FilterConfigurations\DispatcherFilterConfiguration;

$filters = $this->filterService->generateFilters(
    DispatcherFilterConfiguration::getModelClass(),
    DispatcherFilterConfiguration::getConfiguration()
);
```

## Testing

The service includes comprehensive tests. Run them with:

```bash
php artisan test tests/Unit/Services/FilterConfigurationServiceTest.php
```

## Benefits

- **DRY Principle** - No duplicate filter logic across controllers
- **Consistency** - All filters follow the same structure and behavior
- **Maintainability** - Changes to filter logic only need to be made in one place
- **Performance** - Efficient database queries with proper counting
- **Type Safety** - Consistent data structure for frontend consumption
