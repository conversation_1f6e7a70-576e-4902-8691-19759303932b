# Telematic Sync Cron Job Setup

## Overview

The telematic sync command is designed to run both interactively and via cron jobs. It provides different output modes depending on how it's executed.

> **Note**: As of the current implementation, the telematic sync is configured to run **twice daily** using <PERSON><PERSON>'s scheduler at 9:00 AM and 9:00 PM. This replaces the previous daily schedule to provide more frequent data updates while maintaining reasonable API usage.

## Command Options

### Basic Usage
```bash
php artisan sync:telematics:initial
```

### Available Options

- `--cron` - Run in cron mode with minimal output (silent on success, structured errors)
- `--quiet` - Suppress all output (<PERSON><PERSON> built-in, works with both modes)
- `--verbose` or `-v` - Show detailed information (<PERSON><PERSON> built-in)

## Output Modes

### Summary Table

| Mode | Success Output | Error Output | Use Case |
|------|----------------|--------------|----------|
| **Interactive** | `✅ Initial entities were synced successfully.` | `❌ Configuration Error: ...` + helpful instructions | Manual execution, debugging |
| **Cron** | `TELEMATIC_SYNC_SUCCESS: 815 vehicles synced from samsara` | `TELEMATIC_SYNC_FAILED: Configuration error - ...` | Automated execution, monitoring |
| **Cron + Verbose** | Same as Cron + detailed stats | Same as Cron + context details | Debugging automated runs |
| **Quiet** | *(no output)* | *(no output, but logged)* | Complete silence when needed |

## Output Modes

### 1. Interactive Mode (Default)
**When to use**: Manual execution, debugging, development

**Example output:**
```bash
# Success
Starting initial entity sync from enabled provider...
✅ Initial entities were synced successfully.

# Error
Starting initial entity sync from enabled provider...
❌ Configuration Error: API key is invalid for samsara provider

💡 The API key for samsara appears to be invalid. Please verify the key in your telematic settings.

To fix this issue:
1. Go to your application settings
2. Navigate to Integrations > Telematics
3. Configure the correct API credentials
```

### 2. Cron Mode (`--cron`)
**When to use**: Automated execution, monitoring systems, log parsing

**Key behavior**:
- **Informative success messages** with sync statistics
- **Structured errors** for easy parsing
- **Respects `--quiet`** for complete silence

**Example output:**
```bash
# Success
TELEMATIC_SYNC_SUCCESS: 815 vehicles synced from samsara

# Success with errors
TELEMATIC_SYNC_SUCCESS: 810 vehicles synced, 5 errors from samsara

# Success with verbose (-v)
TELEMATIC_SYNC_SUCCESS: 815 vehicles synced from samsara
PROVIDER: samsara
TOTAL_SYNCED: 815
TOTAL_ERRORS: 0

# Error
TELEMATIC_SYNC_FAILED: Configuration error - API key is invalid for samsara provider

# Error with verbose (-v)
TELEMATIC_SYNC_FAILED: Configuration error - API key is invalid for samsara provider
PROVIDER: samsara
ACTIONABLE_MESSAGE: The API key for samsara appears to be invalid. Please verify the key in your telematic settings.
CONTEXT_issue: invalid_api_key

# With --quiet: No output at all (errors still logged to Laravel logs)
```

## Cron Job Setup

### Scheduling Frequency

The telematic sync is configured to run **twice daily** at 9:00 AM and 9:00 PM. This frequency provides:

- **Regular data updates** without overwhelming the external API
- **Business hours coverage** with morning sync for daily operations
- **Evening sync** to capture end-of-day data
- **Reduced API rate limiting** compared to hourly syncing
- **Better resource utilization** during off-peak hours

### Direct Cron Configuration (Alternative)

If you prefer direct cron jobs instead of Laravel Scheduler:

```bash
# Recommended: Twice daily at 9 AM and 9 PM with cron mode
0 9,21 * * * cd /path/to/your/app && php artisan sync:telematics:initial --cron >> /var/log/telematic-sync.log 2>&1

# Alternative: Completely silent (errors only in Laravel logs)
0 9,21 * * * cd /path/to/your/app && php artisan sync:telematics:initial --cron --quiet

# For debugging: Verbose output
0 9,21 * * * cd /path/to/your/app && php artisan sync:telematics:initial --cron -v >> /var/log/telematic-sync.log 2>&1

# Legacy hourly schedule (not recommended for production)
0 * * * * cd /path/to/your/app && php artisan sync:telematics:initial --cron >> /var/log/telematic-sync.log 2>&1
```

### Laravel Scheduler (Recommended)

Add to `routes/console.php`:

```php
use Illuminate\Support\Facades\Schedule;

Schedule::command('sync:telematics:initial --cron')
    ->twiceDaily(9, 21)
    ->withoutOverlapping()
    ->runInBackground();
```

This configuration:
- Runs the telematic sync **twice daily** at 9:00 AM and 9:00 PM
- Uses `--cron` mode for structured output suitable for automated execution
- Prevents overlapping executions with `withoutOverlapping()`
- Runs in background for better performance with `runInBackground()`

Then set up a single cron job to run Laravel's scheduler:
```bash
* * * * * cd /path/to/your/app && php artisan schedule:run >> /dev/null 2>&1
```

## Error Handling in Cron

### Exit Codes
- `0` - Success
- `1` - Failure (configuration, API, or sync errors)

### Error Types and Messages

#### Configuration Errors
```
TELEMATIC_SYNC_FAILED: Configuration error - API key is missing for samsara provider
TELEMATIC_SYNC_FAILED: Configuration error - API key is invalid for samsara provider
```

#### API Errors
```
TELEMATIC_SYNC_FAILED: API request to samsara failed with status 429
TELEMATIC_SYNC_FAILED: Failed to connect to samsara API: Connection timeout
```

#### Sync Errors
```
TELEMATIC_SYNC_FAILED: No active telematic provider found
TELEMATIC_SYNC_FAILED: Partial sync failure for samsara: 10 successful, 2 failed
```

### Monitoring and Alerting

You can monitor the cron job by:

1. **Checking exit codes**:
   ```bash
   php artisan sync:telematics:initial --cron --quiet
   echo "Exit code: $?"
   ```

2. **Parsing structured output**:
   ```bash
   # Look for TELEMATIC_SYNC_FAILED in logs
   grep "TELEMATIC_SYNC_FAILED" /var/log/telematic-sync.log
   ```

3. **Using Laravel's failed job monitoring** (if using queues)

### Log Analysis

The structured output in cron mode makes it easy to parse logs:

```bash
# Count failures by type
grep "TELEMATIC_SYNC_FAILED" /var/log/telematic-sync.log | cut -d: -f2 | sort | uniq -c

# Get recent configuration errors
grep "Configuration error" /var/log/telematic-sync.log | tail -10

# Check for API issues
grep "API request.*failed" /var/log/telematic-sync.log
```

## Best Practices

1. **Use Laravel Scheduler** - Better than direct cron for Laravel apps, provides better error handling and logging
2. **Twice daily frequency** - Balances data freshness with API rate limits and system resources
3. **Use `--cron` mode for automation** - Provides structured output suitable for monitoring and log parsing
4. **Use `--cron --quiet` for production** - Minimal output, errors still logged to Laravel logs
5. **Use `--cron -v` for debugging** - Structured verbose output when troubleshooting
6. **Set up monitoring** - Alert on non-zero exit codes and parse structured error messages
7. **Log rotation** - Ensure log files don't grow too large
8. **Test both modes** - Verify interactive and cron modes work correctly
9. **Use `withoutOverlapping()`** - Prevents concurrent executions that could cause data conflicts
10. **Use `runInBackground()`** - Allows other scheduled tasks to run simultaneously

## Troubleshooting

### Common Issues

1. **Permission errors**: Ensure the web server user can write to log files
2. **Path issues**: Use absolute paths in cron jobs
3. **Environment variables**: Cron may not have the same environment as your shell
4. **Timezone differences**: Cron may run in a different timezone

### Debug Commands

```bash
# Test cron mode manually
php artisan sync:telematics:initial --cron -v

# Check if provider is enabled
php artisan tinker --execute="dd(app(\App\Settings\SamsaraSettings::class)->toArray());"

# Verify configuration
php artisan config:show services.samsara
```
