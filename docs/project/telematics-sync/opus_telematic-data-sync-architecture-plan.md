# Unified Telematic Data Sync Architecture Plan for TMS

This comprehensive plan unifies two telematic data synchronization approaches into a scalable, maintainable Laravel-based solution that integrates seamlessly with Samsara tracking software and existing maintenance management systems.

## Executive summary

**The unified architecture combines the service layer enhancement approach of the first plan with the maintenance integration emphasis of the second plan**, creating a robust system that handles both real-time telematic data synchronization and comprehensive maintenance workflow integration. The solution leverages Laravel 12's enhanced features, implements intelligent queue management with circuit breaker patterns, and provides a scalable database schema optimized for time-series telematic data.

**Key architectural decisions include adopting a hybrid model approach** (combining TelematicDataPoint entities with telematic_readings storage), implementing a multi-tier service layer with dedicated Samsara integration services, and establishing a comprehensive queue-based synchronization engine that handles rate limiting, error recovery, and batch processing efficiently.

## Unified database schema design

The combined approach leverages the best of both plans with a flexible, performance-optimized schema that supports both operational and analytical workloads.

### Core telematic data structure

```sql
-- Primary telematic events table (combines both approaches)
CREATE TABLE telematic_events (
    event_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    device_id VARCHAR(50) NOT NULL,
    vehicle_id VARCHAR(50) NOT NULL,
    driver_id VARCHAR(50),
    timestamp TIMESTAMP(3) NOT NULL,
    event_type ENUM('GPS', 'ENGINE_STATE', 'DIAGNOSTIC', 'ALERT', 'MAINTENANCE') NOT NULL,
    
    -- Location data (from first plan's TelematicDataPoint)
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    speed DECIMAL(5,2),
    heading DECIMAL(5,2),
    altitude DECIMAL(7,2),
    
    -- Engine and diagnostic data
    odometer_reading DECIMAL(10,2),
    fuel_level DECIMAL(5,2),
    engine_rpm SMALLINT UNSIGNED,
    engine_coolant_temp SMALLINT,
    battery_voltage DECIMAL(4,2),
    ignition_status ENUM('OFF', 'ACC', 'ON', 'START'),
    
    -- Maintenance integration fields (from second plan)
    maintenance_alert_type VARCHAR(50),
    maintenance_priority ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL'),
    fault_codes JSON,
    
    -- Raw Samsara data for future extensibility
    raw_samsara_data JSON,
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sync_status ENUM('PENDING', 'SYNCED', 'FAILED') DEFAULT 'PENDING',
    
    -- Optimized indexes for telematic queries
    INDEX idx_device_timestamp (device_id, timestamp DESC),
    INDEX idx_vehicle_timestamp (vehicle_id, timestamp DESC),
    INDEX idx_event_type (event_type, timestamp DESC),
    INDEX idx_maintenance_alerts (maintenance_alert_type, maintenance_priority),
    SPATIAL INDEX idx_location (latitude, longitude)
) PARTITION BY RANGE (UNIX_TIMESTAMP(timestamp));

-- Vehicles table with enhanced telematic integration
CREATE TABLE vehicles (
    vehicle_id VARCHAR(50) PRIMARY KEY,
    vin VARCHAR(17) UNIQUE NOT NULL,
    samsara_device_id VARCHAR(50) UNIQUE,
    make VARCHAR(50),
    model VARCHAR(50),
    year INT,
    fleet_id VARCHAR(50),
    
    -- Current state caching for performance
    current_latitude DECIMAL(10,8),
    current_longitude DECIMAL(11,8),
    current_speed DECIMAL(5,2),
    current_fuel_level DECIMAL(5,2),
    last_sync_at TIMESTAMP,
    
    -- Maintenance integration
    maintenance_system_id VARCHAR(50),
    maintenance_status ENUM('OPERATIONAL', 'MAINTENANCE_DUE', 'OUT_OF_SERVICE'),
    next_maintenance_due DATE,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Maintenance integration bridge table
CREATE TABLE maintenance_telematic_integration (
    integration_id VARCHAR(50) PRIMARY KEY,
    vehicle_id VARCHAR(50),
    maintenance_work_order_id VARCHAR(50),
    telematic_event_id BIGINT,
    integration_type ENUM('ALERT_TRIGGERED', 'DIAGNOSTIC_DATA', 'USAGE_METRICS'),
    sync_status ENUM('PENDING', 'SYNCED', 'FAILED'),
    last_sync_attempt TIMESTAMP,
    error_message TEXT,
    
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(vehicle_id),
    FOREIGN KEY (telematic_event_id) REFERENCES telematic_events(event_id),
    INDEX idx_vehicle_maintenance (vehicle_id, integration_type),
    INDEX idx_sync_status (sync_status, last_sync_attempt)
);
```

### Performance optimization strategy

**Time-based partitioning with automated management:**
```sql
-- Monthly partitions for optimal query performance
DELIMITER $$
CREATE PROCEDURE manage_telematic_partitions()
BEGIN
    SET @next_month = DATE_ADD(LAST_DAY(NOW()), INTERVAL 1 DAY);
    SET @partition_name = CONCAT('p', DATE_FORMAT(@next_month, '%Y%m'));
    SET @partition_value = UNIX_TIMESTAMP(DATE_ADD(@next_month, INTERVAL 1 MONTH));
    
    SET @sql = CONCAT('ALTER TABLE telematic_events ADD PARTITION (PARTITION ', 
                     @partition_name, ' VALUES LESS THAN (', @partition_value, '))');
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END$$
DELIMITER ;
```

**Tiered storage for data lifecycle management:**
- Hot data (last 30 days): High-performance SSD storage
- Warm data (30 days - 1 year): Standard storage with compression
- Cold data (1+ years): Archive storage with automated cleanup

## Enhanced service layer architecture

The unified service layer combines the service enhancement focus of the first plan with robust maintenance integration capabilities.

### Core telematic service structure

```php
<?php

namespace App\Services\Telematics;

class UnifiedTelematicSyncService
{
    public function __construct(
        private readonly SamsaraApiClient $samsaraClient,
        private readonly VehicleRepository $vehicleRepository,
        private readonly TelematicEventRepository $telematicRepository,
        private readonly MaintenanceIntegrationService $maintenanceService,
        private readonly EventDispatcher $eventDispatcher,
        private readonly CircuitBreaker $circuitBreaker
    ) {}

    public function syncVehicleData(Vehicle $vehicle): SyncResult
    {
        return DB::transaction(function () use ($vehicle) {
            try {
                // Fetch data from Samsara with circuit breaker protection
                $telematicData = $this->circuitBreaker->call(function () use ($vehicle) {
                    return $this->samsaraClient->getVehicleData($vehicle->samsara_device_id);
                });

                // Process and store multiple event types
                $syncResults = [];
                foreach ($telematicData as $dataPoint) {
                    $event = $this->processTelematicDataPoint($vehicle, $dataPoint);
                    $syncResults[] = $event;
                    
                    // Trigger maintenance integration if needed
                    if ($this->shouldTriggerMaintenanceIntegration($event)) {
                        $this->maintenanceService->processMaintenanceAlert($vehicle, $event);
                    }
                }

                // Update vehicle current state
                $this->updateVehicleCurrentState($vehicle, $telematicData);

                // Dispatch events for real-time processing
                $this->eventDispatcher->dispatch(new VehicleDataSynced($vehicle, $syncResults));

                return new SyncResult(true, count($syncResults), 'Sync completed successfully');

            } catch (CircuitBreakerOpenException $e) {
                // Graceful degradation - use cached data
                return $this->handleCircuitBreakerOpen($vehicle);
            }
        });
    }

    private function processTelematicDataPoint(Vehicle $vehicle, array $dataPoint): TelematicEvent
    {
        // Determine event type and priority
        $eventType = $this->determineEventType($dataPoint);
        
        $telematicEvent = new TelematicEvent([
            'device_id' => $vehicle->samsara_device_id,
            'vehicle_id' => $vehicle->vehicle_id,
            'timestamp' => Carbon::parse($dataPoint['time']),
            'event_type' => $eventType,
            'latitude' => $dataPoint['gps']['latitude'] ?? null,
            'longitude' => $dataPoint['gps']['longitude'] ?? null,
            'speed' => $dataPoint['gps']['speed'] ?? null,
            'heading' => $dataPoint['gps']['heading'] ?? null,
            'raw_samsara_data' => $dataPoint,
        ]);

        // Add maintenance-specific processing
        if ($this->isMaintenanceRelevant($dataPoint)) {
            $this->enrichWithMaintenanceData($telematicEvent, $dataPoint);
        }

        return $this->telematicRepository->create($telematicEvent);
    }
}
```

### Samsara API integration service

```php
<?php

namespace App\Services\External;

class SamsaraApiClient
{
    private const BASE_URL = 'https://api.samsara.com';
    private const RATE_LIMIT = 150; // requests per second
    
    public function __construct(
        private readonly string $apiToken,
        private readonly RateLimiter $rateLimiter,
        private readonly CacheRepository $cache
    ) {}

    public function getVehicleData(string $deviceId): array
    {
        $cacheKey = "samsara_vehicle_data:{$deviceId}";
        
        return $this->cache->remember($cacheKey, 300, function () use ($deviceId) {
            return $this->rateLimiter->attempt(
                'samsara_api_calls',
                self::RATE_LIMIT,
                function () use ($deviceId) {
                    $response = Http::withHeaders([
                        'Authorization' => "Bearer {$this->apiToken}",
                        'Accept' => 'application/json',
                    ])
                    ->timeout(30)
                    ->retry(3, 1000, function ($exception) {
                        return $exception instanceof ConnectionException;
                    })
                    ->get(self::BASE_URL . "/fleet/vehicles/stats/feed", [
                        'types' => 'gps,engineStates,obdOdometerMeters,auxInput1',
                        'deviceIds' => $deviceId
                    ]);

                    if ($response->failed()) {
                        throw new SamsaraApiException(
                            "Failed to fetch data for device {$deviceId}: " . $response->body()
                        );
                    }

                    return $response->json()['data'][0] ?? [];
                },
                function () {
                    throw new RateLimitExceededException('Samsara API rate limit exceeded');
                }
            );
        });
    }

    public function setupWebhook(string $webhookUrl, array $eventTypes): array
    {
        return Http::withHeaders([
            'Authorization' => "Bearer {$this->apiToken}",
            'Content-Type' => 'application/json',
        ])->post(self::BASE_URL . '/webhooks', [
            'name' => 'TMS Integration Webhook',
            'url' => $webhookUrl,
            'eventTypes' => $eventTypes,
            'version' => '2018-01-01',
        ])->json();
    }
}
```

## Intelligent queue job architecture

The unified queue system combines batch processing capabilities with real-time synchronization and maintenance integration workflows.

### Multi-tier queue structure

```php
<?php

namespace App\Jobs\Telematics;

class UnifiedVehicleDataSyncJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 5;
    public $timeout = 180;
    public $backoff = [1, 5, 15, 30, 60];
    public $uniqueFor = 300;

    public function __construct(
        public readonly string $vehicleId,
        public readonly array $syncOptions = []
    ) {
        // Dynamic queue assignment based on priority
        $this->onQueue($this->determineQueuePriority());
    }

    public function uniqueId(): string
    {
        return "vehicle_sync:{$this->vehicleId}";
    }

    public function handle(UnifiedTelematicSyncService $syncService): void
    {
        $vehicle = Vehicle::findOrFail($this->vehicleId);
        
        // Check if vehicle requires maintenance attention
        if ($vehicle->requiresMaintenanceSync()) {
            MaintenanceIntegrationJob::dispatch($vehicle)->onQueue('maintenance-priority');
        }

        $result = $syncService->syncVehicleData($vehicle);

        if (!$result->isSuccessful()) {
            throw new TelematicSyncException($result->getErrorMessage());
        }

        // Chain follow-up jobs based on sync results
        $this->dispatchFollowUpJobs($vehicle, $result);
    }

    private function determineQueuePriority(): string
    {
        return match($this->syncOptions['priority'] ?? 'normal') {
            'critical' => 'critical-telematic',
            'maintenance' => 'maintenance-priority', 
            'real-time' => 'real-time-sync',
            default => 'default-telematic'
        };
    }

    private function dispatchFollowUpJobs(Vehicle $vehicle, SyncResult $result): void
    {
        $followUpJobs = [];

        if ($result->hasMaintenanceAlerts()) {
            $followUpJobs[] = new ProcessMaintenanceAlertsJob($vehicle);
        }

        if ($result->hasGeofenceViolations()) {
            $followUpJobs[] = new ProcessGeofenceAlertsJob($vehicle);
        }

        if ($result->hasPerformanceAnomalies()) {
            $followUpJobs[] = new AnalyzeVehiclePerformanceJob($vehicle);
        }

        if (!empty($followUpJobs)) {
            $this->batch()->add($followUpJobs);
        }
    }
}
```

### Batch processing for fleet-wide synchronization

```php
<?php

class FleetSynchronizationJob implements ShouldQueue
{
    public function handle(UnifiedTelematicSyncService $syncService): string
    {
        $fleet = Fleet::with('vehicles')->findOrFail($this->fleetId);
        
        // Create batched sync jobs with intelligent prioritization
        $syncJobs = $fleet->vehicles->map(function (Vehicle $vehicle) {
            $priority = $this->calculateSyncPriority($vehicle);
            return new UnifiedVehicleDataSyncJob($vehicle->vehicle_id, ['priority' => $priority]);
        });

        $batch = Bus::batch($syncJobs->toArray())
            ->before(function (Batch $batch) {
                Log::info('Fleet sync batch started', ['batch_id' => $batch->id, 'fleet_id' => $this->fleetId]);
            })
            ->progress(function (Batch $batch) {
                // Update fleet sync progress in real-time
                broadcast(new FleetSyncProgressEvent($this->fleetId, $batch->progress()));
            })
            ->then(function (Batch $batch) {
                // Generate fleet health report
                GenerateFleetHealthReportJob::dispatch($this->fleetId);
            })
            ->catch(function (Batch $batch, Throwable $e) {
                // Handle batch failures with detailed logging
                $this->handleBatchFailure($batch, $e);
            })
            ->allowFailures()
            ->name("Fleet Sync - {$fleet->name}")
            ->onConnection('redis')
            ->onQueue('fleet-operations')
            ->dispatch();

        return $batch->id;
    }

    private function calculateSyncPriority(Vehicle $vehicle): string
    {
        if ($vehicle->hasActiveMaintenanceAlerts()) return 'critical';
        if ($vehicle->isInActiveRoute()) return 'real-time';
        if ($vehicle->requiresMaintenanceSync()) return 'maintenance';
        return 'normal';
    }
}
```

## Maintenance management system integration

The unified approach emphasizes seamless integration between telematic data and maintenance workflows, combining proactive maintenance scheduling with real-time diagnostic capabilities.

### Enhanced maintenance integration service

```php
<?php

namespace App\Services\Maintenance;

class MaintenanceIntegrationService
{
    public function __construct(
        private readonly MaintenanceApiClient $maintenanceClient,
        private readonly VehicleRepository $vehicleRepository,
        private readonly MaintenanceWorkOrderRepository $workOrderRepository
    ) {}

    public function processMaintenanceAlert(Vehicle $vehicle, TelematicEvent $event): void
    {
        $alertData = $this->analyzeTelematicEvent($event);
        
        if ($alertData['requiresImmediateAttention']) {
            $this->createUrgentMaintenanceWorkOrder($vehicle, $alertData);
        } elseif ($alertData['requiresScheduledMaintenance']) {
            $this->schedulePreventiveMaintenance($vehicle, $alertData);
        }

        // Update maintenance integration tracking
        $this->updateMaintenanceIntegrationRecord($vehicle, $event, $alertData);
    }

    public function syncMaintenanceData(Vehicle $vehicle): MaintenanceSyncResult
    {
        // Fetch current maintenance status from external CMMS
        $maintenanceData = $this->maintenanceClient->getVehicleMaintenanceStatus($vehicle->maintenance_system_id);
        
        // Update vehicle maintenance status
        $vehicle->update([
            'maintenance_status' => $maintenanceData['status'],
            'next_maintenance_due' => Carbon::parse($maintenanceData['next_due_date']),
        ]);

        // Sync recent telematic data that might affect maintenance schedules
        $this->syncRecentTelematicDataToMaintenance($vehicle);

        return new MaintenanceSyncResult(true, 'Maintenance data synchronized successfully');
    }

    private function analyzeTelematicEvent(TelematicEvent $event): array
    {
        $analysis = [
            'requiresImmediateAttention' => false,
            'requiresScheduledMaintenance' => false,
            'maintenanceType' => null,
            'priority' => 'LOW',
            'estimatedCost' => null,
        ];

        // Engine temperature analysis
        if ($event->engine_coolant_temp > 105) {
            $analysis['requiresImmediateAttention'] = true;
            $analysis['maintenanceType'] = 'COOLING_SYSTEM';
            $analysis['priority'] = 'CRITICAL';
        }

        // Fuel efficiency analysis
        if ($this->detectPoorFuelEfficiency($event)) {
            $analysis['requiresScheduledMaintenance'] = true;
            $analysis['maintenanceType'] = 'ENGINE_TUNE_UP';
            $analysis['priority'] = 'MEDIUM';
        }

        // Battery voltage analysis
        if ($event->battery_voltage < 12.0) {
            $analysis['requiresScheduledMaintenance'] = true;
            $analysis['maintenanceType'] = 'ELECTRICAL_SYSTEM';
            $analysis['priority'] = 'HIGH';
        }

        return $analysis;
    }

    private function createUrgentMaintenanceWorkOrder(Vehicle $vehicle, array $alertData): void
    {
        $workOrderData = [
            'vehicle_id' => $vehicle->vehicle_id,
            'maintenance_type' => $alertData['maintenanceType'],
            'priority' => $alertData['priority'],
            'description' => "Urgent maintenance required based on telematic alert: {$alertData['maintenanceType']}",
            'created_from_telematic' => true,
            'due_date' => now()->addHours(4), // Urgent - 4 hour SLA
        ];

        $workOrder = $this->workOrderRepository->create($workOrderData);
        
        // Notify maintenance team immediately
        $this->notifyMaintenanceTeam($workOrder, 'urgent');
        
        // Update vehicle status
        $vehicle->update(['maintenance_status' => 'MAINTENANCE_REQUIRED']);
    }
}
```

## Implementation phases and jira task breakdown

Building on the research findings, the unified implementation follows a structured 6-phase approach over 12 weeks, combining the 8-week timeline of the first plan with the comprehensive scope of the second plan.

### Phase 1: Architecture foundation (Weeks 1-2)

**Epic: TMS-001 - Architecture Foundation**
- **Story: TMS-001-1** - Database schema design and migration
  - Task: Design unified telematic_events table structure (8 points)
  - Task: Create vehicle-maintenance integration tables (5 points)
  - Task: Implement time-based partitioning strategy (13 points)
  - Task: Create database migration scripts (8 points)
- **Story: TMS-001-2** - Laravel service layer architecture
  - Task: Design unified telematic service structure (8 points)
  - Task: Implement repository pattern for telematic data (5 points)
  - Task: Create maintenance integration service framework (8 points)
  - Task: Set up dependency injection configuration (3 points)

### Phase 2: Samsara API integration (Weeks 3-4)

**Epic: TMS-002 - Samsara Integration**
- **Story: TMS-002-1** - API client development
  - Task: Implement SamsaraApiClient with rate limiting (13 points)
  - Task: Create webhook endpoint for real-time updates (8 points)
  - Task: Implement API authentication and security (5 points)
  - Task: Add circuit breaker pattern for API resilience (8 points)
- **Story: TMS-002-2** - Data mapping and transformation
  - Task: Create Samsara to TMS data mapping service (8 points)
  - Task: Implement data validation and sanitization (5 points)
  - Task: Create data transformation pipelines (8 points)

### Phase 3: Queue architecture implementation (Weeks 5-6)

**Epic: TMS-003 - Queue System**
- **Story: TMS-003-1** - Core queue job development
  - Task: Implement UnifiedVehicleDataSyncJob (13 points)
  - Task: Create FleetSynchronizationJob with batching (8 points)
  - Task: Implement maintenance integration jobs (8 points)
  - Task: Add job monitoring and logging (5 points)
- **Story: TMS-003-2** - Queue configuration and optimization
  - Task: Configure Redis queue connections (3 points)
  - Task: Set up job prioritization system (5 points)
  - Task: Implement failed job handling (5 points)
  - Task: Configure Supervisor for queue workers (3 points)

### Phase 4: Maintenance system integration (Weeks 7-8)

**Epic: TMS-004 - Maintenance Integration**
- **Story: TMS-004-1** - CMMS integration
  - Task: Develop MaintenanceApiClient (8 points)
  - Task: Implement bi-directional data sync (13 points)
  - Task: Create maintenance alert processing (8 points)
  - Task: Build maintenance workflow automation (8 points)
- **Story: TMS-004-2** - Predictive maintenance features
  - Task: Implement telematic data analysis algorithms (13 points)
  - Task: Create maintenance prediction models (8 points)
  - Task: Build maintenance scheduling optimization (8 points)

### Phase 5: Testing and quality assurance (Weeks 9-10)

**Epic: TMS-005 - Testing and QA**
- **Story: TMS-005-1** - Automated testing suite
  - Task: Unit tests for all service classes (13 points)
  - Task: Integration tests for API endpoints (8 points)
  - Task: Queue job testing framework (8 points)
  - Task: Database performance testing (5 points)
- **Story: TMS-005-2** - System integration testing
  - Task: End-to-end Samsara integration testing (8 points)
  - Task: Maintenance system integration testing (8 points)
  - Task: Load testing for high-volume scenarios (8 points)
  - Task: Failover and recovery testing (5 points)

### Phase 6: Deployment and monitoring (Weeks 11-12)

**Epic: TMS-006 - Deployment**
- **Story: TMS-006-1** - Production deployment
  - Task: Set up production environment configuration (5 points)
  - Task: Implement blue-green deployment pipeline (8 points)
  - Task: Configure monitoring and alerting (8 points)
  - Task: Create rollback procedures (5 points)
- **Story: TMS-006-2** - Performance monitoring and optimization
  - Task: Set up Laravel Horizon dashboard (3 points)
  - Task: Implement custom performance metrics (5 points)
  - Task: Create automated performance optimization (8 points)
  - Task: Deploy health check endpoints (3 points)

## Laravel 12 best practices implementation

The unified solution leverages Laravel 12's enhanced features for optimal performance and maintainability:

### Enhanced starter kit utilization
```php
// Utilize Laravel 12's improved authentication system
php artisan make:auth --kit=api --with-teams

// Configure enhanced HTTP client pooling
Http::pool(fn (Pool $pool) => [
    'samsara' => $pool->as('samsara')
        ->baseUrl('https://api.samsara.com')
        ->withHeaders(['Authorization' => "Bearer {$apiKey}"])
        ->timeout(30)
        ->retry(3, 1000),
]);
```

### Advanced queue configuration
```php
// config/queue.php - Laravel 12 optimized settings
'connections' => [
    'redis' => [
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => env('REDIS_QUEUE', 'default'),
        'retry_after' => 300,
        'block_for' => 5,
        'after_commit' => true,
        'monitor_failed_jobs' => true, // Laravel 12 enhancement
    ],
],
```

### Event-driven architecture optimization
```php
// Utilize Laravel 12's improved event discovery
class TelematicEventServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        Event::listen(
            VehicleDataSynced::class,
            [
                UpdateMaintenanceStatus::class,
                NotifyFleetManager::class,
                CacheVehicleLocation::class,
            ]
        );
    }
}
```

## Risk mitigation and monitoring strategy

The unified plan addresses both technical and operational risks through comprehensive monitoring and proactive mitigation:

### Circuit breaker implementation
```php
class TelematicCircuitBreaker
{
    private const FAILURE_THRESHOLD = 5;
    private const RECOVERY_TIMEOUT = 300;

    public function call(callable $operation)
    {
        if ($this->isOpen()) {
            throw new CircuitBreakerOpenException();
        }

        try {
            $result = $operation();
            $this->recordSuccess();
            return $result;
        } catch (Exception $e) {
            $this->recordFailure();
            throw $e;
        }
    }
}
```

### Comprehensive monitoring setup
```php
// Custom metrics for telematic system health
class TelematicSystemHealthCheck
{
    public function check(): array
    {
        return [
            'samsara_api_status' => $this->checkSamsaraApi(),
            'queue_health' => $this->checkQueueHealth(),
            'database_performance' => $this->checkDatabasePerformance(),
            'maintenance_integration' => $this->checkMaintenanceIntegration(),
            'data_freshness' => $this->checkDataFreshness(),
        ];
    }
}
```

This unified telematic data sync architecture plan provides a comprehensive, scalable solution that combines the service layer enhancement approach with robust maintenance integration capabilities. The implementation leverages Laravel 12's advanced features, incorporates industry best practices for API integration and queue management, and establishes a solid foundation for long-term growth and maintenance of the TMS system.