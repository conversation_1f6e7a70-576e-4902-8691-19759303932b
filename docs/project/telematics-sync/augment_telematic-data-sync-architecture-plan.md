# Telematic Data Synchronization Architecture Plan

## Executive Summary

This document outlines a comprehensive plan for implementing a scalable, provider-agnostic telematic data synchronization architecture that efficiently syncs truck, trailer, and asset data from Samsara to our local database, with focus on mileage/odometer readings and sensor data for maintenance scheduling.

## Current State Analysis

### Existing Infrastructure
- **External Entity Mappings**: Robust `external_entity_mappings` table already in place
- **Provider Abstraction**: Well-designed telematic provider system with strategy pattern
- **Samsara Integration**: Basic vehicle sync functionality implemented
- **Service Architecture**: Clean separation with Services, Settings, and Sync strategies
- **Queue System**: Database-driven queue system configured and ready
- **Maintenance UI**: Frontend maintenance interfaces already implemented (awaiting backend data)

### Current Limitations
- Only basic vehicle information sync (no mileage/sensor data)
- No maintenance-specific data storage
- Limited to initial sync only (no ongoing data updates)
- No trailer/asset sync capabilities
- No maintenance calculation integration

## Optimal Samsara API Endpoints Analysis

Based on `/docs/samsara/split` documentation analysis, the most efficient endpoints for bulk data retrieval are:

### Primary Endpoints for Implementation

1. **`/fleet/vehicles/stats`** - Bulk vehicle statistics
   - **Purpose**: Real-time mileage and sensor data for all vehicles
   - **Key Data**: `obdOdometerMeters`, `gpsOdometerMeters`, `engineStates`, `faultCodes`
   - **Efficiency**: Single call for all vehicles with up to 4 stat types
   - **Rate Limit Friendly**: Designed for bulk operations

2. **`/fleet/vehicles/stats/feed`** - Incremental updates
   - **Purpose**: Follow feed pattern for ongoing synchronization
   - **Key Feature**: Cursor-based pagination with `endCursor` for incremental updates
   - **Efficiency**: Only retrieves changes since last sync
   - **Ideal for**: Scheduled maintenance data updates

3. **`/fleet/vehicles/stats/history`** - Historical data backfill
   - **Purpose**: Historical mileage trends for maintenance calculations
   - **Use Case**: Initial data population and gap filling
   - **Time Range**: Configurable date ranges for bulk historical import

4. **`/v1/fleet/assets`** - Asset/trailer information
   - **Purpose**: Trailer and equipment synchronization
   - **Note**: Legacy API but necessary for trailer data
   - **Integration**: Extends current vehicle-only sync to full fleet

### Secondary Endpoints for Enhanced Features

- **`/fleet/vehicles`** - Vehicle metadata updates
- **`/sensors/history`** - Detailed sensor data for predictive maintenance
- **`/fleet/vehicles/{id}/locations`** - Location-based maintenance scheduling

## Database Schema Design

### New Tables Required

```sql
-- Telematic data storage for mileage and sensor readings
CREATE TABLE telematic_readings (
    id BIGINT PRIMARY KEY,
    external_entity_mapping_id BIGINT NOT NULL,
    reading_type VARCHAR(50) NOT NULL, -- 'odometer', 'engine_hours', 'fuel_level', etc.
    value DECIMAL(15,3) NOT NULL,
    unit VARCHAR(20) NOT NULL, -- 'meters', 'hours', 'percent', etc.
    recorded_at TIMESTAMP NOT NULL,
    synced_at TIMESTAMP NOT NULL,
    metadata JSON,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    FOREIGN KEY (external_entity_mapping_id) REFERENCES external_entity_mappings(id),
    INDEX idx_mapping_type_recorded (external_entity_mapping_id, reading_type, recorded_at),
    INDEX idx_recorded_at (recorded_at),
    UNIQUE KEY unique_reading (external_entity_mapping_id, reading_type, recorded_at)
);

-- Maintenance schedules and intervals
CREATE TABLE maintenance_schedules (
    id BIGINT PRIMARY KEY,
    entity_type VARCHAR(100) NOT NULL, -- 'App\Models\Truck', 'App\Models\Trailer'
    entity_id BIGINT NOT NULL,
    maintenance_type VARCHAR(100) NOT NULL, -- 'oil_change', 'tire_rotation', 'inspection'
    interval_type ENUM('mileage', 'time', 'engine_hours') NOT NULL,
    interval_value INT NOT NULL, -- 5000 miles, 90 days, 250 hours
    last_performed_at TIMESTAMP NULL,
    last_performed_mileage INT NULL,
    next_due_at TIMESTAMP NULL,
    next_due_mileage INT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_next_due (next_due_at, next_due_mileage),
    INDEX idx_maintenance_type (maintenance_type)
);

-- Maintenance history tracking
CREATE TABLE maintenance_records (
    id BIGINT PRIMARY KEY,
    maintenance_schedule_id BIGINT NOT NULL,
    performed_at TIMESTAMP NOT NULL,
    performed_mileage INT NULL,
    performed_engine_hours INT NULL,
    notes TEXT NULL,
    cost DECIMAL(10,2) NULL,
    vendor VARCHAR(255) NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    FOREIGN KEY (maintenance_schedule_id) REFERENCES maintenance_schedules(id),
    INDEX idx_schedule_performed (maintenance_schedule_id, performed_at)
);

-- Sync state tracking for incremental updates
CREATE TABLE telematic_sync_states (
    id BIGINT PRIMARY KEY,
    provider VARCHAR(50) NOT NULL,
    sync_type VARCHAR(50) NOT NULL, -- 'vehicles', 'assets', 'stats'
    last_cursor VARCHAR(255) NULL, -- For feed-based APIs
    last_sync_at TIMESTAMP NOT NULL,
    next_sync_at TIMESTAMP NULL,
    metadata JSON,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    UNIQUE KEY unique_provider_type (provider, sync_type)
);
```

### Enhanced External Entity Mappings Usage

The existing `external_entity_mappings` table will be extended to support:
- Trucks (already implemented)
- Trailers (new)
- Assets/Equipment (new)
- Sensors (new)

## Service Layer Architecture

### Provider Abstraction Enhancement

```php
// Enhanced interface for data sync capabilities
interface TelematicDataSyncInterface extends TelematicSyncInterface
{
    public function syncVehicleStats(array $vehicleIds = []): TelematicSyncResult;
    public function syncAssets(array $assetIds = []): TelematicSyncResult;
    public function syncIncrementalStats(string $cursor = null): TelematicSyncResult;
    public function getLastSyncCursor(string $syncType): ?string;
}

// Enhanced Samsara strategy with data sync capabilities
class SamsaraDataStrategy implements TelematicDataSyncInterface
{
    // Existing vehicle sync + new data sync methods
}
```

### New Service Classes

1. **`TelematicDataSyncService`** - Orchestrates data synchronization
2. **`MaintenanceCalculationService`** - Calculates maintenance schedules
3. **`TelematicReadingService`** - Manages telematic data storage/retrieval
4. **`MaintenanceScheduleService`** - Manages maintenance scheduling logic

## Queue Job Architecture

### Job Hierarchy

```php
// Base job for all telematic operations
abstract class TelematicSyncJob implements ShouldQueue
{
    use Queueable, SerializesModels, InteractsWithQueue, Dispatchable;
    
    public int $timeout = 300; // 5 minutes
    public int $tries = 3;
    public string $queue = 'telematic-sync';
}

// Specific job implementations
class SyncVehicleStatsJob extends TelematicSyncJob
class SyncAssetDataJob extends TelematicSyncJob  
class SyncIncrementalDataJob extends TelematicSyncJob
class CalculateMaintenanceSchedulesJob extends TelematicSyncJob
class ProcessTelematicReadingsJob extends TelematicSyncJob
```

### Job Batching Strategy

```php
// Batch jobs for large fleet synchronization
$batch = Bus::batch([
    new SyncVehicleStatsJob($vehicleChunk1),
    new SyncVehicleStatsJob($vehicleChunk2),
    new SyncAssetDataJob($assetChunk1),
])->then(function (Batch $batch) {
    // Trigger maintenance calculations after all data sync
    CalculateMaintenanceSchedulesJob::dispatch();
})->dispatch();
```

## Command Interface Design

### Enhanced Command Structure

```php
// Enhanced sync command with data-specific options
class TelematicDataSyncCommand extends Command
{
    protected $signature = 'sync:telematics:data 
                           {--type=all : Sync type (vehicles|assets|stats|all)}
                           {--incremental : Use incremental sync where available}
                           {--vehicle-ids=* : Specific vehicle IDs to sync}
                           {--cron : Run in cron mode}
                           {--calculate-maintenance : Trigger maintenance calculations}';
}

// Separate maintenance calculation command
class CalculateMaintenanceCommand extends Command
{
    protected $signature = 'maintenance:calculate
                           {--entity-type=all : Entity type (trucks|trailers|all)}
                           {--entity-ids=* : Specific entity IDs}';
}
```

## Integration with Maintenance System

### Data Flow Architecture

```
Samsara API → Queue Jobs → Telematic Readings → Maintenance Calculations → UI Updates
     ↓              ↓              ↓                    ↓                  ↓
API Clients → Job Processing → Data Storage → Schedule Updates → Real-time Data
```

### Maintenance Calculation Logic

1. **Mileage-based Maintenance**: Use latest odometer readings
2. **Time-based Maintenance**: Calculate from last service dates
3. **Engine Hours**: Track cumulative engine runtime
4. **Predictive Maintenance**: Analyze sensor trends for early warnings

## Scalability Considerations

### Performance Optimizations

1. **Chunked Processing**: Process large datasets in configurable chunks
2. **Queue Prioritization**: High-priority queue for critical maintenance alerts
3. **Caching Strategy**: Cache frequently accessed maintenance schedules
4. **Database Indexing**: Optimized indexes for time-series telematic data
5. **Batch Operations**: Bulk insert/update operations for efficiency

### Rate Limiting & API Management

1. **Exponential Backoff**: Handle Samsara API rate limits gracefully
2. **Circuit Breaker**: Prevent cascade failures during API outages
3. **Request Throttling**: Configurable request rates per provider
4. **Retry Logic**: Intelligent retry with jitter for failed requests

### Monitoring & Observability

1. **Sync Metrics**: Track sync success rates, data volumes, processing times
2. **Maintenance Alerts**: Real-time notifications for overdue maintenance
3. **Data Quality Monitoring**: Detect anomalies in telematic readings
4. **Performance Dashboards**: Monitor queue health and processing efficiency

## Implementation Phases

### Phase 1: Foundation (Weeks 1-2)
- Database schema implementation
- Enhanced service layer architecture
- Basic queue job structure
- Command interface updates

### Phase 2: Core Data Sync (Weeks 3-4)
- Vehicle stats synchronization
- Telematic readings storage
- Incremental sync implementation
- Basic maintenance calculations

### Phase 3: Asset Integration (Weeks 5-6)
- Trailer/asset synchronization
- Extended entity mapping support
- Enhanced maintenance scheduling
- Performance optimizations

### Phase 4: Advanced Features (Weeks 7-8)
- Predictive maintenance algorithms
- Real-time alerting system
- Advanced monitoring and dashboards
- Production deployment and testing

## Risk Mitigation

### Technical Risks
- **API Rate Limits**: Implement robust throttling and backoff strategies
- **Data Volume**: Design for horizontal scaling with queue workers
- **Data Consistency**: Use database transactions and idempotent operations
- **Provider Changes**: Maintain flexible abstraction layer

### Operational Risks
- **Downtime Impact**: Graceful degradation during maintenance windows
- **Data Loss**: Comprehensive backup and recovery procedures
- **Performance Impact**: Monitor and optimize database performance
- **Integration Complexity**: Thorough testing of all integration points

## Success Metrics

### Technical KPIs
- Sync success rate > 99.5%
- Average sync latency < 5 minutes
- Queue processing efficiency > 95%
- Data accuracy rate > 99.9%

### Business KPIs
- Maintenance schedule accuracy improvement
- Reduced manual data entry by 90%
- Faster maintenance decision making
- Improved fleet utilization through better maintenance planning

---

*This plan provides a comprehensive roadmap for implementing a scalable, maintainable telematic data synchronization architecture that will serve as the foundation for advanced fleet maintenance management.*
