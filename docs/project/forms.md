# Form Implementation Guidelines

## Core Requirements

- Form components should be created as reusable components in `resources/js/components/{module}` directory
- Form data handling should use Inertia.js's `useForm` hook
- Form components should accept props for data, errors, and submission handling
- Form fields should use the `FormField` component for consistent layout and error handling
- Forms should be organized into logical sections using card-like containers

## Basic Implementation Structure

### 1. Form Component Creation

Create a reusable form component in your module directory (e.g., `resources/js/components/trucks/TruckForm.tsx`):

```tsx
import { FormEventHandler } from 'react';
import { FormField } from '@/components/ui/form-field';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
// Import other UI components as needed

interface YourFormProps {
  data: any;
  setData: (field: string, value: any) => void;
  errors: Record<string, string>;
  processing: boolean;
  onSubmit: FormEventHandler;
  buttonLabel: string;
  // Other required props
}

export default function YourForm({
  data,
  setData,
  errors,
  processing,
  onSubmit,
  buttonLabel,
  // Other props
}: YourFormProps) {
  // Helper functions for handling specific input types
  const handleCheckboxChange = (field: string) => (checked: boolean) => {
    setData(field, checked);
  };

  const handleDateChange = (field: string) => (date: string | undefined) => {
    setData(field, date || null);
  };

  return (
    <form onSubmit={onSubmit} className="mt-8 max-w-5xl grid gap-4 lg:grid-cols-2">
      {/* Form sections and fields */}
      <div className="lg:col-span-2 flex justify-end">
        <Button type="submit" disabled={processing}>
          {buttonLabel}
        </Button>
      </div>
    </form>
  );
}
```

### 2. Page Component Implementation

In your page component (e.g., `resources/js/pages/trucks/create.tsx`):

```tsx
import { FormEventHandler } from 'react';
import { Head, useForm, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import HeadingSmall from '@/components/heading-small';
import { toast } from 'sonner';
import YourForm from '@/components/your-module/YourForm';
import { YourFormData } from '@/types/your-module';

export default function CreateItem() {
  const { errors, ...otherProps } = usePage<YourPageProps>().props;
  const { data, setData, post, processing } = useForm<YourFormData>({
    // Initial form values
    field1: '',
    field2: null,
    // ...
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();

    post(route('your-route.store'), {
      onSuccess: () => {
        toast.success('Item created successfully');
      },
      onError: (errors) => {
        const errorMessage = Object.values(errors)[0] as string;
        toast.error(errorMessage);
      },
    });
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Create Item" />

      <div className="px-4 py-6">
        <HeadingSmall title="Create Item" description="Create a new item" />
        <YourForm
          data={data}
          setData={setData}
          errors={errors}
          processing={processing}
          onSubmit={submit}
          buttonLabel="Save"
          // Other required props
        />
      </div>
    </AppLayout>
  );
}
```

## Form Field Guidelines

### Using the FormField Component

The `FormField` component provides consistent layout and error handling for form inputs:

```tsx
<FormField label="Field Label" error={errors.field_name} required id="field_name">
  <Input
    type="text"
    value={data.field_name || ''}
    onChange={(e) => setData('field_name', e.target.value)}
    placeholder="Placeholder text"
    aria-invalid={!!errors.field_name}
  />
</FormField>
```

### Text Input Fields

```tsx
<FormField label="Name" error={errors.name} required id="name">
  <Input
    type="text"
    required
    value={data.name}
    onChange={(e) => setData('name', e.target.value)}
    placeholder="Enter name"
    aria-invalid={!!errors.name}
  />
</FormField>
```

### Number Input Fields

```tsx
<FormField label="Year" error={errors.year} id="year">
  <Input
    type="number"
    value={data.year || ''}
    onChange={(e) => setData('year', parseInt(e.target.value) || null)}
    placeholder="Year"
    aria-invalid={!!errors.year}
  />
</FormField>
```

### Select Dropdown Fields

```tsx
<FormField label="Status" error={errors.status} id="status">
  <Select
    value={data.status || ''}
    onValueChange={(value) => setData('status', value)}
  >
    <SelectTrigger>
      <SelectValue placeholder="Select status" />
    </SelectTrigger>
    <SelectContent>
      {statusOptions.map((option) => (
        <SelectItem key={option.value} value={option.value}>
          {option.label}
        </SelectItem>
      ))}
    </SelectContent>
  </Select>
</FormField>
```

### Date Picker Fields

```tsx
<FormField label="Activation Date" error={errors.activation_date} id="activation_date">
  <DatePicker
    value={data.activation_date || undefined}
    onChange={handleDateChange('activation_date')}
  />
</FormField>
```

### Checkbox Fields

```tsx
<div className="flex items-center space-x-2">
  <Checkbox
    id="feature_enabled"
    checked={data.feature_enabled}
    onCheckedChange={handleCheckboxChange('feature_enabled')}
  />
  <label
    htmlFor="feature_enabled"
    className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
  >
    Enable Feature
  </label>
</div>
```

### Searchable Dropdown with Combobox

For more complex selection interfaces, use a combobox pattern:

```tsx
<FormField label="Driver" error={errors.driver_id} id="driver_id">
  {(() => {
    const selectedDriver = drivers.find((d) => d.id === data.driver_id);
    return (
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={false}
            className={cn('w-full justify-between', errors.driver_id && 'border-destructive', 'relative')}
          >
            {selectedDriver ? (
              <span className="flex items-center gap-2">
                {`${selectedDriver.name}`}
                <span
                  className="ml-2 text-muted-foreground hover:text-destructive cursor-pointer"
                  onClick={e => {
                    e.stopPropagation();
                    setData('driver_id', null);
                  }}
                  tabIndex={0}
                  role="button"
                  aria-label="Clear selection"
                >
                  <X className="h-4 w-4" />
                </span>
              </span>
            ) : (
              <span className="placeholder:text-muted-foreground text-muted-foreground">Select driver</span>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput placeholder="Search..." />
            <CommandList>
              <CommandEmpty>No results found.</CommandEmpty>
              <CommandGroup>
                {drivers.map((driver) => (
                  <CommandItem
                    key={driver.id}
                    value={driver.name}
                    onSelect={() => setData('driver_id', driver.id)}
                  >
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        data.driver_id === driver.id ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                    {driver.name}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    );
  })()}
</FormField>
```

## Form Layout Guidelines

### Organizing Form Sections

Group related fields into sections using card-like containers:

```tsx
<div className="space-y-6 border rounded-lg p-6">
  <h3 className="text-lg font-semibold mb-2">Section Title</h3>
  <div className="grid grid-cols-2 gap-4">
    {/* Form fields */}
  </div>
</div>
```

### Responsive Grid Layout

Use responsive grid layouts for form sections and fields:

```tsx
<form onSubmit={onSubmit} className="mt-8 max-w-5xl grid gap-4 lg:grid-cols-2">
  {/* Form sections */}
  <div className="lg:col-span-2 flex justify-end">
    <Button type="submit" disabled={processing}>
      {buttonLabel}
    </Button>
  </div>
</form>
```

### Field Grid Layout

Arrange fields within sections using grid:

```tsx
<div className="grid grid-cols-2 gap-4">
  <FormField label="First Name" error={errors.first_name} id="first_name">
    {/* Input component */}
  </FormField>
  <FormField label="Last Name" error={errors.last_name} id="last_name">
    {/* Input component */}
  </FormField>
</div>
```

## Form Submission and Error Handling

### Form Submission

Use the Inertia.js `useForm` hook for form submission:

```tsx
const submit: FormEventHandler = (e) => {
  e.preventDefault();

  post(route('your-route.store'), {
    onSuccess: () => {
      toast.success('Item created successfully');
    },
    onError: (errors) => {
      const errorMessage = Object.values(errors)[0] as string;
      toast.error(errorMessage);
    },
  });
};
```

### Error Handling

Display validation errors using the `FormField` component:

```tsx
<FormField label="Field Label" error={errors.field_name} id="field_name">
  {/* Input component */}
</FormField>
```

## Type Definitions

### Form Data Type

Define a type for your form data in the `resources/js/types` directory:

```tsx
// resources/js/types/your-module.ts
export interface YourFormData {
  field1: string;
  field2: string | null;
  field3: number | null;
  field4: boolean;
  // Other fields
}
```

### Page Props Type

Define a type for your page props:

```tsx
interface YourPageProps {
  errors: Record<string, string>;
  // Other props passed from the controller
}
```

## Best Practices

1. **Separation of Concerns**
   - Create reusable form components in the components directory
   - Keep form logic separate from page components
   - Define form data types in dedicated type files

2. **Form Field Organization**
   - Group related fields into logical sections
   - Use consistent layout patterns across forms
   - Provide clear section headings

3. **Error Handling**
   - Always display validation errors next to the relevant fields
   - Provide toast notifications for form submission results
   - Use aria attributes for accessibility

4. **Default Values**
   - Always provide default values for form fields
   - Handle null/undefined values gracefully
   - Use empty strings for text fields, null for optional fields

5. **Type Safety**
   - Define proper types for form data and props
   - Avoid using `any` type; create proper interfaces instead

## Complete Example

### Form Component (TruckForm.tsx)

```tsx
import { FormEventHandler } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FormField } from '@/components/ui/form-field';
import { Checkbox } from '@/components/ui/checkbox';
import { DatePicker } from '@/components/ui/date-picker';

interface TruckFormProps {
  data: any;
  setData: (field: string, value: any) => void;
  errors: Record<string, string>;
  processing: boolean;
  onSubmit: FormEventHandler;
  regions: { value: string; label: string }[];
  fleetStatuses: { value: string; label: string }[];
  ownershipType: { value: string; label: string }[];
  buttonLabel: string;
}

export default function TruckForm({
  data,
  setData,
  errors,
  processing,
  onSubmit,
  regions,
  fleetStatuses,
  ownershipType,
  buttonLabel,
}: TruckFormProps) {
  const handleCheckboxChange = (field: string) => (checked: boolean) => {
    setData(field, checked);
  };

  const handleDateChange = (field: string) => (date: string | undefined) => {
    setData(field, date || null);
  };

  return (
    <form onSubmit={onSubmit} className="mt-8 max-w-5xl grid gap-4 lg:grid-cols-2">
      <div className="space-y-6 border rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-2">Truck Details</h3>
        <div className="grid grid-cols-2 gap-4">
          <FormField label="Unit Number" error={errors.unit_number} required id="unit_number">
            <Input
              type="text"
              required
              value={data.unit_number}
              onChange={(e) => setData('unit_number', e.target.value)}
              placeholder="Unit number"
              aria-invalid={!!errors.unit_number}
            />
          </FormField>
          <FormField label="Make" error={errors.make} id="make">
            <Input
              type="text"
              value={data.make || ''}
              onChange={(e) => setData('make', e.target.value)}
              placeholder="Make"
              aria-invalid={!!errors.make}
            />
          </FormField>
        </div>
      </div>

      <div className="space-y-6 border rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-2">Registration</h3>
        <div className="grid grid-cols-2 gap-4">
          <FormField label="Plate" error={errors.plate} id="plate">
            <Input
              type="text"
              value={data.plate || ''}
              onChange={(e) => setData('plate', e.target.value)}
              placeholder="License plate"
              aria-invalid={!!errors.plate}
            />
          </FormField>
          <FormField label="Plate Region" error={errors.plate_region} id="plate_region">
            <Select value={data.plate_region || ''} onValueChange={(value) => setData('plate_region', value)}>
              <SelectTrigger aria-invalid={!!errors.plate_region}>
                <SelectValue placeholder="Select region" />
              </SelectTrigger>
              <SelectContent>
                {regions.map((region) => (
                  <SelectItem key={region.value} value={region.value}>
                    {region.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormField>
        </div>
      </div>

      <div className="lg:col-span-2 flex justify-end">
        <Button type="submit" disabled={processing}>
          {buttonLabel}
        </Button>
      </div>
    </form>
  );
}
```

### Page Component (create.tsx)

```tsx
import { BreadcrumbItem } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { Head, useForm, usePage } from '@inertiajs/react';
import HeadingSmall from '@/components/heading-small';
import { FormEventHandler } from 'react';
import { toast } from 'sonner';
import { TruckFormData } from '@/types/truck';
import TruckForm from '@/components/trucks/TruckForm';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Trucks',
    href: route('trucks.index'),
  },
  {
    title: 'Create Truck',
    href: route('trucks.create'),
  },
];

interface CreateTruckPageProps {
  errors: Record<string, string>;
  regions: { value: string; label: string }[];
  [key: string]: unknown;
}

export default function CreateTruck() {
  const { errors, regions } = usePage<CreateTruckPageProps>().props;
  const { data, setData, post, processing } = useForm<TruckFormData>({
    unit_number: '',
    make: null,
    model: null,
    year: null,
    plate: null,
    plate_region: 'il',
    fleet_status: null,
    ownership_type: null,
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();

    post(route('trucks.store'), {
      onSuccess: () => {
        toast.success('Truck created successfully');
      },
      onError: (errors) => {
        const errorMessage = Object.values(errors)[0] as string;
        toast.error(errorMessage);
      },
    });
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Create Truck" />

      <div className="px-4 py-6">
        <HeadingSmall title="Create Truck" description="Create a new truck in the fleet" />
        <TruckForm
          data={data}
          setData={setData}
          errors={errors}
          processing={processing}
          onSubmit={submit}
          regions={regions}
          fleetStatuses={Object.entries(fleetStatusLabels).map(([value, label]) => ({ value, label }))}
          ownershipType={Object.entries(ownershipTypeLabels).map(([value, label]) => ({ value, label }))}
          buttonLabel="Save"
        />
      </div>
    </AppLayout>
  );
}
```