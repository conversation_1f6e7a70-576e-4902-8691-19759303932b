# TMS-216: Maintenance Calculation Service Implementation

## Overview

This implementation provides the core maintenance calculation logic that uses telematic data (odometer readings, engine hours) to automatically calculate and update maintenance schedules for trucks and trailers. It builds upon the telematic data synchronization architecture to provide intelligent maintenance scheduling.

## Story Details

- **Epic**: TMS-207 - Telematic Data Synchronization Architecture Implementation
- **Story**: TMS-216 - Maintenance Calculation Service Implementation
- **Story Points**: 21
- **Phase**: 3 (Maintenance Integration - Weeks 5-6)

## Implementation Files

### Core Service Implementation

- [MaintenanceCalculationService.md](./MaintenanceCalculationService.md) - Core maintenance calculation business logic
- [MaintenanceIntervalService.md](./MaintenanceIntervalService.md) - Maintenance interval management
- [MaintenanceDueDateCalculator.md](./MaintenanceDueDateCalculator.md) - Due date prediction algorithms

### Queue Jobs

- [CalculateMaintenanceSchedulesJob.md](./CalculateMaintenanceSchedulesJob.md) - Automated maintenance calculation job
- [MaintenanceAlertJob.md](./MaintenanceAlertJob.md) - Overdue maintenance alert processing

### Events and Listeners

- [MaintenanceCalculatedEvent.md](./MaintenanceCalculatedEvent.md) - Event for maintenance calculations
- [MaintenanceOverdueEvent.md](./MaintenanceOverdueEvent.md) - Event for overdue maintenance detection
- [MaintenanceEventListeners.md](./MaintenanceEventListeners.md) - Event listeners for maintenance events

### Data Transfer Objects

- [MaintenanceCalculationResult.md](./MaintenanceCalculationResult.md) - Calculation result DTOs
- [MaintenanceScheduleData.md](./MaintenanceScheduleData.md) - Schedule data structures

### Configuration and Rules

- [MaintenanceRulesConfiguration.md](./MaintenanceRulesConfiguration.md) - Maintenance rules and intervals
- [MaintenanceTypeDefinitions.md](./MaintenanceTypeDefinitions.md) - Maintenance type configurations

## Architecture Overview

### Core Components

1. **MaintenanceCalculationService**: Central service for all maintenance calculations
2. **MaintenanceIntervalService**: Manages maintenance intervals and rules
3. **MaintenanceDueDateCalculator**: Calculates due dates based on various factors
4. **CalculateMaintenanceSchedulesJob**: Queue job for automated processing
5. **Maintenance Events**: Event-driven architecture for maintenance notifications

### Key Design Principles

- **Data-Driven**: Uses latest telematic readings for accurate calculations
- **Configurable**: Flexible maintenance intervals and rules
- **Event-Driven**: Publishes events for maintenance state changes
- **Idempotent**: Calculations can be safely re-run
- **Performance Optimized**: Efficient bulk processing capabilities

## Service Descriptions

### 1. MaintenanceCalculationService

**Purpose**: Core service for calculating maintenance schedules based on telematic data

**Key Features**:
- Mileage-based maintenance calculations
- Engine hours-based scheduling
- Time-based maintenance intervals
- Due date prediction algorithms
- Overdue maintenance detection
- Multiple maintenance types support

**Supported Maintenance Types**:
- Oil changes (mileage/engine hours)
- Tire rotations (mileage/time)
- Brake inspections (mileage/time)
- Annual inspections (time-based)
- Engine service (engine hours/mileage)
- Transmission service (mileage/time)
- DOT inspections (time-based)
- Preventive maintenance (configurable)

### 2. MaintenanceIntervalService

**Purpose**: Manages maintenance intervals and rules configuration

**Key Features**:
- Configurable maintenance intervals
- Vehicle-specific maintenance rules
- Maintenance type definitions
- Interval validation and normalization
- Rule inheritance and overrides

### 3. MaintenanceDueDateCalculator

**Purpose**: Calculates maintenance due dates using prediction algorithms

**Key Features**:
- Usage pattern analysis
- Predictive due date calculations
- Multiple calculation methods
- Confidence scoring
- Historical data analysis

### 4. CalculateMaintenanceSchedulesJob

**Purpose**: Queue job for automated maintenance schedule calculations

**Key Features**:
- Batch processing for multiple vehicles
- Targeted calculations for specific entities
- Progress tracking and reporting
- Error handling and retry logic
- Integration with telematic sync jobs

## Installation Instructions

### 1. Create Service Files

Create the service files using the provided specifications:

```bash
# Create core services
# Copy content from: MaintenanceCalculationService.md
# Copy content from: MaintenanceIntervalService.md
# Copy content from: MaintenanceDueDateCalculator.md

# Create queue jobs
# Copy content from: CalculateMaintenanceSchedulesJob.md
# Copy content from: MaintenanceAlertJob.md

# Create events and listeners
# Copy content from: MaintenanceCalculatedEvent.md
# Copy content from: MaintenanceOverdueEvent.md
# Copy content from: MaintenanceEventListeners.md

# Create DTOs
# Copy content from: MaintenanceCalculationResult.md
# Copy content from: MaintenanceScheduleData.md

# Create configuration
# Copy content from: MaintenanceRulesConfiguration.md
# Copy content from: MaintenanceTypeDefinitions.md
```

### 2. Update Service Provider Registration

Register the maintenance services in the TelematicSyncProvider:

```php
// In app/Providers/TelematicSyncProvider.php
$this->app->singleton(MaintenanceCalculationService::class);
$this->app->singleton(MaintenanceIntervalService::class);
$this->app->singleton(MaintenanceDueDateCalculator::class);
```

### 3. Configure Maintenance Rules

Set up default maintenance intervals in configuration:

```php
// In config/maintenance.php
return [
    'intervals' => [
        'oil_change' => [
            'interval_type' => 'mileage',
            'interval_value' => 24140, // 15,000 miles in meters
            'warning_threshold' => 0.9, // 90% of interval
        ],
        // ... other intervals
    ],
];
```

### 4. Verify Service Registration

```bash
php artisan tinker
>>> app(App\Services\Maintenance\MaintenanceCalculationService::class);
>>> app(App\Services\Maintenance\MaintenanceIntervalService::class);
>>> app(App\Services\Maintenance\MaintenanceDueDateCalculator::class);
```

## Usage Examples

### Basic Maintenance Calculation

```php
use App\Services\Maintenance\MaintenanceCalculationService;

$maintenanceService = app(MaintenanceCalculationService::class);

// Calculate maintenance schedules for a truck
$truck = Truck::find(1);
$result = $maintenanceService->calculateMaintenanceSchedules($truck);

// Get overdue maintenance
$overdueSchedules = $maintenanceService->getOverdueMaintenanceSchedules();

// Calculate specific maintenance type
$oilChangeSchedule = $maintenanceService->calculateMaintenanceSchedule(
    $truck, 
    'oil_change'
);
```

### Queue Job Processing

```php
use App\Jobs\Telematics\CalculateMaintenanceSchedulesJob;

// Queue maintenance calculation for specific trucks
CalculateMaintenanceSchedulesJob::dispatch([
    'entity_type' => 'trucks',
    'entity_ids' => [1, 2, 3],
]);

// Queue calculation for all vehicles
CalculateMaintenanceSchedulesJob::dispatch([
    'entity_type' => 'all',
]);
```

### Event Handling

```php
use App\Events\Telematics\MaintenanceCalculatedEvent;
use App\Events\Telematics\MaintenanceOverdueEvent;

// Listen for maintenance calculations
Event::listen(MaintenanceCalculatedEvent::class, function ($event) {
    // Handle maintenance calculation completion
    Log::info('Maintenance calculated', [
        'entity_id' => $event->entityId,
        'schedules_updated' => count($event->schedules),
    ]);
});

// Listen for overdue maintenance
Event::listen(MaintenanceOverdueEvent::class, function ($event) {
    // Send notifications for overdue maintenance
    NotificationService::sendOverdueMaintenanceAlert($event->schedule);
});
```

## Configuration

### Maintenance Intervals

Configure default maintenance intervals in `config/maintenance.php`:

```php
return [
    'intervals' => [
        'oil_change' => [
            'interval_type' => 'mileage',
            'interval_value' => 24140, // 15,000 miles in meters
            'warning_threshold' => 0.9,
            'critical_threshold' => 1.1,
        ],
        'tire_rotation' => [
            'interval_type' => 'mileage',
            'interval_value' => 12070, // 7,500 miles in meters
            'warning_threshold' => 0.9,
        ],
        'annual_inspection' => [
            'interval_type' => 'time',
            'interval_value' => 365, // days
            'warning_threshold' => 0.9,
        ],
    ],
    'calculation' => [
        'batch_size' => 100,
        'prediction_days' => 90,
        'confidence_threshold' => 0.8,
    ],
];
```

### Vehicle-Specific Rules

Override intervals for specific vehicle types:

```php
// In MaintenanceIntervalService
private const VEHICLE_TYPE_OVERRIDES = [
    'heavy_duty' => [
        'oil_change' => ['interval_value' => 19312], // 12,000 miles
    ],
    'light_duty' => [
        'oil_change' => ['interval_value' => 32187], // 20,000 miles
    ],
];
```

## Testing

### Service Testing

```php
// Test maintenance calculation
$maintenanceService = app(MaintenanceCalculationService::class);
$truck = Truck::factory()->create();
$result = $maintenanceService->calculateMaintenanceSchedules($truck);

// Test interval service
$intervalService = app(MaintenanceIntervalService::class);
$intervals = $intervalService->getMaintenanceIntervals($truck);

// Test due date calculator
$calculator = app(MaintenanceDueDateCalculator::class);
$dueDate = $calculator->calculateDueDate($truck, 'oil_change');
```

### Job Testing

```php
// Test maintenance calculation job
$job = new CalculateMaintenanceSchedulesJob([
    'entity_type' => 'trucks',
    'entity_ids' => [1],
]);

$job->handle();
```

## Error Handling

### Calculation Errors
- Missing telematic data validation
- Invalid maintenance rule handling
- Calculation overflow protection
- Data consistency checks

### Job Errors
- Entity existence validation
- Batch processing error recovery
- Progress tracking for failures
- Retry logic for transient errors

## Performance Considerations

### Bulk Processing
- Process vehicles in configurable batches
- Use database transactions for consistency
- Implement memory-efficient processing
- Monitor calculation performance

### Caching Strategy
- Cache maintenance intervals and rules
- Cache vehicle-specific configurations
- Implement result caching for frequent calculations
- Use Redis for distributed caching

## Dependencies

### Required Services
- TelematicReadingService (for latest readings)
- MaintenanceSchedule model
- MaintenanceRecord model
- Event system for notifications

### External Dependencies
- Laravel Queue system
- Laravel Events
- Carbon for date calculations
- Spatie Laravel Data for DTOs

## Next Steps

After implementing the maintenance calculation service:

1. **Integration Testing**: Test with real telematic data
2. **Performance Optimization**: Monitor and optimize calculation performance
3. **Alert System**: Implement maintenance alert notifications
4. **Dashboard Integration**: Add maintenance status to dashboards

## Troubleshooting

### Common Issues
1. **Missing Telematic Data**: Ensure telematic sync is working properly
2. **Incorrect Calculations**: Verify maintenance interval configurations
3. **Performance Issues**: Monitor batch sizes and query performance
4. **Event Processing**: Check event listener registration and processing
