# TMS-215: Enhanced Command Interface for Telematic Data Sync

## Overview

This implementation extends the existing telematic sync commands to support comprehensive data synchronization options, including incremental sync, data type filtering, vehicle targeting, progress reporting, and maintenance calculation triggers. It provides a user-friendly command interface with robust validation and testing capabilities.

## Story Details

- **Epic**: TMS-207 - Telematic Data Synchronization Architecture Implementation
- **Story**: TMS-215 - Enhanced Command Interface for Telematic Data Sync
- **Story Points**: 8
- **Phase**: 2 (Enhancement - Weeks 3-4)

## Implementation Files

### Enhanced Commands

```
app/Console/Commands/
├── InitialTelematicSyncCommand.php (enhanced)
├── TelematicDataSyncCommand.php (new)
├── CalculateMaintenanceCommand.php (new)
└── IncrementalTelematicSyncCommand.php (from TMS-214)
```

### Supporting Services

```
app/Services/Telematics/Commands/
├── CommandOptionValidator.php (new)
├── ProgressReporter.php (new)
├── DryRunService.php (new)
└── CommandHelpProvider.php (new)
```

### Configuration

```
config/
└── telematics.php (enhanced with command options)
```

## Architecture Overview

### Enhanced Command Structure

```php
// Enhanced InitialTelematicSyncCommand
class InitialTelematicSyncCommand extends Command
{
    protected $signature = 'sync:telematics:initial
                           {--types=* : Data types to sync (odometer,engine_hours,diagnostics)}
                           {--vehicle-ids=* : Specific vehicle IDs to sync}
                           {--calculate-maintenance : Trigger maintenance calculations after sync}
                           {--dry-run : Preview operations without making changes}
                           {--cron : Run in cron mode with minimal output}';
}

// New comprehensive data sync command
class TelematicDataSyncCommand extends Command
{
    protected $signature = 'sync:telematics:data
                           {--sync-mode=initial : Sync mode (initial,incremental,historical)}
                           {--data-types=* : Data types to sync}
                           {--vehicle-ids=* : Specific vehicle IDs}
                           {--date-range=* : Date range for historical sync (start,end)}
                           {--calculate-maintenance : Trigger maintenance calculations}
                           {--dry-run : Preview operations without changes}
                           {--cron : Run in cron mode}';
}

// Dedicated maintenance calculation command
class CalculateMaintenanceCommand extends Command
{
    protected $signature = 'maintenance:calculate
                           {--entity-type=all : Entity type (trucks,trailers,all)}
                           {--entity-ids=* : Specific entity IDs}
                           {--dry-run : Preview calculations without changes}';
}
```

### Command Option Validation

```php
// Comprehensive option validation service
class CommandOptionValidator
{
    private const SUPPORTED_DATA_TYPES = [
        'odometer', 'engine_hours', 'diagnostics', 'fuel_level'
    ];
    
    private const SUPPORTED_SYNC_MODES = [
        'initial', 'incremental', 'historical'
    ];
    
    public function validateDataTypes(array $types): ValidationResult
    {
        $invalid = array_diff($types, self::SUPPORTED_DATA_TYPES);
        
        if (!empty($invalid)) {
            return ValidationResult::failed(
                "Invalid data types: " . implode(', ', $invalid),
                "Supported types: " . implode(', ', self::SUPPORTED_DATA_TYPES)
            );
        }
        
        return ValidationResult::passed();
    }
    
    public function validateVehicleIds(array $vehicleIds): ValidationResult
    {
        $existing = Truck::whereIn('id', $vehicleIds)->pluck('id')->toArray();
        $missing = array_diff($vehicleIds, $existing);
        
        if (!empty($missing)) {
            return ValidationResult::failed(
                "Vehicle IDs not found: " . implode(', ', $missing)
            );
        }
        
        return ValidationResult::passed();
    }
}
```

### Progress Reporting System

```php
// Real-time progress reporting for long operations
class ProgressReporter
{
    public function createProgressBar(int $total, string $label = 'Processing'): ProgressBar
    {
        $bar = $this->command->getOutput()->createProgressBar($total);
        $bar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %elapsed:6s%/%estimated:-6s% %memory:6s% - %message%');
        $bar->setMessage($label);
        
        return $bar;
    }
    
    public function reportSyncProgress(SyncProgress $progress): void
    {
        $this->command->line(sprintf(
            "Processed: %d/%d | Success: %d | Errors: %d | Rate: %.1f/sec",
            $progress->processed,
            $progress->total,
            $progress->successful,
            $progress->errors,
            $progress->getProcessingRate()
        ));
    }
    
    public function displaySummaryTable(SyncResult $result): void
    {
        $this->command->table(
            ['Metric', 'Value'],
            [
                ['Total Records', number_format($result->totalRecords)],
                ['Processed', number_format($result->processedRecords)],
                ['Successful', number_format($result->successfulRecords)],
                ['Failed', number_format($result->failedRecords)],
                ['Success Rate', $result->getSuccessRate() . '%'],
                ['Duration', $result->getDurationFormatted()],
                ['Average Rate', $result->getAverageRate() . ' records/sec'],
            ]
        );
    }
}
```

### Dry-Run Service

```php
// Safe operation preview without data changes
class DryRunService
{
    public function previewSync(SyncOptions $options): DryRunResult
    {
        // Validate configuration without making changes
        $configValidation = $this->validateConfiguration($options);
        
        // Test API connectivity
        $apiConnectivity = $this->testApiConnectivity($options->provider);
        
        // Estimate operation scope
        $operationScope = $this->estimateOperationScope($options);
        
        // Validate data without processing
        $dataValidation = $this->validateDataSources($options);
        
        return new DryRunResult(
            $configValidation,
            $apiConnectivity,
            $operationScope,
            $dataValidation
        );
    }
    
    public function previewMaintenanceCalculation(MaintenanceOptions $options): DryRunResult
    {
        // Identify entities for calculation
        $entities = $this->identifyTargetEntities($options);
        
        // Validate maintenance rules
        $rulesValidation = $this->validateMaintenanceRules($entities);
        
        // Estimate calculation scope
        $calculationScope = $this->estimateCalculationScope($entities);
        
        return new DryRunResult(
            entities: $entities,
            rulesValidation: $rulesValidation,
            scope: $calculationScope
        );
    }
}
```

## Key Design Principles

- **User-Friendly**: Intuitive command interface with comprehensive help
- **Robust Validation**: Comprehensive option validation with helpful error messages
- **Progress Transparency**: Real-time progress reporting for long operations
- **Safe Testing**: Dry-run mode for safe operation preview
- **Backward Compatibility**: Existing functionality preserved
- **Comprehensive Options**: Support for all sync scenarios and filtering

## Enhanced Features

### 1. Enhanced InitialTelematicSyncCommand

**Purpose**: Extend existing command with data sync capabilities

**New Options**:
- `--types`: Filter data types (odometer, engine_hours, diagnostics)
- `--vehicle-ids`: Target specific vehicles
- `--calculate-maintenance`: Trigger maintenance calculations
- `--dry-run`: Preview operations without changes

### 2. TelematicDataSyncCommand

**Purpose**: Comprehensive command for all data sync operations

**Key Options**:
- `--sync-mode`: Choose sync mode (initial, incremental, historical)
- `--data-types`: Filter specific data types
- `--date-range`: Specify date range for historical sync
- `--vehicle-ids`: Target specific vehicles
- Progress reporting and detailed statistics

### 3. CalculateMaintenanceCommand

**Purpose**: Dedicated maintenance calculation command

**Key Options**:
- `--entity-type`: Filter by entity type (trucks, trailers, all)
- `--entity-ids`: Target specific entities
- `--dry-run`: Preview calculations
- Progress reporting for large fleets

### 4. Command Option Validation

**Purpose**: Comprehensive validation with helpful error messages

**Features**:
- Data type validation with supported options list
- Vehicle ID validation against existing records
- Date range validation for logical ranges
- Option combination validation
- User-friendly error messages with suggestions

### 5. Progress Reporting

**Purpose**: Real-time progress tracking for long operations

**Features**:
- Progress bars with completion percentage
- Real-time status updates
- Performance metrics (records/second)
- Estimated time remaining
- Detailed summary reports

### 6. Dry-Run Mode

**Purpose**: Safe operation preview and testing

**Features**:
- Accurate operation preview without changes
- Configuration and API connectivity testing
- Data validation without processing
- Detailed dry-run reports
- Compatibility checks

## Installation Instructions

### 1. Install Enhanced Commands

```bash
# Copy enhanced initial sync command
cp TMS-270_InitialTelematicSyncCommand.php app/Console/Commands/InitialTelematicSyncCommand.php

# Copy new data sync command
cp TMS-271_TelematicDataSyncCommand.php app/Console/Commands/TelematicDataSyncCommand.php

# Copy maintenance calculation command
cp TMS-274_CalculateMaintenanceCommand.php app/Console/Commands/CalculateMaintenanceCommand.php
```

### 2. Install Supporting Services

```bash
# Copy validation service
cp TMS-272_CommandOptionValidator.php app/Services/Telematics/Commands/CommandOptionValidator.php

# Copy progress reporter
cp TMS-273_ProgressReporter.php app/Services/Telematics/Commands/ProgressReporter.php

# Copy dry-run service
cp TMS-275_DryRunService.php app/Services/Telematics/Commands/DryRunService.php

# Copy help provider
cp TMS-276_CommandHelpProvider.php app/Services/Telematics/Commands/CommandHelpProvider.php
```

### 3. Update Configuration

```bash
# Copy enhanced configuration
cp TMS-215_telematics.php config/telematics.php
```

### 4. Register Services

Update `TelematicSyncProvider` to register new command services.

## Usage Examples

### Enhanced Initial Sync

```bash
# Basic initial sync (existing functionality)
php artisan sync:telematics:initial

# Sync specific data types
php artisan sync:telematics:initial --types=odometer,engine_hours

# Sync specific vehicles
php artisan sync:telematics:initial --vehicle-ids=123,456,789

# Sync with maintenance calculation
php artisan sync:telematics:initial --calculate-maintenance

# Dry-run preview
php artisan sync:telematics:initial --dry-run --types=odometer

# Cron mode (existing functionality preserved)
php artisan sync:telematics:initial --cron
```

### Comprehensive Data Sync

```bash
# Initial data sync
php artisan sync:telematics:data --sync-mode=initial --data-types=odometer,engine_hours

# Incremental sync
php artisan sync:telematics:data --sync-mode=incremental

# Historical sync with date range
php artisan sync:telematics:data --sync-mode=historical --date-range=2024-01-01,2024-01-31

# Targeted vehicle sync
php artisan sync:telematics:data --vehicle-ids=123,456 --data-types=odometer

# Full sync with maintenance calculation
php artisan sync:telematics:data --calculate-maintenance --data-types=all
```

### Maintenance Calculations

```bash
# Calculate maintenance for all entities
php artisan maintenance:calculate

# Calculate for specific entity type
php artisan maintenance:calculate --entity-type=trucks

# Calculate for specific entities
php artisan maintenance:calculate --entity-ids=123,456,789

# Dry-run preview
php artisan maintenance:calculate --dry-run --entity-type=trucks
```

### Help and Documentation

```bash
# Get command help
php artisan sync:telematics:initial --help
php artisan sync:telematics:data --help
php artisan maintenance:calculate --help

# List all telematic commands
php artisan list sync:telematics
```

## Configuration

### Command Options Configuration

```php
// config/telematics.php
'commands' => [
    'supported_data_types' => [
        'odometer' => 'Vehicle odometer readings',
        'engine_hours' => 'Engine operating hours',
        'diagnostics' => 'Diagnostic trouble codes',
        'fuel_level' => 'Fuel level percentages',
    ],
    
    'supported_sync_modes' => [
        'initial' => 'Full initial synchronization',
        'incremental' => 'Incremental updates only',
        'historical' => 'Historical data backfill',
    ],
    
    'validation' => [
        'max_vehicle_ids' => 1000,
        'max_date_range_days' => 365,
        'require_existing_vehicles' => true,
    ],
    
    'progress_reporting' => [
        'enabled' => true,
        'update_frequency' => 100, // records
        'show_memory_usage' => true,
        'show_performance_metrics' => true,
    ],
],
```

### Dry-Run Configuration

```php
'dry_run' => [
    'enabled' => true,
    'test_api_connectivity' => true,
    'validate_data_sources' => true,
    'estimate_operation_scope' => true,
    'max_preview_records' => 100,
],
```

## Error Handling and Validation

### Validation Error Examples

```bash
# Invalid data type
$ php artisan sync:telematics:data --data-types=invalid_type
Error: Invalid data types: invalid_type
Supported types: odometer, engine_hours, diagnostics, fuel_level

# Non-existent vehicle IDs
$ php artisan sync:telematics:data --vehicle-ids=999999
Error: Vehicle IDs not found: 999999
Use --dry-run to validate vehicle IDs before sync

# Invalid date range
$ php artisan sync:telematics:data --sync-mode=historical --date-range=2024-12-31,2024-01-01
Error: Invalid date range: end date must be after start date
Format: YYYY-MM-DD,YYYY-MM-DD (start,end)
```

### Progress Reporting Examples

```bash
# Real-time progress display
Syncing vehicle data...
 150/1000 [████░░░░░░░░░░░░░░░░] 15% 00:30/03:20 32MB - Processing vehicle stats

Processed: 150/1000 | Success: 148 | Errors: 2 | Rate: 5.2/sec

# Summary table
┌─────────────────┬─────────────┐
│ Metric          │ Value       │
├─────────────────┼─────────────┤
│ Total Records   │ 1,000       │
│ Processed       │ 1,000       │
│ Successful      │ 985         │
│ Failed          │ 15          │
│ Success Rate    │ 98.5%       │
│ Duration        │ 3m 45s      │
│ Average Rate    │ 4.4 rec/sec │
└─────────────────┴─────────────┘
```

## Dependencies

### Required Services
- Enhanced TelematicSyncService with data sync capabilities (TMS-209)
- TelematicDataPipeline for processing (TMS-213)
- SyncIncrementalDataJob for incremental operations (TMS-214)
- MaintenanceCalculationService for maintenance triggers

### External Dependencies
- Laravel Console component for command infrastructure
- Symfony Console for progress bars and formatting
- Existing telematic provider integrations

## Next Steps

After implementing enhanced command interface:

1. **TMS-216**: Advanced monitoring and alerting dashboard
2. **TMS-217**: API endpoints for web-based sync management
3. **TMS-218**: Automated sync scheduling and optimization

## Troubleshooting

### Common Issues
1. **Option Validation Failures**: Check supported options with --help
2. **Progress Reporting Issues**: Verify output formatting and terminal support
3. **Dry-Run Inaccuracies**: Check API connectivity and data source validation
4. **Performance Issues**: Monitor memory usage and adjust batch sizes
5. **Maintenance Calculation Errors**: Verify entity existence and maintenance rules

### Debug Commands

```bash
# Test command validation
php artisan sync:telematics:data --dry-run --data-types=all --verbose

# Check supported options
php artisan sync:telematics:data --help

# Validate specific vehicles
php artisan sync:telematics:data --dry-run --vehicle-ids=123,456

# Test maintenance calculations
php artisan maintenance:calculate --dry-run --entity-type=trucks --verbose
```
