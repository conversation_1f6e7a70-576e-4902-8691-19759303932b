# TMS-217: Real-time Maintenance Alerts and Event System

## Overview

This implementation provides an event-driven system for real-time maintenance alerts that triggers when telematic data indicates maintenance is due or overdue based on mileage, engine hours, or time intervals. It builds upon the maintenance calculation service to provide intelligent, real-time notifications and alert management.

## Story Details

- **Epic**: TMS-207 - Telematic Data Synchronization Architecture Implementation
- **Story**: TMS-217 - Real-time Maintenance Alerts and Event System
- **Story Points**: 13
- **Phase**: 3 (Maintenance Integration - Weeks 5-6)

## Implementation Files

### Core Alert System

- [MaintenanceAlertService.md](./MaintenanceAlertService.md) - Core alert management and processing
- [AlertPriorityManager.md](./AlertPriorityManager.md) - Alert priority and classification system
- [AlertDeduplicationService.md](./AlertDeduplicationService.md) - Alert deduplication and spam prevention

### Event System

- [MaintenanceDueEvent.md](./MaintenanceDueEvent.md) - Event for maintenance due conditions
- [MaintenanceOverdueEvent.md](./MaintenanceOverdueEvent.md) - Event for overdue maintenance
- [MaintenanceCriticalEvent.md](./MaintenanceCriticalEvent.md) - Event for critical maintenance alerts
- [MaintenanceAlertEventListeners.md](./MaintenanceAlertEventListeners.md) - Event listeners for alert processing

### Notification System

- [NotificationChannelManager.md](./NotificationChannelManager.md) - Multi-channel notification management
- [EmailNotificationChannel.md](./EmailNotificationChannel.md) - Email notification implementation
- [SlackNotificationChannel.md](./SlackNotificationChannel.md) - Slack notification integration
- [WebSocketNotificationChannel.md](./WebSocketNotificationChannel.md) - Real-time web notifications

### Queue Jobs

- [ProcessMaintenanceAlertJob.md](./ProcessMaintenanceAlertJob.md) - Alert processing job
- [SendMaintenanceNotificationJob.md](./SendMaintenanceNotificationJob.md) - Notification delivery job
- [AlertDigestJob.md](./AlertDigestJob.md) - Daily/weekly alert digest processing

### Data Structures

- [MaintenanceAlert.md](./MaintenanceAlert.md) - Alert model and relationships
- [AlertNotification.md](./AlertNotification.md) - Notification tracking model
- [AlertConfiguration.md](./AlertConfiguration.md) - Alert configuration DTOs

### Frontend Integration

- [AlertApiController.md](./AlertApiController.md) - API endpoints for alert management
- [AlertWebSocketController.md](./AlertWebSocketController.md) - WebSocket real-time updates
- [AlertDashboardComponents.md](./AlertDashboardComponents.md) - Frontend dashboard integration

## Architecture Overview

### Core Components

1. **MaintenanceAlertService**: Central service for alert management and processing
2. **AlertPriorityManager**: Manages alert priorities and classification
3. **AlertDeduplicationService**: Prevents duplicate alerts and spam
4. **Event System**: Laravel events for maintenance state changes
5. **Notification Channels**: Multi-channel notification delivery
6. **Real-time Updates**: WebSocket integration for live alerts

### Key Design Principles

- **Event-Driven**: Decoupled architecture using Laravel events
- **Real-time**: Immediate alert delivery via WebSocket
- **Multi-channel**: Email, Slack, web, and mobile notifications
- **Configurable**: Flexible alert rules and thresholds
- **Scalable**: Queue-based processing for high volume
- **Deduplication**: Intelligent spam prevention

## Service Descriptions

### 1. MaintenanceAlertService

**Purpose**: Core service for managing maintenance alerts and processing

**Key Features**:
- Alert generation based on maintenance calculations
- Priority classification (critical, warning, info)
- Alert lifecycle management
- Integration with notification channels
- Alert history and tracking

**Alert Types**:
- Due maintenance alerts (approaching threshold)
- Overdue maintenance alerts (past due date)
- Critical maintenance alerts (safety-related)
- Preventive maintenance reminders
- Inspection due notifications

### 2. AlertPriorityManager

**Purpose**: Manages alert priorities and classification logic

**Key Features**:
- Dynamic priority calculation
- Maintenance type-based classification
- Vehicle criticality assessment
- Escalation rules management
- Priority-based routing

**Priority Levels**:
- **Critical**: Safety-related, immediate action required
- **High**: Overdue maintenance, potential breakdown risk
- **Medium**: Due soon, schedule maintenance
- **Low**: Informational, future planning

### 3. AlertDeduplicationService

**Purpose**: Prevents duplicate alerts and manages alert frequency

**Key Features**:
- Duplicate alert detection
- Frequency-based throttling
- Alert consolidation
- Spam prevention
- Smart alert grouping

### 4. Event System

**Purpose**: Event-driven architecture for maintenance alerts

**Events**:
- `MaintenanceDueEvent`: Triggered when maintenance becomes due
- `MaintenanceOverdueEvent`: Triggered when maintenance is overdue
- `MaintenanceCriticalEvent`: Triggered for critical maintenance issues
- `AlertProcessedEvent`: Triggered when alert is processed
- `NotificationSentEvent`: Triggered when notification is delivered

### 5. Notification System

**Purpose**: Multi-channel notification delivery

**Channels**:
- **Email**: Traditional email notifications
- **Slack**: Team collaboration notifications
- **WebSocket**: Real-time web notifications
- **SMS**: Critical alert text messages (future)
- **Mobile Push**: Mobile app notifications (future)

## Installation Instructions

### 1. Create Alert System Files

Create the alert system files using the provided specifications:

```bash
# Create core services
# Copy content from: MaintenanceAlertService.md
# Copy content from: AlertPriorityManager.md
# Copy content from: AlertDeduplicationService.md

# Create event system
# Copy content from: MaintenanceDueEvent.md
# Copy content from: MaintenanceOverdueEvent.md
# Copy content from: MaintenanceCriticalEvent.md
# Copy content from: MaintenanceAlertEventListeners.md

# Create notification system
# Copy content from: NotificationChannelManager.md
# Copy content from: EmailNotificationChannel.md
# Copy content from: SlackNotificationChannel.md
# Copy content from: WebSocketNotificationChannel.md

# Create queue jobs
# Copy content from: ProcessMaintenanceAlertJob.md
# Copy content from: SendMaintenanceNotificationJob.md
# Copy content from: AlertDigestJob.md

# Create models and DTOs
# Copy content from: MaintenanceAlert.md
# Copy content from: AlertNotification.md
# Copy content from: AlertConfiguration.md

# Create API and frontend integration
# Copy content from: AlertApiController.md
# Copy content from: AlertWebSocketController.md
# Copy content from: AlertDashboardComponents.md
```

### 2. Database Migrations

Run the alert system migrations:

```bash
php artisan migrate
```

### 3. Update Service Provider Registration

Register the alert services in the TelematicSyncProvider:

```php
// In app/Providers/TelematicSyncProvider.php
$this->app->singleton(MaintenanceAlertService::class);
$this->app->singleton(AlertPriorityManager::class);
$this->app->singleton(AlertDeduplicationService::class);
$this->app->singleton(NotificationChannelManager::class);
```

### 4. Configure Event Listeners

Register event listeners in EventServiceProvider:

```php
// In app/Providers/EventServiceProvider.php
protected $listen = [
    MaintenanceDueEvent::class => [
        MaintenanceAlertEventListeners::class,
    ],
    MaintenanceOverdueEvent::class => [
        MaintenanceAlertEventListeners::class,
    ],
    MaintenanceCriticalEvent::class => [
        MaintenanceAlertEventListeners::class,
    ],
];
```

### 5. Configure WebSocket Broadcasting

Set up broadcasting for real-time alerts:

```php
// In config/broadcasting.php
'pusher' => [
    'driver' => 'pusher',
    'key' => env('PUSHER_APP_KEY'),
    'secret' => env('PUSHER_APP_SECRET'),
    'app_id' => env('PUSHER_APP_ID'),
    'options' => [
        'cluster' => env('PUSHER_APP_CLUSTER'),
        'useTLS' => true,
    ],
],
```

### 6. Verify Service Registration

```bash
php artisan tinker
>>> app(App\Services\Maintenance\MaintenanceAlertService::class);
>>> app(App\Services\Maintenance\AlertPriorityManager::class);
>>> app(App\Services\Maintenance\AlertDeduplicationService::class);
```

## Usage Examples

### Basic Alert Processing

```php
use App\Services\Maintenance\MaintenanceAlertService;

$alertService = app(MaintenanceAlertService::class);

// Process alerts for a truck
$truck = Truck::find(1);
$alerts = $alertService->processMaintenanceAlerts($truck);

// Get active alerts
$activeAlerts = $alertService->getActiveAlerts();

// Mark alert as acknowledged
$alertService->acknowledgeAlert($alertId, $userId);
```

### Event-Driven Alert Processing

```php
use App\Events\Telematics\MaintenanceDueEvent;
use App\Events\Telematics\MaintenanceOverdueEvent;

// Fire maintenance due event
event(new MaintenanceDueEvent($maintenanceSchedule));

// Fire overdue maintenance event
event(new MaintenanceOverdueEvent($maintenanceSchedule));
```

### Real-time Notifications

```php
use App\Services\Maintenance\NotificationChannelManager;

$notificationManager = app(NotificationChannelManager::class);

// Send multi-channel notification
$notificationManager->sendAlert($alert, [
    'email' => ['<EMAIL>'],
    'slack' => ['#maintenance-alerts'],
    'websocket' => ['maintenance-dashboard'],
]);
```

### API Integration

```php
// Get alerts via API
GET /api/maintenance/alerts
GET /api/maintenance/alerts/{id}
POST /api/maintenance/alerts/{id}/acknowledge
GET /api/maintenance/alerts/dashboard

// WebSocket subscription
Echo.channel('maintenance-alerts')
    .listen('MaintenanceAlertBroadcast', (e) => {
        // Handle real-time alert
        updateAlertDashboard(e.alert);
    });
```

## Configuration

### Alert Configuration

Configure alert rules in `config/maintenance-alerts.php`:

```php
return [
    'priorities' => [
        'critical' => [
            'threshold_days' => 0, // Overdue
            'notification_channels' => ['email', 'slack', 'websocket', 'sms'],
            'escalation_hours' => 2,
        ],
        'high' => [
            'threshold_days' => 7, // Due within 7 days
            'notification_channels' => ['email', 'slack', 'websocket'],
            'escalation_hours' => 24,
        ],
        'medium' => [
            'threshold_days' => 30, // Due within 30 days
            'notification_channels' => ['email', 'websocket'],
            'escalation_hours' => 72,
        ],
    ],
    'deduplication' => [
        'window_hours' => 24,
        'max_alerts_per_vehicle' => 5,
        'consolidation_enabled' => true,
    ],
    'digest' => [
        'daily_enabled' => true,
        'weekly_enabled' => true,
        'send_time' => '08:00',
    ],
];
```

### Notification Channels

Configure notification channels:

```php
// Email configuration
'email' => [
    'templates' => [
        'critical' => 'emails.maintenance.critical-alert',
        'high' => 'emails.maintenance.high-alert',
        'medium' => 'emails.maintenance.medium-alert',
    ],
],

// Slack configuration
'slack' => [
    'webhook_url' => env('SLACK_MAINTENANCE_WEBHOOK'),
    'channels' => [
        'critical' => '#maintenance-critical',
        'high' => '#maintenance-alerts',
        'medium' => '#maintenance-general',
    ],
],
```

## Testing

### Alert Service Testing

```php
// Test alert generation
$alertService = app(MaintenanceAlertService::class);
$truck = Truck::factory()->create();
$alerts = $alertService->processMaintenanceAlerts($truck);

// Test priority classification
$priorityManager = app(AlertPriorityManager::class);
$priority = $priorityManager->calculatePriority($maintenanceSchedule);

// Test deduplication
$deduplicationService = app(AlertDeduplicationService::class);
$isDuplicate = $deduplicationService->isDuplicateAlert($alert);
```

### Event Testing

```php
// Test event firing
Event::fake();
$alertService->processMaintenanceAlerts($truck);
Event::assertDispatched(MaintenanceDueEvent::class);

// Test notification delivery
Notification::fake();
event(new MaintenanceOverdueEvent($schedule));
Notification::assertSentTo($user, MaintenanceOverdueNotification::class);
```

### Real-time Testing

```php
// Test WebSocket broadcasting
Event::fake();
$alertService->createAlert($alertData);
Event::assertDispatched(MaintenanceAlertBroadcast::class);
```

## Error Handling

### Alert Processing Errors
- Invalid maintenance data validation
- Alert generation failures
- Notification delivery errors
- WebSocket connection issues

### Performance Considerations
- Queue-based alert processing
- Batch notification delivery
- Alert deduplication optimization
- Real-time update throttling

## Dependencies

### Required Services
- MaintenanceCalculationService (for alert triggers)
- Laravel Queue system
- Laravel Events and Broadcasting
- WebSocket server (Pusher/Laravel Echo)

### External Dependencies
- Pusher (for WebSocket broadcasting)
- Slack API (for Slack notifications)
- Email service provider
- Laravel Horizon (for queue monitoring)

## Next Steps

After implementing the real-time alert system:

1. **Performance Monitoring**: Monitor alert processing performance
2. **Mobile Integration**: Add mobile push notifications
3. **Advanced Analytics**: Implement alert analytics and reporting
4. **Machine Learning**: Add predictive alert intelligence

## Troubleshooting

### Common Issues
1. **Missing Alerts**: Check event listener registration and queue processing
2. **Duplicate Alerts**: Verify deduplication service configuration
3. **WebSocket Issues**: Check broadcasting configuration and Pusher setup
4. **Performance Problems**: Monitor queue processing and alert volume
