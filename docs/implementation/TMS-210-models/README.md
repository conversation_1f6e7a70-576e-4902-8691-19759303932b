# TMS-210: Telematic Data Models and Relationships Implementation

## Overview

This implementation provides comprehensive Eloquent models with proper relationships, attribute casting, and business logic for the telematic data synchronization system. It includes models for telematic readings, maintenance schedules, sync states, and maintenance records, along with enhancements to existing Truck and Trailer models.

## Story Details

- **Epic**: TMS-207 - Telematic Data Synchronization Architecture Implementation
- **Story**: TMS-210 - Telematic Data Models and Relationships Implementation
- **Story Points**: 8
- **Phase**: 1 (Foundation - Weeks 1-2)

## Implementation Files

### Core Models

- [TelematicReading.md](./TelematicReading.md) - Time-series telematic data model
- [MaintenanceSchedule.md](./MaintenanceSchedule.md) - Maintenance scheduling model
- [TelematicSyncState.md](./TelematicSyncState.md) - Sync state tracking model
- [MaintenanceRecord.md](./MaintenanceRecord.md) - Maintenance history model

### Model Factories

- [TelematicReadingFactory.md](./TelematicReadingFactory.md) - Factory for telematic readings
- [MaintenanceScheduleFactory.md](./MaintenanceScheduleFactory.md) - Factory for maintenance schedules

### Model Enhancements (Traits)

- [TelematicDataRelationships.md](./TelematicDataRelationships.md) - Trait for Truck model
- [TrailerTelematicDataRelationships.md](./TrailerTelematicDataRelationships.md) - Trait for Trailer model

## Model Overview

### 1. TelematicReading Model

**Purpose**: Store time-series telematic data from various providers

**Key Features**:
- High-precision timestamp casting
- JSON metadata handling
- Unit conversion methods
- Query scopes for efficient filtering
- Anomaly detection support
- Provider identification

**Relationships**:
- `belongsTo(ExternalEntityMapping::class)`
- Access to entity through mapping

**Key Methods**:
- `convertTo($targetUnit)` - Unit conversion
- `hasAnomaly()` - Anomaly detection
- `isRecent()` - Check if reading is recent
- Various scopes for filtering

### 2. MaintenanceSchedule Model

**Purpose**: Define maintenance intervals and scheduling for entities

**Key Features**:
- Polymorphic relationships (trucks, trailers)
- Multiple interval types (mileage, time, engine hours)
- Real-time status calculations
- Due date predictions
- Priority management

**Relationships**:
- `morphTo()` - Polymorphic entity relationship
- `hasMany(MaintenanceRecord::class)`

**Key Methods**:
- `isOverdue()` - Check if maintenance is overdue
- `isDueSoon()` - Check if due soon
- `getCompletionPercentageAttribute()` - Progress calculation
- Various scopes for filtering

### 3. TelematicSyncState Model

**Purpose**: Track sync cursors and state for incremental synchronization

**Key Features**:
- Provider-specific cursor tracking
- Sync performance metrics
- Status management
- Error tracking
- Health monitoring

**Key Methods**:
- `updateSyncProgress()` - Update sync statistics
- `markAsStarted()` - Mark sync as running
- `markAsCompleted()` - Mark sync as completed
- `markAsFailed()` - Handle sync failures
- `addSyncAttempt()` - Track sync history

### 4. MaintenanceRecord Model

**Purpose**: Track completed maintenance activities

**Key Features**:
- Cost tracking (parts and labor)
- Quality ratings
- File attachments support
- Parts usage tracking
- Completion status management

**Relationships**:
- `belongsTo(MaintenanceSchedule::class)`

**Key Methods**:
- `getTotalCostAttribute()` - Calculate total cost
- `wasPerformedOnTime()` - Check if on time
- `addAttachment()` - Add file attachment
- `addPartUsed()` - Track parts usage

### 5. Enhanced Entity Models

**Truck Model Enhancements**:
- Telematic data relationships
- Latest reading properties
- Maintenance status tracking
- Data quality metrics
- Current vehicle state

**Trailer Model Enhancements**:
- Trailer-specific telematic capabilities
- Temperature monitoring (refrigerated)
- Door status tracking
- Tire pressure monitoring
- Location tracking

## Installation Instructions

### 1. Create Model Files

Create the model files using the provided specifications:

```bash
# Create core models
# Copy content from: TelematicReading.md
# Copy content from: MaintenanceSchedule.md
# Copy content from: TelematicSyncState.md
# Copy content from: MaintenanceRecord.md

# Create factories
# Copy content from: TelematicReadingFactory.md
# Copy content from: MaintenanceScheduleFactory.md

# Create traits
# Copy content from: TelematicDataRelationships.md
# Copy content from: TrailerTelematicDataRelationships.md
```

### 2. Enhance Existing Models

Add the traits to your existing Truck and Trailer models:

```php
// In app/Models/Truck.php
use App\Models\Traits\TelematicDataRelationships;

class Truck extends Model
{
    use TelematicDataRelationships;
    
    // ... existing model code
}

// In app/Models/Trailer.php
use App\Models\Traits\TrailerTelematicDataRelationships;

class Trailer extends Model
{
    use TrailerTelematicDataRelationships;
    
    // ... existing model code
}
```

### 3. Verify Model Registration

```bash
php artisan tinker
>>> App\Models\TelematicReading::count();
>>> App\Models\MaintenanceSchedule::count();
>>> App\Models\TelematicSyncState::count();
>>> App\Models\MaintenanceRecord::count();
```

## Usage Examples

### TelematicReading Model

```php
use App\Models\TelematicReading;

// Create a reading
$reading = TelematicReading::create([
    'external_entity_mapping_id' => 1,
    'reading_type' => 'odometer',
    'value' => 150000,
    'unit' => 'meters',
    'recorded_at' => now(),
    'synced_at' => now(),
]);

// Query scopes
$odometerReadings = TelematicReading::ofType('odometer')
    ->forEntity(1)
    ->latest()
    ->get();

// Unit conversion
$milesValue = $reading->convertTo('miles');

// Check for anomalies
if ($reading->hasAnomaly()) {
    // Handle anomalous reading
}
```

### MaintenanceSchedule Model

```php
use App\Models\MaintenanceSchedule;

// Create a maintenance schedule
$schedule = MaintenanceSchedule::create([
    'entity_type' => 'App\Models\Truck',
    'entity_id' => 1,
    'maintenance_type' => 'oil_change',
    'interval_type' => 'mileage',
    'interval_value' => 24140, // 15,000 miles in meters
    'priority' => 'high',
]);

// Query overdue maintenance
$overdue = MaintenanceSchedule::overdue()->get();

// Check status
if ($schedule->isOverdue()) {
    // Handle overdue maintenance
}

// Get completion percentage
$completion = $schedule->completion_percentage;
```

### Enhanced Truck Model

```php
use App\Models\Truck;

$truck = Truck::find(1);

// Get latest telematic readings
$latestOdometer = $truck->latest_odometer;
$currentMileage = $truck->current_mileage;
$currentFuelLevel = $truck->current_fuel_level;

// Get maintenance status
$maintenanceStatus = $truck->maintenance_status;
$overdueMaintenance = $truck->overdue_maintenance;

// Check telematic data quality
$dataQuality = $truck->telematic_data_quality;

// Get current vehicle state
$currentState = $truck->current_state;
```

### Enhanced Trailer Model

```php
use App\Models\Trailer;

$trailer = Trailer::find(1);

// Get trailer-specific readings
$currentLocation = $trailer->current_location;
$currentTemperature = $trailer->current_temperature;
$tirePressureStatus = $trailer->tire_pressure_status;

// Check if refrigerated
if ($trailer->is_refrigerated) {
    $tempHistory = $trailer->getTemperatureHistory(24);
    $inRange = $trailer->isTemperatureInRange(-18, 2);
}

// Get utilization metrics
$utilization = $trailer->utilization_metrics;
```

## Model Relationships

### Relationship Diagram

```
Truck/Trailer
├── externalMappings (existing)
│   └── telematicReadings (new)
├── maintenanceSchedules (new, polymorphic)
│   └── maintenanceRecords (new)
└── computed properties for latest readings

TelematicReading
├── externalEntityMapping
└── entity (through mapping)

MaintenanceSchedule
├── entity (polymorphic)
└── maintenanceRecords

TelematicSyncState
└── provider/sync_type tracking
```

### Key Relationships

1. **TelematicReading → ExternalEntityMapping**: Links readings to entities
2. **MaintenanceSchedule → Entity**: Polymorphic relationship to trucks/trailers
3. **MaintenanceRecord → MaintenanceSchedule**: Tracks completed maintenance
4. **Truck/Trailer → TelematicReading**: Through external mappings

## Factory Usage

### TelematicReading Factory

```php
use App\Models\TelematicReading;

// Create basic reading
$reading = TelematicReading::factory()->create();

// Create specific reading types
$odometer = TelematicReading::factory()->odometer(150000)->create();
$engineHours = TelematicReading::factory()->engineHours(5000)->create();

// Create reading with anomaly
$anomalous = TelematicReading::factory()->withAnomaly()->create();

// Create recent reading
$recent = TelematicReading::factory()->recent()->create();
```

### MaintenanceSchedule Factory

```php
use App\Models\MaintenanceSchedule;

// Create basic schedule
$schedule = MaintenanceSchedule::factory()->create();

// Create overdue maintenance
$overdue = MaintenanceSchedule::factory()->overdue()->create();

// Create specific maintenance type
$oilChange = MaintenanceSchedule::factory()
    ->ofType('oil_change')
    ->forEntity($truck)
    ->create();

// Create high priority maintenance
$critical = MaintenanceSchedule::factory()
    ->critical()
    ->dueSoon(3)
    ->create();
```

## Query Optimization

### Efficient Queries

```php
// Use scopes for common queries
$recentReadings = TelematicReading::recent()
    ->ofType('odometer')
    ->with('externalEntityMapping')
    ->get();

// Eager load relationships
$trucks = Truck::with([
    'maintenanceSchedules' => function ($query) {
        $query->active()->overdue();
    },
    'externalMappings.telematicReadings' => function ($query) {
        $query->latest()->limit(1);
    }
])->get();

// Use aggregation for performance
$maintenanceStats = MaintenanceSchedule::selectRaw('
    COUNT(*) as total,
    SUM(CASE WHEN next_due_at < NOW() THEN 1 ELSE 0 END) as overdue,
    SUM(CASE WHEN next_due_at BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as due_soon
')->first();
```

## Testing

### Model Testing

```php
// Test model creation
$reading = TelematicReading::factory()->create();
$this->assertDatabaseHas('telematic_readings', ['id' => $reading->id]);

// Test relationships
$truck = Truck::factory()->create();
$schedule = MaintenanceSchedule::factory()->forEntity($truck)->create();
$this->assertEquals($truck->id, $schedule->entity_id);

// Test computed properties
$truck = Truck::factory()->create();
$odometer = TelematicReading::factory()
    ->odometer(150000)
    ->forEntityMapping($truck->externalMappings->first()->id)
    ->create();
$this->assertEquals(93.21, $truck->current_mileage, 0.1); // 150000 meters to miles
```

## Performance Considerations

### Database Optimization
- Use proper indexes for relationship queries
- Implement eager loading for related data
- Consider caching for frequently accessed computed properties

### Memory Management
- Use chunked queries for large datasets
- Implement lazy loading where appropriate
- Monitor memory usage in bulk operations

## Dependencies

### Required Packages
- Laravel Eloquent ORM
- Carbon for date handling
- Laravel Factories for testing

### Model Dependencies
- ExternalEntityMapping (existing)
- Truck and Trailer models (existing)

## Next Steps

After implementing the models:

1. **TMS-211**: Implement queue job structure
2. **TMS-212**: Enhance Samsara strategy with stats endpoints
3. **TMS-213**: Implement telematic data storage and processing

## Troubleshooting

### Common Issues
1. **Relationship Loading**: Use eager loading to avoid N+1 queries
2. **Attribute Casting**: Ensure proper casting for JSON and decimal fields
3. **Factory Dependencies**: Check factory relationships and dependencies
4. **Polymorphic Issues**: Verify entity type strings match model classes
