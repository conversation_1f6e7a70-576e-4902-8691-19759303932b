# TMS-214: Incremental Sync Implementation with Feed Endpoints

## Overview

This implementation provides cursor-based incremental synchronization using Samsar<PERSON>'s feed endpoints to efficiently sync only new or updated telematic data since the last sync. It includes high-frequency scheduling, robust error recovery, and comprehensive monitoring for optimal data freshness.

## Story Details

- **Epic**: TMS-207 - Telematic Data Synchronization Architecture Implementation
- **Story**: TMS-214 - Incremental Sync Implementation with Feed Endpoints
- **Story Points**: 13
- **Phase**: 2 (Enhancement - Weeks 3-4)

## Implementation Files

### Core Services

```
app/Services/Telematics/Sync/
├── CursorStateManager.php (new)
├── DataFreshnessTracker.php (new)
└── IncrementalSyncOptimizer.php (new)
```

### Enhanced Jobs

```
app/Jobs/Telematics/
└── SyncIncrementalDataJob.php (enhanced)
```

### Commands

```
app/Console/Commands/
└── IncrementalTelematicSyncCommand.php (new)
```

### Scheduling

```
app/Console/
└── Kernel.php (updated with incremental sync scheduling)
```

### Configuration

```
config/
└── telematics.php (enhanced with incremental sync settings)
```

## Architecture Overview

### Cursor-Based Incremental Sync Flow

```php
// High-level incremental sync process
class IncrementalSyncFlow
{
    public function execute(): IncrementalSyncResult
    {
        // 1. Validate cursor state
        $cursor = $this->cursorManager->getValidCursor('stats');
        
        // 2. Fetch incremental data using feed endpoint
        $feedData = $this->samsaraClient->getVehicleStatsFeed([
            'after' => $cursor,
            'types' => 'obdOdometerMeters,engineStates'
        ]);
        
        // 3. Process data through pipeline
        $result = $this->dataPipeline->process($feedData->data);
        
        // 4. Update cursor state
        $this->cursorManager->updateCursor('stats', $feedData->pagination->endCursor);
        
        // 5. Track data freshness
        $this->freshnessTracker->updateFreshness('stats', now());
        
        return $result;
    }
}
```

### Cursor State Management

```php
// Robust cursor state management
class CursorStateManager
{
    public function getValidCursor(string $syncType): ?string
    {
        $state = TelematicSyncState::forProvider('samsara')
            ->forSyncType($syncType)
            ->first();
            
        if (!$state || $this->isCursorExpired($state)) {
            return $this->initializeCursor($syncType);
        }
        
        return $this->validateCursor($state->last_cursor);
    }
    
    public function handleCursorFailure(string $syncType, string $error): void
    {
        // Log cursor failure
        Log::warning('Cursor validation failed', [
            'sync_type' => $syncType,
            'error' => $error
        ]);
        
        // Reset cursor and trigger full sync fallback
        $this->resetCursor($syncType);
        $this->triggerFullSyncFallback($syncType);
    }
}
```

### High-Frequency Scheduling

```php
// Laravel scheduler configuration for high-frequency sync
class Kernel extends ConsoleKernel
{
    protected function schedule(Schedule $schedule): void
    {
        // High-frequency incremental sync every 30 minutes
        $schedule->command('sync:telematics:incremental --cron')
            ->everyThirtyMinutes()
            ->withoutOverlapping(25) // Prevent overlap with 5-minute buffer
            ->runInBackground()
            ->onFailure(function () {
                // Alert on sync failures
                $this->notifyOpsTeam('Incremental sync failed');
            });
            
        // Data freshness monitoring every 15 minutes
        $schedule->command('telematics:check-freshness')
            ->everyFifteenMinutes()
            ->runInBackground();
    }
}
```

## Key Design Principles

- **High Frequency**: Optimized for 30-minute sync intervals
- **Data Freshness**: Comprehensive tracking and alerting
- **Fault Tolerance**: Robust error recovery and fallback mechanisms
- **Performance**: Optimized for minimal resource usage
- **Monitoring**: Real-time tracking of sync performance
- **Idempotency**: Safe for re-execution and overlap prevention

## Enhanced Features

### 1. Cursor State Management

**Purpose**: Manage pagination cursors for incremental sync operations

**Features**:
- Cursor validation and integrity checks
- Automatic cursor recovery for invalid states
- Cursor expiration and refresh logic
- Fallback to full sync when cursor fails
- Comprehensive cursor state monitoring

### 2. Enhanced SyncIncrementalDataJob

**Purpose**: Process incremental data from feed endpoints

**Features**:
- Feed endpoint integration with cursor pagination
- Support for multiple stat types in single operation
- Idempotent operations for data consistency
- Optimized for high-frequency execution
- Comprehensive error handling and retry logic

### 3. IncrementalTelematicSyncCommand

**Purpose**: Console command for scheduled incremental sync

**Features**:
- Support for specific stat types targeting
- Cron-friendly output for monitoring
- Dry-run mode for testing
- Cursor state validation before sync
- Integration with job queue system

### 4. High-Frequency Scheduling

**Purpose**: Automated scheduling for 30-minute sync intervals

**Features**:
- Laravel scheduler integration
- Overlap prevention mechanisms
- Performance monitoring and metrics
- Automated health checks
- Sync failure alerting

### 5. Feed Endpoint Optimization

**Purpose**: Efficient handling of Samsara feed endpoints

**Features**:
- Robust pagination handling
- Cursor validation and error recovery
- Automatic fallback to full sync
- Exponential backoff for API errors
- Pagination state recovery

### 6. Data Freshness Tracking

**Purpose**: Monitor data freshness and detect staleness

**Features**:
- Freshness metrics for different data types
- Configurable staleness thresholds
- Automated alerts for stale data
- Data gap detection and reporting
- Real-time freshness dashboard

## Installation Instructions

### 1. Install Core Services

```bash
# Copy cursor state manager
cp TMS-263_CursorStateManager.php app/Services/Telematics/Sync/CursorStateManager.php

# Copy data freshness tracker
cp TMS-269_DataFreshnessTracker.php app/Services/Telematics/Sync/DataFreshnessTracker.php

# Copy performance optimizer
cp TMS-268_IncrementalSyncOptimizer.php app/Services/Telematics/Sync/IncrementalSyncOptimizer.php
```

### 2. Install Enhanced Job

```bash
# Copy enhanced incremental sync job
cp TMS-264_SyncIncrementalDataJob.php app/Jobs/Telematics/SyncIncrementalDataJob.php
```

### 3. Install Command

```bash
# Copy incremental sync command
cp TMS-265_IncrementalTelematicSyncCommand.php app/Console/Commands/IncrementalTelematicSyncCommand.php
```

### 4. Update Scheduling

```bash
# Update console kernel with scheduling
cp TMS-266_Kernel.php app/Console/Kernel.php
```

### 5. Update Configuration

```bash
# Copy enhanced configuration
cp TMS-214_telematics.php config/telematics.php
```

## Usage Examples

### Manual Incremental Sync

```php
use App\Jobs\Telematics\SyncIncrementalDataJob;

// Dispatch incremental sync job
SyncIncrementalDataJob::dispatch([
    'stat_types' => ['odometer', 'engine_hours'],
    'provider' => 'samsara'
]);

// Sync specific stat types
SyncIncrementalDataJob::dispatch([
    'stat_types' => ['odometer'],
    'force_cursor_reset' => false
]);
```

### Command Line Usage

```bash
# Run incremental sync for all stat types
php artisan sync:telematics:incremental

# Sync specific stat types
php artisan sync:telematics:incremental --types=odometer,engine_hours

# Run in cron mode (minimal output)
php artisan sync:telematics:incremental --cron

# Dry run mode for testing
php artisan sync:telematics:incremental --dry-run

# Force cursor reset and full sync
php artisan sync:telematics:incremental --reset-cursor
```

### Cursor State Management

```php
use App\Services\Telematics\Sync\CursorStateManager;

$cursorManager = app(CursorStateManager::class);

// Get valid cursor for sync type
$cursor = $cursorManager->getValidCursor('stats');

// Update cursor after successful sync
$cursorManager->updateCursor('stats', $newCursor, [
    'records_processed' => 150,
    'sync_duration' => 45.2
]);

// Handle cursor failure
$cursorManager->handleCursorFailure('stats', 'Invalid cursor format');

// Reset cursor for fresh start
$cursorManager->resetCursor('stats');
```

### Data Freshness Monitoring

```php
use App\Services\Telematics\Sync\DataFreshnessTracker;

$freshnessTracker = app(DataFreshnessTracker::class);

// Check data freshness
$freshness = $freshnessTracker->getFreshness('odometer');

// Update freshness after sync
$freshnessTracker->updateFreshness('odometer', now());

// Check for stale data
$staleData = $freshnessTracker->getStaleDataTypes();

// Get freshness metrics
$metrics = $freshnessTracker->getFreshnessMetrics();
```

## Configuration

### Incremental Sync Configuration

```php
// config/telematics.php
'incremental_sync' => [
    'enabled' => true,
    'frequency_minutes' => 30,
    'stat_types' => ['odometer', 'engine_hours', 'fuel_level'],
    'cursor_expiry_hours' => 24,
    'max_retries' => 3,
    'fallback_to_full_sync' => true,
],
```

### Cursor Management Configuration

```php
'cursor_management' => [
    'validation_enabled' => true,
    'auto_recovery' => true,
    'expiry_check_enabled' => true,
    'fallback_strategy' => 'full_sync', // full_sync, skip, alert
],
```

### Data Freshness Configuration

```php
'data_freshness' => [
    'tracking_enabled' => true,
    'staleness_thresholds' => [
        'odometer' => 60, // minutes
        'engine_hours' => 60,
        'fuel_level' => 120,
    ],
    'alert_on_staleness' => true,
    'gap_detection_enabled' => true,
],
```

### Performance Optimization Configuration

```php
'performance' => [
    'connection_pooling' => true,
    'cache_enabled' => true,
    'cache_ttl_minutes' => 15,
    'memory_limit' => '256M',
    'timeout_seconds' => 300,
],
```

## Monitoring and Metrics

### Sync Performance Metrics

Monitor the following metrics:
- Incremental sync frequency and duration
- Cursor validation success/failure rates
- Data processing throughput
- API response times and error rates
- Memory usage during sync operations

### Data Freshness Metrics

Track data freshness indicators:
- Time since last successful sync per data type
- Data staleness alerts and resolution times
- Data gap detection and backfill operations
- Freshness trend analysis over time

### Error and Recovery Metrics

Monitor error handling:
- Cursor failure rates and recovery success
- Fallback to full sync frequency
- API error categorization and retry success
- Sync overlap detection and prevention

## Performance Considerations

### High-Frequency Optimization
- Optimized database queries for incremental operations
- Efficient data processing pipelines
- Intelligent caching for frequently accessed data
- Connection pooling for API calls

### Memory Management
- Streaming processing for large datasets
- Configurable chunk sizes for memory efficiency
- Memory usage monitoring and alerts
- Garbage collection optimization

### API Efficiency
- Minimal API calls through feed endpoints
- Efficient cursor-based pagination
- Rate limit compliance and optimization
- Connection reuse and pooling

## Dependencies

### Required Services
- Enhanced TelematicSyncState model (TMS-210)
- Enhanced SamsaraStrategy with feed endpoints (TMS-212)
- TelematicDataPipeline for processing (TMS-213)
- Laravel Horizon for job management (TMS-211)

### External Dependencies
- Samsara API with feed endpoint access
- Redis for caching and state management
- Laravel Scheduler for automated execution

## Next Steps

After implementing incremental sync:

1. **TMS-215**: Real-time data streaming capabilities
2. **TMS-216**: Advanced analytics and reporting
3. **TMS-217**: Machine learning integration for predictive maintenance

## Troubleshooting

### Common Issues
1. **Cursor Validation Failures**: Check cursor format and expiry
2. **High Sync Frequency**: Monitor performance and adjust intervals
3. **Data Staleness**: Verify sync scheduling and API connectivity
4. **Memory Issues**: Adjust chunk sizes and memory limits
5. **API Rate Limits**: Check rate limiting and backoff strategies

### Debug Commands

```bash
# Check cursor state
php artisan telematics:cursor-status --provider=samsara

# Test incremental sync
php artisan sync:telematics:incremental --dry-run --verbose

# Check data freshness
php artisan telematics:freshness-report

# Monitor sync performance
php artisan telematics:sync-metrics --hours=24

# Validate feed endpoint connectivity
php artisan telematics:test-feed-endpoint --provider=samsara
```
