# TMS-212: Enhanced Samsara Strategy with Vehicle Stats Endpoints

## Overview

This implementation extends the existing Samsara integration to support vehicle stats endpoints for retrieving odometer readings, engine hours, and diagnostic data. It includes comprehensive rate limiting, circuit breaker patterns, and data transformation capabilities for robust API integration.

## Story Details

- **Epic**: TMS-207 - Telematic Data Synchronization Architecture Implementation
- **Story**: TMS-212 - Enhanced Samsara Strategy with Vehicle Stats Endpoints
- **Story Points**: 13
- **Phase**: 2 (Enhancement - Weeks 3-4)

## Implementation Files

### Enhanced Client and DTOs

```
app/Services/Telematics/Clients/Samsara/
├── SamsaraClient.php (enhanced)
└── SamsaraDataTransformer.php (new)

app/Data/Samsara/
├── SamsaraVehicleStatsResponseData.php (new)
├── SamsaraVehicleStatsData.php (new)
└── SamsaraStatData.php (new)
```

### Enhanced Strategy

```
app/Services/Telematics/Sync/Strategies/
└── SamsaraStrategy.php (enhanced with TelematicDataSyncInterface)
```

### Resilience Services

```
app/Services/Telematics/Resilience/
├── SamsaraRateLimiter.php (new)
└── TelematicCircuitBreaker.php (new)
```

### Service Provider

```
app/Providers/
└── TelematicSyncProvider.php (updated)
```

## Architecture Overview

### Enhanced API Integration

```php
// Enhanced SamsaraClient with stats endpoints
class SamsaraClient
{
    // Existing methods...
    
    // New vehicle stats methods
    public function getVehicleStats(array $params = []): SamsaraVehicleStatsResponseData
    public function getVehicleStatsFeed(array $params = []): SamsaraVehicleStatsResponseData
    public function getHistoricalVehicleStats(array $params = []): SamsaraVehicleStatsResponseData
}
```

### Data Transformation Pipeline

```php
// Samsara data transformation
class SamsaraDataTransformer
{
    public function transformVehicleStats(SamsaraVehicleStatsData $stats): array
    public function convertOdometerReading(SamsaraStatData $stat): TelematicReading
    public function convertEngineHours(SamsaraStatData $stat): TelematicReading
    public function convertDiagnosticData(SamsaraStatData $stat): TelematicReading
}
```

### Resilience Patterns

```php
// Rate limiting for API calls
class SamsaraRateLimiter
{
    private const REQUESTS_PER_SECOND = 25;
    private const DAILY_LIMIT = 100000;
    
    public function attempt(string $key, callable $callback): mixed
    public function remaining(string $key): int
}

// Circuit breaker for API resilience
class TelematicCircuitBreaker
{
    private const FAILURE_THRESHOLD = 5;
    private const RECOVERY_TIMEOUT = 300;
    
    public function call(callable $operation): mixed
    public function isOpen(): bool
}
```

## Key Design Principles

- **API Resilience**: Circuit breaker and rate limiting prevent service degradation
- **Data Quality**: Comprehensive validation and transformation
- **Performance**: Efficient data processing and caching
- **Monitoring**: Detailed logging and metrics collection
- **Backward Compatibility**: Existing functionality remains unchanged
- **Provider Agnostic**: Follows established interface patterns

## Enhanced Features

### 1. Vehicle Stats Endpoints

**Purpose**: Retrieve real-time and historical vehicle statistics

**Supported Stat Types**:
- `obdOdometerMeters`: OBD-based odometer readings
- `gpsOdometerMeters`: GPS-based odometer readings  
- `engineStates`: Engine on/off states and hours
- `fuelPercentRemaining`: Fuel level percentages
- `diagnosticTroubleCodes`: Engine diagnostic codes

**API Endpoints**:
- `/fleet/vehicles/stats` - Current vehicle stats
- `/fleet/vehicles/stats/feed` - Incremental stats feed
- `/fleet/vehicles/stats/history` - Historical stats data

### 2. Rate Limiting Implementation

**Purpose**: Prevent API quota violations and ensure fair usage

**Features**:
- 25 requests per second limit (Samsara specification)
- 100,000 requests per day limit
- Exponential backoff for rate limit violations
- Per-endpoint rate limiting
- Redis-based distributed rate limiting

### 3. Circuit Breaker Pattern

**Purpose**: Protect against cascading failures and API outages

**Features**:
- Configurable failure threshold (default: 5 failures)
- Recovery timeout (default: 5 minutes)
- Half-open state for gradual recovery
- Comprehensive failure tracking
- Automatic fallback mechanisms

### 4. Data Transformation

**Purpose**: Convert Samsara data format to internal telematic readings

**Features**:
- Unit conversion (meters ↔ miles ↔ kilometers)
- Time zone handling for timestamps
- Data quality validation
- Anomaly detection for suspicious readings
- Metadata preservation for debugging

### 5. Enhanced Logging and Monitoring

**Purpose**: Comprehensive observability for API interactions

**Features**:
- Structured logging for all API calls
- Performance metrics (response times, throughput)
- Error categorization and tracking
- API usage statistics
- Debug logging with request/response details

## Installation Instructions

### 1. Install Enhanced Client Files

```bash
# Copy enhanced SamsaraClient
cp TMS-249_SamsaraClient.php app/Services/Telematics/Clients/Samsara/SamsaraClient.php

# Copy new DTOs
cp TMS-250_SamsaraVehicleStatsResponseData.php app/Data/Samsara/SamsaraVehicleStatsResponseData.php
cp TMS-250_SamsaraVehicleStatsData.php app/Data/Samsara/SamsaraVehicleStatsData.php
cp TMS-250_SamsaraStatData.php app/Data/Samsara/SamsaraStatData.php

# Copy data transformer
cp TMS-253_SamsaraDataTransformer.php app/Services/Telematics/Clients/Samsara/SamsaraDataTransformer.php
```

### 2. Install Resilience Services

```bash
# Copy rate limiter
cp TMS-251_SamsaraRateLimiter.php app/Services/Telematics/Resilience/SamsaraRateLimiter.php

# Copy circuit breaker
cp TMS-251_TelematicCircuitBreaker.php app/Services/Telematics/Resilience/TelematicCircuitBreaker.php
```

### 3. Install Enhanced Strategy

```bash
# Copy enhanced SamsaraStrategy
cp TMS-252_SamsaraStrategy.php app/Services/Telematics/Sync/Strategies/SamsaraStrategy.php
```

### 4. Update Service Provider

```bash
# Copy updated service provider
cp TMS-255_TelematicSyncProvider.php app/Providers/TelematicSyncProvider.php
```

### 5. Configure Environment

Update `.env` with Samsara configuration:
```env
SAMSARA_API_URL=https://api.samsara.com/
SAMSARA_API_KEY=your_api_key_here
SAMSARA_RATE_LIMIT_ENABLED=true
SAMSARA_CIRCUIT_BREAKER_ENABLED=true
```

## Usage Examples

### Vehicle Stats Synchronization

```php
use App\Services\Telematics\Sync\Strategies\SamsaraStrategy;

$strategy = app(SamsaraStrategy::class);

// Sync specific vehicle stats
$result = $strategy->syncVehicleStats(
    vehicleIds: ['vehicle_123', 'vehicle_456'],
    statTypes: ['odometer', 'engine_hours']
);

// Incremental sync with cursor
$incrementalResult = $strategy->syncIncrementalData($lastCursor);

// Historical data sync
$historicalResult = $strategy->syncHistoricalData(
    startDate: now()->subDays(7),
    endDate: now(),
    statTypes: ['odometer']
);
```

### Direct Client Usage

```php
use App\Services\Telematics\Clients\Samsara\SamsaraClient;

$client = app(SamsaraClient::class);

// Get current vehicle stats
$stats = $client->getVehicleStats([
    'types' => 'obdOdometerMeters,engineStates',
    'vehicleIds' => 'vehicle_123,vehicle_456'
]);

// Get incremental feed
$feed = $client->getVehicleStatsFeed([
    'types' => 'obdOdometerMeters',
    'after' => $cursor
]);
```

### Data Transformation

```php
use App\Services\Telematics\Clients\Samsara\SamsaraDataTransformer;

$transformer = app(SamsaraDataTransformer::class);

// Transform vehicle stats to internal format
$readings = $transformer->transformVehicleStats($samsaraStats);

// Convert specific stat types
$odometerReading = $transformer->convertOdometerReading($statData);
$engineHours = $transformer->convertEngineHours($statData);
```

## Configuration

### Rate Limiting Configuration

```php
// config/services.php
'samsara' => [
    'base_url' => env('SAMSARA_API_URL', 'https://api.samsara.com/'),
    'api_key' => env('SAMSARA_API_KEY'),
    'rate_limit' => [
        'enabled' => env('SAMSARA_RATE_LIMIT_ENABLED', true),
        'requests_per_second' => 25,
        'daily_limit' => 100000,
        'cache_store' => 'redis',
    ],
],
```

### Circuit Breaker Configuration

```php
// config/telematics.php
'circuit_breaker' => [
    'enabled' => env('SAMSARA_CIRCUIT_BREAKER_ENABLED', true),
    'failure_threshold' => 5,
    'recovery_timeout' => 300, // 5 minutes
    'half_open_max_calls' => 3,
],
```

### Supported Stat Types Mapping

```php
// Internal mapping of stat types
private const SUPPORTED_STAT_TYPES = [
    'odometer' => 'obdOdometerMeters',
    'gps_odometer' => 'gpsOdometerMeters',
    'engine_hours' => 'engineStates',
    'fuel_level' => 'fuelPercentRemaining',
    'diagnostics' => 'diagnosticTroubleCodes',
];
```

## Monitoring and Debugging

### API Metrics

Monitor the following metrics:
- Request rate and response times
- Error rates by endpoint and error type
- Rate limit violations and backoff events
- Circuit breaker state changes
- Data transformation success/failure rates

### Logging

All API interactions are logged with:
- Request parameters and headers
- Response status and timing
- Error details and stack traces
- Rate limiting and circuit breaker events
- Data transformation results

### Debug Commands

```bash
# Check Samsara API connectivity
php artisan telematics:test-samsara

# View rate limiting status
php artisan telematics:rate-limit-status samsara

# Check circuit breaker state
php artisan telematics:circuit-breaker-status samsara

# Test vehicle stats endpoint
php artisan telematics:test-vehicle-stats --vehicle-id=123
```

## Performance Considerations

### API Optimization
- Batch requests when possible to reduce API calls
- Use incremental sync to minimize data transfer
- Implement intelligent caching for frequently accessed data
- Monitor and respect rate limits to avoid throttling

### Data Processing
- Process stats data in chunks to manage memory usage
- Use database transactions for consistency
- Implement efficient indexing for time-series queries
- Cache transformed data to reduce processing overhead

### Resilience
- Circuit breaker prevents cascading failures
- Rate limiting ensures sustainable API usage
- Exponential backoff handles temporary failures gracefully
- Comprehensive error handling with retry mechanisms

## Dependencies

### Required Services
- Enhanced TelematicDataService (TMS-209)
- TelematicReadingService (TMS-209)
- Redis for rate limiting and circuit breaker state
- Existing Samsara configuration and credentials

### External Dependencies
- Samsara API access with appropriate permissions
- Redis server for distributed rate limiting
- Laravel Cache for circuit breaker state management

## Next Steps

After implementing the enhanced Samsara strategy:

1. **TMS-213**: Advanced monitoring and alerting
2. **TMS-214**: Performance optimization and scaling
3. **TMS-215**: Additional provider integrations

## Troubleshooting

### Common Issues
1. **Rate Limit Violations**: Check API usage and adjust request frequency
2. **Circuit Breaker Open**: Investigate API connectivity and error rates
3. **Data Transformation Errors**: Verify Samsara data format and mapping
4. **Authentication Failures**: Check API key validity and permissions

### Debug Steps

```bash
# Test API connectivity
curl -H "Authorization: Bearer $SAMSARA_API_KEY" \
     "https://api.samsara.com/fleet/vehicles/stats?types=obdOdometerMeters"

# Check rate limiting status
php artisan tinker --execute="app('samsara.rate_limiter')->remaining('api')"

# Verify circuit breaker state
php artisan tinker --execute="app('telematic.circuit_breaker')->isOpen()"
```
