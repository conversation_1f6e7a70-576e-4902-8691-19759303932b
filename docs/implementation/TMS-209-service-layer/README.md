# TMS-209: Enhanced Service Layer Architecture for Telematic Data Sync

## Overview

This implementation provides an enhanced service layer architecture for telematic data synchronization. It extends the existing telematic sync infrastructure with data processing capabilities, maintenance calculations, and provider-agnostic data handling while maintaining backward compatibility.

## Story Details

- **Epic**: TMS-207 - Telematic Data Synchronization Architecture Implementation
- **Story**: TMS-209 - Enhanced Service Layer Architecture for Telematic Data Sync
- **Story Points**: 8
- **Phase**: 1 (Foundation - Weeks 1-2)

## Implementation Files

### Interfaces and DTOs

- [TelematicDataSyncInterface.md](./TelematicDataSyncInterface.md) - Provider interface contract
- [TelematicSyncResult.md](./TelematicSyncResult.md) - Sync result DTOs

### Core Services

- [TelematicDataService.md](./TelematicDataService.md) - Data processing and validation
- [TelematicReadingService.md](./TelematicReadingService.md) - Reading storage and retrieval
- [MaintenanceCalculationService.md](./MaintenanceCalculationService.md) - Maintenance calculations

### Enhanced Strategy

- [SamsaraStrategy.md](./SamsaraStrategy.md) - Enhanced Samsara provider implementation

### Service Provider

- [TelematicSyncProvider.md](./TelematicSyncProvider.md) - Service registration and configuration

## Architecture Overview

### Service Layer Components

1. **TelematicDataSyncInterface**: Extended interface for data sync capabilities
2. **TelematicDataService**: Data processing, validation, and transformation
3. **TelematicReadingService**: Repository pattern for data storage/retrieval
4. **MaintenanceCalculationService**: Business logic for maintenance scheduling
5. **Enhanced SamsaraStrategy**: Implements new data sync interface

### Key Design Principles

- **Provider-Agnostic**: Works with any telematic provider
- **SOLID Principles**: Clean separation of concerns
- **Backward Compatibility**: Existing functionality unchanged
- **Performance Optimized**: Bulk operations and efficient queries
- **Extensible**: Easy to add new providers and data types

## Service Descriptions

### 1. TelematicDataSyncInterface

**Purpose**: Extended interface for telematic data synchronization

**Key Methods**:
- `syncVehicleStats()` - Sync vehicle statistics data
- `syncIncrementalData()` - Cursor-based incremental sync
- `syncHistoricalData()` - Historical data synchronization
- `getLastSyncCursor()` - Cursor management
- `getSyncCapabilities()` - Provider capability detection

**Features**:
- Extends existing TelematicSyncInterface
- Structured result objects (TelematicSyncResult, IncrementalSyncResult)
- Provider capability discovery
- Flexible sync options

### 2. TelematicDataService

**Purpose**: Process, validate, and transform telematic data

**Key Features**:
- Data validation and quality checks
- Unit conversion and normalization
- Anomaly detection for suspicious readings
- Batch processing for large datasets
- Provider-specific data transformation

**Supported Reading Types**:
- Odometer (meters, kilometers, miles)
- Engine hours (hours, minutes)
- Fuel level (percent, liters, gallons)
- Diagnostics (codes, status)
- GPS coordinates
- Speed (mph, kmh, m/s)
- Temperature (celsius, fahrenheit)

### 3. TelematicReadingService

**Purpose**: Repository pattern for telematic data storage and retrieval

**Key Features**:
- Bulk insert operations for performance
- Latest reading retrieval methods
- Time-range queries for historical data
- Data aggregation for dashboards
- Current vehicle state queries

**Performance Optimizations**:
- Chunked bulk operations
- Optimized database queries
- Efficient indexing usage
- Memory-conscious processing

### 4. MaintenanceCalculationService

**Purpose**: Calculate maintenance schedules based on telematic data

**Key Features**:
- Mileage-based maintenance calculations
- Engine hours-based scheduling
- Time-based maintenance intervals
- Due date prediction algorithms
- Overdue maintenance detection

**Maintenance Types Supported**:
- Oil changes
- Tire rotations
- Brake inspections
- Annual inspections
- Engine service
- Transmission service

### 5. Enhanced SamsaraStrategy

**Purpose**: Implements enhanced data sync capabilities for Samsara

**New Capabilities**:
- Vehicle stats synchronization
- Incremental sync with feed endpoints
- Historical data retrieval
- Cursor-based pagination
- Enhanced error handling

## Installation Instructions

### 1. Create Service Files

Create the service files using the provided specifications:

```bash
# Create interface and DTOs
# Copy content from: TelematicDataSyncInterface.md
# Copy content from: TelematicSyncResult.md

# Create core services
# Copy content from: TelematicDataService.md
# Copy content from: TelematicReadingService.md
# Copy content from: MaintenanceCalculationService.md

# Update existing files
# Copy content from: SamsaraStrategy.md
# Copy content from: TelematicSyncProvider.md
```

### 2. Update Service Provider Registration

The enhanced service provider automatically registers all new services. Ensure it's registered in `config/app.php`:

```php
'providers' => [
    // ... other providers
    App\Providers\TelematicSyncProvider::class,
],
```

### 3. Verify Service Registration

```bash
php artisan tinker
>>> app(App\Services\Telematics\Data\TelematicDataService::class);
>>> app(App\Services\Telematics\Data\TelematicReadingService::class);
>>> app(App\Services\Maintenance\MaintenanceCalculationService::class);
```

## Usage Examples

### Data Processing Service

```php
use App\Services\Telematics\Data\TelematicDataService;

$dataService = app(TelematicDataService::class);

// Process raw telematic data
$rawData = [
    [
        'reading_type' => 'odometer',
        'value' => 150000,
        'unit' => 'meters',
        'recorded_at' => '2024-01-15T10:30:00Z',
    ]
];

$processedReadings = $dataService->processRawData(
    $rawData, 
    $entityMapping, 
    'samsara'
);
```

### Reading Service

```php
use App\Services\Telematics\Data\TelematicReadingService;

$readingService = app(TelematicReadingService::class);

// Get latest odometer reading
$latestOdometer = $readingService->getLatestReading(
    $entityMappingId, 
    'odometer'
);

// Bulk store readings
$result = $readingService->bulkStore($readings);
```

### Maintenance Calculation Service

```php
use App\Services\Maintenance\MaintenanceCalculationService;

$maintenanceService = app(MaintenanceCalculationService::class);

// Calculate maintenance schedules for a truck
$schedules = $maintenanceService->calculateMaintenanceSchedules($truck);

// Get overdue maintenance
$overdue = $maintenanceService->getOverdueMaintenanceSchedules();
```

### Enhanced Samsara Strategy

```php
use App\Services\Telematics\Sync\TelematicDataSyncInterface;

$strategy = app('telematic.data.sync.strategy.map')['samsara'];

// Sync vehicle stats
$result = $strategy->syncVehicleStats(
    vehicleIds: ['vehicle_123'],
    statTypes: ['odometer', 'engine_hours']
);

// Incremental sync
$incrementalResult = $strategy->syncIncrementalData($lastCursor);
```

## Configuration

### Data Validation Thresholds

Configure anomaly detection thresholds in `TelematicDataService`:

```php
private const ANOMALY_THRESHOLDS = [
    'odometer' => ['max_daily_increase' => 1000000], // 1000km per day
    'engine_hours' => ['max_daily_increase' => 24],   // 24 hours per day
    'fuel_level' => ['min' => 0, 'max' => 100],       // 0-100%
];
```

### Maintenance Intervals

Configure default maintenance intervals in `MaintenanceCalculationService`:

```php
private const DEFAULT_MAINTENANCE_INTERVALS = [
    'oil_change' => [
        'interval_type' => 'mileage',
        'interval_value' => 24140, // 15,000 miles in meters
    ],
    // ... other intervals
];
```

## Testing

### Service Testing

```php
// Test data processing
$dataService = app(TelematicDataService::class);
$result = $dataService->processRawData($testData, $mapping, 'samsara');

// Test reading service
$readingService = app(TelematicReadingService::class);
$latest = $readingService->getLatestReading(1, 'odometer');

// Test maintenance calculations
$maintenanceService = app(MaintenanceCalculationService::class);
$schedules = $maintenanceService->calculateMaintenanceSchedules($truck);
```

## Error Handling

### Data Processing Errors
- Invalid data structure validation
- Unit conversion errors
- Anomaly detection warnings
- Provider-specific error handling

### Service Errors
- Database connection issues
- External API failures
- Data consistency problems
- Performance degradation

## Performance Considerations

### Bulk Operations
- Process data in configurable chunks (default: 1000 records)
- Use database transactions for consistency
- Implement memory-efficient processing

### Query Optimization
- Use proper indexes for time-based queries
- Implement query result caching where appropriate
- Monitor slow query performance

## Dependencies

### Required Services
- Existing TelematicSyncService
- ExternalEntityMapping model
- Laravel service container

### External Dependencies
- Spatie Laravel Data (for DTOs)
- Carbon (for date handling)
- Laravel Eloquent ORM

## Next Steps

After implementing the service layer:

1. **TMS-210**: Create Eloquent models and relationships
2. **TMS-211**: Implement queue job structure
3. **TMS-212**: Enhance Samsara strategy with stats endpoints

## Troubleshooting

### Common Issues
1. **Service Resolution Errors**: Check service provider registration
2. **Interface Binding Issues**: Verify dependency injection configuration
3. **Data Processing Failures**: Check validation rules and error logs
4. **Performance Issues**: Monitor bulk operation sizes and query performance
