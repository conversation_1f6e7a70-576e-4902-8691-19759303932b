# Telematic Sync Result DTOs

## Overview

This file contains Data Transfer Objects for representing telematic synchronization results, including both standard sync results and incremental sync results with cursor management.

## Result Classes

### TelematicSyncResult

**Location**: `app/Services/Telematics/Sync/TelematicSyncResult.php`

```php
<?php

namespace App\Services\Telematics\Sync;

use Carbon\Carbon;

class TelematicSyncResult
{
    public function __construct(
        public readonly bool $successful,
        public readonly int $recordsProcessed,
        public readonly int $recordsStored,
        public readonly int $recordsSkipped,
        public readonly int $recordsErrored,
        public readonly array $errors,
        public readonly ?string $errorMessage,
        public readonly Carbon $startedAt,
        public readonly Carbon $completedAt,
        public readonly array $metadata = []
    ) {}

    public static function success(array $data): self
    {
        return new self(
            successful: true,
            recordsProcessed: $data['records_processed'] ?? 0,
            recordsStored: $data['records_stored'] ?? 0,
            recordsSkipped: $data['records_skipped'] ?? 0,
            recordsErrored: $data['records_errored'] ?? 0,
            errors: $data['errors'] ?? [],
            errorMessage: null,
            startedAt: $data['started_at'] ?? now(),
            completedAt: $data['completed_at'] ?? now(),
            metadata: $data['metadata'] ?? []
        );
    }

    public static function failed(string $errorMessage, array $errors = []): self
    {
        return new self(
            successful: false,
            recordsProcessed: 0,
            recordsStored: 0,
            recordsSkipped: 0,
            recordsErrored: 0,
            errors: $errors,
            errorMessage: $errorMessage,
            startedAt: now(),
            completedAt: now(),
            metadata: []
        );
    }

    public function isSuccessful(): bool
    {
        return $this->successful;
    }

    public function hasErrors(): bool
    {
        return !empty($this->errors) || $this->recordsErrored > 0;
    }

    public function getSuccessRate(): float
    {
        if ($this->recordsProcessed === 0) {
            return 0.0;
        }

        return ($this->recordsStored / $this->recordsProcessed) * 100;
    }

    public function getDuration(): float
    {
        return $this->completedAt->diffInSeconds($this->startedAt, true);
    }

    public function getProcessingRate(): float
    {
        $duration = $this->getDuration();
        
        if ($duration === 0.0) {
            return 0.0;
        }

        return $this->recordsProcessed / $duration;
    }

    public function toArray(): array
    {
        return [
            'successful' => $this->successful,
            'records_processed' => $this->recordsProcessed,
            'records_stored' => $this->recordsStored,
            'records_skipped' => $this->recordsSkipped,
            'records_errored' => $this->recordsErrored,
            'errors' => $this->errors,
            'error_message' => $this->errorMessage,
            'started_at' => $this->startedAt->toISOString(),
            'completed_at' => $this->completedAt->toISOString(),
            'duration_seconds' => $this->getDuration(),
            'success_rate' => $this->getSuccessRate(),
            'processing_rate' => $this->getProcessingRate(),
            'metadata' => $this->metadata,
        ];
    }

    public function merge(self $other): self
    {
        return new self(
            successful: $this->successful && $other->successful,
            recordsProcessed: $this->recordsProcessed + $other->recordsProcessed,
            recordsStored: $this->recordsStored + $other->recordsStored,
            recordsSkipped: $this->recordsSkipped + $other->recordsSkipped,
            recordsErrored: $this->recordsErrored + $other->recordsErrored,
            errors: array_merge($this->errors, $other->errors),
            errorMessage: $this->errorMessage ?? $other->errorMessage,
            startedAt: $this->startedAt->min($other->startedAt),
            completedAt: $this->completedAt->max($other->completedAt),
            metadata: array_merge($this->metadata, $other->metadata)
        );
    }
}
```

### IncrementalSyncResult

**Location**: `app/Services/Telematics/Sync/IncrementalSyncResult.php`

```php
<?php

namespace App\Services\Telematics\Sync;

use Carbon\Carbon;

class IncrementalSyncResult extends TelematicSyncResult
{
    public function __construct(
        bool $successful,
        int $recordsProcessed,
        int $recordsStored,
        int $recordsSkipped,
        int $recordsErrored,
        array $errors,
        ?string $errorMessage,
        Carbon $startedAt,
        Carbon $completedAt,
        array $metadata,
        public readonly ?string $nextCursor,
        public readonly ?string $previousCursor,
        public readonly bool $hasMoreData,
        public readonly ?Carbon $cursorExpiresAt
    ) {
        parent::__construct(
            $successful,
            $recordsProcessed,
            $recordsStored,
            $recordsSkipped,
            $recordsErrored,
            $errors,
            $errorMessage,
            $startedAt,
            $completedAt,
            $metadata
        );
    }

    public static function success(array $data, ?string $nextCursor = null): self
    {
        return new self(
            successful: true,
            recordsProcessed: $data['records_processed'] ?? 0,
            recordsStored: $data['records_stored'] ?? 0,
            recordsSkipped: $data['records_skipped'] ?? 0,
            recordsErrored: $data['records_errored'] ?? 0,
            errors: $data['errors'] ?? [],
            errorMessage: null,
            startedAt: $data['started_at'] ?? now(),
            completedAt: $data['completed_at'] ?? now(),
            metadata: $data['metadata'] ?? [],
            nextCursor: $nextCursor,
            previousCursor: $data['previous_cursor'] ?? null,
            hasMoreData: $data['has_more_data'] ?? ($nextCursor !== null),
            cursorExpiresAt: $data['cursor_expires_at'] ?? null
        );
    }

    public static function failed(string $errorMessage, array $errors = []): self
    {
        return new self(
            successful: false,
            recordsProcessed: 0,
            recordsStored: 0,
            recordsSkipped: 0,
            recordsErrored: 0,
            errors: $errors,
            errorMessage: $errorMessage,
            startedAt: now(),
            completedAt: now(),
            metadata: [],
            nextCursor: null,
            previousCursor: null,
            hasMoreData: false,
            cursorExpiresAt: null
        );
    }

    public function isCursorValid(): bool
    {
        return $this->nextCursor !== null && 
               ($this->cursorExpiresAt === null || $this->cursorExpiresAt->isFuture());
    }

    public function shouldContinueSync(): bool
    {
        return $this->successful && $this->hasMoreData && $this->isCursorValid();
    }

    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'next_cursor' => $this->nextCursor,
            'previous_cursor' => $this->previousCursor,
            'has_more_data' => $this->hasMoreData,
            'cursor_expires_at' => $this->cursorExpiresAt?->toISOString(),
            'cursor_valid' => $this->isCursorValid(),
            'should_continue' => $this->shouldContinueSync(),
        ]);
    }
}
```

## Usage Examples

### Basic Sync Result

```php
// Success result
$result = TelematicSyncResult::success([
    'records_processed' => 1000,
    'records_stored' => 985,
    'records_skipped' => 10,
    'records_errored' => 5,
    'started_at' => now()->subMinutes(5),
    'completed_at' => now(),
    'metadata' => [
        'provider' => 'samsara',
        'sync_type' => 'vehicle_stats',
        'vehicle_count' => 50,
    ]
]);

// Check results
if ($result->isSuccessful()) {
    echo "Processed {$result->recordsProcessed} records";
    echo "Success rate: {$result->getSuccessRate()}%";
    echo "Processing rate: {$result->getProcessingRate()} records/sec";
}

// Failed result
$failedResult = TelematicSyncResult::failed(
    'API connection timeout',
    ['Connection timeout after 30 seconds']
);
```

### Incremental Sync Result

```php
// Successful incremental sync
$incrementalResult = IncrementalSyncResult::success([
    'records_processed' => 500,
    'records_stored' => 495,
    'records_skipped' => 3,
    'records_errored' => 2,
    'has_more_data' => true,
    'cursor_expires_at' => now()->addHours(24),
], 'cursor_abc123');

// Check if sync should continue
if ($incrementalResult->shouldContinueSync()) {
    // Continue with next batch using cursor
    $nextResult = $strategy->syncIncrementalData(
        cursor: $incrementalResult->nextCursor
    );
}

// Handle cursor expiry
if (!$incrementalResult->isCursorValid()) {
    // Reset cursor and start fresh
    $freshResult = $strategy->syncIncrementalData(cursor: null);
}
```

### Merging Results

```php
// Merge multiple sync results
$result1 = TelematicSyncResult::success([
    'records_processed' => 500,
    'records_stored' => 490,
]);

$result2 = TelematicSyncResult::success([
    'records_processed' => 300,
    'records_stored' => 295,
]);

$combinedResult = $result1->merge($result2);
// Combined: 800 processed, 785 stored
```

### Converting to Array

```php
$result = TelematicSyncResult::success([
    'records_processed' => 1000,
    'records_stored' => 985,
]);

$array = $result->toArray();
/*
[
    'successful' => true,
    'records_processed' => 1000,
    'records_stored' => 985,
    'records_skipped' => 0,
    'records_errored' => 0,
    'errors' => [],
    'error_message' => null,
    'started_at' => '2024-01-15T10:30:00.000000Z',
    'completed_at' => '2024-01-15T10:35:00.000000Z',
    'duration_seconds' => 300.0,
    'success_rate' => 98.5,
    'processing_rate' => 3.33,
    'metadata' => [],
]
*/
```

## Error Handling

### Error Categories

```php
class SyncErrorType
{
    const API_ERROR = 'api_error';
    const VALIDATION_ERROR = 'validation_error';
    const NETWORK_ERROR = 'network_error';
    const RATE_LIMIT_ERROR = 'rate_limit_error';
    const AUTHENTICATION_ERROR = 'auth_error';
    const DATA_ERROR = 'data_error';
}

// Usage in results
$result = TelematicSyncResult::failed(
    'Rate limit exceeded',
    [
        [
            'type' => SyncErrorType::RATE_LIMIT_ERROR,
            'message' => 'API rate limit of 25 req/sec exceeded',
            'retry_after' => 60,
        ]
    ]
);
```

### Detailed Error Information

```php
$result = TelematicSyncResult::success([
    'records_processed' => 1000,
    'records_stored' => 950,
    'records_errored' => 50,
    'errors' => [
        [
            'type' => 'validation_error',
            'message' => 'Invalid odometer reading: -100 meters',
            'record_id' => 'vehicle_123_reading_456',
            'field' => 'odometer_value',
        ],
        [
            'type' => 'data_error',
            'message' => 'Missing timestamp for reading',
            'record_id' => 'vehicle_456_reading_789',
            'field' => 'recorded_at',
        ],
    ]
]);
```

## Performance Metrics

### Built-in Metrics

```php
$result = TelematicSyncResult::success([
    'records_processed' => 10000,
    'records_stored' => 9850,
    'started_at' => now()->subMinutes(10),
    'completed_at' => now(),
]);

// Performance analysis
echo "Duration: " . $result->getDuration() . " seconds\n";
echo "Success Rate: " . $result->getSuccessRate() . "%\n";
echo "Processing Rate: " . $result->getProcessingRate() . " records/sec\n";
```

### Custom Metrics

```php
$result = TelematicSyncResult::success([
    'records_processed' => 1000,
    'records_stored' => 985,
    'metadata' => [
        'api_calls_made' => 25,
        'cache_hits' => 150,
        'cache_misses' => 50,
        'memory_peak_mb' => 128,
        'database_queries' => 10,
    ]
]);

// Access custom metrics
$apiCalls = $result->metadata['api_calls_made'];
$cacheHitRate = $result->metadata['cache_hits'] / 
                ($result->metadata['cache_hits'] + $result->metadata['cache_misses']);
```

## Testing

### Unit Tests

```php
class TelematicSyncResultTest extends TestCase
{
    public function test_success_result_creation()
    {
        $result = TelematicSyncResult::success([
            'records_processed' => 100,
            'records_stored' => 95,
        ]);

        $this->assertTrue($result->isSuccessful());
        $this->assertEquals(100, $result->recordsProcessed);
        $this->assertEquals(95, $result->recordsStored);
        $this->assertEquals(95.0, $result->getSuccessRate());
    }

    public function test_failed_result_creation()
    {
        $result = TelematicSyncResult::failed('Test error');

        $this->assertFalse($result->isSuccessful());
        $this->assertEquals('Test error', $result->errorMessage);
    }

    public function test_result_merging()
    {
        $result1 = TelematicSyncResult::success(['records_processed' => 50]);
        $result2 = TelematicSyncResult::success(['records_processed' => 30]);

        $merged = $result1->merge($result2);

        $this->assertEquals(80, $merged->recordsProcessed);
        $this->assertTrue($merged->isSuccessful());
    }
}
```
