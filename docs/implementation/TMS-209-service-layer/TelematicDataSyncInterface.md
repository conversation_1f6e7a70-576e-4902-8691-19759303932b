# Telematic Data Sync Interface

## Overview

This interface defines the contract for telematic data synchronization providers, ensuring consistent implementation across different telematic service providers like Samsara, Geotab, etc.

## Interface File

**Location**: `app/Services/Telematics/Sync/TelematicDataSyncInterface.php`

```php
<?php

namespace App\Services\Telematics\Sync;

use App\Models\ExternalEntityMapping;
use Carbon\Carbon;

interface TelematicDataSyncInterface
{
    /**
     * Get the provider name
     */
    public function getProviderName(): string;

    /**
     * Get supported sync capabilities
     */
    public function getSyncCapabilities(): array;

    /**
     * Sync vehicle statistics data
     */
    public function syncVehicleStats(
        array $vehicleIds = [],
        array $statTypes = [],
        ?Carbon $startDate = null,
        ?Carbon $endDate = null
    ): TelematicSyncResult;

    /**
     * Sync incremental data using cursor-based pagination
     */
    public function syncIncrementalData(
        ?string $cursor = null,
        array $statTypes = [],
        int $limit = 1000
    ): IncrementalSyncResult;

    /**
     * Sync historical data for a specific date range
     */
    public function syncHistoricalData(
        Carbon $startDate,
        Carbon $endDate,
        array $vehicleIds = [],
        array $statTypes = []
    ): TelematicSyncResult;

    /**
     * Get available vehicles from the provider
     */
    public function getAvailableVehicles(): array;

    /**
     * Validate provider configuration
     */
    public function validateConfiguration(): bool;

    /**
     * Test provider connectivity
     */
    public function testConnectivity(): bool;

    /**
     * Get last sync status for a specific sync type
     */
    public function getLastSyncStatus(string $syncType): ?array;

    /**
     * Reset sync state for a specific sync type
     */
    public function resetSyncState(string $syncType): bool;
}
```

## Method Specifications

### Core Sync Methods

#### syncVehicleStats()

**Purpose**: Synchronize vehicle statistics data from the provider

**Parameters**:
- `$vehicleIds` (array): Specific vehicle IDs to sync (empty = all vehicles)
- `$statTypes` (array): Types of stats to sync (odometer, engine_hours, etc.)
- `$startDate` (Carbon|null): Start date for data range
- `$endDate` (Carbon|null): End date for data range

**Returns**: `TelematicSyncResult` with sync statistics and results

**Example**:
```php
$result = $strategy->syncVehicleStats(
    vehicleIds: ['vehicle_123', 'vehicle_456'],
    statTypes: ['odometer', 'engine_hours'],
    startDate: now()->subDays(7),
    endDate: now()
);
```

#### syncIncrementalData()

**Purpose**: Perform cursor-based incremental synchronization

**Parameters**:
- `$cursor` (string|null): Pagination cursor from last sync
- `$statTypes` (array): Types of stats to sync
- `$limit` (int): Maximum records per request

**Returns**: `IncrementalSyncResult` with new cursor and sync data

**Example**:
```php
$result = $strategy->syncIncrementalData(
    cursor: $lastCursor,
    statTypes: ['odometer'],
    limit: 1000
);
```

#### syncHistoricalData()

**Purpose**: Sync historical data for backfill operations

**Parameters**:
- `$startDate` (Carbon): Start date for historical sync
- `$endDate` (Carbon): End date for historical sync
- `$vehicleIds` (array): Specific vehicles to sync
- `$statTypes` (array): Types of stats to sync

**Returns**: `TelematicSyncResult` with historical sync results

### Utility Methods

#### getSyncCapabilities()

**Purpose**: Return provider-specific capabilities

**Returns**: Array of supported features

**Example Response**:
```php
[
    'incremental_sync' => true,
    'historical_sync' => true,
    'real_time_sync' => false,
    'supported_stat_types' => [
        'odometer',
        'engine_hours',
        'fuel_level',
        'diagnostics'
    ],
    'max_date_range_days' => 365,
    'rate_limit_per_second' => 25,
]
```

#### getAvailableVehicles()

**Purpose**: Retrieve list of vehicles available from provider

**Returns**: Array of vehicle data

**Example Response**:
```php
[
    [
        'provider_id' => 'samsara_123',
        'name' => 'Truck 001',
        'vin' => '1HGBH41JXMN109186',
        'license_plate' => 'ABC123',
        'vehicle_type' => 'truck',
    ],
    // ... more vehicles
]
```

## Implementation Guidelines

### Error Handling

All methods should handle errors gracefully and return appropriate results:

```php
public function syncVehicleStats(...): TelematicSyncResult
{
    try {
        // Sync logic here
        return TelematicSyncResult::success($data);
    } catch (ApiException $e) {
        Log::error('API error during sync', [
            'provider' => $this->getProviderName(),
            'error' => $e->getMessage()
        ]);
        
        return TelematicSyncResult::failed($e->getMessage());
    } catch (\Exception $e) {
        Log::error('Unexpected error during sync', [
            'provider' => $this->getProviderName(),
            'error' => $e->getMessage()
        ]);
        
        return TelematicSyncResult::failed('Unexpected error occurred');
    }
}
```

### Configuration Validation

```php
public function validateConfiguration(): bool
{
    $requiredConfig = ['api_key', 'base_url'];
    
    foreach ($requiredConfig as $key) {
        if (empty(config("services.{$this->getProviderName()}.{$key}"))) {
            return false;
        }
    }
    
    return $this->testConnectivity();
}
```

### Connectivity Testing

```php
public function testConnectivity(): bool
{
    try {
        $response = $this->client->get('/test-endpoint');
        return $response->successful();
    } catch (\Exception $e) {
        Log::warning('Connectivity test failed', [
            'provider' => $this->getProviderName(),
            'error' => $e->getMessage()
        ]);
        
        return false;
    }
}
```

## Supported Stat Types

### Standard Stat Types

```php
const SUPPORTED_STAT_TYPES = [
    'odometer' => 'Vehicle odometer readings',
    'engine_hours' => 'Engine operating hours',
    'fuel_level' => 'Fuel level percentage',
    'diagnostics' => 'Diagnostic trouble codes',
    'location' => 'GPS location data',
    'speed' => 'Vehicle speed data',
    'engine_rpm' => 'Engine RPM readings',
    'coolant_temp' => 'Engine coolant temperature',
    'oil_pressure' => 'Engine oil pressure',
];
```

### Provider-Specific Extensions

Providers can extend the standard stat types:

```php
public function getSupportedStatTypes(): array
{
    $standard = self::SUPPORTED_STAT_TYPES;
    
    // Add provider-specific stat types
    $providerSpecific = [
        'tire_pressure' => 'Tire pressure monitoring',
        'cargo_weight' => 'Cargo weight sensors',
    ];
    
    return array_merge($standard, $providerSpecific);
}
```

## Usage Examples

### Basic Implementation

```php
class SamsaraStrategy implements TelematicDataSyncInterface
{
    public function __construct(
        private SamsaraClient $client,
        private TelematicDataService $dataService
    ) {}

    public function getProviderName(): string
    {
        return 'samsara';
    }

    public function syncVehicleStats(
        array $vehicleIds = [],
        array $statTypes = [],
        ?Carbon $startDate = null,
        ?Carbon $endDate = null
    ): TelematicSyncResult {
        // Implementation here
    }

    // ... other methods
}
```

### Service Registration

```php
// In TelematicSyncProvider
public function register(): void
{
    $this->app->bind(TelematicDataSyncInterface::class, function ($app) {
        $provider = config('telematics.default_provider', 'samsara');
        
        return match($provider) {
            'samsara' => $app->make(SamsaraStrategy::class),
            'geotab' => $app->make(GeotabStrategy::class),
            default => throw new InvalidArgumentException("Unsupported provider: {$provider}")
        };
    });
}
```

## Testing

### Interface Testing

```php
class TelematicDataSyncInterfaceTest extends TestCase
{
    public function test_sync_vehicle_stats_returns_valid_result()
    {
        $strategy = $this->app->make(TelematicDataSyncInterface::class);
        
        $result = $strategy->syncVehicleStats(
            vehicleIds: ['test_vehicle'],
            statTypes: ['odometer']
        );
        
        $this->assertInstanceOf(TelematicSyncResult::class, $result);
        $this->assertTrue($result->isSuccessful());
    }

    public function test_get_sync_capabilities_returns_array()
    {
        $strategy = $this->app->make(TelematicDataSyncInterface::class);
        
        $capabilities = $strategy->getSyncCapabilities();
        
        $this->assertIsArray($capabilities);
        $this->assertArrayHasKey('supported_stat_types', $capabilities);
    }
}
```

### Mock Implementation

```php
class MockTelematicStrategy implements TelematicDataSyncInterface
{
    public function syncVehicleStats(...): TelematicSyncResult
    {
        return TelematicSyncResult::success([
            'records_processed' => 100,
            'records_stored' => 95,
            'errors' => 5,
        ]);
    }

    public function syncIncrementalData(...): IncrementalSyncResult
    {
        return IncrementalSyncResult::success(
            data: ['mock_data'],
            nextCursor: 'mock_cursor_123'
        );
    }

    // ... other methods
}
```

## Best Practices

### 1. Consistent Error Handling

- Always return appropriate result objects
- Log errors with sufficient context
- Handle rate limiting gracefully
- Implement retry logic for transient failures

### 2. Performance Optimization

- Use pagination for large datasets
- Implement caching where appropriate
- Batch API requests when possible
- Monitor and respect rate limits

### 3. Data Validation

- Validate input parameters
- Sanitize data from external APIs
- Implement data quality checks
- Handle missing or invalid data gracefully

### 4. Configuration Management

- Use environment-specific configuration
- Validate configuration on startup
- Support multiple provider configurations
- Implement secure credential storage
