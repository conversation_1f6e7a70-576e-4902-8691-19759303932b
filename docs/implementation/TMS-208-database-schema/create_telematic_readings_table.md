# Create Telematic Readings Table Migration

## Overview

This migration creates the main `telematic_readings` table for storing time-series telematic data from various providers with PostgreSQL declarative partitioning for optimal performance.

## Migration File

**Location**: `database/migrations/YYYY_MM_DD_HHMMSS_create_telematic_readings_table.php`

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        // Create the main partitioned table
        Schema::create('telematic_readings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('external_entity_mapping_id')
                  ->constrained('external_entity_mappings')
                  ->onDelete('cascade');
            $table->string('reading_type', 50); // odometer, engine_hours, fuel_level, etc.
            $table->decimal('value', 15, 6); // High precision for various measurements
            $table->string('unit', 20); // meters, hours, percent, etc.
            $table->timestamp('recorded_at', 6); // High precision timestamp from provider
            $table->timestamp('synced_at', 6); // When synced to our system
            $table->jsonb('metadata')->nullable(); // Provider-specific data (PostgreSQL JSONB)
            $table->timestamps();

            // Indexes for performance
            $table->index(['external_entity_mapping_id', 'reading_type', 'recorded_at']);
            $table->index(['reading_type', 'recorded_at']);
            $table->index('recorded_at');
            $table->index('synced_at');

            // Unique constraint to prevent duplicates
            $table->unique([
                'external_entity_mapping_id',
                'reading_type',
                'recorded_at'
            ], 'telematic_readings_unique');
        });

        // Convert to partitioned table using PostgreSQL declarative partitioning
        DB::statement('
            ALTER TABLE telematic_readings 
            PARTITION BY RANGE (recorded_at)
        ');

        // Create initial partitions for current and next 3 months
        $this->createInitialPartitions();
    }

    public function down(): void
    {
        // Drop all partitions first
        $partitions = DB::select("
            SELECT schemaname, tablename 
            FROM pg_tables 
            WHERE tablename LIKE 'telematic_readings_%'
        ");

        foreach ($partitions as $partition) {
            DB::statement("DROP TABLE IF EXISTS {$partition->tablename} CASCADE");
        }

        Schema::dropIfExists('telematic_readings');
    }

    private function createInitialPartitions(): void
    {
        $startDate = now()->startOfMonth();
        
        for ($i = 0; $i < 4; $i++) {
            $partitionStart = $startDate->copy()->addMonths($i);
            $partitionEnd = $partitionStart->copy()->addMonth();
            $partitionName = 'telematic_readings_' . $partitionStart->format('Y_m');
            
            DB::statement("
                CREATE TABLE {$partitionName} PARTITION OF telematic_readings
                FOR VALUES FROM ('{$partitionStart->toDateString()}') 
                TO ('{$partitionEnd->toDateString()}')
            ");
        }
    }
};
```

## Table Structure

### Columns

| Column | Type | Description |
|--------|------|-------------|
| `id` | bigint | Primary key |
| `external_entity_mapping_id` | bigint | Foreign key to external_entity_mappings |
| `reading_type` | varchar(50) | Type of reading (odometer, engine_hours, etc.) |
| `value` | decimal(15,6) | Numeric value with high precision |
| `unit` | varchar(20) | Unit of measurement |
| `recorded_at` | timestamp(6) | When reading was recorded by provider |
| `synced_at` | timestamp(6) | When reading was synced to our system |
| `metadata` | jsonb | Provider-specific data (PostgreSQL JSONB) |
| `created_at` | timestamp | Laravel timestamp |
| `updated_at` | timestamp | Laravel timestamp |

### Indexes

1. **Composite Index**: `(external_entity_mapping_id, reading_type, recorded_at)` - Primary query pattern
2. **Type-Time Index**: `(reading_type, recorded_at)` - Filtering by type and time
3. **Time Index**: `(recorded_at)` - Time-based queries
4. **Sync Index**: `(synced_at)` - Sync monitoring queries

### Constraints

- **Unique Constraint**: Prevents duplicate readings for same entity, type, and time
- **Foreign Key**: Links to external_entity_mappings table
- **Check Constraints**: Can be added for value validation

## Partitioning Strategy

### PostgreSQL Declarative Partitioning

- **Partition Key**: `recorded_at` (timestamp)
- **Partition Type**: RANGE partitioning
- **Partition Interval**: Monthly partitions
- **Retention**: Configurable via partition management command

### Benefits

1. **Query Performance**: Partition pruning for time-based queries
2. **Maintenance**: Easier data archival and cleanup
3. **Scalability**: Better performance with large datasets
4. **Storage**: Efficient storage management

### Partition Naming Convention

- Format: `telematic_readings_YYYY_MM`
- Example: `telematic_readings_2024_01` for January 2024

## Usage Examples

### Insert Reading

```php
DB::table('telematic_readings')->insert([
    'external_entity_mapping_id' => 1,
    'reading_type' => 'odometer',
    'value' => 150000.500,
    'unit' => 'meters',
    'recorded_at' => '2024-01-15 10:30:00.123456',
    'synced_at' => now(),
    'metadata' => json_encode(['provider_id' => 'samsara_123']),
]);
```

### Query with Partition Pruning

```sql
-- This query will only scan the relevant partition
SELECT * FROM telematic_readings 
WHERE recorded_at >= '2024-01-01' 
  AND recorded_at < '2024-02-01'
  AND reading_type = 'odometer';
```

### Check Partition Information

```sql
-- View all partitions
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE tablename LIKE 'telematic_readings_%'
ORDER BY tablename;
```

## Performance Considerations

### Query Optimization

1. **Always include `recorded_at` in WHERE clauses** for partition pruning
2. **Use appropriate indexes** for your query patterns
3. **Consider JSONB operators** for metadata queries

### Maintenance

1. **Regular partition creation** via scheduled command
2. **Old partition cleanup** based on retention policy
3. **Index maintenance** on new partitions
4. **Statistics updates** for query planner

## Monitoring

### Partition Health

```sql
-- Check partition sizes
SELECT 
    tablename,
    pg_size_pretty(pg_total_relation_size(tablename)) as size,
    (SELECT count(*) FROM information_schema.tables WHERE table_name = tablename) as exists
FROM (
    SELECT 'telematic_readings_' || to_char(generate_series(
        date_trunc('month', now() - interval '12 months'),
        date_trunc('month', now() + interval '3 months'),
        interval '1 month'
    ), 'YYYY_MM') as tablename
) partitions;
```

### Query Performance

```sql
-- Check if partition pruning is working
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM telematic_readings 
WHERE recorded_at >= '2024-01-01' 
  AND recorded_at < '2024-02-01';
```

## Troubleshooting

### Common Issues

1. **Partition Not Found**: Ensure partitions exist for the date range
2. **Slow Queries**: Check if partition pruning is working
3. **Insert Failures**: Verify partition exists for the recorded_at date
4. **Constraint Violations**: Check for duplicate readings

### Solutions

1. **Create Missing Partitions**: Run partition management command
2. **Optimize Queries**: Include recorded_at in WHERE clauses
3. **Monitor Partition Creation**: Set up automated partition management
4. **Handle Duplicates**: Implement proper duplicate detection
