# Create Telematic Sync States Table Migration

## Overview

This migration creates the `telematic_sync_states` table for tracking cursor positions and sync state for incremental synchronization with telematic providers.

## Migration File

**Location**: `database/migrations/YYYY_MM_DD_HHMMSS_create_telematic_sync_states_table.php`

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('telematic_sync_states', function (Blueprint $table) {
            $table->id();
            
            // Provider and sync type identification
            $table->string('provider', 50); // samsara, geotab, etc.
            $table->string('sync_type', 50); // stats, locations, diagnostics, etc.
            
            // Cursor tracking for incremental sync
            $table->text('last_cursor')->nullable(); // Provider-specific cursor
            $table->timestamp('last_sync_at')->nullable();
            $table->timestamp('cursor_expires_at')->nullable(); // Cursor expiration
            
            // Sync performance metrics
            $table->unsignedInteger('records_synced')->default(0);
            $table->unsignedInteger('total_records')->default(0);
            $table->decimal('sync_duration_seconds', 8, 3)->nullable();
            $table->decimal('records_per_second', 8, 2)->nullable();
            
            // Error tracking
            $table->unsignedInteger('errors_count')->default(0);
            $table->unsignedInteger('consecutive_errors')->default(0);
            $table->text('last_error')->nullable();
            $table->timestamp('last_error_at')->nullable();
            
            // Sync status and health
            $table->enum('status', [
                'idle',           // Not currently syncing
                'running',        // Sync in progress
                'completed',      // Last sync completed successfully
                'failed',         // Last sync failed
                'paused',         // Sync paused due to errors
                'disabled'        // Sync disabled
            ])->default('idle');
            
            // Sync configuration
            $table->json('sync_config')->nullable(); // Provider-specific configuration
            $table->boolean('auto_sync_enabled')->default(true);
            $table->unsignedInteger('retry_count')->default(0);
            $table->timestamp('next_retry_at')->nullable();
            
            // Metadata and tracking
            $table->json('metadata')->nullable(); // Additional provider data
            $table->timestamp('started_at')->nullable(); // Current sync start time
            $table->string('sync_job_id')->nullable(); // Queue job ID if applicable
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['provider', 'sync_type']);
            $table->index(['status', 'auto_sync_enabled']);
            $table->index(['last_sync_at', 'status']);
            $table->index(['next_retry_at', 'status']);
            $table->index('cursor_expires_at');
            
            // Unique constraint for provider/sync_type combination
            $table->unique(['provider', 'sync_type'], 'sync_states_provider_type_unique');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('telematic_sync_states');
    }
};
```

## Table Structure

### Columns

| Column | Type | Description |
|--------|------|-------------|
| `id` | bigint | Primary key |
| `provider` | varchar(50) | Telematic provider (samsara, geotab, etc.) |
| `sync_type` | varchar(50) | Type of sync (stats, locations, diagnostics) |
| `last_cursor` | text | Provider-specific cursor for incremental sync |
| `last_sync_at` | timestamp | When last sync was performed |
| `cursor_expires_at` | timestamp | When cursor expires |
| `records_synced` | integer | Number of records synced in last operation |
| `total_records` | integer | Total records available from provider |
| `sync_duration_seconds` | decimal(8,3) | Duration of last sync in seconds |
| `records_per_second` | decimal(8,2) | Sync performance metric |
| `errors_count` | integer | Total error count |
| `consecutive_errors` | integer | Consecutive errors (for circuit breaker) |
| `last_error` | text | Last error message |
| `last_error_at` | timestamp | When last error occurred |
| `status` | enum | Current sync status |
| `sync_config` | json | Provider-specific configuration |
| `auto_sync_enabled` | boolean | Whether auto-sync is enabled |
| `retry_count` | integer | Current retry attempt count |
| `next_retry_at` | timestamp | When to retry after failure |
| `metadata` | json | Additional provider metadata |
| `started_at` | timestamp | When current sync started |
| `sync_job_id` | varchar(255) | Queue job ID if applicable |
| `created_at` | timestamp | Laravel timestamp |
| `updated_at` | timestamp | Laravel timestamp |

### Indexes

1. **Provider-Type Index**: `(provider, sync_type)` - Primary lookup pattern
2. **Status Index**: `(status, auto_sync_enabled)` - Status filtering
3. **Sync Time Index**: `(last_sync_at, status)` - Time-based queries
4. **Retry Index**: `(next_retry_at, status)` - Retry scheduling
5. **Cursor Expiry Index**: `(cursor_expires_at)` - Cursor cleanup

### Constraints

- **Unique Constraint**: One sync state per provider/sync_type combination

## Sync Status Values

### Status Definitions

```php
const SYNC_STATUSES = [
    'idle' => 'Not currently syncing',
    'running' => 'Sync operation in progress',
    'completed' => 'Last sync completed successfully',
    'failed' => 'Last sync failed',
    'paused' => 'Sync paused due to consecutive errors',
    'disabled' => 'Sync manually disabled'
];
```

### Status Transitions

```
idle → running → completed → idle
idle → running → failed → idle (with retry)
failed → paused (after consecutive failures)
paused → idle (manual reset)
any → disabled (manual disable)
```

## Usage Examples

### Initialize Sync State

```php
use App\Models\TelematicSyncState;

TelematicSyncState::create([
    'provider' => 'samsara',
    'sync_type' => 'stats',
    'status' => 'idle',
    'auto_sync_enabled' => true,
    'sync_config' => [
        'stat_types' => ['odometer', 'engine_hours'],
        'batch_size' => 1000,
    ],
]);
```

### Update Sync Progress

```php
$syncState = TelematicSyncState::where('provider', 'samsara')
    ->where('sync_type', 'stats')
    ->first();

// Start sync
$syncState->update([
    'status' => 'running',
    'started_at' => now(),
    'sync_job_id' => $jobId,
]);

// Complete sync
$syncState->update([
    'status' => 'completed',
    'last_sync_at' => now(),
    'last_cursor' => $newCursor,
    'records_synced' => $recordCount,
    'sync_duration_seconds' => $duration,
    'records_per_second' => $recordCount / $duration,
    'consecutive_errors' => 0,
    'started_at' => null,
    'sync_job_id' => null,
]);
```

### Handle Sync Failure

```php
$syncState->update([
    'status' => 'failed',
    'last_error' => $errorMessage,
    'last_error_at' => now(),
    'errors_count' => $syncState->errors_count + 1,
    'consecutive_errors' => $syncState->consecutive_errors + 1,
    'retry_count' => $syncState->retry_count + 1,
    'next_retry_at' => now()->addMinutes(pow(2, $syncState->retry_count)), // Exponential backoff
    'started_at' => null,
    'sync_job_id' => null,
]);

// Pause if too many consecutive errors
if ($syncState->consecutive_errors >= 5) {
    $syncState->update(['status' => 'paused']);
}
```

### Query Sync States

```php
// Get active sync states
$activeStates = TelematicSyncState::where('auto_sync_enabled', true)
    ->whereIn('status', ['idle', 'completed'])
    ->get();

// Get failed syncs needing retry
$retryStates = TelematicSyncState::where('status', 'failed')
    ->where('next_retry_at', '<=', now())
    ->get();

// Get sync performance metrics
$metrics = TelematicSyncState::selectRaw('
    provider,
    AVG(records_per_second) as avg_rate,
    AVG(sync_duration_seconds) as avg_duration,
    SUM(records_synced) as total_records,
    MAX(last_sync_at) as last_sync
')->groupBy('provider')->get();
```

## Cursor Management

### Cursor Validation

```php
public function isCursorValid(): bool
{
    return $this->last_cursor !== null 
        && ($this->cursor_expires_at === null || $this->cursor_expires_at->isFuture());
}

public function resetCursor(): void
{
    $this->update([
        'last_cursor' => null,
        'cursor_expires_at' => null,
        'consecutive_errors' => 0,
    ]);
}
```

### Cursor Expiry Handling

```php
// Check for expired cursors
$expiredStates = TelematicSyncState::where('cursor_expires_at', '<', now())
    ->whereNotNull('last_cursor')
    ->get();

foreach ($expiredStates as $state) {
    $state->resetCursor();
    Log::info("Reset expired cursor for {$state->provider}:{$state->sync_type}");
}
```

## Performance Monitoring

### Sync Performance Queries

```sql
-- Average sync performance by provider
SELECT 
    provider,
    AVG(records_per_second) as avg_rate,
    AVG(sync_duration_seconds) as avg_duration,
    COUNT(*) as sync_count
FROM telematic_sync_states 
WHERE last_sync_at >= NOW() - INTERVAL '7 days'
GROUP BY provider;

-- Error rates by provider
SELECT 
    provider,
    SUM(errors_count) as total_errors,
    COUNT(*) as total_syncs,
    (SUM(errors_count) * 100.0 / COUNT(*)) as error_rate
FROM telematic_sync_states 
GROUP BY provider;
```

### Health Monitoring

```php
// Check sync health
public function getSyncHealth(): array
{
    return [
        'status' => $this->status,
        'last_sync_age' => $this->last_sync_at?->diffForHumans(),
        'error_rate' => $this->getErrorRate(),
        'consecutive_errors' => $this->consecutive_errors,
        'cursor_valid' => $this->isCursorValid(),
        'auto_sync_enabled' => $this->auto_sync_enabled,
    ];
}

public function getErrorRate(): float
{
    $totalSyncs = $this->records_synced > 0 ? 1 : 0; // Simplified
    return $totalSyncs > 0 ? ($this->errors_count / $totalSyncs) * 100 : 0;
}
```

## Troubleshooting

### Common Issues

1. **Cursor Expiry**: Cursors become invalid over time
2. **Consecutive Failures**: Circuit breaker pauses sync
3. **Performance Degradation**: Sync rates decrease
4. **Status Inconsistency**: Status doesn't match actual state

### Solutions

1. **Reset Expired Cursors**: Implement cursor cleanup job
2. **Manual Reset**: Reset consecutive error count
3. **Performance Analysis**: Monitor sync metrics
4. **Status Reconciliation**: Verify status matches reality

### Debug Queries

```sql
-- Find problematic sync states
SELECT * FROM telematic_sync_states 
WHERE consecutive_errors > 3 
   OR status = 'paused'
   OR (last_sync_at < NOW() - INTERVAL '1 hour' AND auto_sync_enabled = true);

-- Check cursor expiry
SELECT provider, sync_type, cursor_expires_at, last_cursor IS NOT NULL as has_cursor
FROM telematic_sync_states 
WHERE cursor_expires_at < NOW() AND last_cursor IS NOT NULL;
```
