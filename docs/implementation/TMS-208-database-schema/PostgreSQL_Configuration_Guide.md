# PostgreSQL 17 Configuration Guide for Telematic Data Synchronization

## Overview

This guide provides PostgreSQL-specific configuration and optimization recommendations for the telematic data synchronization architecture. The implementation leverages PostgreSQL 17's advanced features including declarative partitioning, JSONB support, and native time-series optimizations.

## PostgreSQL 17 Features Utilized

### 1. Declarative Partitioning
- **Native Range Partitioning**: Monthly partitions for `telematic_readings` table
- **Automatic Partition Pruning**: Query optimizer automatically excludes irrelevant partitions
- **Constraint Exclusion**: Improved query performance through partition constraints

### 2. JSONB Data Type
- **Binary JSON Storage**: More efficient than standard JSON
- **GIN Indexing**: Fast queries on JSON attributes
- **Native JSON Operators**: Rich querying capabilities

### 3. Advanced Indexing
- **Partial Indexes**: Indexes on specific conditions
- **Expression Indexes**: Indexes on computed values
- **Concurrent Index Creation**: Non-blocking index operations

## Database Configuration

### PostgreSQL Configuration (postgresql.conf)

```ini
# Memory Configuration
shared_buffers = 256MB                    # 25% of RAM for dedicated server
effective_cache_size = 1GB                # 75% of RAM
work_mem = 16MB                           # Per-operation memory
maintenance_work_mem = 256MB              # For maintenance operations

# Partitioning Optimization
constraint_exclusion = partition          # Enable partition pruning
enable_partition_pruning = on             # PostgreSQL 11+ partition pruning
enable_partitionwise_join = on            # Partition-wise joins
enable_partitionwise_aggregate = on       # Partition-wise aggregates

# JSONB Performance
gin_pending_list_limit = 4MB              # GIN index performance

# Checkpoint and WAL Configuration
checkpoint_completion_target = 0.9        # Spread checkpoints
wal_buffers = 16MB                        # WAL buffer size
max_wal_size = 2GB                        # Maximum WAL size

# Query Planner
random_page_cost = 1.1                    # SSD optimization
effective_io_concurrency = 200            # SSD concurrent I/O

# Logging for Monitoring
log_min_duration_statement = 1000         # Log slow queries (1 second)
log_checkpoints = on                      # Log checkpoint activity
log_lock_waits = on                       # Log lock waits
```

### Connection Configuration

```ini
# Connection Settings
max_connections = 200                     # Adjust based on application needs
shared_preload_libraries = 'pg_stat_statements'  # Query statistics
```

## Partition Management Strategy

### 1. Automatic Partition Creation

The `ManageTelematicPartitionsCommand` creates partitions using PostgreSQL's declarative partitioning:

```sql
-- Create partitioned table
ALTER TABLE telematic_readings PARTITION BY RANGE (recorded_at);

-- Create monthly partition
CREATE TABLE telematic_readings_2025_01 PARTITION OF telematic_readings
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

### 2. Partition Indexes

Each partition gets optimized indexes:

```sql
-- Time-based index for partition pruning
CREATE INDEX telematic_readings_2025_01_recorded_at_idx 
ON telematic_readings_2025_01 (recorded_at);

-- Composite index for common queries
CREATE INDEX telematic_readings_2025_01_mapping_type_idx 
ON telematic_readings_2025_01 (external_entity_mapping_id, reading_type);
```

### 3. Partition Maintenance

```bash
# Create future partitions
php artisan telematic:manage-partitions --create-future

# Drop old partitions (12 month retention)
php artisan telematic:manage-partitions --drop-old --retention-months=12

# Dry run to see what would be done
php artisan telematic:manage-partitions --dry-run
```

## JSONB Optimization

### 1. GIN Indexes for Metadata

```sql
-- Create GIN index for JSONB queries
CREATE INDEX telematic_readings_metadata_gin_idx 
ON telematic_readings USING GIN (metadata);

-- Query examples
SELECT * FROM telematic_readings 
WHERE metadata @> '{"provider": "samsara"}';

SELECT * FROM telematic_readings 
WHERE metadata ? 'vehicle_id';
```

### 2. JSONB Query Patterns

```sql
-- Extract specific JSON values
SELECT 
    id,
    metadata->>'vehicle_id' as vehicle_id,
    metadata->'location'->>'latitude' as latitude
FROM telematic_readings;

-- Filter by JSON attributes
SELECT * FROM telematic_readings 
WHERE (metadata->>'temperature')::numeric > 80;
```

## Performance Monitoring

### 1. Partition Statistics

```sql
-- Check partition sizes
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE tablename LIKE 'telematic_readings_%'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### 2. Query Performance

```sql
-- Enable pg_stat_statements for query analysis
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Check slow queries
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
WHERE query LIKE '%telematic_readings%'
ORDER BY mean_time DESC;
```

### 3. Index Usage

```sql
-- Check index usage statistics
SELECT 
    indexrelname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes 
WHERE relname = 'telematic_readings'
ORDER BY idx_scan DESC;
```

## Backup and Recovery

### 1. Partition-Aware Backups

```bash
# Backup specific partition
pg_dump -t telematic_readings_2025_01 ride4 > partition_2025_01.sql

# Backup all partitions
pg_dump --table='telematic_readings*' ride4 > telematic_partitions.sql
```

### 2. Point-in-Time Recovery

```bash
# Create base backup
pg_basebackup -D /backup/base -Ft -z -P

# Restore to specific point in time
pg_ctl stop -D /var/lib/postgresql/data
rm -rf /var/lib/postgresql/data/*
tar -xzf /backup/base/base.tar.gz -C /var/lib/postgresql/data/
# Configure recovery.conf for PITR
pg_ctl start -D /var/lib/postgresql/data
```

## Troubleshooting

### 1. Partition Pruning Issues

```sql
-- Check if partition pruning is working
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM telematic_readings 
WHERE recorded_at >= '2025-01-01' AND recorded_at < '2025-02-01';

-- Should show "Partitions pruned: X"
```

### 2. JSONB Performance Issues

```sql
-- Check if GIN indexes are being used
EXPLAIN (ANALYZE, BUFFERS)
SELECT * FROM telematic_readings 
WHERE metadata @> '{"provider": "samsara"}';

-- Should show "Bitmap Index Scan on telematic_readings_metadata_gin_idx"
```

### 3. Common Issues

1. **Partition Constraint Violations**: Ensure date ranges don't overlap
2. **Missing Indexes**: Create indexes on frequently queried columns
3. **Lock Contention**: Use concurrent operations where possible
4. **Memory Issues**: Adjust work_mem for large operations

## Migration from MySQL

If migrating from MySQL implementation:

1. **Partitioning Syntax**: Convert MySQL `PARTITION BY RANGE` to PostgreSQL declarative partitioning
2. **JSON vs JSONB**: Update application code to use JSONB operators
3. **Function Differences**: Replace `UNIX_TIMESTAMP()` with `EXTRACT(epoch FROM timestamp)`
4. **Index Types**: Convert MySQL indexes to PostgreSQL equivalents

## Maintenance Schedule

### Daily
- Monitor partition sizes and query performance
- Check for failed partition operations

### Weekly  
- Analyze slow query log
- Review index usage statistics

### Monthly
- Run partition management command
- Update table statistics: `ANALYZE telematic_readings;`
- Review and optimize queries based on pg_stat_statements

## Security Considerations

### 1. Row Level Security (RLS)

```sql
-- Enable RLS for multi-tenant scenarios
ALTER TABLE telematic_readings ENABLE ROW LEVEL SECURITY;

-- Create policy for organization-based access
CREATE POLICY telematic_readings_org_policy ON telematic_readings
FOR ALL TO app_role
USING (external_entity_mapping_id IN (
    SELECT id FROM external_entity_mappings 
    WHERE organization_id = current_setting('app.current_org_id')::bigint
));
```

### 2. Connection Security

```ini
# postgresql.conf
ssl = on
ssl_cert_file = 'server.crt'
ssl_key_file = 'server.key'
ssl_ca_file = 'ca.crt'
```

This configuration guide ensures optimal performance and reliability for the telematic data synchronization architecture on PostgreSQL 17.
