# Create Maintenance Schedules Table Migration

## Overview

This migration creates the `maintenance_schedules` table for defining maintenance intervals and scheduling for trucks and trailers with polymorphic relationships and automated due date calculations.

## Migration File

**Location**: `database/migrations/YYYY_MM_DD_HHMMSS_create_maintenance_schedules_table.php`

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('maintenance_schedules', function (Blueprint $table) {
            $table->id();
            
            // Polymorphic relationship to support trucks, trailers, etc.
            $table->string('entity_type'); // App\Models\Truck, App\Models\Trailer
            $table->unsignedBigInteger('entity_id');
            $table->index(['entity_type', 'entity_id']);
            
            // Maintenance definition
            $table->string('maintenance_type', 100); // oil_change, tire_rotation, etc.
            $table->text('description')->nullable();
            
            // Interval configuration
            $table->enum('interval_type', ['mileage', 'time', 'engine_hours']);
            $table->unsignedInteger('interval_value'); // e.g., 15000 miles, 6 months, 500 hours
            
            // Last performed tracking
            $table->timestamp('last_performed_at')->nullable();
            $table->decimal('last_performed_mileage', 12, 2)->nullable(); // in meters
            $table->decimal('last_performed_engine_hours', 10, 2)->nullable();
            
            // Next due calculations (computed fields)
            $table->timestamp('next_due_at')->nullable();
            $table->decimal('next_due_mileage', 12, 2)->nullable(); // in meters
            $table->decimal('next_due_engine_hours', 10, 2)->nullable();
            
            // Priority and status
            $table->enum('priority', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->enum('status', ['active', 'inactive', 'completed'])->default('active');
            
            // Cost estimation
            $table->decimal('estimated_cost', 10, 2)->nullable();
            $table->text('notes')->nullable();
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['entity_type', 'entity_id', 'status']);
            $table->index(['maintenance_type', 'status']);
            $table->index(['next_due_at', 'status']);
            $table->index(['priority', 'status']);
            $table->index('interval_type');
            
            // Unique constraint to prevent duplicate schedules
            $table->unique([
                'entity_type',
                'entity_id', 
                'maintenance_type'
            ], 'maintenance_schedules_unique');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('maintenance_schedules');
    }
};
```

## Table Structure

### Columns

| Column | Type | Description |
|--------|------|-------------|
| `id` | bigint | Primary key |
| `entity_type` | varchar(255) | Polymorphic entity type (App\Models\Truck, etc.) |
| `entity_id` | bigint | Polymorphic entity ID |
| `maintenance_type` | varchar(100) | Type of maintenance (oil_change, tire_rotation, etc.) |
| `description` | text | Detailed description of maintenance |
| `interval_type` | enum | Interval basis (mileage, time, engine_hours) |
| `interval_value` | integer | Interval amount |
| `last_performed_at` | timestamp | When maintenance was last performed |
| `last_performed_mileage` | decimal(12,2) | Mileage when last performed (meters) |
| `last_performed_engine_hours` | decimal(10,2) | Engine hours when last performed |
| `next_due_at` | timestamp | When maintenance is next due |
| `next_due_mileage` | decimal(12,2) | Mileage when next due (meters) |
| `next_due_engine_hours` | decimal(10,2) | Engine hours when next due |
| `priority` | enum | Priority level (low, medium, high, critical) |
| `status` | enum | Schedule status (active, inactive, completed) |
| `estimated_cost` | decimal(10,2) | Estimated maintenance cost |
| `notes` | text | Additional notes |
| `created_at` | timestamp | Laravel timestamp |
| `updated_at` | timestamp | Laravel timestamp |

### Indexes

1. **Entity Index**: `(entity_type, entity_id, status)` - Primary entity queries
2. **Type Index**: `(maintenance_type, status)` - Maintenance type filtering
3. **Due Date Index**: `(next_due_at, status)` - Due date queries
4. **Priority Index**: `(priority, status)` - Priority filtering
5. **Interval Index**: `(interval_type)` - Interval type queries

### Constraints

- **Unique Constraint**: Prevents duplicate maintenance schedules for same entity and type
- **Polymorphic Index**: Optimizes polymorphic relationship queries

## Maintenance Types

### Standard Maintenance Types

```php
// Common maintenance types
const MAINTENANCE_TYPES = [
    'oil_change' => 'Oil Change',
    'tire_rotation' => 'Tire Rotation',
    'brake_inspection' => 'Brake Inspection',
    'annual_inspection' => 'Annual Inspection',
    'engine_service' => 'Engine Service',
    'transmission_service' => 'Transmission Service',
    'coolant_flush' => 'Coolant Flush',
    'air_filter_replacement' => 'Air Filter Replacement',
    'fuel_filter_replacement' => 'Fuel Filter Replacement',
    'belt_replacement' => 'Belt Replacement',
];
```

### Interval Types

1. **Mileage**: Based on vehicle odometer readings (stored in meters)
2. **Time**: Based on calendar time (months, days)
3. **Engine Hours**: Based on engine operating hours

## Usage Examples

### Create Maintenance Schedule

```php
use App\Models\MaintenanceSchedule;
use App\Models\Truck;

$truck = Truck::find(1);

MaintenanceSchedule::create([
    'entity_type' => get_class($truck),
    'entity_id' => $truck->id,
    'maintenance_type' => 'oil_change',
    'description' => 'Regular oil change service',
    'interval_type' => 'mileage',
    'interval_value' => 24140, // 15,000 miles in meters
    'priority' => 'high',
    'estimated_cost' => 150.00,
]);
```

### Query Overdue Maintenance

```php
// Get all overdue maintenance
$overdue = MaintenanceSchedule::where('status', 'active')
    ->where('next_due_at', '<', now())
    ->orWhere(function ($query) {
        $query->whereNotNull('next_due_mileage')
              ->whereHas('entity', function ($q) {
                  $q->whereRaw('current_mileage >= next_due_mileage');
              });
    })
    ->get();
```

### Update After Maintenance

```php
$schedule = MaintenanceSchedule::find(1);

// Update after performing maintenance
$schedule->update([
    'last_performed_at' => now(),
    'last_performed_mileage' => $currentMileage,
    'next_due_at' => now()->addMonths(6), // For time-based
    'next_due_mileage' => $currentMileage + $schedule->interval_value,
]);
```

## Business Logic

### Due Date Calculation

```php
// Example calculation logic
public function calculateNextDue(): void
{
    switch ($this->interval_type) {
        case 'mileage':
            $this->next_due_mileage = $this->last_performed_mileage + $this->interval_value;
            break;
            
        case 'time':
            $this->next_due_at = $this->last_performed_at->addMonths($this->interval_value);
            break;
            
        case 'engine_hours':
            $this->next_due_engine_hours = $this->last_performed_engine_hours + $this->interval_value;
            break;
    }
}
```

### Status Determination

```php
public function getStatusAttribute(): string
{
    if ($this->isOverdue()) {
        return 'overdue';
    }
    
    if ($this->isDueSoon()) {
        return 'due_soon';
    }
    
    return 'current';
}

public function isOverdue(): bool
{
    return match($this->interval_type) {
        'time' => $this->next_due_at && $this->next_due_at->isPast(),
        'mileage' => $this->next_due_mileage && $this->entity->current_mileage >= $this->next_due_mileage,
        'engine_hours' => $this->next_due_engine_hours && $this->entity->current_engine_hours >= $this->next_due_engine_hours,
    };
}
```

## Performance Considerations

### Query Optimization

1. **Use appropriate indexes** for your query patterns
2. **Include status in WHERE clauses** to filter inactive schedules
3. **Use eager loading** for polymorphic relationships
4. **Consider caching** for frequently accessed calculations

### Maintenance Calculations

1. **Batch updates** for multiple schedules
2. **Queue calculations** for large fleets
3. **Cache results** for dashboard queries
4. **Use database functions** for complex calculations

## Monitoring

### Maintenance Dashboard Queries

```sql
-- Overdue maintenance count by priority
SELECT priority, COUNT(*) as count
FROM maintenance_schedules 
WHERE status = 'active' 
  AND next_due_at < NOW()
GROUP BY priority;

-- Upcoming maintenance (next 30 days)
SELECT maintenance_type, COUNT(*) as count
FROM maintenance_schedules 
WHERE status = 'active' 
  AND next_due_at BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 30 DAY)
GROUP BY maintenance_type;
```

### Performance Monitoring

```sql
-- Check index usage
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename = 'maintenance_schedules'
ORDER BY idx_scan DESC;
```

## Troubleshooting

### Common Issues

1. **Polymorphic Relationship Errors**: Verify entity_type matches model class
2. **Due Date Calculation Issues**: Check interval_type and values
3. **Performance Issues**: Ensure proper indexing and query optimization
4. **Duplicate Schedules**: Check unique constraint compliance

### Solutions

1. **Use Model::class** for entity_type to avoid typos
2. **Implement calculation methods** in model for consistency
3. **Monitor query performance** and add indexes as needed
4. **Handle duplicates** gracefully in application logic
