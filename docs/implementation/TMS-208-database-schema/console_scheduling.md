# Console Scheduling Configuration

## Overview

This document outlines the Laravel scheduler configuration for telematic database maintenance tasks, including partition management and database optimization.

## Scheduler Configuration

**Location**: `app/Console/Kernel.php`

```php
<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class <PERSON>el extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Telematic partition management - Monthly on 1st at 2:00 AM
        $schedule->command('telematic:manage-partitions')
            ->monthly()
            ->at('02:00')
            ->timezone('UTC')
            ->emailOutputOnFailure('<EMAIL>')
            ->before(function () {
                \Log::info('Starting monthly telematic partition management');
            })
            ->after(function () {
                \Log::info('Completed monthly telematic partition management');
            });

        // Partition health check - Weekly on Sundays at 1:00 AM
        $schedule->command('telematic:manage-partitions --list')
            ->weekly()
            ->sundays()
            ->at('01:00')
            ->timezone('UTC')
            ->emailOutputOnFailure('<EMAIL>');

        // Database statistics update - Daily at 3:00 AM
        $schedule->call(function () {
            \DB::statement('ANALYZE telematic_readings');
            \DB::statement('ANALYZE maintenance_schedules');
            \DB::statement('ANALYZE maintenance_records');
            \DB::statement('ANALYZE telematic_sync_states');
        })
            ->daily()
            ->at('03:00')
            ->timezone('UTC')
            ->name('update-database-statistics');

        // Cleanup old sync states - Weekly on Mondays at 4:00 AM
        $schedule->call(function () {
            // Clean up old error logs in sync states
            \DB::table('telematic_sync_states')
                ->where('last_error_at', '<', now()->subDays(30))
                ->update([
                    'last_error' => null,
                    'last_error_at' => null
                ]);
        })
            ->weekly()
            ->mondays()
            ->at('04:00')
            ->timezone('UTC')
            ->name('cleanup-sync-states');

        // Vacuum and reindex - Weekly on Saturdays at 1:00 AM
        $schedule->call(function () {
            // Vacuum analyze for better performance
            \DB::statement('VACUUM ANALYZE telematic_readings');
            \DB::statement('VACUUM ANALYZE maintenance_schedules');
            \DB::statement('VACUUM ANALYZE maintenance_records');
        })
            ->weekly()
            ->saturdays()
            ->at('01:00')
            ->timezone('UTC')
            ->name('database-maintenance')
            ->withoutOverlapping(120); // 2 hour timeout
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
```

## Cron Configuration

### System Crontab

Add to your system crontab (`crontab -e`):

```bash
# Laravel Scheduler - runs every minute
* * * * * cd /path/to/your/project && php artisan schedule:run >> /dev/null 2>&1

# Backup cron jobs (if Laravel scheduler fails)
# Monthly partition management - 1st of month at 2:00 AM
0 2 1 * * cd /path/to/your/project && php artisan telematic:manage-partitions >> /var/log/telematic-partitions.log 2>&1

# Weekly health check - Sundays at 1:00 AM
0 1 * * 0 cd /path/to/your/project && php artisan telematic:manage-partitions --list >> /var/log/telematic-health.log 2>&1
```

### Docker Environment

For Docker deployments, add to your scheduler container:

```dockerfile
# Dockerfile for scheduler service
FROM php:8.4-cli

# Install dependencies and copy application
COPY . /app
WORKDIR /app

# Install cron
RUN apt-get update && apt-get install -y cron

# Add Laravel scheduler to crontab
RUN echo "* * * * * cd /app && php artisan schedule:run >> /var/log/cron.log 2>&1" | crontab -

# Start cron daemon
CMD ["cron", "-f"]
```

### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  scheduler:
    build: .
    container_name: telematic_scheduler
    volumes:
      - .:/app
      - ./storage/logs:/var/log
    environment:
      - APP_ENV=production
    depends_on:
      - database
    restart: unless-stopped
```

## Monitoring and Logging

### Schedule Monitoring

```php
// Add to AppServiceProvider or create a dedicated service
use Illuminate\Console\Events\ScheduledTaskStarting;
use Illuminate\Console\Events\ScheduledTaskFinished;
use Illuminate\Console\Events\ScheduledTaskFailed;

public function boot(): void
{
    Event::listen(ScheduledTaskStarting::class, function ($event) {
        Log::info('Scheduled task starting', [
            'task' => $event->task->description ?? $event->task->command,
            'expression' => $event->task->expression,
        ]);
    });

    Event::listen(ScheduledTaskFinished::class, function ($event) {
        Log::info('Scheduled task finished', [
            'task' => $event->task->description ?? $event->task->command,
            'runtime' => $event->runtime,
        ]);
    });

    Event::listen(ScheduledTaskFailed::class, function ($event) {
        Log::error('Scheduled task failed', [
            'task' => $event->task->description ?? $event->task->command,
            'exception' => $event->exception->getMessage(),
        ]);
    });
}
```

### Health Check Endpoint

```php
// routes/web.php or api.php
Route::get('/health/scheduler', function () {
    $lastRun = Cache::get('schedule:last_run');
    $isHealthy = $lastRun && $lastRun > now()->subMinutes(2);
    
    return response()->json([
        'status' => $isHealthy ? 'healthy' : 'unhealthy',
        'last_run' => $lastRun,
        'message' => $isHealthy ? 'Scheduler is running' : 'Scheduler may be down',
    ], $isHealthy ? 200 : 503);
});

// Add to schedule in Kernel.php
$schedule->call(function () {
    Cache::put('schedule:last_run', now(), now()->addMinutes(5));
})->everyMinute();
```

## Task Definitions

### Partition Management Tasks

```php
// Monthly partition management
$schedule->command('telematic:manage-partitions')
    ->monthly()
    ->at('02:00')
    ->timezone('UTC')
    ->emailOutputOnFailure('<EMAIL>')
    ->onSuccess(function () {
        // Log success metrics
        Log::info('Partition management completed successfully');
    })
    ->onFailure(function () {
        // Send alert
        Mail::to('<EMAIL>')->send(new PartitionManagementFailed());
    });
```

### Database Maintenance Tasks

```php
// Database statistics update
$schedule->call(function () {
    $tables = [
        'telematic_readings',
        'maintenance_schedules', 
        'maintenance_records',
        'telematic_sync_states'
    ];
    
    foreach ($tables as $table) {
        DB::statement("ANALYZE {$table}");
        Log::info("Updated statistics for table: {$table}");
    }
})
    ->daily()
    ->at('03:00')
    ->timezone('UTC')
    ->name('update-database-statistics');
```

### Cleanup Tasks

```php
// Cleanup old data
$schedule->call(function () {
    // Clean up old telematic sync error logs
    $cleaned = DB::table('telematic_sync_states')
        ->where('last_error_at', '<', now()->subDays(30))
        ->whereNotNull('last_error')
        ->update([
            'last_error' => null,
            'last_error_at' => null
        ]);
    
    Log::info("Cleaned up {$cleaned} old sync error logs");
    
    // Clean up old maintenance record attachments
    $oldRecords = DB::table('maintenance_records')
        ->where('created_at', '<', now()->subYears(2))
        ->whereNotNull('attachments')
        ->get();
    
    foreach ($oldRecords as $record) {
        // Archive or delete old attachments
        // Implementation depends on your storage strategy
    }
})
    ->weekly()
    ->mondays()
    ->at('04:00')
    ->timezone('UTC')
    ->name('cleanup-old-data');
```

## Performance Optimization

### Vacuum and Reindex

```php
// Weekly database maintenance
$schedule->call(function () {
    $startTime = microtime(true);
    
    // Vacuum analyze for better performance
    DB::statement('VACUUM ANALYZE telematic_readings');
    DB::statement('VACUUM ANALYZE maintenance_schedules');
    DB::statement('VACUUM ANALYZE maintenance_records');
    
    $duration = microtime(true) - $startTime;
    
    Log::info('Database vacuum completed', [
        'duration_seconds' => round($duration, 2),
        'tables_processed' => 3,
    ]);
})
    ->weekly()
    ->saturdays()
    ->at('01:00')
    ->timezone('UTC')
    ->name('database-vacuum')
    ->withoutOverlapping(120); // 2 hour timeout
```

### Index Maintenance

```php
// Monthly index maintenance
$schedule->call(function () {
    // Reindex heavily used indexes
    $indexes = [
        'telematic_readings_entity_type_time_idx',
        'telematic_readings_type_time_idx',
        'maintenance_schedules_entity_status_idx',
    ];
    
    foreach ($indexes as $index) {
        try {
            DB::statement("REINDEX INDEX {$index}");
            Log::info("Reindexed: {$index}");
        } catch (\Exception $e) {
            Log::error("Failed to reindex {$index}: " . $e->getMessage());
        }
    }
})
    ->monthly()
    ->at('05:00')
    ->timezone('UTC')
    ->name('reindex-maintenance');
```

## Error Handling and Alerts

### Email Notifications

```php
// Configure email notifications for critical tasks
$schedule->command('telematic:manage-partitions')
    ->monthly()
    ->emailOutputOnFailure('<EMAIL>')
    ->emailOutputTo('<EMAIL>'); // Send all output
```

### Slack Notifications

```php
// Send Slack notifications for failures
$schedule->command('telematic:manage-partitions')
    ->monthly()
    ->onFailure(function () {
        // Send to Slack
        Http::post(config('services.slack.webhook'), [
            'text' => 'Telematic partition management failed!',
            'channel' => '#alerts',
            'username' => 'Laravel Scheduler',
        ]);
    });
```

### Custom Monitoring

```php
// Custom monitoring integration
$schedule->call(function () {
    // Check partition health
    $partitions = DB::select("
        SELECT count(*) as count 
        FROM pg_tables 
        WHERE tablename LIKE 'telematic_readings_%'
    ");
    
    $partitionCount = $partitions[0]->count ?? 0;
    
    // Send metrics to monitoring service
    Http::post('https://monitoring.example.com/metrics', [
        'metric' => 'telematic.partitions.count',
        'value' => $partitionCount,
        'timestamp' => now()->timestamp,
    ]);
})
    ->hourly()
    ->name('partition-monitoring');
```

## Troubleshooting

### Common Issues

1. **Scheduler Not Running**: Check cron configuration
2. **Task Overlapping**: Use `withoutOverlapping()` method
3. **Memory Issues**: Monitor memory usage in long-running tasks
4. **Timezone Issues**: Always specify timezone explicitly

### Debug Commands

```bash
# Test scheduler configuration
php artisan schedule:list

# Run scheduler manually
php artisan schedule:run

# Test specific command
php artisan schedule:test

# Check last run times
php artisan schedule:list --next
```

### Monitoring Commands

```bash
# Check if scheduler is running
ps aux | grep "schedule:run"

# Check cron logs
tail -f /var/log/cron.log

# Check Laravel logs
tail -f storage/logs/laravel.log
```
