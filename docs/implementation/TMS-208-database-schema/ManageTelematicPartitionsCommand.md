# Manage Telematic Partitions Command

## Overview

This command manages PostgreSQL declarative partitions for the `telematic_readings` table, including automatic partition creation, cleanup of old partitions, and maintenance operations.

## Command File

**Location**: `app/Console/Commands/ManageTelematicPartitionsCommand.php`

```php
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ManageTelematicPartitionsCommand extends Command
{
    protected $signature = 'telematic:manage-partitions
                           {--create-future : Create future partitions}
                           {--drop-old : Drop old partitions}
                           {--retention-months=12 : Number of months to retain}
                           {--future-months=3 : Number of future months to create}
                           {--dry-run : Show what would be done without executing}
                           {--list : List existing partitions}';

    protected $description = 'Manage telematic_readings table partitions';

    public function handle(): int
    {
        $this->info('Managing telematic_readings partitions...');

        if ($this->option('list')) {
            return $this->listPartitions();
        }

        if ($this->option('dry-run')) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        $success = true;

        // Create future partitions
        if ($this->option('create-future') || !$this->hasOptions()) {
            $success &= $this->createFuturePartitions();
        }

        // Drop old partitions
        if ($this->option('drop-old') || !$this->hasOptions()) {
            $success &= $this->dropOldPartitions();
        }

        return $success ? self::SUCCESS : self::FAILURE;
    }

    private function hasOptions(): bool
    {
        return $this->option('create-future') || 
               $this->option('drop-old') || 
               $this->option('list');
    }

    private function listPartitions(): int
    {
        $partitions = $this->getExistingPartitions();

        if ($partitions->isEmpty()) {
            $this->info('No partitions found.');
            return self::SUCCESS;
        }

        $this->table(
            ['Partition Name', 'Start Date', 'End Date', 'Size', 'Row Count'],
            $partitions->map(function ($partition) {
                return [
                    $partition->partition_name,
                    $partition->start_date ?? 'N/A',
                    $partition->end_date ?? 'N/A',
                    $this->formatBytes($partition->size_bytes ?? 0),
                    number_format($partition->row_count ?? 0),
                ];
            })->toArray()
        );

        return self::SUCCESS;
    }

    private function createFuturePartitions(): bool
    {
        $futureMonths = (int) $this->option('future-months');
        $startDate = now()->startOfMonth();
        $created = 0;

        $this->info("Creating partitions for next {$futureMonths} months...");

        for ($i = 0; $i < $futureMonths; $i++) {
            $partitionStart = $startDate->copy()->addMonths($i);
            $partitionEnd = $partitionStart->copy()->addMonth();
            $partitionName = 'telematic_readings_' . $partitionStart->format('Y_m');

            if ($this->partitionExists($partitionName)) {
                $this->line("  Partition {$partitionName} already exists");
                continue;
            }

            if ($this->option('dry-run')) {
                $this->line("  Would create partition: {$partitionName}");
                $created++;
                continue;
            }

            try {
                DB::statement("
                    CREATE TABLE {$partitionName} PARTITION OF telematic_readings
                    FOR VALUES FROM ('{$partitionStart->toDateString()}') 
                    TO ('{$partitionEnd->toDateString()}')
                ");

                $this->info("  Created partition: {$partitionName}");
                $created++;
            } catch (\Exception $e) {
                $this->error("  Failed to create partition {$partitionName}: " . $e->getMessage());
                return false;
            }
        }

        $this->info("Created {$created} new partitions");
        return true;
    }

    private function dropOldPartitions(): bool
    {
        $retentionMonths = (int) $this->option('retention-months');
        $cutoffDate = now()->subMonths($retentionMonths)->startOfMonth();
        $dropped = 0;

        $this->info("Dropping partitions older than {$retentionMonths} months (before {$cutoffDate->toDateString()})...");

        $oldPartitions = $this->getOldPartitions($cutoffDate);

        if ($oldPartitions->isEmpty()) {
            $this->info('No old partitions to drop');
            return true;
        }

        foreach ($oldPartitions as $partition) {
            if ($this->option('dry-run')) {
                $this->line("  Would drop partition: {$partition->partition_name}");
                $dropped++;
                continue;
            }

            if ($this->confirm("Drop partition {$partition->partition_name}? This will permanently delete data.")) {
                try {
                    DB::statement("DROP TABLE {$partition->partition_name}");
                    $this->info("  Dropped partition: {$partition->partition_name}");
                    $dropped++;
                } catch (\Exception $e) {
                    $this->error("  Failed to drop partition {$partition->partition_name}: " . $e->getMessage());
                    return false;
                }
            }
        }

        $this->info("Dropped {$dropped} old partitions");
        return true;
    }

    private function getExistingPartitions()
    {
        return collect(DB::select("
            SELECT 
                pt.schemaname,
                pt.tablename as partition_name,
                pg_get_expr(c.relpartbound, c.oid) as partition_bound,
                pg_size_pretty(pg_total_relation_size(c.oid)) as size,
                pg_total_relation_size(c.oid) as size_bytes,
                (SELECT count(*) FROM information_schema.tables WHERE table_name = pt.tablename) as exists,
                CASE 
                    WHEN pg_get_expr(c.relpartbound, c.oid) ~ 'FOR VALUES FROM' THEN
                        regexp_replace(
                            regexp_replace(pg_get_expr(c.relpartbound, c.oid), '.*FROM \\(''([^'']+)''\\).*', '\\1'),
                            'T.*', ''
                        )
                END as start_date,
                CASE 
                    WHEN pg_get_expr(c.relpartbound, c.oid) ~ 'TO' THEN
                        regexp_replace(
                            regexp_replace(pg_get_expr(c.relpartbound, c.oid), '.*TO \\(''([^'']+)''\\).*', '\\1'),
                            'T.*', ''
                        )
                END as end_date
            FROM pg_tables pt
            JOIN pg_class c ON c.relname = pt.tablename
            WHERE pt.tablename LIKE 'telematic_readings_%'
            ORDER BY pt.tablename
        "));
    }

    private function getOldPartitions(Carbon $cutoffDate)
    {
        return $this->getExistingPartitions()->filter(function ($partition) use ($cutoffDate) {
            if (!$partition->start_date) {
                return false;
            }

            try {
                $partitionDate = Carbon::parse($partition->start_date);
                return $partitionDate->lt($cutoffDate);
            } catch (\Exception $e) {
                $this->warn("Could not parse date for partition {$partition->partition_name}");
                return false;
            }
        });
    }

    private function partitionExists(string $partitionName): bool
    {
        $result = DB::select("
            SELECT 1 FROM pg_tables 
            WHERE tablename = ?
        ", [$partitionName]);

        return !empty($result);
    }

    private function formatBytes(int $bytes): string
    {
        if ($bytes === 0) {
            return '0 B';
        }

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $power = floor(log($bytes, 1024));
        
        return round($bytes / (1024 ** $power), 2) . ' ' . $units[$power];
    }
}
```

## Command Options

### Available Options

| Option | Description | Default |
|--------|-------------|---------|
| `--create-future` | Create future partitions | Auto if no options |
| `--drop-old` | Drop old partitions | Auto if no options |
| `--retention-months` | Number of months to retain | 12 |
| `--future-months` | Number of future months to create | 3 |
| `--dry-run` | Show what would be done without executing | false |
| `--list` | List existing partitions | false |

### Usage Examples

```bash
# List existing partitions
php artisan telematic:manage-partitions --list

# Create future partitions (dry run)
php artisan telematic:manage-partitions --create-future --dry-run

# Drop old partitions with 6-month retention
php artisan telematic:manage-partitions --drop-old --retention-months=6

# Full maintenance (create future + drop old)
php artisan telematic:manage-partitions

# Create 6 months of future partitions
php artisan telematic:manage-partitions --create-future --future-months=6
```

## Partition Management Strategy

### Automatic Creation

- **Default**: Creates 3 months of future partitions
- **Timing**: Run monthly via scheduler
- **Naming**: `telematic_readings_YYYY_MM` format
- **Range**: Monthly partitions from start to end of month

### Automatic Cleanup

- **Default**: Retains 12 months of data
- **Safety**: Requires confirmation for data deletion
- **Configurable**: Retention period can be adjusted
- **Logging**: All operations are logged

### Partition Monitoring

```php
// Check partition health
$partitions = DB::select("
    SELECT 
        tablename,
        pg_size_pretty(pg_total_relation_size(tablename)) as size,
        (SELECT reltuples::bigint FROM pg_class WHERE relname = tablename) as estimated_rows
    FROM pg_tables 
    WHERE tablename LIKE 'telematic_readings_%'
    ORDER BY tablename DESC
    LIMIT 12
");
```

## Scheduling Configuration

### Laravel Scheduler

Add to `app/Console/Kernel.php`:

```php
protected function schedule(Schedule $schedule): void
{
    // Monthly partition management
    $schedule->command('telematic:manage-partitions')
        ->monthly()
        ->at('02:00')
        ->emailOutputOnFailure('<EMAIL>');
    
    // Weekly partition health check
    $schedule->command('telematic:manage-partitions --list')
        ->weekly()
        ->sundays()
        ->at('01:00');
}
```

### Cron Configuration

```bash
# Monthly partition management
0 2 1 * * cd /path/to/app && php artisan telematic:manage-partitions

# Weekly health check
0 1 * * 0 cd /path/to/app && php artisan telematic:manage-partitions --list
```

## Performance Considerations

### Partition Pruning

```sql
-- Verify partition pruning is working
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM telematic_readings 
WHERE recorded_at >= '2024-01-01' 
  AND recorded_at < '2024-02-01';

-- Should show "Partitions removed: X" in the plan
```

### Index Management

```php
// Ensure indexes exist on new partitions
private function createIndexesOnPartition(string $partitionName): void
{
    $indexes = [
        "CREATE INDEX IF NOT EXISTS {$partitionName}_entity_type_time_idx 
         ON {$partitionName} (external_entity_mapping_id, reading_type, recorded_at)",
        
        "CREATE INDEX IF NOT EXISTS {$partitionName}_type_time_idx 
         ON {$partitionName} (reading_type, recorded_at)",
        
        "CREATE INDEX IF NOT EXISTS {$partitionName}_time_idx 
         ON {$partitionName} (recorded_at)",
    ];
    
    foreach ($indexes as $sql) {
        DB::statement($sql);
    }
}
```

## Monitoring and Alerting

### Health Checks

```php
public function checkPartitionHealth(): array
{
    $issues = [];
    
    // Check for missing future partitions
    $futurePartitions = $this->getFuturePartitionsNeeded();
    if ($futurePartitions > 0) {
        $issues[] = "Missing {$futurePartitions} future partitions";
    }
    
    // Check for oversized partitions
    $largePartitions = $this->getLargePartitions();
    if ($largePartitions->isNotEmpty()) {
        $issues[] = "Large partitions detected: " . $largePartitions->pluck('partition_name')->join(', ');
    }
    
    return $issues;
}
```

### Alerting

```php
// In a monitoring job
$issues = $this->checkPartitionHealth();
if (!empty($issues)) {
    // Send alert
    Mail::to('<EMAIL>')->send(new PartitionHealthAlert($issues));
}
```

## Troubleshooting

### Common Issues

1. **Partition Creation Fails**: Check PostgreSQL permissions
2. **Data in Wrong Partition**: Verify partition constraints
3. **Query Performance**: Ensure partition pruning is working
4. **Disk Space**: Monitor partition sizes

### Debug Commands

```bash
# Check partition constraints
SELECT tablename, pg_get_expr(c.relpartbound, c.oid) as constraint
FROM pg_tables pt
JOIN pg_class c ON c.relname = pt.tablename
WHERE tablename LIKE 'telematic_readings_%';

# Check partition pruning
EXPLAIN (ANALYZE, BUFFERS) 
SELECT count(*) FROM telematic_readings 
WHERE recorded_at >= '2024-01-01' AND recorded_at < '2024-02-01';

# Monitor partition sizes
SELECT 
    tablename,
    pg_size_pretty(pg_total_relation_size(tablename)) as size
FROM pg_tables 
WHERE tablename LIKE 'telematic_readings_%'
ORDER BY pg_total_relation_size(tablename) DESC;
```
