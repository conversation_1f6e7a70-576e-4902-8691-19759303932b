# TMS-208: Database Schema Design and Implementation

## Overview

This implementation provides the foundational database schema for the telematic data synchronization architecture. It includes tables for storing telematic readings, maintenance schedules, sync states, and maintenance records, along with automated partition management for performance optimization.

## Story Details

- **Epic**: TMS-207 - Telematic Data Synchronization Architecture Implementation
- **Story**: TMS-208 - Database Schema Design and Implementation for Telematic Data
- **Story Points**: 13
- **Phase**: 1 (Foundation - Weeks 1-2)

## Implementation Files

### Database Migrations

- [create_telematic_readings_table.md](./create_telematic_readings_table.md) - Main telematic readings table with partitioning
- [create_maintenance_schedules_table.md](./create_maintenance_schedules_table.md) - Maintenance scheduling table
- [create_telematic_sync_states_table.md](./create_telematic_sync_states_table.md) - Sync state tracking table
- [create_maintenance_records_table.md](./create_maintenance_records_table.md) - Maintenance records table

### Console Commands

- [ManageTelematicPartitionsCommand.md](./ManageTelematicPartitionsCommand.md) - Partition management command

### Scheduling Configuration

- [console_scheduling.md](./console_scheduling.md) - Laravel scheduler configuration

## Database Schema Overview

### 1. telematic_readings Table
**Purpose**: Store time-series telematic data from various providers

**Key Features**:
- High-precision timestamps for accurate data recording
- Provider-agnostic design through external_entity_mappings
- JSON metadata for flexible provider-specific data
- Time-based partitioning for performance at scale
- Unique constraints to prevent duplicate readings

**Columns**:
- `id` - Primary key
- `external_entity_mapping_id` - Foreign key to external_entity_mappings
- `reading_type` - Type of reading (odometer, engine_hours, fuel_level, etc.)
- `value` - Numeric value with high precision
- `unit` - Unit of measurement
- `recorded_at` - High precision timestamp when reading was recorded
- `synced_at` - When reading was synced to our system
- `metadata` - JSONB field for provider-specific data (PostgreSQL optimized)

### 2. maintenance_schedules Table
**Purpose**: Define maintenance intervals and scheduling for trucks and trailers

**Key Features**:
- Polymorphic relationships to support multiple entity types
- Multiple interval types (mileage, time, engine hours)
- Priority-based scheduling
- Automatic due date calculations

**Columns**:
- `id` - Primary key
- `entity_type` - Polymorphic entity type (App\Models\Truck, etc.)
- `entity_id` - Polymorphic entity ID
- `maintenance_type` - Type of maintenance (oil_change, tire_rotation, etc.)
- `interval_type` - Interval basis (mileage, time, engine_hours)
- `interval_value` - Interval amount
- `last_performed_*` - Last maintenance tracking
- `next_due_*` - Next due calculations
- `priority` - Maintenance priority level

### 3. telematic_sync_states Table
**Purpose**: Track cursor positions and sync state for incremental synchronization

**Key Features**:
- Provider-specific cursor tracking
- Sync performance metrics
- Error tracking and recovery
- Status monitoring

**Columns**:
- `id` - Primary key
- `provider` - Telematic provider name
- `sync_type` - Type of sync operation
- `last_cursor` - Cursor for incremental sync
- `last_sync_at` - Last sync timestamp
- `records_synced` - Performance tracking
- `errors_count` - Error monitoring
- `status` - Current sync status

### 4. maintenance_records Table
**Purpose**: Track completed maintenance activities

**Key Features**:
- Links to maintenance schedules
- Cost tracking (parts and labor)
- Quality ratings and vendor information
- File attachments support
- Parts usage tracking

**Columns**:
- `id` - Primary key
- `maintenance_schedule_id` - Foreign key to schedules
- `performed_at` - When maintenance was performed
- `performed_mileage` - Vehicle mileage at service
- `cost` - Total maintenance cost
- `vendor` - Service provider
- `attachments` - JSON array of file attachments
- `parts_used` - JSON array of parts information

## Performance Optimizations

### Indexing Strategy
- Composite indexes for common query patterns
- Time-based indexes for efficient date range queries
- Foreign key indexes for relationship queries

### Partitioning (PostgreSQL Declarative Partitioning)
- Monthly partitioning for telematic_readings table using PostgreSQL declarative partitioning
- Automated partition creation and cleanup via custom command
- Configurable data retention policies
- Native PostgreSQL partition pruning for optimal query performance

## Installation Instructions

### 1. Create Database Migrations

Create the following migration files using the provided specifications:

```bash
# Create telematic readings table migration
php artisan make:migration create_telematic_readings_table
# Copy content from: create_telematic_readings_table.md

# Create maintenance schedules table migration
php artisan make:migration create_maintenance_schedules_table
# Copy content from: create_maintenance_schedules_table.md

# Create telematic sync states table migration
php artisan make:migration create_telematic_sync_states_table
# Copy content from: create_telematic_sync_states_table.md

# Create maintenance records table migration
php artisan make:migration create_maintenance_records_table
# Copy content from: create_maintenance_records_table.md

# Run migrations
php artisan migrate

# Verify tables were created
php artisan migrate:status
```

### 2. Install Partition Management Command

```bash
# Create partition management command
php artisan make:command ManageTelematicPartitionsCommand
# Copy content from: ManageTelematicPartitionsCommand.md

# Test the command
php artisan telematic:manage-partitions --dry-run
```

### 3. Configure Automated Partition Management

Update `app/Console/Kernel.php` with scheduling configuration:

```bash
# See: console_scheduling.md for complete configuration
```

### 4. Verify Schema

```bash
# Check table structure
php artisan tinker
>>> Schema::hasTable('telematic_readings');
>>> Schema::hasTable('maintenance_schedules');
>>> Schema::hasTable('telematic_sync_states');
>>> Schema::hasTable('maintenance_records');
```

## Configuration Options

### Partition Management
- **Retention Period**: Default 12 months (configurable via command option)
- **Partition Creation**: 3 months in advance
- **Cleanup Schedule**: Monthly automated cleanup

### Data Retention
- **Telematic Readings**: Configurable via partition retention
- **Maintenance Records**: Permanent retention
- **Sync States**: Permanent retention with metadata cleanup

## Monitoring and Maintenance

### Partition Health Check
```bash
# Check current partitions
php artisan telematic:manage-partitions --dry-run

# Manual partition creation
php artisan telematic:manage-partitions --create-future

# Manual cleanup
php artisan telematic:manage-partitions --drop-old --retention-months=6
```

### Database Performance
- Monitor partition sizes and query performance
- Adjust retention policies based on storage requirements
- Review index usage and optimize as needed

## Dependencies

### Required Laravel Features
- Laravel Migrations
- Laravel Console Commands
- Laravel Task Scheduling

### Database Requirements
- PostgreSQL 17+ (for declarative partitioning and JSONB support)
- Sufficient storage for time-series data
- Proper backup strategy for partitioned tables
- pg_partman extension (optional, for advanced partition management)

## Testing

### Migration Testing
```bash
# Test migrations
php artisan migrate:fresh
php artisan migrate:rollback
php artisan migrate
```

### Partition Testing
```bash
# Test partition management
php artisan telematic:manage-partitions --dry-run
```

## Rollback Procedures

### Migration Rollback
```bash
# Rollback specific migrations
php artisan migrate:rollback --step=4
```

### Partition Cleanup
```bash
# Remove all partitions (data loss!)
# Only in development/testing
ALTER TABLE telematic_readings REMOVE PARTITIONING;
```

## Next Steps

After implementing this database schema:

1. **TMS-209**: Implement the service layer architecture
2. **TMS-210**: Create Eloquent models and relationships
3. **TMS-211**: Implement queue job structure

## Support and Troubleshooting

### Common Issues
1. **Partition Creation Fails**: Check MySQL version and partitioning support
2. **Migration Timeout**: Increase timeout for large table operations
3. **Index Performance**: Monitor slow query log and optimize indexes

### Performance Tuning
- Adjust partition size based on data volume
- Monitor query performance and add indexes as needed
- Consider read replicas for reporting queries
