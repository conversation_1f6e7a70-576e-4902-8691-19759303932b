# Create Maintenance Records Table Migration

## Overview

This migration creates the `maintenance_records` table for tracking completed maintenance activities with cost tracking, quality ratings, and file attachments support.

## Migration File

**Location**: `database/migrations/YYYY_MM_DD_HHMMSS_create_maintenance_records_table.php`

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('maintenance_records', function (Blueprint $table) {
            $table->id();
            
            // Link to maintenance schedule
            $table->foreignId('maintenance_schedule_id')
                  ->constrained('maintenance_schedules')
                  ->onDelete('cascade');
            
            // When and where maintenance was performed
            $table->timestamp('performed_at');
            $table->decimal('performed_mileage', 12, 2)->nullable(); // in meters
            $table->decimal('performed_engine_hours', 10, 2)->nullable();
            
            // Service provider information
            $table->string('vendor', 200)->nullable();
            $table->string('technician', 100)->nullable();
            $table->string('service_location', 200)->nullable();
            $table->string('work_order_number', 100)->nullable();
            
            // Cost tracking
            $table->decimal('labor_cost', 10, 2)->default(0);
            $table->decimal('parts_cost', 10, 2)->default(0);
            $table->decimal('other_cost', 10, 2)->default(0);
            $table->decimal('total_cost', 10, 2)->default(0);
            $table->string('currency', 3)->default('USD');
            
            // Quality and completion tracking
            $table->enum('completion_status', [
                'completed',
                'partially_completed',
                'deferred',
                'cancelled'
            ])->default('completed');
            
            $table->unsignedTinyInteger('quality_rating')->nullable(); // 1-5 scale
            $table->text('notes')->nullable();
            $table->text('issues_found')->nullable();
            $table->text('recommendations')->nullable();
            
            // Parts and materials used
            $table->json('parts_used')->nullable(); // Array of parts with quantities and costs
            $table->json('attachments')->nullable(); // Array of file attachments
            
            // Warranty information
            $table->unsignedInteger('warranty_months')->nullable();
            $table->timestamp('warranty_expires_at')->nullable();
            
            // Next maintenance scheduling
            $table->boolean('schedule_next_maintenance')->default(true);
            $table->timestamp('next_maintenance_due')->nullable();
            $table->decimal('next_maintenance_mileage', 12, 2)->nullable();
            
            // Approval and verification
            $table->string('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->boolean('verified')->default(false);
            $table->string('verified_by')->nullable();
            $table->timestamp('verified_at')->nullable();
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['maintenance_schedule_id', 'performed_at']);
            $table->index(['performed_at', 'completion_status']);
            $table->index(['vendor', 'performed_at']);
            $table->index(['completion_status', 'verified']);
            $table->index('warranty_expires_at');
            $table->index('next_maintenance_due');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('maintenance_records');
    }
};
```

## Table Structure

### Columns

| Column | Type | Description |
|--------|------|-------------|
| `id` | bigint | Primary key |
| `maintenance_schedule_id` | bigint | Foreign key to maintenance_schedules |
| `performed_at` | timestamp | When maintenance was performed |
| `performed_mileage` | decimal(12,2) | Vehicle mileage at service (meters) |
| `performed_engine_hours` | decimal(10,2) | Engine hours at service |
| `vendor` | varchar(200) | Service provider name |
| `technician` | varchar(100) | Technician who performed work |
| `service_location` | varchar(200) | Where service was performed |
| `work_order_number` | varchar(100) | Work order reference |
| `labor_cost` | decimal(10,2) | Labor cost |
| `parts_cost` | decimal(10,2) | Parts cost |
| `other_cost` | decimal(10,2) | Other costs |
| `total_cost` | decimal(10,2) | Total maintenance cost |
| `currency` | varchar(3) | Currency code (USD, EUR, etc.) |
| `completion_status` | enum | Completion status |
| `quality_rating` | tinyint | Quality rating (1-5) |
| `notes` | text | General notes |
| `issues_found` | text | Issues discovered during service |
| `recommendations` | text | Technician recommendations |
| `parts_used` | json | Parts and materials used |
| `attachments` | json | File attachments |
| `warranty_months` | integer | Warranty period in months |
| `warranty_expires_at` | timestamp | Warranty expiration date |
| `schedule_next_maintenance` | boolean | Whether to schedule next maintenance |
| `next_maintenance_due` | timestamp | Next maintenance due date |
| `next_maintenance_mileage` | decimal(12,2) | Next maintenance mileage |
| `approved_by` | varchar(255) | Who approved the work |
| `approved_at` | timestamp | When work was approved |
| `verified` | boolean | Whether work was verified |
| `verified_by` | varchar(255) | Who verified the work |
| `verified_at` | timestamp | When work was verified |
| `created_at` | timestamp | Laravel timestamp |
| `updated_at` | timestamp | Laravel timestamp |

### Indexes

1. **Schedule-Date Index**: `(maintenance_schedule_id, performed_at)` - Schedule history
2. **Date-Status Index**: `(performed_at, completion_status)` - Time-based filtering
3. **Vendor Index**: `(vendor, performed_at)` - Vendor performance tracking
4. **Status-Verification Index**: `(completion_status, verified)` - Status queries
5. **Warranty Index**: `(warranty_expires_at)` - Warranty tracking
6. **Next Due Index**: `(next_maintenance_due)` - Scheduling queries

## JSON Field Structures

### Parts Used Structure

```json
{
  "parts": [
    {
      "part_number": "OF-123",
      "description": "Oil Filter",
      "quantity": 1,
      "unit_cost": 15.99,
      "total_cost": 15.99,
      "supplier": "ACME Parts"
    },
    {
      "part_number": "OIL-5W30",
      "description": "5W-30 Motor Oil",
      "quantity": 6,
      "unit": "quarts",
      "unit_cost": 8.50,
      "total_cost": 51.00,
      "supplier": "Oil Co"
    }
  ]
}
```

### Attachments Structure

```json
{
  "files": [
    {
      "filename": "work_order_123.pdf",
      "original_name": "Work Order #123.pdf",
      "path": "maintenance/2024/01/work_order_123.pdf",
      "size": 245760,
      "mime_type": "application/pdf",
      "uploaded_at": "2024-01-15T10:30:00Z"
    },
    {
      "filename": "before_photo.jpg",
      "original_name": "Before Photo.jpg",
      "path": "maintenance/2024/01/before_photo.jpg",
      "size": 1048576,
      "mime_type": "image/jpeg",
      "uploaded_at": "2024-01-15T09:15:00Z"
    }
  ]
}
```

## Usage Examples

### Create Maintenance Record

```php
use App\Models\MaintenanceRecord;
use App\Models\MaintenanceSchedule;

$schedule = MaintenanceSchedule::find(1);

$record = MaintenanceRecord::create([
    'maintenance_schedule_id' => $schedule->id,
    'performed_at' => now(),
    'performed_mileage' => 150000, // meters
    'vendor' => 'Quick Lube Service',
    'technician' => 'John Smith',
    'service_location' => 'Main Shop',
    'work_order_number' => 'WO-2024-001',
    'labor_cost' => 75.00,
    'parts_cost' => 45.99,
    'total_cost' => 120.99,
    'completion_status' => 'completed',
    'quality_rating' => 5,
    'notes' => 'Oil change completed successfully',
    'parts_used' => [
        'parts' => [
            [
                'part_number' => 'OF-123',
                'description' => 'Oil Filter',
                'quantity' => 1,
                'unit_cost' => 15.99,
                'total_cost' => 15.99,
            ],
            [
                'part_number' => 'OIL-5W30',
                'description' => '5W-30 Motor Oil',
                'quantity' => 6,
                'unit' => 'quarts',
                'unit_cost' => 5.00,
                'total_cost' => 30.00,
            ]
        ]
    ],
    'warranty_months' => 6,
    'warranty_expires_at' => now()->addMonths(6),
]);
```

### Query Maintenance History

```php
// Get maintenance history for a vehicle
$history = MaintenanceRecord::whereHas('maintenanceSchedule', function ($query) use ($truck) {
    $query->where('entity_type', get_class($truck))
          ->where('entity_id', $truck->id);
})
->orderBy('performed_at', 'desc')
->get();

// Get records by vendor
$vendorRecords = MaintenanceRecord::where('vendor', 'Quick Lube Service')
    ->where('performed_at', '>=', now()->subYear())
    ->get();

// Get warranty expiring soon
$expiringWarranties = MaintenanceRecord::where('warranty_expires_at', '<=', now()->addDays(30))
    ->where('warranty_expires_at', '>', now())
    ->get();
```

### Cost Analysis

```php
// Monthly maintenance costs
$monthlyCosts = MaintenanceRecord::selectRaw('
    DATE_FORMAT(performed_at, "%Y-%m") as month,
    SUM(total_cost) as total_cost,
    AVG(total_cost) as avg_cost,
    COUNT(*) as record_count
')
->where('performed_at', '>=', now()->subYear())
->where('completion_status', 'completed')
->groupBy('month')
->orderBy('month')
->get();

// Vendor performance analysis
$vendorPerformance = MaintenanceRecord::selectRaw('
    vendor,
    AVG(quality_rating) as avg_rating,
    AVG(total_cost) as avg_cost,
    COUNT(*) as service_count
')
->whereNotNull('vendor')
->whereNotNull('quality_rating')
->groupBy('vendor')
->having('service_count', '>=', 5)
->orderBy('avg_rating', 'desc')
->get();
```

### File Attachment Management

```php
// Add attachment to record
public function addAttachment(string $filename, string $originalName, string $path, int $size, string $mimeType): void
{
    $attachments = $this->attachments ?? ['files' => []];
    
    $attachments['files'][] = [
        'filename' => $filename,
        'original_name' => $originalName,
        'path' => $path,
        'size' => $size,
        'mime_type' => $mimeType,
        'uploaded_at' => now()->toISOString(),
    ];
    
    $this->update(['attachments' => $attachments]);
}

// Get attachment URLs
public function getAttachmentUrls(): array
{
    if (!$this->attachments || !isset($this->attachments['files'])) {
        return [];
    }
    
    return collect($this->attachments['files'])->map(function ($file) {
        return [
            'name' => $file['original_name'],
            'url' => Storage::url($file['path']),
            'size' => $file['size'],
            'type' => $file['mime_type'],
        ];
    })->toArray();
}
```

## Business Logic

### Cost Calculations

```php
public function calculateTotalCost(): void
{
    $this->total_cost = $this->labor_cost + $this->parts_cost + $this->other_cost;
    $this->save();
}

public function getPartsCostFromJson(): float
{
    if (!$this->parts_used || !isset($this->parts_used['parts'])) {
        return 0;
    }
    
    return collect($this->parts_used['parts'])->sum('total_cost');
}
```

### Warranty Tracking

```php
public function isUnderWarranty(): bool
{
    return $this->warranty_expires_at && $this->warranty_expires_at->isFuture();
}

public function getWarrantyStatus(): string
{
    if (!$this->warranty_expires_at) {
        return 'no_warranty';
    }
    
    if ($this->warranty_expires_at->isPast()) {
        return 'expired';
    }
    
    if ($this->warranty_expires_at->diffInDays() <= 30) {
        return 'expiring_soon';
    }
    
    return 'active';
}
```

## Performance Considerations

### Query Optimization

1. **Use appropriate indexes** for your query patterns
2. **Include completion_status** in WHERE clauses
3. **Use eager loading** for relationships
4. **Consider pagination** for large result sets

### JSON Field Queries

```sql
-- Query parts used
SELECT * FROM maintenance_records 
WHERE JSON_EXTRACT(parts_used, '$.parts[*].part_number') LIKE '%OF-123%';

-- Query attachments
SELECT * FROM maintenance_records 
WHERE JSON_LENGTH(attachments, '$.files') > 0;
```

## Troubleshooting

### Common Issues

1. **Cost Calculation Errors**: Ensure all cost fields are properly updated
2. **JSON Structure Issues**: Validate JSON structure before saving
3. **File Attachment Problems**: Verify file paths and permissions
4. **Warranty Tracking**: Check date calculations and timezone handling

### Solutions

1. **Use model events** to automatically calculate totals
2. **Validate JSON schemas** before saving
3. **Implement proper file storage** with Laravel Storage
4. **Use Carbon for date handling** to avoid timezone issues
