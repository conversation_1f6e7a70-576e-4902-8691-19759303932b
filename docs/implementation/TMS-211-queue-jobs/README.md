# TMS-211: Basic Queue Job Structure for Telematic Data Processing

## Overview

This implementation provides a comprehensive queue job architecture for telematic data processing using Laravel Horizon. It establishes the foundational job classes, batching strategies, and monitoring capabilities for scalable telematic data synchronization.

## Story Details

- **Epic**: TMS-207 - Telematic Data Synchronization Architecture Implementation
- **Story**: TMS-211 - Basic Queue Job Structure for Telematic Data Processing
- **Story Points**: 8
- **Phase**: 1 (Foundation - Weeks 1-2)

## Implementation Files

### Queue Job Structure

```
app/Jobs/Telematics/
├── TelematicSyncJob.php (abstract base class)
├── SyncVehicleStatsJob.php
├── ProcessTelematicReadingsJob.php
├── SyncIncrementalDataJob.php
└── CalculateMaintenanceSchedulesJob.php
```

### Service Classes

```
app/Services/Telematics/Jobs/
└── TelematicJobBatchService.php
```

### Configuration

```
config/
├── horizon.php (Laravel Horizon configuration)
└── queue.php (updated for Redis)
```

### Commands

```
app/Console/Commands/
├── InitialTelematicSyncCommand.php (updated)
└── IncrementalTelematicSyncCommand.php (new)
```

## Architecture Overview

### Job Hierarchy

```php
// Base job for all telematic operations
abstract class TelematicSyncJob implements ShouldQueue, ShouldBeUnique
{
    use Queueable, SerializesModels, InteractsWithQueue, Dispatchable;
    
    public int $timeout = 300; // 5 minutes
    public int $tries = 3;
    public string $queue = 'telematic-sync';
    
    // Common functionality for all telematic jobs
}

// Specific job implementations
class SyncVehicleStatsJob extends TelematicSyncJob
class ProcessTelematicReadingsJob extends TelematicSyncJob  
class SyncIncrementalDataJob extends TelematicSyncJob
class CalculateMaintenanceSchedulesJob extends TelematicSyncJob
```

### Job Batching Strategy

```php
// Batch jobs for large fleet synchronization
$batch = Bus::batch([
    new SyncVehicleStatsJob($vehicleChunk1),
    new SyncVehicleStatsJob($vehicleChunk2),
    new ProcessTelematicReadingsJob($readingsChunk1),
])->then(function (Batch $batch) {
    // Trigger maintenance calculations after all data sync
    CalculateMaintenanceSchedulesJob::dispatch();
})->dispatch();
```

## Key Design Principles

- **Scalable Processing**: Jobs handle individual vehicles and batch operations
- **Provider-Agnostic**: Works with any telematic provider through service layer
- **Fault Tolerant**: Comprehensive error handling and retry mechanisms
- **Monitorable**: Full integration with Laravel Horizon for monitoring
- **Memory Efficient**: Chunked processing for large datasets
- **Queue Optimized**: Proper queue configuration and job prioritization

## Job Descriptions

### 1. TelematicSyncJob (Abstract Base)

**Purpose**: Foundational abstract class for all telematic queue jobs

**Key Features**:
- Common queue configuration (timeout, retries, queue name)
- Exponential backoff strategy for failed jobs
- Comprehensive logging and error handling
- Job tagging for monitoring and organization
- Unique job constraints to prevent duplicates

### 2. SyncVehicleStatsJob

**Purpose**: Process individual vehicle statistics data

**Key Features**:
- Handles single vehicle data synchronization
- Processes odometer, engine hours, fuel level, diagnostics
- Integrates with provider strategies via TelematicDataSyncInterface
- Uses service layer for data validation and storage

### 3. ProcessTelematicReadingsJob

**Purpose**: Batch processing of raw telematic readings

**Key Features**:
- Chunked processing for large datasets
- Memory-efficient bulk operations
- Progress tracking for long-running operations
- Partial failure handling with detailed error reporting

### 4. SyncIncrementalDataJob

**Purpose**: Cursor-based incremental data synchronization

**Key Features**:
- Uses TelematicSyncState for cursor management
- Provider-agnostic incremental sync
- Efficient processing of only new/updated data
- Automatic cursor advancement on success

### 5. CalculateMaintenanceSchedulesJob

**Purpose**: Calculate maintenance schedules based on telematic data

**Key Features**:
- Triggered after successful data synchronization
- Uses MaintenanceCalculationService for business logic
- Supports both individual and batch vehicle processing
- Updates maintenance schedules and detects overdue items

### 6. TelematicJobBatchService

**Purpose**: Orchestrate complex job batching and chaining workflows

**Key Features**:
- Fleet-wide synchronization batching
- Job chaining for complex workflows
- Batch completion callbacks
- Progress monitoring and reporting

## Installation Instructions

### 1. Install Laravel Horizon

```bash
composer require laravel/horizon
php artisan horizon:install
```

### 2. Configure Redis for Queues

Update `.env`:
```env
QUEUE_CONNECTION=redis
REDIS_QUEUE_CONNECTION=default
```

### 3. Install Job Files

Copy the job files to their respective locations:

```bash
# Copy base job class
cp TMS-241_TelematicSyncJob.php app/Jobs/Telematics/TelematicSyncJob.php

# Copy specific job implementations
cp TMS-242_SyncVehicleStatsJob.php app/Jobs/Telematics/SyncVehicleStatsJob.php
cp TMS-243_ProcessTelematicReadingsJob.php app/Jobs/Telematics/ProcessTelematicReadingsJob.php
cp TMS-244_SyncIncrementalDataJob.php app/Jobs/Telematics/SyncIncrementalDataJob.php
cp TMS-245_CalculateMaintenanceSchedulesJob.php app/Jobs/Telematics/CalculateMaintenanceSchedulesJob.php

# Copy batch service
cp TMS-246_TelematicJobBatchService.php app/Services/Telematics/Jobs/TelematicJobBatchService.php

# Copy updated commands
cp TMS-247_InitialTelematicSyncCommand.php app/Console/Commands/InitialTelematicSyncCommand.php
cp TMS-247_IncrementalTelematicSyncCommand.php app/Console/Commands/IncrementalTelematicSyncCommand.php

# Copy configuration
cp TMS-240_horizon.php config/horizon.php
```

### 4. Update Queue Configuration

The queue configuration will be updated to use Redis and configure the telematic-sync queue with appropriate priorities.

### 5. Configure Horizon Dashboard

Update `config/horizon.php` with telematic-specific configurations and authentication settings.

## Usage Examples

### Dispatching Individual Jobs

```php
use App\Jobs\Telematics\SyncVehicleStatsJob;

// Sync specific vehicle stats
SyncVehicleStatsJob::dispatch($vehicleId, ['odometer', 'engine_hours']);

// Process batch of readings
ProcessTelematicReadingsJob::dispatch($telematicReadings);
```

### Batch Operations

```php
use App\Services\Telematics\Jobs\TelematicJobBatchService;

$batchService = app(TelematicJobBatchService::class);

// Create batch for fleet synchronization
$batch = $batchService->createFleetSyncBatch($vehicleIds);

// Monitor batch progress
$progress = $batchService->getBatchProgress($batch->id);
```

### Command Integration

```bash
# Initial sync using jobs
php artisan sync:telematics:initial --use-jobs

# Incremental sync
php artisan sync:telematics:incremental --cron
```

## Configuration

### Horizon Configuration

```php
// config/horizon.php
'environments' => [
    'production' => [
        'telematic-supervisor' => [
            'connection' => 'redis',
            'queue' => ['telematic-sync', 'default'],
            'balance' => 'auto',
            'maxProcesses' => 10,
            'tries' => 3,
            'timeout' => 300,
        ],
    ],
],
```

### Queue Priorities

```php
'telematic-sync' => [
    'priority' => 'high',
    'timeout' => 300,
    'retry_after' => 90,
],
```

## Monitoring and Debugging

### Horizon Dashboard

Access the Horizon dashboard at `/horizon` to monitor:
- Job throughput and performance
- Failed job details and retry attempts
- Queue metrics and wait times
- Batch progress and completion status

### Job Tags

All telematic jobs are tagged for easy filtering:
- `telematic-sync`: All telematic synchronization jobs
- `vehicle-stats`: Vehicle statistics processing
- `batch-processing`: Bulk data operations
- `maintenance`: Maintenance calculation jobs

### Logging

Jobs include comprehensive logging:
- Job start/completion times
- Processing statistics
- Error details and stack traces
- Performance metrics

## Testing

### Job Testing

```php
use Illuminate\Support\Facades\Queue;

// Test job dispatching
Queue::fake();
SyncVehicleStatsJob::dispatch($vehicleId, ['odometer']);
Queue::assertPushed(SyncVehicleStatsJob::class);

// Test job processing
$job = new SyncVehicleStatsJob($vehicleId, ['odometer']);
$job->handle();
```

### Batch Testing

```php
use Illuminate\Support\Facades\Bus;

Bus::fake();
$batchService->createFleetSyncBatch($vehicleIds);
Bus::assertBatched(function ($batch) {
    return $batch->jobs->count() === 3;
});
```

## Performance Considerations

### Memory Management
- Jobs process data in configurable chunks (default: 1000 records)
- Memory usage is monitored and limited
- Large datasets are streamed rather than loaded entirely

### Queue Optimization
- Dedicated telematic-sync queue with high priority
- Appropriate worker scaling based on load
- Job uniqueness prevents duplicate processing

### Database Optimization
- Bulk operations for improved performance
- Proper indexing for job-related queries
- Connection pooling for high-throughput scenarios

## Dependencies

### Required Packages
- Laravel Horizon (`laravel/horizon`)
- Redis server for queue backend
- Existing telematic service layer (TMS-209)

### Service Dependencies
- TelematicSyncService
- TelematicDataService
- TelematicReadingService
- MaintenanceCalculationService

## Next Steps

After implementing the queue job structure:

1. **TMS-212**: Enhanced Samsara strategy with stats endpoints
2. **TMS-213**: Advanced monitoring and alerting
3. **TMS-214**: Performance optimization and scaling

## Troubleshooting

### Common Issues
1. **Redis Connection**: Ensure Redis is running and properly configured
2. **Job Failures**: Check Horizon dashboard for detailed error information
3. **Memory Issues**: Adjust chunk sizes for large dataset processing
4. **Queue Workers**: Ensure Horizon workers are running and properly scaled

### Debug Commands

```bash
# Check Horizon status
php artisan horizon:status

# View failed jobs
php artisan horizon:failed

# Clear failed jobs
php artisan horizon:forget --all

# Monitor queue in real-time
php artisan queue:monitor redis:telematic-sync
```
