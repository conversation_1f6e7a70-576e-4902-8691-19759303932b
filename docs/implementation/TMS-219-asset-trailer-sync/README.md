# TMS-219: Asset and Trailer Synchronization Implementation

## Overview

This implementation extends the telematic synchronization system to support trailers and other assets, implementing the same data sync capabilities that exist for trucks. It uses polymorphic relationships for entity-agnostic design while maintaining consistency with existing truck sync patterns and ensuring scalability for large trailer fleets.

## Story Details

- **Epic**: TMS-207 - Telematic Data Synchronization Architecture Implementation
- **Story**: TMS-219 - Asset and Trailer Synchronization Implementation
- **Story Points**: 13
- **Phase**: 4 (Advanced Features - Weeks 7-8)

## Implementation Files

### Core Asset Synchronization Services

- [AssetSyncService.md](./AssetSyncService.md) - Core asset synchronization service
- [TrailerSyncService.md](./TrailerSyncService.md) - Trailer-specific synchronization logic
- [AssetDataProcessor.md](./AssetDataProcessor.md) - Asset data processing and validation
- [AssetEntityMappingService.md](./AssetEntityMappingService.md) - Entity mapping management for assets

### Samsara Integration

- [SamsaraAssetStrategy.md](./SamsaraAssetStrategy.md) - Samsara assets endpoint integration
- [AssetDataTransformer.md](./AssetDataTransformer.md) - Asset data transformation utilities
- [TrailerDataNormalizer.md](./TrailerDataNormalizer.md) - Trailer-specific data normalization

### Queue Jobs

- [SyncAssetDataJob.md](./SyncAssetDataJob.md) - Asset data synchronization job
- [SyncTrailerDataJob.md](./SyncTrailerDataJob.md) - Trailer-specific sync job
- [ProcessAssetMaintenanceJob.md](./ProcessAssetMaintenanceJob.md) - Asset maintenance processing

### Models and Relationships

- [AssetTelematicReading.md](./AssetTelematicReading.md) - Asset telematic reading model
- [TrailerMaintenanceSchedule.md](./TrailerMaintenanceSchedule.md) - Trailer maintenance schedule model
- [AssetSyncState.md](./AssetSyncState.md) - Asset synchronization state tracking
- [PolymorphicEntityRelationships.md](./PolymorphicEntityRelationships.md) - Polymorphic relationship implementations

### Maintenance Integration

- [AssetMaintenanceCalculationService.md](./AssetMaintenanceCalculationService.md) - Asset maintenance calculations
- [TrailerMaintenanceIntervalService.md](./TrailerMaintenanceIntervalService.md) - Trailer maintenance intervals
- [AssetMaintenanceAlertService.md](./AssetMaintenanceAlertService.md) - Asset maintenance alerts

### Commands and Configuration

- [SyncAssetDataCommand.md](./SyncAssetDataCommand.md) - Asset sync command interface
- [AssetSyncConfiguration.md](./AssetSyncConfiguration.md) - Asset sync configuration
- [TrailerValidationRules.md](./TrailerValidationRules.md) - Trailer data validation rules

## Architecture Overview

### Core Components

1. **AssetSyncService**: Central service for asset synchronization coordination
2. **TrailerSyncService**: Trailer-specific synchronization logic
3. **AssetDataProcessor**: Data processing and validation for assets
4. **SamsaraAssetStrategy**: Integration with Samsara assets endpoints
5. **AssetEntityMappingService**: Entity mapping management for assets
6. **AssetMaintenanceCalculationService**: Maintenance calculations for assets

### Key Design Principles

- **Polymorphic Design**: Entity-agnostic architecture using polymorphic relationships
- **Consistency**: Maintains patterns established for truck synchronization
- **Scalability**: Handles large trailer fleets efficiently
- **Extensibility**: Easy to add new asset types in the future
- **Data Integrity**: Proper validation for asset-specific data
- **Performance**: Optimized for high-volume asset synchronization

## Service Descriptions

### 1. AssetSyncService

**Purpose**: Central service for coordinating asset synchronization across different asset types

**Key Features**:
- Polymorphic entity handling
- Asset type detection and routing
- Sync coordination across asset types
- Error handling and retry logic
- Performance optimization for large fleets
- Integration with existing sync infrastructure

**Supported Asset Types**:
- Trailers (primary focus)
- Equipment attachments
- Auxiliary devices
- Future asset types (extensible)

### 2. TrailerSyncService

**Purpose**: Trailer-specific synchronization logic and data handling

**Key Features**:
- Trailer-specific data processing
- Trailer maintenance schedule management
- Trailer telematic reading processing
- Integration with truck-trailer relationships
- Trailer-specific validation rules
- Performance optimization for trailer fleets

**Trailer Data Types**:
- Location and GPS tracking
- Mileage and usage data
- Temperature monitoring (refrigerated trailers)
- Door status and security
- Tire pressure monitoring
- Maintenance indicators

### 3. AssetDataProcessor

**Purpose**: Process and validate asset data from telematic providers

**Key Features**:
- Asset data validation and normalization
- Data quality checks for assets
- Asset-specific data transformation
- Anomaly detection for asset readings
- Data enrichment and standardization
- Integration with existing data processing pipeline

### 4. SamsaraAssetStrategy

**Purpose**: Integration with Samsara assets endpoints for asset data retrieval

**Key Features**:
- Samsara assets API integration
- Asset discovery and enumeration
- Asset data retrieval and processing
- Rate limiting and API optimization
- Error handling for asset endpoints
- Incremental sync support for assets

### 5. AssetMaintenanceCalculationService

**Purpose**: Calculate maintenance schedules for assets based on telematic data

**Key Features**:
- Asset-specific maintenance calculations
- Trailer maintenance interval management
- Asset usage-based maintenance scheduling
- Integration with existing maintenance system
- Asset maintenance alert generation
- Performance optimization for large asset fleets

## Installation Instructions

### 1. Create Asset Synchronization Files

Create the asset synchronization files using the provided specifications:

```bash
# Create core asset services
# Copy content from: AssetSyncService.md
# Copy content from: TrailerSyncService.md
# Copy content from: AssetDataProcessor.md
# Copy content from: AssetEntityMappingService.md

# Create Samsara integration
# Copy content from: SamsaraAssetStrategy.md
# Copy content from: AssetDataTransformer.md
# Copy content from: TrailerDataNormalizer.md

# Create queue jobs
# Copy content from: SyncAssetDataJob.md
# Copy content from: SyncTrailerDataJob.md
# Copy content from: ProcessAssetMaintenanceJob.md

# Create models and relationships
# Copy content from: AssetTelematicReading.md
# Copy content from: TrailerMaintenanceSchedule.md
# Copy content from: AssetSyncState.md
# Copy content from: PolymorphicEntityRelationships.md

# Create maintenance integration
# Copy content from: AssetMaintenanceCalculationService.md
# Copy content from: TrailerMaintenanceIntervalService.md
# Copy content from: AssetMaintenanceAlertService.md

# Create commands and configuration
# Copy content from: SyncAssetDataCommand.md
# Copy content from: AssetSyncConfiguration.md
# Copy content from: TrailerValidationRules.md
```

### 2. Database Migrations

Run asset synchronization migrations:

```bash
# Create asset-related tables
php artisan migrate --path=database/migrations/assets

# Update external_entity_mappings for assets
php artisan migrate --path=database/migrations/asset_mappings
```

### 3. Update Service Provider Registration

Register the asset services:

```php
// In app/Providers/TelematicSyncProvider.php
$this->app->singleton(AssetSyncService::class);
$this->app->singleton(TrailerSyncService::class);
$this->app->singleton(AssetDataProcessor::class);
$this->app->singleton(AssetEntityMappingService::class);
$this->app->singleton(AssetMaintenanceCalculationService::class);
```

### 4. Configure Asset Sync Settings

Set up asset synchronization configuration:

```php
// In config/asset-sync.php
return [
    'enabled_asset_types' => ['trailers', 'equipment'],
    'sync_intervals' => [
        'trailers' => 30, // minutes
        'equipment' => 60, // minutes
    ],
    'validation_rules' => [
        'trailers' => TrailerValidationRules::class,
    ],
];
```

### 5. Update External Entity Mappings

Extend external_entity_mappings table for assets:

```sql
ALTER TABLE external_entity_mappings 
ADD COLUMN entity_type VARCHAR(50) DEFAULT 'truck',
ADD INDEX idx_entity_type_external_id (entity_type, external_id);
```

### 6. Verify Service Registration

```bash
php artisan tinker
>>> app(App\Services\Telematics\Assets\AssetSyncService::class);
>>> app(App\Services\Telematics\Assets\TrailerSyncService::class);
>>> app(App\Services\Telematics\Assets\AssetDataProcessor::class);
```

## Usage Examples

### Asset Synchronization

```php
use App\Services\Telematics\Assets\AssetSyncService;

$assetSync = app(AssetSyncService::class);

// Sync all assets
$result = $assetSync->syncAllAssets();

// Sync specific asset type
$trailerResult = $assetSync->syncAssetType('trailers');

// Sync specific asset
$asset = Trailer::find(1);
$syncResult = $assetSync->syncAsset($asset);
```

### Trailer-Specific Operations

```php
use App\Services\Telematics\Assets\TrailerSyncService;

$trailerSync = app(TrailerSyncService::class);

// Sync trailer data
$trailer = Trailer::find(1);
$result = $trailerSync->syncTrailerData($trailer);

// Get trailer telematic readings
$readings = $trailerSync->getTrailerReadings($trailer);

// Process trailer maintenance
$maintenance = $trailerSync->processTrailerMaintenance($trailer);
```

### Asset Data Processing

```php
use App\Services\Telematics\Assets\AssetDataProcessor;

$processor = app(AssetDataProcessor::class);

// Process raw asset data
$rawData = [
    'asset_type' => 'trailer',
    'external_id' => 'trailer_123',
    'readings' => [...],
];

$processedData = $processor->processAssetData($rawData);
```

### Queue Job Processing

```php
use App\Jobs\Telematics\Assets\SyncAssetDataJob;

// Queue asset sync job
SyncAssetDataJob::dispatch([
    'asset_type' => 'trailers',
    'asset_ids' => [1, 2, 3],
]);

// Queue trailer-specific sync
SyncTrailerDataJob::dispatch($trailer);
```

## Configuration

### Asset Sync Configuration

Configure asset synchronization in `config/asset-sync.php`:

```php
return [
    'enabled_asset_types' => [
        'trailers' => [
            'enabled' => true,
            'sync_interval' => 30, // minutes
            'batch_size' => 100,
            'validation_rules' => TrailerValidationRules::class,
        ],
        'equipment' => [
            'enabled' => true,
            'sync_interval' => 60, // minutes
            'batch_size' => 50,
            'validation_rules' => EquipmentValidationRules::class,
        ],
    ],
    'samsara' => [
        'assets_endpoint' => '/fleet/assets',
        'rate_limit' => 100, // requests per minute
        'timeout' => 30, // seconds
    ],
    'maintenance' => [
        'enabled' => true,
        'calculation_intervals' => [
            'trailers' => [
                'tire_rotation' => 50000, // miles
                'brake_inspection' => 100000, // miles
                'annual_inspection' => 365, // days
            ],
        ],
    ],
];
```

### Polymorphic Relationships

Configure polymorphic relationships for assets:

```php
// In external_entity_mappings migration
Schema::table('external_entity_mappings', function (Blueprint $table) {
    $table->string('entity_type', 50)->default('truck');
    $table->index(['entity_type', 'external_id']);
    $table->index(['entity_type', 'entity_id']);
});

// In models
class ExternalEntityMapping extends Model
{
    public function entity()
    {
        return $this->morphTo();
    }
}

class Trailer extends Model
{
    public function externalEntityMappings()
    {
        return $this->morphMany(ExternalEntityMapping::class, 'entity');
    }
}
```

## Testing

### Asset Sync Testing

```php
// Test asset synchronization
$assetSync = app(AssetSyncService::class);
$result = $assetSync->syncAssetType('trailers');

// Test trailer sync
$trailerSync = app(TrailerSyncService::class);
$trailer = Trailer::factory()->create();
$syncResult = $trailerSync->syncTrailerData($trailer);

// Test asset data processing
$processor = app(AssetDataProcessor::class);
$processedData = $processor->processAssetData($testData);
```

### Integration Testing

```php
// Test end-to-end asset sync
$job = new SyncAssetDataJob(['asset_type' => 'trailers']);
$job->handle();

// Test maintenance calculations
$maintenanceService = app(AssetMaintenanceCalculationService::class);
$schedules = $maintenanceService->calculateAssetMaintenance($trailer);
```

## Error Handling

### Asset Sync Errors
- Invalid asset type handling
- Missing asset data validation
- API endpoint failures
- Data transformation errors

### Data Quality Issues
- Asset data validation failures
- Inconsistent asset readings
- Missing required asset fields
- Asset relationship validation

## Performance Considerations

### Large Fleet Optimization
- Batch processing for asset synchronization
- Parallel processing for different asset types
- Efficient database queries for asset data
- Caching for frequently accessed asset information

### Memory Management
- Chunked processing for large asset datasets
- Memory-efficient data structures
- Resource cleanup for long-running processes
- Garbage collection optimization

## Dependencies

### Required Services
- Existing telematic sync infrastructure
- External entity mapping system
- Maintenance calculation services
- Queue processing system

### External Dependencies
- Samsara assets API endpoints
- Asset data validation libraries
- Polymorphic relationship support
- Performance monitoring tools

## Next Steps

After implementing asset and trailer synchronization:

1. **Extended Asset Types**: Add support for additional asset types
2. **Advanced Analytics**: Implement asset-specific analytics and reporting
3. **Mobile Integration**: Add mobile support for asset management
4. **IoT Integration**: Expand to support additional IoT devices

## Troubleshooting

### Common Issues
1. **Asset Type Recognition**: Verify asset type configuration and validation
2. **Polymorphic Relationships**: Check entity mapping and relationship setup
3. **Data Validation**: Ensure asset-specific validation rules are correct
4. **Performance Issues**: Monitor batch sizes and processing efficiency
