# User Management
A set of user authentication and organization security features designed to provide a fast, scalable integration while handling all of the user management complexity that comes with advanced business and customer needs.

To automatically respond to User Management activities, like authentication and changes related to the users, use the corresponding events.

## User
Represents a user identity in your application. A user can sign up in your application directly with a method like password, or they can be JIT-provisioned through an organization’s SSO connection.

Users may belong to organizations as members.

See the events reference documentation for the user events.

```php
<?php

$user = [
    "object" => "user",
    "id" => "user_01E4ZCR3C56J083X43JQXF3JK5",
    "email" => "<EMAIL>",
    "firstName" => "<PERSON><PERSON>",
    "lastName" => "Davis",
    "emailVerified" => true,
    "profilePictureUrl" => "https://workoscdn.com/images/v1/123abc",
    "lastSignInAt" => "2021-06-25T19:07:33.155Z",
    "createdAt" => "2021-06-25T19:07:33.155Z",
    "updatedAt" => "2021-06-25T19:07:33.155Z",
];
```

### Get a user
Get the details of an existing user.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$user = $userManagement->getUser("user_01E4ZCR3C56J083X43JQXF3JK5");
```

Response:
```json lines
{
  "object": "user",
  "id": "user_01E4ZCR3C56J083X43JQXF3JK5",
  "email": "<EMAIL>",
  "first_name": "Marcelina",
  "last_name": "Davis",
  "email_verified": true,
  "profile_picture_url": "https://workoscdn.com/images/v1/123abc",
  "last_sign_in_at": "2021-06-25T19:07:33.155Z",
  "external_id": "f1ffa2b2-c20b-4d39-be5c-212726e11222",
  "metadata": {
    "language": "en"
  },
  "created_at": "2021-06-25T19:07:33.155Z",
  "updated_at": "2021-06-25T19:07:33.155Z"
}
```

### Get a user by external ID
Get the details of an existing user by an external identifier.

Request:
```
curl https://api.workos.com/user_management/users/external_id/f1ffa2b2-c20b-4d39-be5c-212726e11222 \
  --header "Authorization: Bearer sk_example_123456789"
```

Response:
```json lines
{
  "object": "user",
  "id": "user_01E4ZCR3C56J083X43JQXF3JK5",
  "email": "<EMAIL>",
  "first_name": "Marcelina",
  "last_name": "Davis",
  "email_verified": true,
  "profile_picture_url": "https://workoscdn.com/images/v1/123abc",
  "last_sign_in_at": "2021-06-25T19:07:33.155Z",
  "external_id": "f1ffa2b2-c20b-4d39-be5c-212726e11222",
  "metadata": {
    "language": "en"
  },
  "created_at": "2021-06-25T19:07:33.155Z",
  "updated_at": "2021-06-25T19:07:33.155Z"
}
```

### List users
Get a list of all of your existing users matching the criteria specified.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

[$before, $after, $users] = $userManagement->listUsers();
```

Response:
```json lines
{
  "data": [
    {
      "object": "user",
      "id": "user_01E4ZCR3C56J083X43JQXF3JK5",
      "email": "<EMAIL>",
      "first_name": "Marcelina",
      "last_name": "Davis",
      "email_verified": true,
      "profile_picture_url": "https://workoscdn.com/images/v1/123abc",
      "last_sign_in_at": "2021-06-25T19:07:33.155Z",
      "external_id": "f1ffa2b2-c20b-4d39-be5c-212726e11222",
      "metadata": {
        "language": "en"
      },
      "created_at": "2021-06-25T19:07:33.155Z",
      "updated_at": "2021-06-25T19:07:33.155Z"
    }
  ],
  "list_metadata": {
    "before": "user_01E4ZCR3C56J083X43JQXF3JK5",
    "after": "user_01EJBGJT2PC6638TN5Y380M40Z"
  }
}
```

### Create a user
Create a new user in the current environment.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$user = $userManagement->createUser(
    "<EMAIL>",
    "i8uv6g34kd490s",
    "Marcelina",
    "Davis"
);
```

Response:
```json lines
{
  "object": "user",
  "id": "user_01E4ZCR3C56J083X43JQXF3JK5",
  "email": "<EMAIL>",
  "first_name": "Marcelina",
  "last_name": "Davis",
  "email_verified": true,
  "profile_picture_url": "https://workoscdn.com/images/v1/123abc",
  "last_sign_in_at": "2021-06-25T19:07:33.155Z",
  "created_at": "2021-06-25T19:07:33.155Z",
  "updated_at": "2021-06-25T19:07:33.155Z"
}
```

### Update a user
Updates properties of a user. The omitted properties will be left unchanged.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$user = $userManagement->updateUser(
    "user_01EHQ7ZGZ2CZVQJGZ5ZJZ1ZJGZ",
    "Marcelina",
    "Davis"
);
```

Response:
```json lines
{
  "object": "user",
  "id": "user_01E4ZCR3C56J083X43JQXF3JK5",
  "email": "<EMAIL>",
  "first_name": "Marcelina",
  "last_name": "Davis",
  "email_verified": true,
  "profile_picture_url": "https://workoscdn.com/images/v1/123abc",
  "external_id": "2fe01467-f7ea-4dd2-8b79-c2b4f56d0191",
  "metadata": {
    "language": "en"
  },
  "last_sign_in_at": "2021-06-25T19:07:33.155Z",
  "created_at": "2021-06-25T19:07:33.155Z",
  "updated_at": "2021-06-25T19:07:33.155Z"
}
```

### Delete a user
Permanently deletes a user in the current environment. It cannot be undone.

```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$userManagement->deleteUser("user_01E4ZCR3C56J083X43JQXF3JK5");
```

## Identities
Represents User identities obtained from external identity providers.

When a user authenticates using an external provider like Google OAuth, information from that provider will be made available as one of the user’s Identities. You can read more about the process in our identity linking guide.

Applications should check the type before making assumptions about the shape of the identity. Currently only OAuth identities are supported, but more types may be added in the future.

```json lines
{
  "idp_id": "4F42ABDE-1E44-4B66-824A-5F733C037A6D",
  "type": "OAuth",
  "provider": "MicrosoftOAuth"
}
```

### Get user identities
Get a list of identities associated with the user. A user can have multiple associated identities after going through identity linking. Currently only OAuth identities are supported. More provider types may be added in the future.

Request:
```
curl https://api.workos.com/user_management/users/user_01E4ZCR3C56J083X43JQXF3JK5/identities \
  --header "Authorization: Bearer sk_example_123456789"

```

Response:
```json lines
[
  {
    "idp_id": "4F42ABDE-1E44-4B66-824A-5F733C037A6D",
    "type": "OAuth",
    "provider": "MicrosoftOAuth"
  }
]
```

## Authentication

Authenticate a user with a specified authentication method.

### Get an authorization URL
Generates an OAuth 2.0 authorization URL to authenticate a user with AuthKit or SSO.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$authorizationUrl = $userManagement->getAuthorizationUrl(
    "https://your-app.com/callback",
    "dj1kUXc0dzlXZ1hjUQ==",
    "conn_01E4ZCR3C56J083X43JQXF3JK5",
    "org_01H945H0YD4F97JN9MATX7BYAG"
);
```

Response:
```
https://api.workos.com/user_management/authorize?
response_type=code&
client_id=client_123456789&
redirect_uri=https://your-app.com/callback&
state=dj1kUXc0dzlXZ1hjUQ==&
connection_id=conn_01E4ZCR3C56J083X43JQXF3JK5
```

If you are using AuthKit, set the provider parameter to "authkit", which will generate an authorization URL for your AuthKit domain. AuthKit will take care of detecting the user’s authentication method, such as identifying whether they use Email + Password or Single Sign-On,and direct them to the corresponding login flow.

Otherwise, to generate an authorization URL for a WorkOS SSO connection, you’ll have to specify the user’s connection, organization, or OAuth provider as a parameter. These connection selectors are mutually exclusive, and exactly one must be provided. The generated URL automatically directs the user to their identity provider. Once the user authenticates with their identity provider, WorkOS then issues a redirect to your redirect URI to complete the sign-in flow.

#### Redirect URI

In the OAuth 2.0 protocol, a redirect URI is the location that the user is redirected to once they have successfully authenticated with their identity provider.

When redirecting the user, WorkOS will generate an authorization code and pass it to your redirect URI as a code query parameter, your app will use this code to authenticate the user. Additionally, WorkOS can pass a state parameter back to your application that you may use to encode arbitrary information to restore your application state between the redirects.

Redirect URI with query parameters
`https://your-app.com/callback?code=01E2RJ4C05B52KKZ8FSRDAP23J&state=dj1kUXc0dzlXZ1hjUQ==`

You can use state to encode parameters like originating URL and query parameters. This is useful in a flow where unauthenticated users are automatically redirected to a login page. After successful sign in, users will be routed to your redirect URI callback route. From there you can extract the originating URL from state and redirect the user to their intended destination.

You’ll need to configure the allowed redirect URIs for your application via the
Redirects
page in the dashboard. Without a valid redirect URI, your users will be unable to sign in. Make sure that the redirect URI you use as a parameter to get the authorization URL matches one of the redirect URIs you have configured in the dashboard.

Redirect URIs follow stricter requirements in production environments:

- HTTPS protocol is required in production environments
- HTTP and localhost are allowed in staging environments
- Wildcard characters are not allowed in production environments

#### PKCE
The
Proof Key for Code Exchange
(PKCE) flow is an extension to the OAuth 2.0 Authorization Code flow. It enables public clients, like native apps or single-page apps, to perform the authorization code flow securely. If you are developing a client that makes API calls in public, you’ll need to use this flow.

In this flow, your client generates a code verifier which is a high-entropy cryptographic random string. A code challenge is derived by hashing the code verifier. Instead of using a client secret, provide the code challenge when getting the authorization URL and the code verifier when authenticating a User.

#### Error codes
If there is an issue generating an authorization URL, the API will return the original redirect URI with error and error_description query parameters. If provided, the state value will also be included.

Redirect URI with an error code
`https://your-app.com/callback?error=organization_invalid&error_description=No%20connection%20associated%20with%20organization&state=123456789`

Possible error codes and the corresponding descriptions are listed below.

| Error Code                       | Description                                                                                                                                                                                                             |
|----------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| access_denied                    | The user denied an OAuth authorization request at the identity provider.                                                                                                                                                 |
| ambiguous_connection_selector    | A connection could not be uniquely identified using the provided connection selector (e.g., organization). This can occur when there are multiple SSO connections under the same organization. If you need multiple SSO connections for an organization, use the connection parameter to identify which connection to use for SSO. |
| connection_invalid               | There is no connection for the provided ID.                                                                                                                                                                              |
| connection_strategy_invalid      | The provider has multiple strategies associated per environment.                                                                                                                                                        |
| connection_unlinked              | The connection associated with the request is unlinked.                                                                                                                                                                  |
| invalid_connection_selector      | A valid connection selector query parameter must be provided in order to correctly determine the proper connection to return an authorization URL for. Valid connection selectors are either connection, organization, or provider.   |
| organization_invalid             | There is no organization matching the provided ID.                                                                                                                                                                       |
| oauth_failed                     | An OAuth authorization request failed for a user.                                                                                                                                                                        |
| server_error                     | The SSO authentication failed for the user. More detailed errors and steps to resolve are available in the Sessions tab on the connection page in the WorkOS Dashboard.                                                 |

### Authenticate with code
Authenticates a user using AuthKit, OAuth or an organization’s SSO connection.

AuthKit handles all authentication methods, however it is conceptually similar to a social login experience. Like OAuth and SSO, AuthKit returns you a code that you can exchange for an authenticated user. See Integrating with AuthKit.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$user = $userManagement->authenticateWithCode(
    "client_123456789",
    "01E2RJ4C05B52KKZ8FSRDAP23J",
    "*********",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
);
```

Response:
```json lines
{
  "user": {
    "object": "user",
    "id": "user_01E4ZCR3C56J083X43JQXF3JK5",
    "email": "<EMAIL>",
    "first_name": "Marcelina",
    "last_name": "Davis",
    "email_verified": true,
    "profile_picture_url": "https://workoscdn.com/images/v1/123abc",
    "created_at": "2021-06-25T19:07:33.155Z",
    "updated_at": "2021-06-25T19:07:33.155Z"
  },
  "organization_id": "org_01H945H0YD4F97JN9MATX7BYAG",
  "access_token": "eyJhb.nNzb19vaWRjX2tleV9.lc5Uk4yWVk5In0",
  "refresh_token": "yAjhKk123NLIjdrBdGZPf8pLIDvK",
  "impersonator": {
    "email": "<EMAIL>",
    "reason": "Investigating an issue with the customer's account."
  }
}
```

### Authenticate a user with password
Authenticates a user with email and password.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$user = $userManagement->authenticateWithPassword(
    "client_123456789",
    "<EMAIL>",
    "i8uv6g34kd490s",
    "*********",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
);
```

Response:
```json lines
{
  "user": {
    "object": "user",
    "id": "user_01E4ZCR3C56J083X43JQXF3JK5",
    "email": "<EMAIL>",
    "first_name": "Marcelina",
    "last_name": "Davis",
    "email_verified": true,
    "profile_picture_url": "https://workoscdn.com/images/v1/123abc",
    "created_at": "2021-06-25T19:07:33.155Z",
    "updated_at": "2021-06-25T19:07:33.155Z"
  },
  "organization_id": "org_01H945H0YD4F97JN9MATX7BYAG",
  "access_token": "eyJhb.nNzb19vaWRjX2tleV9.lc5Uk4yWVk5In0",
  "refresh_token": "yAjhKk123NLIjdrBdGZPf8pLIDvK"
}
```

### Authenticate with Magic Auth
Authenticates a user by verifying the Magic Auth code sent to the user’s email.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$user = $userManagement->authenticateWithMagicAuth(
    "client_123456789",
    "123456",
    "<EMAIL>",
    "ql1AJgNoLN1tb9llaQ8jyC2dn",
    "*********",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
);
```

Response:
```json lines
{
  "user": {
    "object": "user",
    "id": "user_01E4ZCR3C56J083X43JQXF3JK5",
    "email": "<EMAIL>",
    "first_name": "Marcelina",
    "last_name": "Davis",
    "email_verified": true,
    "profile_picture_url": "https://workoscdn.com/images/v1/123abc",
    "created_at": "2021-06-25T19:07:33.155Z",
    "updated_at": "2021-06-25T19:07:33.155Z"
  },
  "organization_id": "org_01H945H0YD4F97JN9MATX7BYAG"
}
```

### Authenticate with refresh token
Use this endpoint to exchange a refresh token for a new access token. Refresh tokens are single use, so a new refresh token is returned.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$user = $userManagement->authenticateWithRefreshToken(
    "client_123456789",
    "Xw0NsCVXMBf7svAoIoKBmkpEK",
    "*********",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
);
```

Response:
```json lines
{
  "access_token": "eyJhbGciOiJSUzI1NiIsImtpZCI6",
  "refresh_token": "gTsE0Rb9MJq7eL0dUmcGvoCwL"
}
```

### Authenticate with an email verification code
Authenticates a user with an unverified email and verifies their email address.

A user with an unverified email address won’t be able to authenticate right away. When they attempt to authenticate with their credentials, the API will return an email verification required error that contains a pending authentication token.

If the email setting for email verification is enabled, WorkOS will automatically send a one-time email verification code to the user’s email address. If the email setting is not enabled, retrieve the email verification code to send the email yourself. Use the pending authentication token from the error and the one-time code the user received to authenticate them and to complete the email verification process.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$user = $userManagement->authenticateWithEmailVerification(
    "client_123456789",
    "123456",
    "ql1AJgNoLN1tb9llaQ8jyC2dn",
    "*********",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
);
```

Response:
```json lines
{
  "user": {
    "object": "user",
    "id": "user_01E4ZCR3C56J083X43JQXF3JK5",
    "email": "<EMAIL>",
    "first_name": "Marcelina",
    "last_name": "Davis",
    "email_verified": true,
    "profile_picture_url": "https://workoscdn.com/images/v1/123abc",
    "created_at": "2021-06-25T19:07:33.155Z",
    "updated_at": "2021-06-25T19:07:33.155Z"
  },
  "organization_id": "org_01H945H0YD4F97JN9MATX7BYAG"
}
```

### Authenticate with a time-based one-time password
Authenticates a user enrolled into MFA using time-based one-time password (TOTP).

Users enrolled into MFA are required to enter a TOTP each time they sign in. When they attempt to authenticate with their credentials, the API will return an MFA challenge error that contains a pending authentication token.

To continue with the authentication flow, challenge one of the factors returned by the MFA challenge error response and present a UI to the user to enter the TOTP code. Then, authenticate the user with the TOTP code, the challenge from the factor, and the pending authentication token from the MFA challenge error.

MFA can be enabled via the
Authentication page
in the WorkOS dashboard.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$user = $userManagement->authenticateWithTotp(
    "client_123456789",
    "123456",
    "ql1AJgNoLN1tb9llaQ8jyC2dn",
    "auth_challenge_01FVYZWQTZQ5VB6BC5MPG2EYC5",
    "*********",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
);
```

Response:
```json lines
{
  "user": {
    "object": "user",
    "id": "user_01E4ZCR3C56J083X43JQXF3JK5",
    "email": "<EMAIL>",
    "first_name": "Marcelina",
    "last_name": "Davis",
    "email_verified": true,
    "profile_picture_url": "https://workoscdn.com/images/v1/123abc",
    "created_at": "2021-06-25T19:07:33.155Z",
    "updated_at": "2021-06-25T19:07:33.155Z"
  },
  "organization_id": "org_01H945H0YD4F97JN9MATX7BYAG"
}
```

### Authenticate with organization selection
Authenticates a user into an organization they are a member of.

When a user who is a member of multiple organizations attempts to authenticate with their credentials, the API will return an organization selection error that contains a pending authentication token. To continue with the authentication flow, your application should display the list of organizations for the user to choose.

Use the pending authentication token from the error and the organization the user selected in your UI to complete the authentication.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$user = $userManagement->authenticateWithSelectedOrganization(
    "client_123456789",
    "org_01H945H0YD4F97JN9MATX7BYAG",
    "ql1AJgNoLN1tb9llaQ8jyC2dn",
    "*********",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
);
```

Response:
```json lines
{
  "user": {
    "object": "user",
    "id": "user_01E4ZCR3C56J083X43JQXF3JK5",
    "email": "<EMAIL>",
    "first_name": "Marcelina",
    "last_name": "Davis",
    "email_verified": true,
    "profile_picture_url": "https://workoscdn.com/images/v1/123abc",
    "created_at": "2021-06-25T19:07:33.155Z",
    "updated_at": "2021-06-25T19:07:33.155Z"
  },
  "organization_id": "org_01H945H0YD4F97JN9MATX7BYAG"
}
```

### Authenticate with session cookie

Authenticates a user using an AuthKit session cookie. This method does not make a network call, but simply unseals an existing session cookie and decodes the JWT claims from the access token.

Request:
```javascript
import {
  AuthenticateWithSessionCookieFailureReason,
  WorkOS,
} from '@workos-inc/node';

const workos = new WorkOS('sk_example_123456789', {
  // clientId is required to be passed in to use the authenticateWithSessionCookie method
  clientId: 'client_123456789',
});

const { authenticated, ...restOfAuthenticationResponse } =
  await workos.userManagement.authenticateWithSessionCookie({
    sessionData: 'sealed_session_cookie_data',
    cookiePassword: 'password_previously_used_to_seal_session_cookie',
  });

if (authenticated) {
  // User is authenticated and session data can be utilized
  const { sessionId, organizationId, role, permissions } =
    restOfAuthenticationResponse;
} else {
  const { reason } = restOfAuthenticationResponse;

  // Can use AuthenticateWithSessionCookieFailureReason to handle failure reasons
  if (
    reason ===
    AuthenticateWithSessionCookieFailureReason.NO_SESSION_COOKIE_PROVIDED
  ) {
    // Redirect the user to the login page
  }
}
```

Successful Response:
```json lines
{
  "authenticated": true,
  "session_id": "session_01HQSXZGF8FHF7A9ZZFCW4387R",
  "organization_id": "org_01H945H0YD4F97JN9MATX7BYAG",
  "role": "member",
  "permissions": ["posts:read", "posts:write"]
}
```

Failed Response:
```json lines
{
    "authenticated": false,
    "reason": "invalid_session_cookie"
}
```

### Refresh and seal session data
Unseals the provided session data from a user’s session cookie, authenticates with the existing refresh token, and returns the sealed data for the refreshed session.

Request:
```javascript
import {
  RefreshAndSealSessionDataFailureReason,
  WorkOS,
} from '@workos-inc/node';

const workos = new WorkOS('sk_example_123456789', {
  // clientId is required to be passed in to use the refreshAndSealSessionData method
  clientId: 'client_123456789',
});

const { authenticated, ...restOfRefreshResponse } =
  await workos.userManagement.refreshAndSealSessionData({
    sessionData: 'sealed_session_cookie_data',
    cookiePassword: 'password_previously_used_to_seal_session_cookie',
  });

if (authenticated) {
  const { sealedSession } = restOfRefreshResponse;

  // Set the sealed session in a cookie
} else {
  const { reason } = restOfRefreshResponse;

  // Can use RefreshAndSealSessionDataFailureReason to handle failure reasons
  if (
    reason === RefreshAndSealSessionDataFailureReason.NO_SESSION_COOKIE_PROVIDED
  ) {
    // Redirect the user to the login page
  }
}
```

Successful Response:
```json lines
{
  "authenticated": true,
  "sealed_session": "Fe26.2*1*d7f59d8b9d29c26c44dd3df2b56a7d1d40d4"
}
```

Failed Response:
```json lines
{
  "authenticated": false,
  "reason": "invalid_session_cookie"
}
```

## Session tokens

### JWKS URL
This hosts the public key that is used for verifying access tokens.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$jwksUrl = $userManagement->getJwksUrl("client_123456789");
```

Response: `https://api.workos.com/sso/jwks/client_123456789`

### Access token
The access token that is returned in successful authentication responses is a JWT that can be used to verify that a user has an active session. The JWT is signed by a JWKS which can be retrieved from the WorkOS API.

Decoded access token:
```json lines
{
  "iss": "https://api.workos.com",
  "sub": "user_01HBEQKA6K4QJAS93VPE39W1JT",
  "act": {
    "sub": "<EMAIL>"
  },
  "org_id": "org_01HRDMC6CM357W30QMHMQ96Q0S",
  "role": "member",
  "permissions": ["posts:read", "posts:write"],
  "entitlements": ["audit-logs"],
  "sid": "session_01HQSXZGF8FHF7A9ZZFCW4387R",
  "jti": "01HQSXZXPPFPKMDD32RKTFY6PV",
  "exp": 1709193857,
  "iat": 1709193557
}
```

### Refresh token
The refresh token can be used to obtain a new access token using the authenticate with refresh token endpoint. Refresh tokens may only be used once. Refreshes will succeed as long as the user’s session is still active.

## Session helpers
After authenticating and storing the encrypted session as a cookie, retrieving and decrypting the session is made easy via the session helper methods.

### Load sealed session
Load the session by providing the sealed session and the cookie password.

Load sealed session
```javascript
import { WorkOS } from '@workos-inc/node';

const workos = new WorkOS('sk_example_123456789', {
  clientId: 'client_123456789',
});

const session = await workos.userManagement.loadSealedSession({
  sessionData: 'sealed_session_cookie_data',
  cookiePassword: 'password_previously_used_to_seal_session_cookie',
});
```

### Authenticate
Unseals the session data and checks if the session is still valid.

Authenticate
```javascript
import { WorkOS } from '@workos-inc/node';

const workos = new WorkOS('sk_example_123456789', {
  clientId: 'client_123456789',
});

const session = await workos.userManagement.loadSealedSession({
  sessionData: 'sealed_session_cookie_data',
  cookiePassword: 'password_previously_used_to_seal_session_cookie',
});

const authResponse = await session.authenticate();

if (authResponse.authenticated) {
  // User is authenticated and session data can be used
  const { sessionId, organizationId, role, permissions, user } = authResponse;
} else {
  if (authResponse.reason === 'no_session_cookie_provided') {
    // Redirect the user to the login page
  }
}
```

### Refresh
Refreshes the user’s session with the refresh token. Passing in a new organization ID will switch the user to that organization.

Refresh
```javascript
import { WorkOS } from '@workos-inc/node';

const workos = new WorkOS('sk_example_123456789', {
  clientId: 'client_123456789',
});

const session = await workos.userManagement.loadSealedSession({
  sessionData: 'sealed_session_cookie_data',
  cookiePassword: 'password_previously_used_to_seal_session_cookie',
});

const refreshResult = await session.refresh();

if (!refreshResult.authenticated) {
  // Redirect the user to the login page
}

const {
  session: userSession,
  sealedSession,
  user,
  organizationId,
  role,
  permissions,
  entitlements,
  impersonator,
} = refreshResult;

// Use claims and userSession for further business logic

// Set the sealedSession in a cookie
```

### Get log out URL
End a user’s session. The user’s browser should be redirected to this URL. Functionally similar to Get logout URL but extracts the session ID automatically from the session data.

Get log out URL
```javascript
import { WorkOS } from '@workos-inc/node';

const workos = new WorkOS('sk_example_123456789', {
  clientId: 'client_123456789',
});

const session = await workos.userManagement.loadSealedSession({
  sessionData: 'sealed_session_cookie_data',
  cookiePassword: 'password_previously_used_to_seal_session_cookie',
});

const logOutUrl = await session.getLogOutUrl();

// Redirect the user to the log out URL
```

## Authentication errors
Integrating the authentication API directly requires handling error responses for email verification, MFA challenges, identity linking, and organization selection. One or more of these responses may be returned for an authentication attempt with any authentication method.

Hosted AuthKit handles authentication errors for you and may be a good choice if you prefer a simpler integration.

### Email verification required error
This error indicates that a user with an unverified email address attempted to authenticate in an environment where email verification is required. It includes a pending authentication token that should be used to complete the authentication.

Email verification required error
```json lines
{
  "code": "email_verification_required",
  "message": "Email ownership must be verified before authentication.",
  "pending_authentication_token": "YQyCkYfuVw2mI3tzSrk2C1Y7S",
  "email": "<EMAIL>",
  "email_verification_id": "email_verification_01HYGGEB6FYMWQNWF3XDZG7VV3"
}
```

When this error occurs and the email setting for email verification is enabled, WorkOS will automatically send a one-time email verification code to the user’s email address and issue a pending authentication token. If the email setting is not enabled, retrieve the email verification code to send the email verification email yourself. To complete the authentication process, use the pending authentication token from the error and the one-time code the user received to authenticate them and to verify their email address.

The same applies when a user attempts to authenticate with OAuth or SSO, but there was already an account with a matching unverified email address.

### MFA enrollment error
This error indicates that a user who is not enrolled into MFA attempted to authenticate in an environment where MFA is required. It includes a pending authentication token that should be used to authenticate the user once they enroll into MFA.

MFA enrollment error
```json lines
{
  "code": "mfa_enrollment",
  "message": "The user must enroll in MFA to finish authenticating.",
  "pending_authentication_token": "YQyCkYfuVw2mI3tzSrk2C1Y7S",
  "user": {
    "object": "user",
    "id": "user_01E4ZCR3C56J083X43JQXF3JK5",
    "email": "<EMAIL>",
    "first_name": "Marcelina",
    "last_name": "Davis",
    "email_verified": true,
    "profile_picture_url": "https://workoscdn.com/images/v1/123abc",
    "created_at": "2021-06-25T19: 07: 33.155Z",
    "updated_at": "2021-06-25T19: 07: 33.155Z"
  }
}
```

When this error occurs, you’ll need to present an MFA enrollment UI to the user. Once the user has enrolled, present an MFA challenge UI to the user and authenticate them with their TOTP code and the pending authentication token from this error.

MFA can be enabled via the Authentication page in the WorkOS dashboard.

### MFA challenge error
This error indicates that a user enrolled into MFA attempted to authenticate in an environment where MFA is required. It includes a pending authentication token and a list of factors that the user is enrolled in that should be used to complete the authentication.

MFA challenge error
```json lines
{
  "code": "mfa_challenge",
  "message": "The user must complete an MFA challenge to finish authenticating.",
  "pending_authentication_token": "YQyCkYfuVw2mI3tzSrk2C1Y7S",
  "authentication_factors": [
    {
      "id": "auth_factor_01FVYZ5QM8N98T9ME5BCB2BBMJ",
      "type": "totp"
    }
  ],
  "user": {
    "object": "user",
    "id": "user_01E4ZCR3C56J083X43JQXF3JK5",
    "email": "<EMAIL>",
    "first_name": "Marcelina",
    "last_name": "Davis",
    "email_verified": true,
    "profile_picture_url": "https://workoscdn.com/images/v1/123abc",
    "created_at": "2021-06-25T19:07:33.155Z",
    "updated_at": "2021-06-25T19:07:33.155Z"
  }
}
```

When this error occurs, you’ll need to present an MFA challenge UI to the user and authenticate them with their TOTP code, the pending authentication token from this error, and a challenge that corresponds to one of the authentication factors.

MFA can be enabled via the Authentication page in the WorkOS dashboard.

### Organization selection required error
This error indicates that the user is a member of multiple organizations and must select which organization to sign in to. It includes a list of organizations the user is a member of and a pending authentication token that should be used to complete the authentication.

Organization selection required error
```json lines
{
  "code": "organization_selection_required",
  "message": "The user must choose an organization to finish their authentication.",
  "pending_authentication_token": "YQyCkYfuVw2mI3tzSrk2C1Y7S",
  "organizations": [
    {
      "id": "org_01H93RZAP85YGYZJXYPAZ9QTXF",
      "name": "Foo Corp"
    },
    {
      "id": "org_01H93S4E6GB5A8PFNKGTA4S42X",
      "name": "Bar Corp"
    }
  ],
  "user": {
    "object": "user",
    "id": "user_01E4ZCR3C56J083X43JQXF3JK5",
    "email": "<EMAIL>",
    "first_name": "Marcelina",
    "last_name": "Davis",
    "email_verified": true,
    "profile_picture_url": "https://workoscdn.com/images/v1/123abc",
    "created_at": "2021-06-25T19:07:33.155Z",
    "updated_at": "2021-06-25T19:07:33.155Z"
  }
}
```

When this error occurs, you’ll need to display the list of organizations that the user is a member of and authenticate them with the selected organization using the pending authentication token from the error.

### SSO required error
This error indicates that a user attempted to authenticate into an organization that requires SSO using a different authentication method. It includes a list of SSO connections that may be used to complete the authentication.

SSO required error
```json lines
{
  "error": "sso_required",
  "error_description": "User must authenticate using one of the matching connections.",
  "connection_ids": ["conn_01DRF1T7JN6GXS8KHS0WYWX1YD"]
}
```

When this error occurs, you’ll need to use one of the SSO connections from the error to get the authorization URL and redirect the user to that URL to complete the authentication with the organization’s identity provider.

### Organization authentication required error
This error indicates that a user attempted to authenticate with an authentication method that is not allowed by the organization that has a domain policy managing this user. It includes all the possible methods the user can use to authenticate.

Organization authentication required error
```json lines
{
  "error": "organization_authentication_methods_required",
  "error_description": "User must authenticate using one of the methods allowed by the organization.",
  "sso_connection_ids": ["conn_01DRF1T7JN6GXS8KHS0WYWX1YD"],
  "auth_methods": {
    "apple_oauth": false,
    "github_oauth": false,
    "google_oauth": true,
    "magic_auth": false,
    "microsoft_oauth": false,
    "password": false
  }
}
```

When this error occurs, you’ll need to present the user with these options so they can choose which method to continue authentication.

## Magic Auth
Magic Auth is a passwordless authentication method that allows users to sign in or sign up via a unique, six digit one-time-use code sent to their email inbox. To verify the code, authenticate the user with Magic Auth.

Example Magic Auth
```php
<?php

$magicAuth = [
    "object" => "magic_auth",
    "id" => "magic_auth_01E4ZCR3C56J083X43JQXF3JK5",
    "userId" => "user_01HWWYEH2NPT48X82ZT23K5AX4",
    "email" => "<EMAIL>",
    "expiresAt" => "2021-06-25T19:07:33.155Z",
    "token" => "123456",
    "createdAt" => "2021-06-25T19:07:33.155Z",
    "updatedAt" => "2021-06-25T19:07:33.155Z",
];
```

### Get a Magic Auth code
Get the details of an existing Magic Auth code that can be used to send an email to a user for authentication.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$magicAuth = $userManagement->getMagicAuth(
    "magic_auth_01E4ZCR3C56J083X43JQXF3JK5"
);
```

Response:
```json lines
{
  "id": "magic_auth_01E4ZCR3C56J083X43JQXF3JK5",
  "user_id": "user_01HWWYEH2NPT48X82ZT23K5AX4",
  "email": "<EMAIL>",
  "expires_at": "2021-07-01T19:07:33.155Z",
  "code": "123456",
  "created_at": "2021-06-25T19:07:33.155Z",
  "updated_at": "2021-06-25T19:07:33.155Z"
}
```

### Create a Magic Auth code
Creates a one-time authentication code that can be sent to the user’s email address. The code expires in 10 minutes. To verify the code, authenticate the user with Magic Auth.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$magicAuth = $userManagement->createMagicAuth("<EMAIL>");
```

Response:
```json lines
{
  "id": "magic_auth_01E4ZCR3C56J083X43JQXF3JK5",
  "user_id": "user_01HWWYEH2NPT48X82ZT23K5AX4",
  "email": "<EMAIL>",
  "expires_at": "2021-07-01T19:07:33.155Z",
  "code": "123456",
  "created_at": "2021-06-25T19:07:33.155Z",
  "updated_at": "2021-06-25T19:07:33.155Z"
}
```

## Multi-Factor Authentication
Enroll users in multi-factor authentication for an additional layer of security. MFA can be enabled via the Authentication page in the WorkOS dashboard.

### Authentication factor
Represents an authentication factor.

Authentication factor
```php
<?php

$authenticationFactor = [
    "object" => "authentication_factor",
    "id" => "auth_factor_01FVYZ5QM8N98T9ME5BCB2BBMJ",
    "created_at" => "2022-02-15T15:14:19.392Z",
    "updated_at" => "2022-02-15T15:14:19.392Z",
    "type" => "totp",
    "totp" => [
        "issuer" => "Foo Corp",
        "user" => "<EMAIL>",
        "qr_code" => "data:image/png;base64,{base64EncodedPng}",
        "secret" => "NAGCCFS3EYRB422HNAKAKY3XDUORMSRF",
        "uri" =>
            "otpauth://totp/FooCorp:<EMAIL>?secret=NAGCCFS3EYRB422HNAKAKY3XDUORMSRF&issuer=FooCorp",
    ],
    "user_id" > "user_01FVYZ5QM8N98T9ME5BCB2BBMJ",
];
```

### Authentication challenge
Represents a challenge of an authentication factor.

Authentication challenge

```php
<?php

$authenticationChallenge = [
    "object" => "authentication_challenge",
    "id" => "auth_challenge_01FVYZWQTZQ5VB6BC5MPG2EYC5",
    "created_at" => "2022-02-15T15:26:53.274Z",
    "updated_at" => "2022-02-15T15:26:53.274Z",
    "expires_at" => "2022-02-15T15:36:53.279Z",
    "authentication_factor_id" => "auth_factor_01FVYZ5QM8N98T9ME5BCB2BBMJ",
];
```

### Enroll an authentication factor
Enrolls a user in a new authentication factor.

Request
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$authenticationFactorAndChallenge = $userManagement->enrollAuthFactor(
    "user_01E4ZCR3C56J083X43JQXF3JK5",
    "totp",
    "WorkOS",
    "<EMAIL>"
);
```

### List authentication factors
Lists the authentication factors for a user.

Request
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$authFactors = $userManagement->listAuthFactors(
    "user_01E4ZCR3C56J083X43JQXF3JK5"
);
```

## Email verification
Email verification is a security feature that requires users to verify their email address before they can sign in to your application. It is enabled by default.

Users signing in with Magic Auth, Google OAuth, Apple OAuth, or SSO are automatically verified. For other authentication methods, an email verification flow is required to confirm that the user’s email address belongs to them.

Example email verification
```php
<?php

$emailVerification = [
    "object" => "email_verification",
    "id" => "email_verification_01HYGGEB6FYMWQNWF3XDZG7VV3",
    "userId" => "user_01HWWYEH2NPT48X82ZT23K5AX4",
    "email" => "<EMAIL>",
    "expiresAt" => "2021-06-25T19:07:33.155Z",
    "token" => "123456",
    "createdAt" => "2021-06-25T19:07:33.155Z",
    "updatedAt" => "2021-06-25T19:07:33.155Z",
];
```

### Get an email verification code
Get the details of an existing email verification code that can be used to send an email to a user for verification.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$emailVerification = $userManagement->getEmailVerification(
    "email_verification_01HYGGEB6FYMWQNWF3XDZG7VV3"
);
```

Response:
```json lines
{
  "id": "email_verification_01HYGGEB6FYMWQNWF3XDZG7VV3",
  "user_id": "user_01HWWYEH2NPT48X82ZT23K5AX4",
  "email": "<EMAIL>",
  "expires_at": "2021-07-01T19:07:33.155Z",
  "code": "123456",
  "created_at": "2021-06-25T19:07:33.155Z",
  "updated_at": "2021-06-25T19:07:33.155Z"
}
```

## Password reset
Create a password reset token for a user and reset the user’s password.

When a user’s password is reset, all of their active sessions are revoked.

Example Password reset
```php
<?php

$passwordReset = [
    "object" => "password_reset",
    "id" => "password_reset_01HYGDNK5G7FZ4YJFXYXPB5JRW",
    "userId" => "user_01HWWYEH2NPT48X82ZT23K5AX4",
    "email" => "<EMAIL>",
    "passwordResetToken" => "Z1uX3RbwcIl5fIGJJJCXXisdI",
    "passwordResetUrl" =>
        "https://your-app.com/reset-password?token=Z1uX3RbwcIl5fIGJJJCXXisdI",
    "expiresAt" => "2021-06-25T19:07:33.155Z",
    "createdAt" => "2021-06-25T19:07:33.155Z",
];
```

### Get a password reset token
Get the details of an existing password reset token that can be used to reset a user’s password.

```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$passwordReset = $userManagement->getPasswordReset(
    "password_reset_01HYGDNK5G7FZ4YJFXYXPB5JRW"
);
```

```json lines
{
  "id": "password_reset_01HYGDNK5G7FZ4YJFXYXPB5JRW",
  "user_id": "user_01HWWYEH2NPT48X82ZT23K5AX4",
  "email": "<EMAIL>",
  "password_reset_token": "Z1uX3RbwcIl5fIGJJJCXXisdI",
  "password_reset_url": "https://your-app.com/reset-password?token=Z1uX3RbwcIl5fIGJJJCXXisdI",
  "expires_at": "2021-07-01T19:07:33.155Z",
  "created_at": "2021-06-25T19:07:33.155Z"
}
```

### Create a password reset token
Creates a one-time token that can be used to reset a user’s password.

```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$passwordReset = $userManagement->createPasswordReset("<EMAIL>");
```

```json lines
{
  "id": "password_reset_01HYGDNK5G7FZ4YJFXYXPB5JRW",
  "user_id": "user_01HWWYEH2NPT48X82ZT23K5AX4",
  "email": "<EMAIL>",
  "password_reset_token": "Z1uX3RbwcIl5fIGJJJCXXisdI",
  "password_reset_url": "https://your-app.com/reset-password?token=Z1uX3RbwcIl5fIGJJJCXXisdI",
  "expires_at": "2021-07-01T19:07:33.155Z",
  "created_at": "2021-06-25T19:07:33.155Z"
}
```

### Reset the password
Sets a new password using the token query parameter from the link that the user received. Successfully resetting the password will verify a user’s email, if it hasn’t been verified yet.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$user = $userManagement->resetPassword(
    "stpIJ48IFJt0HhSIqjf8eppe0",
    "i8uv6g34kd490s"
);
```

## Organization membership
An organization membership is a top-level resource that represents a user’s relationship with an organization. A user may be a member of zero, one, or many organizations.

See the events reference documentation for the organization membership events.

Example organization membership
```php
<?php

$organizationMembership = [
    "object" => "organization_membership",
    "id" => "om_01E4ZCR3C56J083X43JQXF3JK5",
    "userId" => "user_01E4ZCR3C5A4QZ2Z2JQXGKZJ9E",
    "organizationId" => "org_01E4ZCR3C56J083X43JQXF3JK5",
    "status" => "active",
    "createdAt" => "2021-06-25T19:07:33.155Z",
    "updatedAt" => "2021-06-25T19:07:33.155Z",
];
```

### Get an organization membership
Get the details of an existing organization membership.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$organizationMembership = $userManagement->getOrganizationMembership(
    "om_01E4ZCR3C56J083X43JQXF3JK5"
);
```

Response:
```json lines
{
  "object": "organization_membership",
  "id": "om_01E4ZCR3C56J083X43JQXF3JK5",
  "user_id": "user_01E4ZCR3C5A4QZ2Z2JQXGKZJ9E",
  "organization_id": "org_01E4ZCR3C56J083X43JQXF3JK5",
  "role": {
    "slug": "member"
  },
  "status": "active",
  "created_at": "2021-06-25T19:07:33.155Z",
  "updated_at": "2021-06-25T19:07:33.155Z"
}
```

### List organization memberships
Get a list of all organization memberships matching the criteria specified. At least one of user_id or organization_id must be provided. By default only active memberships are returned. Use the statuses parameter to filter by other statuses.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

[$before, $after, $users] = $userManagement->listOrganizationMemberships(
    "user_01E4ZCR3C5A4QZ2Z2JQXGKZJ9E"
);
```

Response:
```json lines
{
  "data": [
    {
      "object": "organization_membership",
      "id": "om_01E4ZCR3C56J083X43JQXF3JK5",
      "user_id": "user_01E4ZCR3C5A4QZ2Z2JQXGKZJ9E",
      "organization_id": "org_01E4ZCR3C56J083X43JQXF3JK5",
      "role": {
        "slug": "member"
      },
      "status": "active",
      "created_at": "2021-06-25T19:07:33.155Z",
      "updated_at": "2021-06-25T19:07:33.155Z"
    }
  ],
  "list_metadata": {
    "before": "om_01E4ZCR3C56J083X43JQXF3JK5",
    "after": "om_01EJBGJT2PC6638TN5Y380M40Z"
  }
}
```

### Create an organization membership
Creates a new active organization membership for the given organization and user.

Calling this API with an organization and user that match an inactive organization membership will activate the membership with the specified role.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$organizationMembership = $userManagement->createOrganizationMembership(
    "user_01E4ZCR3C5A4QZ2Z2JQXGKZJ9E",
    "org_01E4ZCR3C56J083X43JQXF3JK5"
);
```

Response:
```json lines
{
  "object": "organization_membership",
  "id": "om_01E4ZCR3C56J083X43JQXF3JK5",
  "user_id": "user_01E4ZCR3C5A4QZ2Z2JQXGKZJ9E",
  "organization_id": "org_01E4ZCR3C56J083X43JQXF3JK5",
  "role": {
    "slug": "admin"
  },
  "status": "active",
  "created_at": "2021-06-25T19:07:33.155Z",
  "updated_at": "2021-06-25T19:07:33.155Z"
}
```

### Update an organization membership
Update the details of an existing organization membership.

Request:
```
curl --request PUT \
  --url https://api.workos.com/user_management/organization_memberships/om_01E4ZCR3C56J083X43JQXF3JK5 \
  --header "Authorization: Bearer sk_example_123456789" \
  --header "Content-Type: application/json" \
  -d @- <<BODY
  {
    "role_slug": "admin"
  }
BODY
```

Response:
```json lines
{
  "object": "organization_membership",
  "id": "om_01E4ZCR3C56J083X43JQXF3JK5",
  "user_id": "user_01E4ZCR3C5A4QZ2Z2JQXGKZJ9E",
  "organization_id": "org_01E4ZCR3C56J083X43JQXF3JK5",
  "role": {
    "slug": "admin"
  },
  "status": "active",
  "created_at": "2021-06-25T19:07:33.155Z",
  "updated_at": "2021-06-27T19:07:33.278Z"
}
```

### Delete an organization membership
Permanently deletes an existing organization membership. It cannot be undone.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$userManagement->deleteOrganizationMembership("om_01E4ZCR3C56J083X43JQXF3JK5");
```

### Deactivate an organization membership
Deactivates an active organization membership. Emits an organization_membership.updated event upon successful deactivation.

- Deactivating an inactive membership is a no-op and does not emit an event.
- Deactivating a pending membership returns an error. This membership should be deleted instead.

See the membership management documentation for additional details.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$organizationMembership = $userManagement->deactivateOrganizationMembership(
    "om_01E4ZCR3C56J083X43JQXF3JK5"
);
```

Response:
```json lines
{
  "object": "organization_membership",
  "id": "om_01E4ZCR3C56J083X43JQXF3JK5",
  "user_id": "user_01E4ZCR3C5A4QZ2Z2JQXGKZJ9E",
  "organization_id": "org_01E4ZCR3C56J083X43JQXF3JK5",
  "role": {
    "slug": "member"
  },
  "status": "inactive",
  "created_at": "2021-06-25T19:07:33.155Z",
  "updated_at": "2021-06-25T19:07:33.155Z"
}
```

### Reactivate an organization membership
Reactivates an inactive organization membership, retaining the pre-existing role. Emits an organization_membership.updated event upon successful reactivation.

- Reactivating an active membership is a no-op and does not emit an event.
- Reactivating a pending membership returns an error. The user needs to accept the invitation instead.

See the membership management documentation for additional details.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$organizationMembership = $userManagement->reactivateOrganizationMembership(
    "om_01E4ZCR3C56J083X43JQXF3JK5"
);
```

Response:
```json lines
{
  "object": "organization_membership",
  "id": "om_01E4ZCR3C56J083X43JQXF3JK5",
  "user_id": "user_01E4ZCR3C5A4QZ2Z2JQXGKZJ9E",
  "organization_id": "org_01E4ZCR3C56J083X43JQXF3JK5",
  "role": {
    "slug": "member"
  },
  "status": "active",
  "created_at": "2021-06-25T19:07:33.155Z",
  "updated_at": "2021-06-25T19:07:33.155Z"
}
```

## Invitation
An email invitation allows the recipient to sign up for your app and join a specific organization. When an invitation is accepted, a user and a corresponding organization membership are created.

Users may be invited to your app without joining an organization, or they may be invited to join an organization if they already have an account. Invitations may be also issued on behalf of another user. In this case, the invitation email will mention the name of the user who invited the recipient.

Example invitation
```php
<?php

$invitation = [
    "object" => "invitation",
    "id" => "invitation_01E4ZCR3C56J083X43JQXF3JK5",
    "email" => "<EMAIL>",
    "state" => "pending",
    "acceptedAt" => null,
    "revokedAt" => null,
    "expiresAt" => "2021-06-25T19:07:33.155Z",
    "token" => "Z1uX3RbwcIl5fIGJJJCXXisdI",
    "acceptInvitationUrl" =>
        "https://your-app.com/invite?invitation_token=Z1uX3RbwcIl5fIGJJJCXXisdI",
    "organizationId" => "org_01E4ZCR3C56J083X43JQXF3JK5",
    "inviterUserId" => "user_01HYGBX8ZGD19949T3BM4FW1C3",
    "createdAt" => "2021-06-25T19:07:33.155Z",
    "updatedAt" => "2021-06-25T19:07:33.155Z",
];
```

### Get an invitation
Get the details of an existing invitation.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$invitation = $userManagement->getInvitation(
    "invitation_01EHZNVPK3SFK441A1RGBFSHRT"
);
```

```json lines
{
  "object": "invitation",
  "id": "invitation_01E4ZCR3C56J083X43JQXF3JK5",
  "email": "<EMAIL>",
  "state": "pending",
  "accepted_at": null,
  "revoked_at": null,
  "expires_at": "2021-07-01T19:07:33.155Z",
  "token": "Z1uX3RbwcIl5fIGJJJCXXisdI",
  "accept_invitation_url": "https://your-app.com/invite?invitation_token=Z1uX3RbwcIl5fIGJJJCXXisdI",
  "organization_id": "org_01E4ZCR3C56J083X43JQXF3JK5",
  "inviter_user_id": "user_01HYGBX8ZGD19949T3BM4FW1C3",
  "created_at": "2021-06-25T19:07:33.155Z",
  "updated_at": "2021-06-25T19:07:33.155Z"
}
```

### Find an invitation by token
Retrieve an existing invitation using the token.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$invitation = $userManagement->findInvitationByToken(
    "Z1uX3RbwcIl5fIGJJJCXXisdI"
);
```

Response:
```json lines
{
  "object": "invitation",
  "id": "invitation_01E4ZCR3C56J083X43JQXF3JK5",
  "email": "<EMAIL>",
  "state": "pending",
  "accepted_at": null,
  "revoked_at": null,
  "expires_at": "2021-07-01T19:07:33.155Z",
  "token": "Z1uX3RbwcIl5fIGJJJCXXisdI",
  "accept_invitation_url": "https://your-app.com/invite?invitation_token=Z1uX3RbwcIl5fIGJJJCXXisdI",
  "organization_id": "org_01E4ZCR3C56J083X43JQXF3JK5",
  "inviter_user_id": "user_01HYGBX8ZGD19949T3BM4FW1C3",
  "created_at": "2021-06-25T19:07:33.155Z",
  "updated_at": "2021-06-25T19:07:33.155Z"
}
```

### List invitations
Get a list of all of invitations matching the criteria specified.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

[$before, $after, $invitations] = $userManagement->listInvitations(
    "org_123456789"
);
```

Response:
```json lines
{
  "data": [
    {
      "object": "invitation",
      "id": "invitation_01E4ZCR3C56J083X43JQXF3JK5",
      "email": "<EMAIL>",
      "state": "pending",
      "accepted_at": null,
      "revoked_at": null,
      "expires_at": "2021-07-01T19:07:33.155Z",
      "token": "Z1uX3RbwcIl5fIGJJJCXXisdI",
      "accept_invitation_url": "https://your-app.com/invite?invitation_token=Z1uX3RbwcIl5fIGJJJCXXisdI",
      "organization_id": "org_01E4ZCR3C56J083X43JQXF3JK5",
      "inviter_user_id": "user_01HYGBX8ZGD19949T3BM4FW1C3",
      "created_at": "2021-06-25T19:07:33.155Z",
      "updated_at": "2021-06-25T19:07:33.155Z"
    }
  ],
  "list_metadata": {
    "before": "invitation_01E4ZCR3C56J083X43JQXF3JK5",
    "after": "invitation_01EJBGJT2PC6638TN5Y380M40Z"
  }
}
```

### Send an invitation
Sends an invitation email to the recipient.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$invitation = $userManagement->sendInvitation("<EMAIL>");
```

Response:
```json lines
{
  "object": "invitation",
  "id": "invitation_01E4ZCR3C56J083X43JQXF3JK5",
  "email": "<EMAIL>",
  "state": "pending",
  "accepted_at": null,
  "revoked_at": null,
  "expires_at": "2021-07-01T19:07:33.155Z",
  "token": "Z1uX3RbwcIl5fIGJJJCXXisdI",
  "accept_invitation_url": "https://your-app.com/invite?invitation_token=Z1uX3RbwcIl5fIGJJJCXXisdI",
  "organization_id": "org_01E4ZCR3C56J083X43JQXF3JK5",
  "inviter_user_id": "user_01HYGBX8ZGD19949T3BM4FW1C3",
  "created_at": "2021-06-25T19:07:33.155Z",
  "updated_at": "2021-06-25T19:07:33.155Z"
}
```

### Accept an invitation
Accepts an invitation and, if linked to an organization, activates the user’s membership in that organization.

In most cases, use existing authentication methods like authenticateWithCode, which also accept an invitation token. These methods offer the same functionality (invitation acceptance and membership activation) while also signing the user in.

This method is useful for apps requiring a highly customized invitation flow, as it focuses solely on handling invitations without authentication. It’s also helpful when users can be invited to multiple organizations and need a way to accept invitations after signing in.

Your application should verify that the invitation is intended for the user accepting it. For example, by fetching the invitation using the find-by-token endpoint and ensuring the email matches the email address of the accepting user.

Request:
```
curl --request POST \
  --url https://api.workos.com/user_management/invitations/invitation_01E4ZCR3C56J083X43JQXF3JK5/accept \
  --header "Authorization: Bearer sk_example_123456789"
```

Response:
```json lines
{
  "object": "invitation",
  "id": "invitation_01E4ZCR3C56J083X43JQXF3JK5",
  "email": "<EMAIL>",
  "state": "accepted",
  "accepted_at": "2021-06-27T19:07:33.155Z",
  "revoked_at": null,
  "expires_at": "2021-07-01T19:07:33.155Z",
  "token": "Z1uX3RbwcIl5fIGJJJCXXisdI",
  "accept_invitation_url": "https://your-app.com/invite?invitation_token=Z1uX3RbwcIl5fIGJJJCXXisdI",
  "organization_id": "org_01E4ZCR3C56J083X43JQXF3JK5",
  "inviter_user_id": "user_01HYGBX8ZGD19949T3BM4FW1C3",
  "accepted_user_id": "user_01JBJDMMV04RSWPG30MQE8ADFV",
  "created_at": "2021-06-25T19:07:33.155Z",
  "updated_at": "2021-06-25T19:07:33.155Z"
}
```

### Revoke an invitation
Revokes an existing invitation.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$invitation = $userManagement->revokeInvitation(
    "invitation_01E4ZCR3C56J083X43JQXF3JK5"
);
```

Response:
```json lines
{
  "object": "invitation",
  "id": "invitation_01E4ZCR3C56J083X43JQXF3JK5",
  "email": "<EMAIL>",
  "state": "revoked",
  "accepted_at": null,
  "revoked_at": "2021-07-01T19:07:33.155Z",
  "expires_at": "2021-07-01T19:07:33.155Z",
  "token": "Z1uX3RbwcIl5fIGJJJCXXisdI",
  "accept_invitation_url": "https://your-app.com/invite?invitation_token=Z1uX3RbwcIl5fIGJJJCXXisdI",
  "organization_id": "org_01E4ZCR3C56J083X43JQXF3JK5",
  "inviter_user_id": "user_01HYGBX8ZGD19949T3BM4FW1C3",
  "accepted_user_id": null,
  "created_at": "2021-06-25T19:07:33.155Z",
  "updated_at": "2021-06-25T19:07:33.155Z"
}
```

## Logout
End a user’s session. The user’s browser should be redirected to this URL.

### Get logout URL
Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");

$userManagement = new WorkOS\UserManagement();

$logoutUrl = $userManagement->getLogoutUrl(
    "session_01HQAG1HENBZMAZD82YRXDFC0B",
    return_to: "https://your-app.com/signed-out"
);
```

Response: `https://api.workos.com/user_management/sessions/logout?
session_id=session_01HQAG1HENBZMAZD82YRXDFC0B&return_to=https%3A%2F%your-app.com%2Fsigned-out`

### Get logout URL from session cookie
Generates the logout URL by extracting the session ID from the session cookie. Use this over getLogoutUrl if you don’t have a saved reference to the session ID and you’d like the SDK to handle extracting the session ID from the cookie for you.

```javascript
import { WorkOS } from '@workos-inc/node';

const workos = new WorkOS('sk_test_123');

const logoutUrl = workos.userManagement.getLogoutUrlFromSessionCookie({
  sessionData: 'sealed_session_cookie_data',
  cookiePassword: 'password_previously_used_to_seal_session_cookie',
});
```

Response: `https://api.workos.com/user_management/sessions/logout?
session_id=session_01HQAG1HENBZMAZD82YRXDFC0B`
