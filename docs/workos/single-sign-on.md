# Single Sign-On
The Single Sign-On API has been modeled to meet the OAuth 2.0 framework specification. As a result, authentication flows constructed using the Single Sign-On API replicate the OAuth 2.0 protocol flow.

To automatically respond to changes in your SSO connections, use the Connection events.

### Get an authorization URL
Generates an OAuth 2.0 authorization URL to authenticate a user with SSO.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");
WorkOS\WorkOS::setClientId("client_123456789");

$sso = new WorkOS\SSO();

$authorizationUrl = $sso->getAuthorizationUrl(
    redirectUri: "https://your-app.com/callback",
    state: "dj1kUXc0dzlXZ1hjUQ==",
    connection: "conn_01E4ZCR3C56J083X43JQXF3JK5"
);
```

Response:
```
https://api.workos.com/sso/authorize?
response_type=code&
client_id=client_123456789&
redirect_uri=https://your-app.com/callback&
state=dj1kUXc0dzlXZ1hjUQ==&
connection=conn_01E4ZCR3C56J083X43JQXF3JK5
```

You’ll have to specify the user’s connection, organization, or OAuth provider as a parameter. These connection selectors are mutually exclusive, and exactly one must be provided. The generated URL automatically directs the user to their identity provider. Once the user authenticates with their identity provider, WorkOS then issues a redirect to your redirect URI to complete the sign-in flow.

#### Redirect URI
In the OAuth 2.0 protocol, a redirect URI is the location that the user is redirected to once they have successfully authenticated with their identity provider.

When redirecting the user, WorkOS will generate an authorization code and pass it to your redirect URI as a code query parameter, your app will use this code to get the user’s profile. Additionally, WorkOS can pass a state parameter back to your application that you may use to encode arbitrary information to restore your application state between the redirects.

Redirect URI with query parameters
`https://your-app.com/callback?code=01E2RJ4C05B52KKZ8FSRDAP23J&state=dj1kUXc0dzlXZ1hjUQ==`

You’ll need to configure the allowed redirect URIs for your application via the Redirects page in the dashboard. Without a valid redirect URI, your users will be unable to sign in. Make sure that the redirect URI you use as a parameter to get the authorization URL matches one of the redirect URIs you have configured in the dashboard.

Redirect URIs follow stricter requirements in production environments:

- HTTPS protocol is required in production environments
- HTTP and localhost are allowed in staging environments
- Wildcard characters are not allowed in production environments

#### Error codes
If there is an issue generating an authorization URL, the API will return the original redirect URI with error and error_description query parameters. If provided, the state value will also be included.

Redirect URI with an error code: `https://your-app.com/callback?error=organization_invalid&error_description=No%20connection%20associated%20with%20organization&state=123456789`

Possible error codes and the corresponding descriptions are listed below.

| Error Code                             | Description                                                                                                                                                                                                                                                                                                                             |
|----------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| access_denied                          | The user denied an OAuth authorization request at the identity provider.                                                                                                                                                                                                                                                               |
| ambiguous_connection_selector          | A connection could not be uniquely identified using the provided connection selector (e.g., organization). This can occur when there are multiple SSO connections under the same organization. If you need multiple SSO connections for an organization, use the connection parameter to identify which connection to use for SSO.                         |
| connection_domain_invalid              | There is no connection for the provided domain.                                                                                                                                                                                                                                                                                        |
| connection_invalid                     | There is no connection for the provided ID.                                                                                                                                                                                                                                                                                            |
| connection_strategy_invalid            | The provider has multiple strategies associated per environment.                                                                                                                                                                                                                                                                      |
| connection_unlinked                    | The connection associated with the request is unlinked.                                                                                                                                                                                                                                                                                |
| domain_connection_selector_not_allowed | This is a legacy error code that only applies if using the deprecated “domain” query parameter which is no longer valid for this endpoint. Use the “organization” or “connection” query parameters to target a connection instead.                                                                                                       |
| invalid_connection_selector            | A valid connection selector query parameter must be provided in order to correctly determine the proper connection to return an authorization URL for. Valid connection selectors are either connection, organization, or provider.                                                                                                       |
| organization_invalid                   | There is no organization matching the provided ID.                                                                                                                                                                                                                                                                                     |
| oauth_failed                           | An OAuth authorization request failed for a user.                                                                                                                                                                                                                                                                                      |
| profile_not_allowed_outside_organization| A profile was received that has an email that is outside the organization’s domain and the organization does not allow this. To resolve this, add the missing domain to the organization’s Domains. You can read about other options in the SSO Domains guide.                                                                              |
| server_error                           | The SSO authentication failed for the user. More detailed errors and steps to resolve are available in the Sessions tab on the connection page in the WorkOS Dashboard.                                                                                                                                                                 |

### Profile
A Profile is an object that represents an authenticated user. The Profile object contains information relevant to a user in the form of normalized attributes.

After receiving the Profile for an authenticated user, use the Profile object attributes to persist relevant data to your application’s user model for the specific, authenticated user.

No Profile attributes can be returned other than the normalized attributes listed below.

Example Profile
```php
<?php

$profile = [
    "id" => "prof_01DMC79VCBZ0NY2099737PSVF1",
    "connectionId" => "conn_01E4ZCR3C56J083X43JQXF3JK5",
    "connectionType" => "OktaSAML",
    "organizationId" => "org_01EHWNCE74X7JSDV0X3SZ3KJNY",
    "email" => "<EMAIL>",
    "firstName" => "Todd",
    "lastName" => "Rundgren",
    "idpId" => "00u1a0ufowBJlzPlk357",
    "role" => [
        "slug" => "admin",
    ],
    "rawAttributes" => [],
];
```

### Get a Profile and Token
Get an access token along with the user Profile using the code passed to your Redirect URI.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");
WorkOS\WorkOS::setClientId("client_123456789");

$sso = new WorkOS\SSO();

$profileAndToken = $sso->getProfileAndToken("01E2RJ4C05B52KKZ8FSRDAP23J");
```

Response:
```json lines
{
  "access_token": "01DMEK0J53CVMC32CK5SE0KZ8Q",
  "profile": {
    "object": "profile",
    "id": "prof_01DMC79VCBZ0NY2099737PSVF1",
    "connection_id": "conn_01E4ZCR3C56J083X43JQXF3JK5",
    "connection_type": "OktaSAML",
    "organization_id": "org_01EHWNCE74X7JSDV0X3SZ3KJNY",
    "email": "<EMAIL>",
    "first_name": "Todd",
    "last_name": "Rundgren",
    "idp_id": "00u1a0ufowBJlzPlk357",
    "role": { "slug": "admin" },
    "raw_attributes": {}
  }
}
```

### Get a User Profile
Exchange an access token for a user’s Profile. Because this profile is returned in the Get a Profile and Token endpoint your application usually does not need to call this endpoint. It is available for any authentication flows that require an additional endpoint to retrieve a user’s profile.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");
WorkOS\WorkOS::setClientId("client_123456789");

$sso = new WorkOS\SSO();

$profile = $sso->getProfile("01DMEK0J53CVMC32CK5SE0KZ8Q");
```

Response:
```json lines
{
  "object": "profile",
  "id": "prof_01DMC79VCBZ0NY2099737PSVF1",
  "connection_id": "conn_01E4ZCR3C56J083X43JQXF3JK5",
  "connection_type": "OktaSAML",
  "organization_id": "org_01EHWNCE74X7JSDV0X3SZ3KJNY",
  "email": "<EMAIL>",
  "first_name": "Todd",
  "last_name": "Rundgren",
  "idp_id": "00u1a0ufowBJlzPlk357",
  "role": {
    "slug": "admin"
  },
  "raw_attributes": {}
}
```

### Connection
A connection represents the relationship between WorkOS and any collection of application users. This collection of application users may include personal or enterprise identity providers. As a layer of abstraction, a WorkOS connection rests between an application and its users, separating an application from the implementation details required by specific standards like OAuth 2.0 and SAML.

See the events reference documentation for the connection events.

Example Connection
```php
<?php

$connection = [
    "id" => "conn_01E4ZCR3C56J083X43JQXF3JK5",
    "organizationId" => "org_01EHWNCE74X7JSDV0X3SZ3KJNY",
    "connectionType" => "OktaSAML",
    "name" => "Foo Corp",
    "state" => "active",
    "status" => "linked",
    "createdAt" => "2021-06-25T19:07:33.155Z",
    "updatedAt" => "2021-06-25T19:07:33.155Z",
    "domains" => [
        [
            "id" => "org_domain_01EHZNVPK2QXHMVWCEDQEKY69A",
            "object" => "connection_domain",
            "domain" => "foo-corp.com",
        ],
    ],
];
```

### Get a Connection
Get the details of an existing connection.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");
WorkOS\WorkOS::setClientId("client_123456789");

$sso = new WorkOS\SSO();

$connection = $sso->getConnection("conn_01E4ZCR3C56J083X43JQXF3JK5");
```

Response:
```json lines
{
  "object": "connection",
  "id": "conn_01E4ZCR3C56J083X43JQXF3JK5",
  "organization_id": "org_01EHWNCE74X7JSDV0X3SZ3KJNY",
  "connection_type": "OktaSAML",
  "name": "Foo Corp",
  "state": "active",
  "created_at": "2021-06-25T19:07:33.155Z",
  "updated_at": "2021-06-25T19:07:33.155Z",
  "domains": [
    {
      "id": "org_domain_01EHZNVPK2QXHMVWCEDQEKY69A",
      "object": "connection_domain",
      "domain": "foo-corp.com"
    }
  ]
}
```

### List Connections
Get a list of all of your existing connections matching the criteria specified.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");
WorkOS\WorkOS::setClientId("client_123456789");

$sso = new WorkOS\SSO();

[$before, $after, $connections] = $sso->listConnections();
```

Response:
```json lines
{
  "data": [
    {
      "object": "connection",
      "id": "conn_01E4ZCR3C56J083X43JQXF3JK5",
      "organization_id": "org_01EHWNCE74X7JSDV0X3SZ3KJNY",
      "connection_type": "GoogleOAuth",
      "name": "Foo Corp",
      "state": "active",
      "created_at": "2021-06-25T19:07:33.155Z",
      "updated_at": "2021-06-25T19:08:33.155Z"
    },
    {
      "object": "connection",
      "id": "conn_01E2NPPCT7XQ2MVVYDHWGK1WN4",
      "organization_id": "org_01EHWNCE74X7JSDV0X3SZ3KJNY",
      "connection_type": "OktaSAML",
      "name": "Example Co",
      "state": "active",
      "created_at": "2021-06-25T19:09:33.155Z",
      "updated_at": "2021-06-25T19:10:33.155Z"
    }
  ],
  "list_metadata": {
    "before": "conn_01E2NPPCT7XQ2MVVYDHWGK1WN4",
    "after": null
  }
}
```

### Delete a Connection
Permanently deletes an existing connection. It cannot be undone.

Request:
```php
<?php

WorkOS\WorkOS::setApiKey("sk_example_123456789");
WorkOS\WorkOS::setClientId("client_123456789");

$sso = new WorkOS\SSO();

$sso->deleteConnection("conn_01E2NPPCT7XQ2MVVYDHWGK1WN4");
```
