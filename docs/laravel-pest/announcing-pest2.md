---
title: Announcing Pest 2.0
description: The Pest team is thrilled to unveil the release of Pest 2.0 after a development period of 18 months and over 500 commits. This release introduces several exciting features that promise to improve the user's experience. Among the notable enhancements are robust new plugins, refined syntax, and advanced options that streamline testing, enhance usability, and boost productivity.
---

# Announcing Pest 2.0

The Pest team is thrilled to unveil the release of Pest 2.0 after a development period of 18 months and over 500 commits. This release introduces several exciting features that promise to improve the user's experience. Among the notable enhancements are robust new plugins, refined syntax, and advanced options that streamline testing, enhance usability, and boost productivity.

Today we’re finally making the long-awaited release of Pest 2.0! Our creator is eager to showcase the exciting new features this version has to offer. Tune in to the video below to learn more.

<div class="content-center" markdown="0">
    <iframe width="560" height="315" src="https://www.youtube.com/embed/9EGPo_enEc8" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe>
</div>

Pest 2.0 marks a major milestone in our development, packed with an array of powerful features such as:

- **[Powerful Architecture Plugin](/docs/arch-testing)**, for testing the architectural rules of your application with ease
- **[Up To 80% Speed Improvements on "--parallel" testing](/docs/optimizing-tests#parallel)**, with our fully rewritten parallel core, enjoy significantly faster test runs
- **[--profile option](/docs/optimizing-tests#content-profiling)**, to identify the slowest tests and optimize their execution
- **[--compact printer](/docs/optimizing-tests#content-compact-printer)**, a minimal printer that only outputs information about test failures
- **[--retry option](/docs/filtering-tests#retry)**, for saving time by running only previously unsuccessful tests
- **[--dirty option](/docs/filtering-tests#dirty)**, for only running tests with uncommitted changes
- **[--bail option](/docs/filtering-tests#bail)**, to immediately terminate the test suite upon encountering an error or failure.
- **[todo()](/docs/skipping-tests#content-creating-todos)** method, for creating todos within your test suite
- **[Expectation Interceptors and Pipes](/docs/custom-expectations#content-intercept-expectations)**, allowing you to tailor your expectations to fit your specific testing needs
- **[Scoped Datasets](/docs/datasets#content-scoped-datasets)**, for creating datasets that pertain only to a specific feature or set of folders

In addition to the features detailed above, there's so much more to explore with Pest 2.0! **Our website has been completely revamped**, with fresh documentation and a more user-friendly interface. There's never been a better time to dive in and start exploring.

If you're ready to get started with Pest 2.0 right away, check out our [installation guide](/docs/installation) for step-by-step instructions. And if you're currently using Pest 1, we've got you covered with detailed upgrade instructions in our [upgrade guide](/docs/upgrade-guide).

---

Thank you for reading about Pest 2.0's new features! If you're considering a testing framework for your next project, here's why you should give Pest a try: [Why Pest →](/docs/why-pest)
