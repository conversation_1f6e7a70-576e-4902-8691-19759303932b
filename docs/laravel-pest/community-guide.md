---
title: Community Guide
description: Our project aims to develop the world's finest testing framework that not only establishes itself as the standard choice in the PHP ecosystem, but also serves as a catalyst for change and inspiration in other ecosystems.
---

# Community Guide

Our project aims to develop the world's finest testing framework that not only establishes itself as the standard choice in the PHP ecosystem, but also serves as a catalyst for change and inspiration in other ecosystems.

Your contribution is crucial to achieving our ambitious goal. We strongly believe that the PHP ecosystem has the talent and work ethic necessary to accomplish this goal. In the following section, we will outline the various areas where you can lend your assistance and become an integral part of our mission.

**Develop Educational Resources:** There is a popular saying that the best way to learn is to teach. If you have something interesting to share about your experience with Pest, you can reinforce your knowledge by creating a blog post, conducting a workshop, creating a video, or even publishing a gist.

**Help Fellow Users:** It's important to remember that contributing to the Pest's  growth goes beyond just writing code. Providing assistance to other Pest users is a valuable form of contribution as well. At this time, we are present on Discord and Telegram. However, you are free to create other community channels.

> Discord: **[discord.gg/kaHY6p54JH](https://discord.gg/kaHY6p54JH)**

> Telegram: **[t.me/+kYH5G4d5MV83ODk0](https://t.me/+kYH5G4d5MV83ODk0)**

**Improve Our Documentation:** If you have strong writing skills, you can assist us in enhancing Pest's documentation and coding examples. Simply navigate to a documentation page and click on the "Edit this page →" option located on the top right to get started.

> Pest Documentation Repository: **[github.com/pestphp/docs](https://github.com/pestphp/docs)**

**Speak At Meetups / Conferences:** Delivering a talk or workshop can be a great way to contribute to the growth of Pest. You need not necessarily prepare something entirely from scratch, as there are already several conference talks on Pest available on YouTube that you can use as inspiration for your own talk.

**Become a Community Leader:** By becoming a testing (or Pest) advocate, you can increase its reach and impact. Start by sharing testing tips on social media platforms like Twitter and LinkedIn, and you may be surprised by the significant impact it can have on Pest's growth.

> Twitter: **[@pestphp](https://twitter.com/pestphp)**

**Become a Code Contributor:** If you have ideas for improvements or new features that can be introduced in Pest, you're welcome to share them on the Pest repository's [GitHub issues board](https://github.com/pestphp/pest/issues) or [GitHub discussion board](https://github.com/pestphp/pest/discussions). If you propose a new feature, please consider contributing some of the code needed to implement it. Please keep in mind that discussions regarding Pest development, including bugs, new features, and related topics, take place on GitHub, not through email or Twitter DMs.

> Pest GitHub Repository: **[github.com/pestphp/pest](https://github.com/pestphp/pest)**
