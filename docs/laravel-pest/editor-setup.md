---
title: Editor Setup
description: An editor plugin can significantly enhance the developer experience when working with Pest PHP. Although most editors have built-in support for Pest PHP, plugins can offer additional functionalities that can streamline and simplify the development process.
---

---

Once the installation process is complete, and your editor is ready, you can learn more about how to write tests visiting the next section of the documentation: [Writing Tests →](/docs/writing-tests)
