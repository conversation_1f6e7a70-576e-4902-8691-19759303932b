---
title: Commands
weight: 16
---

## make:data

You can easily generate new data objects with the artisan command `make:data`:

```shell
php artisan make:data PostData
```

By default, this command puts data objects in the `App\Data` namespace, this can be changed as such:

```shell
php artisan make:data PostData --namespace=DataTransferObjects
```

By default, the command creates a new data object within the `\App\Data` namespace and suffixes the class with `Data`, this can be changed by adding the following lines to the `data.php` config file:

```php
'commands' => [
    /*
     * Provides default configuration for the `make:data` command. These settings can be overridden with options
     * passed directly to the `make:data` command for generating single Data classes, or if not set they will
     * automatically fall back to these defaults. See `php artisan make:data --help` for more information
     */
    'make' => [
        /*
         * The default namespace for generated Data classes. This exists under the application's root namespace,
         * so the default 'Data` will end up as '\App\Data', and generated Data classes will be placed in the
         * app/Data/ folder. Data classes can live anywhere, but this is where `make:data` will put them.
         */
        'namespace' => 'Data',
        
        /*
         * This suffix will be appended to all data classes generated by make:data, so that they are less likely
         * to conflict with other related classes, controllers or models with a similar name without resorting
         * to adding an alias for the Data object. Set to a blank string (not null) to disable.
         */
        'suffix' => 'Data',
    ],
]
```
