{"openapi": "3.0.1", "info": {"description": "Samsara API specification for safety related endpoints and schemas.", "title": "Samsara API - Safety", "version": "2025-05-12"}, "servers": [{"url": "https://api.samsara.com/"}, {"url": "https://api.eu.samsara.com/"}], "security": [{"AccessTokenHeader": []}], "components": {"parameters": {"V1harshEventMsParam": {"description": "Timestamp in milliseconds representing the timestamp of a harsh event.", "in": "query", "name": "timestamp", "required": true, "schema": {"format": "int64", "type": "integer"}}, "V1safetyScoreEndMsParam": {"description": "Timestamp in milliseconds representing the end of the period to fetch, inclusive. Used in combination with startMs. Total duration (endMs - startMs) must be greater than or equal to 1 hour.", "in": "query", "name": "endMs", "required": true, "schema": {"format": "int64", "type": "integer"}}, "V1safetyScoreStartMsParam": {"description": "Timestamp in milliseconds representing the start of the period to fetch, inclusive. Used in combination with endMs. Total duration (endMs - startMs) must be greater than or equal to 1 hour.", "in": "query", "name": "startMs", "required": true, "schema": {"format": "int64", "type": "integer"}}}, "requestBodies": {}, "schemas": {"AdvancedIdlingGetIdlingEventsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AdvancedIdlingGetIdlingEventsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AdvancedIdlingGetIdlingEventsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AdvancedIdlingGetIdlingEventsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AdvancedIdlingGetIdlingEventsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AdvancedIdlingGetIdlingEventsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AdvancedIdlingGetIdlingEventsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AdvancedIdlingGetIdlingEventsResponseBody": {"properties": {"data": {"description": "List of idling event objects.", "items": {"$ref": "#/components/schemas/IdlingEventObjectResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "AdvancedIdlingGetIdlingEventsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AdvancedIdlingGetIdlingEventsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AdvancedIdlingGetIdlingEventsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormsScoreObjectResponseBody": {"description": "Forms score object.", "properties": {"maxPoints": {"description": "Total possible points of the form submission.", "example": 80, "format": "double", "type": "number"}, "scorePercent": {"description": "Percentage score of the form submission, calculated as scorePoints / maxPoints.", "example": 75, "format": "double", "type": "number"}, "scorePoints": {"description": "Score, in points, of the form submission.", "example": 60, "format": "double", "type": "number"}}, "required": ["maxPoints", "scorePercent", "scorePoints"], "type": "object"}, "HarshAccelSensitivityGForceSettingsObjectResponseBody": {"description": "The harsh acceleration sensitivity settings.", "properties": {"heavyDuty": {"description": "Harsh acceleration sensitivity settings for heavy duty vehicle.", "example": "0.29", "type": "string"}, "lightDuty": {"description": "Harsh acceleration sensitivity settings for light duty vehicle.", "example": "0.33", "type": "string"}, "passenger": {"description": "Harsh acceleration sensitivity settings for passenger car.", "example": "0.43", "type": "string"}}, "type": "object"}, "HarshAccelSensitivityV2SettingsObjectResponseBody": {"description": "The harsh acceleration sensitivity settings.", "properties": {"heavyDuty": {"description": "Harsh acceleration sensitivity settings for heavy duty vehicle.  Valid values: `unknown`, `invalid`, `off`, `low`, `normal`, `high`", "enum": ["unknown", "invalid", "off", "low", "normal", "high"], "example": "normal", "type": "string"}, "lightDuty": {"description": "Harsh acceleration sensitivity settings for light duty vehicle.  Valid values: `unknown`, `invalid`, `off`, `low`, `normal`, `high`", "enum": ["unknown", "invalid", "off", "low", "normal", "high"], "example": "normal", "type": "string"}, "passenger": {"description": "Harsh acceleration sensitivity settings for passenger car.  Valid values: `unknown`, `invalid`, `off`, `low`, `normal`, `high`", "enum": ["unknown", "invalid", "off", "low", "normal", "high"], "example": "normal", "type": "string"}}, "type": "object"}, "HarshBrakeSensitivityGForceSettingsObjectResponseBody": {"description": "The harsh brake sensitivity settings.", "properties": {"heavyDuty": {"description": "Harsh brake sensitivity settings for heavy duty vehicle.", "example": "0.29", "type": "string"}, "lightDuty": {"description": "Harsh brake sensitivity settings for light duty vehicle.", "example": "0.33", "type": "string"}, "passenger": {"description": "Harsh brake sensitivity settings for passenger car.", "example": "0.43", "type": "string"}}, "type": "object"}, "HarshBrakeSensitivityV2SettingsObjectResponseBody": {"description": "The harsh brake sensitivity settings.", "properties": {"heavyDuty": {"description": "Harsh brake sensitivity settings for heavy duty vehicle.  Valid values: `unknown`, `invalid`, `off`, `veryLow`, `low`, `normal`, `high`", "enum": ["unknown", "invalid", "off", "veryLow", "low", "normal", "high"], "example": "normal", "type": "string"}, "lightDuty": {"description": "Harsh brake sensitivity settings for light duty vehicle.  Valid values: `unknown`, `invalid`, `off`, `veryLow`, `low`, `normal`, `high`", "enum": ["unknown", "invalid", "off", "veryLow", "low", "normal", "high"], "example": "normal", "type": "string"}, "passenger": {"description": "Harsh brake sensitivity settings for passenger car.  Valid values: `unknown`, `invalid`, `off`, `veryLow`, `low`, `normal`, `high`", "enum": ["unknown", "invalid", "off", "veryLow", "low", "normal", "high"], "example": "normal", "type": "string"}}, "type": "object"}, "HarshEventDataResponseBody": {"description": "Details specific to Harsh Event.", "properties": {"driver": {"$ref": "#/components/schemas/alertObjectDriverResponseBody"}, "vehicle": {"$ref": "#/components/schemas/alertObjectVehicleResponseBody"}}, "type": "object"}, "HarshEventSensitivitySettingsObjectResponseBody": {"description": "The configurable sensitivity for Harsh Event Detection on CM11/CM12/CM22 devices. Sensitivity can be measured as a numeric g-force value or the following values: `Normal`, `Less Sensitive`, `More Sensitive`.", "properties": {"harshAccelSensitivityGForce": {"$ref": "#/components/schemas/HarshAccelSensitivityGForceSettingsObjectResponseBody"}, "harshBrakeSensitivityGForce": {"$ref": "#/components/schemas/HarshBrakeSensitivityGForceSettingsObjectResponseBody"}, "harshTurnSensitivityGForce": {"$ref": "#/components/schemas/HarshTurnSensitivityGForceSettingsObjectResponseBody"}}, "type": "object"}, "HarshEventSensitivityV2SettingsObjectResponseBody": {"description": "The configurable sensitivity for Harsh Event Detection. Does not apply to CM11/12/22 devices.", "properties": {"harshAccelSensitivity": {"$ref": "#/components/schemas/HarshAccelSensitivityV2SettingsObjectResponseBody"}, "harshBrakeSensitivity": {"$ref": "#/components/schemas/HarshBrakeSensitivityV2SettingsObjectResponseBody"}, "harshTurnSensitivity": {"$ref": "#/components/schemas/HarshTurnSensitivityV2SettingsObjectResponseBody"}}, "type": "object"}, "HarshEventTriggerDetailsObjectRequestBody": {"description": "Details specific to Harsh Events", "properties": {"types": {"description": "On which harsh events to trigger on.", "example": ["haSmokingPolicy", "haLaneDeparture", "haFoodPolicy", "haSharpTurn"], "items": {"description": "The type of harsh event.  Valid values: `haAccel`, `haBraking`, `haCameraMisaligned`, `haCrash`, `haDistractedDriving`, `haDistractedDrivingCalibration`, `haDrinkPolicy`, `haDriverObstructionPolicy`, `haDrowsinessDetection`, `haEvent`, `haFalsePositive`, `haFoodPolicy`, `haInvalid`, `haLaneDeparture`, `haMaskPolicy`, `haNearCollision`, `haOutwardObstructionPolicy`, `haPassengerPolicy`, `haPhonePolicy`, `haPolicyDetector`, `haRearCollisionWarning`, `haRolledStopSign`, `haRollover`, `haRolloverProtectionBrakeControlActivated`, `haRolloverProtectionEngineControlActivated`, `haSeatbeltPolicy`, `haSharpTurn`, `haSignDetection`, `haSmokingPolicy`, `haSpeeding`, `haTailgating`, `haTileRollingRailroadCrossing`, `haTileRollingStopSign`, `haUnsafeParking`, `haVulnerableRoadUserCollisionWarning`, `haYawControlBrakeControlActivated`, `haYawControlEngineControlActivated`", "enum": ["haAccel", "haBraking", "haCameraMisaligned", "haCrash", "haDistractedDriving", "haDistractedDrivingCalibration", "haDrinkPolicy", "haDriverObstructionPolicy", "haDrowsinessDetection", "haEvent", "haFalsePositive", "haFoodPolicy", "haInvalid", "haLaneDeparture", "haMaskPolicy", "haNearCollision", "haOutwardObstructionPolicy", "haPassengerPolicy", "haPhonePolicy", "haPolicyDetector", "haRearCollisionWarning", "haRolledStopSign", "ha<PERSON><PERSON>over", "haRolloverProtectionBrakeControlActivated", "haRolloverProtectionEngineControlActivated", "haSeatbeltPolicy", "haSharpTurn", "haSignDetection", "haSmokingPolicy", "haSpeeding", "haTailgating", "haTileRollingRailroadCrossing", "haTileRollingStopSign", "haUnsafeParking", "haVulnerableRoadUserCollisionWarning", "haYawControlBrakeControlActivated", "haYawControlEngineControlActivated"], "example": "haTailgating", "type": "string"}, "type": "array"}}, "required": ["types"], "type": "object"}, "HarshEventTriggerDetailsObjectResponseBody": {"description": "Details specific to Harsh Events", "properties": {"types": {"description": "On which harsh events to trigger on.", "example": ["haYawControlBrakeControlActivated", "haFoodPolicy"], "items": {"description": "The type of harsh event.  Valid values: `haAccel`, `haBraking`, `haCameraMisaligned`, `haCrash`, `haDistractedDriving`, `haDistractedDrivingCalibration`, `haDrinkPolicy`, `haDriverObstructionPolicy`, `haDrowsinessDetection`, `haEvent`, `haFalsePositive`, `haFoodPolicy`, `haInvalid`, `haLaneDeparture`, `haMaskPolicy`, `haNearCollision`, `haOutwardObstructionPolicy`, `haPassengerPolicy`, `haPhonePolicy`, `haPolicyDetector`, `haRearCollisionWarning`, `haRolledStopSign`, `haRollover`, `haRolloverProtectionBrakeControlActivated`, `haRolloverProtectionEngineControlActivated`, `haSeatbeltPolicy`, `haSharpTurn`, `haSignDetection`, `haSmokingPolicy`, `haSpeeding`, `haTailgating`, `haTileRollingRailroadCrossing`, `haTileRollingStopSign`, `haUnsafeParking`, `haVulnerableRoadUserCollisionWarning`, `haYawControlBrakeControlActivated`, `haYawControlEngineControlActivated`", "enum": ["haAccel", "haBraking", "haCameraMisaligned", "haCrash", "haDistractedDriving", "haDistractedDrivingCalibration", "haDrinkPolicy", "haDriverObstructionPolicy", "haDrowsinessDetection", "haEvent", "haFalsePositive", "haFoodPolicy", "haInvalid", "haLaneDeparture", "haMaskPolicy", "haNearCollision", "haOutwardObstructionPolicy", "haPassengerPolicy", "haPhonePolicy", "haPolicyDetector", "haRearCollisionWarning", "haRolledStopSign", "ha<PERSON><PERSON>over", "haRolloverProtectionBrakeControlActivated", "haRolloverProtectionEngineControlActivated", "haSeatbeltPolicy", "haSharpTurn", "haSignDetection", "haSmokingPolicy", "haSpeeding", "haTailgating", "haTileRollingRailroadCrossing", "haTileRollingStopSign", "haUnsafeParking", "haVulnerableRoadUserCollisionWarning", "haYawControlBrakeControlActivated", "haYawControlEngineControlActivated"], "example": "haDrinkPolicy", "type": "string"}, "type": "array"}}, "required": ["types"], "type": "object"}, "HarshTurnSensitivityGForceSettingsObjectResponseBody": {"description": "The harsh turn sensitivity settings.", "properties": {"heavyDuty": {"description": "Harsh turn sensitivity settings for heavy duty vehicle.", "example": "0.29", "type": "string"}, "lightDuty": {"description": "Harsh turn sensitivity settings for light duty vehicle.", "example": "0.33", "type": "string"}, "passenger": {"description": "Harsh turn sensitivity settings for passenger car.", "example": "0.43", "type": "string"}}, "type": "object"}, "HarshTurnSensitivityV2SettingsObjectResponseBody": {"description": "The harsh turn sensitivity settings.", "properties": {"heavyDuty": {"description": "Harsh turn sensitivity settings for heavy duty vehicle.  Valid values: `unknown`, `invalid`, `off`, `veryLow`, `low`, `normal`, `high`", "enum": ["unknown", "invalid", "off", "veryLow", "low", "normal", "high"], "example": "normal", "type": "string"}, "lightDuty": {"description": "Harsh turn sensitivity settings for light duty vehicle.  Valid values: `unknown`, `invalid`, `off`, `veryLow`, `low`, `normal`, `high`", "enum": ["unknown", "invalid", "off", "veryLow", "low", "normal", "high"], "example": "normal", "type": "string"}, "passenger": {"description": "Harsh turn sensitivity settings for passenger car.  Valid values: `unknown`, `invalid`, `off`, `veryLow`, `low`, `normal`, `high`", "enum": ["unknown", "invalid", "off", "veryLow", "low", "normal", "high"], "example": "normal", "type": "string"}}, "type": "object"}, "IdlingEventObjectResponseBody": {"description": "An idling event object.", "properties": {"addressId": {"description": "The ID of the geofence address of the idling location, if applicable. It will be the address at the time of idling.", "example": "1234567890", "type": "string"}, "addressType": {"description": "The type of the geofence address of the idling location, if applicable.  Valid values: `agricultureSource`, `alertsOnly`, `industrialSite`, `riskZone`, `shortHaul`, `undefined`, `workforceSite`, `yard`", "enum": ["agricultureSource", "alertsOnly", "industrialSite", "riskZone", "<PERSON><PERSON><PERSON>", "undefined", "workforceSite", "yard"], "example": "yard", "type": "string"}, "airTemperatureMillicelsius": {"description": "The air temperature in millicelsius during the idling event. Value is returned only when it is known.", "example": 25000, "format": "int64", "type": "integer"}, "assetId": {"description": "Samsara ID of the asset assigned to the event. Returns vehicle ID at this time.", "example": 1234567890, "format": "int64", "type": "integer"}, "durationMilliseconds": {"description": "The duration of the idling event in milliseconds.", "example": 860000, "format": "int64", "type": "integer"}, "eventUuid": {"description": "Universally unique identifier of the idling event.", "example": "1234567890", "type": "string"}, "fuelConsumedMilliliters": {"description": "The amount of fuel consumed in milliliters during the idling event.", "example": 2500, "format": "double", "type": "number"}, "fuelCost": {"$ref": "#/components/schemas/FuelCostObjectResponseBody"}, "gaseousFuelConsumedGrams": {"description": "The amount of gaseous fuel consumed in grams during the idling event.", "example": 2500, "format": "double", "type": "number"}, "gaseousFuelCost": {"$ref": "#/components/schemas/GaseousFuelCostObjectResponseBody"}, "operatorId": {"description": "Samsara ID of the operator assigned the event. Returns driver ID at this time. Value is returned when the driver is assigned to the vehicle.", "example": 1234567890, "format": "int64", "type": "integer"}, "ptoState": {"description": "The PTO (Power Take-Off) state during the idling event.  Valid values: `active, inactive`", "enum": ["active, inactive"], "example": "inactive", "type": "string"}, "startTime": {"description": "The start time of the idling event in RFC 3339 format.", "example": "2019-06-13T17:08:25Z", "type": "string"}}, "required": ["assetId", "durationMilliseconds", "eventUuid", "fuelConsumedMilliliters", "fuelCost", "gaseousFuelConsumedGrams", "gaseousFuelCost", "ptoState", "startTime"], "type": "object"}, "IdlingReportEventResponseBody": {"description": "A summary of the idling event.", "properties": {"address": {"$ref": "#/components/schemas/IdlingReportEventAddressResponseBody"}, "durationMs": {"description": "The duration of this idling event in milliseconds.", "example": 860000, "format": "int64", "type": "integer"}, "endTime": {"description": "The end time of this idling event in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "type": "string"}, "fuelConsumptionMl": {"description": "The amount of fuel consumed in milliliters during this idling event.", "example": 2500, "format": "double", "type": "number"}, "isPtoActive": {"description": "Whether or not power take-off was active during this idling event.", "example": false, "type": "boolean"}, "startTime": {"description": "The start time of this idling event in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "type": "string"}, "vehicle": {"$ref": "#/components/schemas/GoaVehicleTinyResponseResponseBody"}}, "required": ["address", "durationMs", "endTime", "fuelConsumptionMl", "isPtoActive", "startTime", "vehicle"], "type": "object"}, "SafetyEvent": {"description": "A safety event.", "properties": {"behaviorLabels": {"$ref": "#/components/schemas/SafetyEventBehaviorLabels"}, "coachingState": {"$ref": "#/components/schemas/SafetyEventCoachingState"}, "downloadForwardVideoUrl": {"$ref": "#/components/schemas/SafetyEventDownloadForwardVideoUrl"}, "downloadInwardVideoUrl": {"$ref": "#/components/schemas/SafetyEventDownloadInwardVideoUrl"}, "downloadTrackedInwardVideoUrl": {"$ref": "#/components/schemas/SafetyEventDownloadTrackedInwardVideoUrl"}, "driver": {"$ref": "#/components/schemas/driverTinyResponse"}, "id": {"$ref": "#/components/schemas/SafetyEventId"}, "location": {"$ref": "#/components/schemas/location"}, "maxAccelerationGForce": {"$ref": "#/components/schemas/SafetyEventMaxAccelerationGForce"}, "time": {"$ref": "#/components/schemas/SafetyEventTime"}, "vehicle": {"$ref": "#/components/schemas/vehicleTinyResponse"}}, "type": "object"}, "SafetyEventActivityFeedItemResponseBody": {"description": "Safety event activity feed item.", "properties": {"id": {"description": "The ID of the activity event feed line item.", "example": "1622151765-212014918174029-1550954461759", "type": "string"}, "safetyEvent": {"$ref": "#/components/schemas/SafetyEventObjectResponseBody"}, "time": {"description": "The time the activity occurred in the corresponding safety event in RFC 3339 milliseconds format.", "example": "2019-10-12T07:20:50.52Z", "type": "string"}, "type": {"description": "The type of activity that occurred in the safety event. We currently only support CoachingStateActivityType, BehaviorLabelActivityType, and CreateSafetyEventActivityType, but we may add support for more activity types in the future.  Valid values: `BehaviorLabelActivityType`, `CoachingStateActivityType`, `CreateSafetyEventActivityType`", "enum": ["BehaviorLabelActivityType", "CoachingStateActivityType", "CreateSafetyEventActivityType"], "example": "CreateSafetyEventActivityType", "type": "string"}}, "required": ["id", "safetyEvent", "time", "type"], "type": "object"}, "SafetyEventBehaviorLabel": {"description": "The label and source of the label associated with the safety event.", "properties": {"label": {"$ref": "#/components/schemas/SafetyEventBehaviorLabelType"}, "name": {"$ref": "#/components/schemas/SafetyEventBehaviorLabelName"}, "source": {"$ref": "#/components/schemas/SafetyEventBehaviorLabelSource"}}, "required": ["label", "source"], "type": "object"}, "SafetyEventBehaviorLabelName": {"description": "The name of the label associated with the safety event.", "example": "Tailgating", "type": "string"}, "SafetyEventBehaviorLabelSource": {"description": "The source of the label associated with the safety event. Valid values: `automated`, `userGenerated`.", "enum": ["automated", "userGenerated"], "type": "string"}, "SafetyEventBehaviorLabelType": {"description": "The label associated with the safety event. This list often changes, so it is recommended that clients gracefully handle any types not enumerated in this list. Valid values: `genericTailgating`, `genericDistraction`, `defensiveDriving`, `rollingStop`, `nearCollison`, `speeding`, `obstructedCamera`, `didNotYield`, `noSeatbelt`, `mobileUsage`, `drowsy`, `laneDeparture`, `followingDistanceSevere`, `followingDistanceModerate`, `lateResponse`, `acceleration`, `braking`, `harshTurn`, `crash`, `rolloverProtection`, `yawControl`, `ranRedLight`, `forwardCollisionWarning`, `eatingDrinking`, `smoking`, `followingDistance`, `edgeDistractedDriving`.", "enum": ["genericTailgating", "genericDistraction", "defensiveDriving", "rollingStop", "nearCollison", "speeding", "obstructedCamera", "didNotYield", "noSeatbelt", "mobileUsage", "drowsy", "laneDeparture", "followingDistanceSevere", "followingDistanceModerate", "lateResponse", "acceleration", "braking", "harshTurn", "crash", "rolloverProtection", "yawControl", "ranRedLight", "forwardCollisionWarning", "eatingDrinking", "smoking", "followingDistance", "edgeDistractedDriving"], "type": "string"}, "SafetyEventBehaviorLabels": {"description": "The most up-to-date behavior labels associated with the safety event. These labels can be updated by the Safety Report Admin.", "items": {"$ref": "#/components/schemas/SafetyEventBehaviorLabel"}, "type": "array"}, "SafetyEventBehaviorLabelsResponseBody": {"description": "Behavior label for a safety event.", "properties": {"name": {"description": "Name of the behavior label.", "example": "Acceleration", "type": "string"}, "type": {"description": "Type of the BehaviorLabel.  Valid values: `Acceleration`, `Braking`, `Crash`, `DefensiveDriving`, `DidNotYield`, `Drinking`, `Drowsy`, `Eating`, `EatingDrinking`, `EdgeDistractedDriving`, `EdgeRailroadCrossingViolation`, `FollowingDistance`, `FollowingDistanceModerate`, `FollowingDistanceSevere`, `ForwardCollisionWarning`, `GenericDistraction`, `GenericTailgating`, `HarshTurn`, `Invalid`, `LaneDeparture`, `LateResponse`, `MobileUsage`, `NearCollison`, `NoSeatbelt`, `ObstructedCamera`, `Passenger`, `PolicyViolationMask`, `RanRedLight`, `RollingStop`, `RolloverProtection`, `Smoking`, `Speeding`, `VulnerableRoadUserCollisionWarning`, `YawControl`", "enum": ["Acceleration", "Braking", "Crash", "DefensiveDriving", "DidNotYield", "Drinking", "Drowsy", "Eating", "EatingDrinking", "EdgeDistractedDriving", "EdgeRailroadCrossingViolation", "FollowingDistance", "FollowingDistanceModerate", "FollowingDistanceSevere", "ForwardCollisionWarning", "GenericDistraction", "GenericTailgating", "HarshTurn", "Invalid", "LaneDeparture", "LateResponse", "MobileUsage", "NearCollison", "NoSeatbelt", "ObstructedCamera", "Passenger", "PolicyViolationMask", "RanRedLight", "RollingStop", "RolloverProtection", "Smoking", "Speeding", "VulnerableRoadUserCollisionWarning", "YawControl"], "example": "Acceleration", "type": "string"}}, "type": "object"}, "SafetyEventCoachingState": {"description": "The current coaching status of the event.  Valid values: `needsReview`, `coached`, `dismissed`, `reviewed`, `archived`, `manualReview`, `needsCoaching`, `autoDismissed`, `needsRecognition`, `recognized`, `invalid`.", "enum": ["needsReview", "coached", "dismissed", "reviewed", "archived", "manualReview", "needsCoaching", "autoDismissed", "needsRecognition", "recognized", "invalid"], "type": "string"}, "SafetyEventDownloadForwardVideoUrl": {"description": "URL to download the forward video.", "example": "https://s3.console.aws.amazon.com/s3/buckets/samsara-dashcam-videos/21575/212014918400828/1553060687222/huKA7IhpBV-camera-video-segment-1244214895.mp4", "type": "string"}, "SafetyEventDownloadInwardVideoUrl": {"description": "URL to download the inward video.", "example": "https://s3.console.aws.amazon.com/s3/buckets/samsara-dashcam-videos/21575/212014918400828/1553060687222/huKA7IhpBV-camera-video-segment-1244214895.mp4", "type": "string"}, "SafetyEventDownloadTrackedInwardVideoUrl": {"description": "URL to download the tracked inward video.", "example": "https://s3.console.aws.amazon.com/s3/buckets/samsara-dashcam-videos/21575/212014918400828/1553060687222/huKA7IhpBV-camera-video-segment-1244214895.mp4", "type": "string"}, "SafetyEventId": {"description": "The unique Samsara ID of the safety event.", "example": "212014918174029-1550954461759", "type": "string"}, "SafetyEventMaxAccelerationGForce": {"description": "The maximum acceleration value as a multiplier on the force of gravity (g).", "example": 0.123, "type": "number"}, "SafetyEventObjectResponseBody": {"description": "The safety event that was updated.", "properties": {"behaviorLabels": {"description": "Behavior labels for a safety event.", "items": {"$ref": "#/components/schemas/SafetyEventBehaviorLabelsResponseBody"}, "type": "array"}, "driver": {"$ref": "#/components/schemas/SafetyEventDriverObjectResponseBody"}, "id": {"description": "The unique Samsara ID of the safety event.", "example": "212014918174029-1550954461759", "type": "string"}, "time": {"description": "The time the safety event occurred in RFC 3339 milliseconds format.", "example": "2019-06-13T19:08:25.455Z", "type": "string"}, "vehicle": {"$ref": "#/components/schemas/SafetyEventVehicleObjectResponseBody"}}, "type": "object"}, "SafetyEventTime": {"description": "The time the safety event occurred in RFC 3339 milliseconds format.", "example": "2019-06-13T19:08:25.455Z", "type": "string"}, "SafetyEventsGetSafetyActivityEventFeedBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetyEventsGetSafetyActivityEventFeedBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetyEventsGetSafetyActivityEventFeedGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetyEventsGetSafetyActivityEventFeedInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetyEventsGetSafetyActivityEventFeedMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetyEventsGetSafetyActivityEventFeedNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetyEventsGetSafetyActivityEventFeedNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetyEventsGetSafetyActivityEventFeedResponseBody": {"properties": {"data": {"description": "Paginated safety event activity feed limited to 10 events.", "items": {"$ref": "#/components/schemas/SafetyEventActivityFeedItemResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "SafetyEventsGetSafetyActivityEventFeedServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetyEventsGetSafetyActivityEventFeedTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetyEventsGetSafetyActivityEventFeedUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetyEventsListResponse": {"description": "List of safety events.", "properties": {"data": {"items": {"$ref": "#/components/schemas/SafetyEvent"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "type": "object"}, "SafetyScoreConfigurationSettingsObjectResponseBody": {"description": "The configurable safety infraction weights.", "properties": {"aiInattentiveDrivingDetectionWeight": {"description": "Score weight for AI-detected inattentive driving behavior.", "example": 2, "format": "int64", "type": "integer"}, "crashWeight": {"description": "Score weight for crash behavior.", "example": 1, "format": "int64", "type": "integer"}, "defensiveDrivingWeight": {"description": "Score weight for defensive driving behavior. This behavior has a positive impact on the safety score.", "example": 0, "format": "int64", "type": "integer"}, "didNotYieldWeight": {"description": "Score weight for driver not yielding.", "example": 0, "format": "int64", "type": "integer"}, "drowsyWeight": {"description": "Score weight for drowsy behavior.", "example": 0, "format": "int64", "type": "integer"}, "eatingDrinkingWeight": {"description": "Score weight for eating/drinking behavior.", "example": 0, "format": "int64", "type": "integer"}, "followingDistanceModerateWeight": {"description": "Score weight for moderate (2-4s) following distance behavior.", "example": 0, "format": "int64", "type": "integer"}, "followingDistanceSevereWeight": {"description": "Score weight for severe (0-2s) following distance behavior.", "example": 0, "format": "int64", "type": "integer"}, "followingDistanceWeight": {"description": "Score weight for following distance behavior.", "example": 2, "format": "int64", "type": "integer"}, "forwardCollisionWarningWeight": {"description": "Score weight for forward collision warning behavior.", "example": 0, "format": "int64", "type": "integer"}, "harshAccelWeight": {"description": "Score weight for harsh acceleration behavior.", "example": 1, "format": "int64", "type": "integer"}, "harshBrakeWeight": {"description": "Score weight for harsh braking behavior.", "example": 1, "format": "int64", "type": "integer"}, "harshTurnWeight": {"description": "Score weight for harsh turn behavior.", "example": 1, "format": "int64", "type": "integer"}, "heavySpeedingWeight": {"description": "Score weight for heavy speeding (20-30% over limit).", "example": 1, "format": "int64", "type": "integer"}, "inattentiveDrivingWeight": {"description": "Score weight for inattentive driving behavior.", "example": 0, "format": "int64", "type": "integer"}, "laneDepartureWeight": {"description": "Score weight for lane departure behavior.", "example": 0, "format": "int64", "type": "integer"}, "lateResponseWeight": {"description": "Score weight for late response behavior.", "example": 0, "format": "int64", "type": "integer"}, "lightSpeedingWeight": {"description": "Score weight for light speeding (0-10% over limit).", "example": 0, "format": "int64", "type": "integer"}, "maxSpeedWeight": {"description": "Score weight for max speed events.", "example": 1, "format": "int64", "type": "integer"}, "mobileUsageWeight": {"description": "Score weight for mobile usage behavior.", "example": 0, "format": "int64", "type": "integer"}, "moderateSpeedingWeight": {"description": "Score weight for moderate speeding (10-20% over limit).", "example": 1, "format": "int64", "type": "integer"}, "nearCollisionWeight": {"description": "Score weight for near collision behavior.", "example": 0, "format": "int64", "type": "integer"}, "noSeatbeltWeight": {"description": "Score weight for no seatbelt behavior.", "example": 0, "format": "int64", "type": "integer"}, "obstructedCameraWeight": {"description": "Score weight for obstructed camera behavior.", "example": 0, "format": "int64", "type": "integer"}, "ranRedLightWeight": {"description": "Score weight for driver running red light.", "example": 0, "format": "int64", "type": "integer"}, "rollingStopWeight": {"description": "Score weight for rolling stop behavior.", "example": 0, "format": "int64", "type": "integer"}, "severeSpeedingWeight": {"description": "Score weight for severe speeding (over 30% over limit).", "example": 1, "format": "int64", "type": "integer"}, "smokingWeight": {"description": "Score weight for smoking behavior.", "example": 0, "format": "int64", "type": "integer"}, "speedingWeight": {"description": "Score weight for manual speeding event.", "example": 0, "format": "int64", "type": "integer"}, "vulnerableRoadUserWeight": {"description": "Score weight for vulnerable road user behavior.", "example": 0, "format": "int64", "type": "integer"}}, "type": "object"}, "SafetySettingsGetSafetySettingsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetySettingsGetSafetySettingsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetySettingsGetSafetySettingsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetySettingsGetSafetySettingsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetySettingsGetSafetySettingsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetySettingsGetSafetySettingsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetySettingsGetSafetySettingsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetySettingsGetSafetySettingsResponseBody": {"properties": {"data": {"description": "Safety settings for a single organization.", "items": {"$ref": "#/components/schemas/SafetySettingsObjectResponseBody"}, "type": "array"}}, "required": ["data"], "type": "object"}, "SafetySettingsGetSafetySettingsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetySettingsGetSafetySettingsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetySettingsGetSafetySettingsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SafetySettingsObjectResponseBody": {"description": "Safety settings for a single organization.", "properties": {"defaultVehicleType": {"description": "Default vehicle type (for newly added or activated vehicles).  Valid values: `off`, `automatic`, `passengerCar`, `lightTruck`, `heavyDuty`", "enum": ["off", "automatic", "passengerCar", "lightTruck", "heavyDuty"], "example": "heavyDuty", "type": "string"}, "distractedDrivingDetectionAlerts": {"$ref": "#/components/schemas/DistractedDrivingDetectionAlertSettingsObjectResponseBody"}, "followingDistanceDetectionAlerts": {"$ref": "#/components/schemas/FollowingDistanceDetectionAlertSettingsObjectResponseBody"}, "forwardCollisionDetectionAlerts": {"$ref": "#/components/schemas/ForwardCollisionDetectionAlertSettingsObjectResponseBody"}, "harshEventSensitivity": {"$ref": "#/components/schemas/HarshEventSensitivitySettingsObjectResponseBody"}, "harshEventSensitivityV2": {"$ref": "#/components/schemas/HarshEventSensitivityV2SettingsObjectResponseBody"}, "policyViolationsDetectionAlerts": {"$ref": "#/components/schemas/PolicyViolationsDetectionAlertSettingsObjectResponseBody"}, "rollingStopDetectionAlerts": {"$ref": "#/components/schemas/RollingStopDetectionAlertSettingsObjectResponseBody"}, "safetyScoreConfiguration": {"$ref": "#/components/schemas/SafetyScoreConfigurationSettingsObjectResponseBody"}, "safetyScoreTarget": {"description": "The fleet-wide target safety score that is shown on safety score graphs. A safety score goal of 0 means that score benchmarking is disabled.", "example": 90, "format": "int64", "type": "integer"}, "speedingSettings": {"$ref": "#/components/schemas/SpeedingSettingsObjectResponseBody"}, "voiceCoaching": {"$ref": "#/components/schemas/VoiceCoachingSettingsObjectResponseBody"}}, "required": ["defaultVehicleType", "distractedDrivingDetectionAlerts", "followingDistanceDetectionAlerts", "forwardCollisionDetectionAlerts", "harshEventSensitivity", "harshEventSensitivityV2", "policyViolationsDetectionAlerts", "rollingStopDetectionAlerts", "safetyScoreConfiguration", "safetyScoreTarget", "speedingSettings", "voiceCoaching"], "type": "object"}, "V1SafetyReportHarshEvent": {"description": "List of harsh events", "properties": {"harshEventType": {"description": "Sensor generated harsh event type.", "example": "Harsh Braking", "type": "string"}, "timestampMs": {"description": "Timestamp that the harsh event occurred in Unix milliseconds since epoch", "example": 1535590776000, "type": "integer"}, "vehicleId": {"description": "Vehicle associated with the harsh event", "example": 212014918086169, "type": "integer"}}, "type": "object"}}, "securitySchemes": {"AccessTokenHeader": {"type": "http", "scheme": "bearer"}}}, "paths": {"/idling/events": {"get": {"description": "Get idling events for the requested time duration.\n\n**Note:** The data from this endpoint comes from the new Advanced Idling Report, which provides additional data fields for each idling event such as air temperature, geofence, PTO state and minimum idle time. This endpoint will initially include data from August 1, 2024. Approx. two weeks later, this will be further back dated to January 1, 2024. If you require additional historical data, you can access it via the [vehicle idling reports API](https://developers.samsara.com/reference/getvehicleidlingreports).\n\n <b>Rate limit:</b> 5 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Read Idling** under the Fuel & Energy category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "getIdlingEvents", "parameters": [{"description": "The start of the time range for filtering idling events in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-11T19:00:00Z, 2015-09-12T14:00:00-04:00). Returns events that begin at or after this timestamp.", "in": "query", "name": "startTime", "required": true, "schema": {"type": "string"}}, {"description": "The end of the time range for filtering idling events in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:00:00Z, 2015-09-15T14:00:00-04:00). Returns events that begin before this timestamp.", "in": "query", "name": "endTime", "required": true, "schema": {"type": "string"}}, {"description": "A filter on the data based on this comma-separated list of asset IDs. Asset IDs only include vehicle IDs at this time.", "explode": false, "in": "query", "name": "assetIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of operator IDs. Operator IDs only include driver IDs at this time.", "explode": false, "in": "query", "name": "operatorIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data on this PTO (Power Take-Off) state. If no specific state is provided, data including any state will be included.  Valid values: `active`, `inactive`", "in": "query", "name": "ptoState", "schema": {"enum": ["active", "inactive"], "type": "string"}}, {"description": "A filter on the data based on the minimum value of air temperature in millicelsius. The acceptable range for this value is between -20,000 and 50,000 millicelsius.", "in": "query", "name": "minAirTemperatureMillicelsius", "schema": {"maximum": 50000, "minimum": -20000, "type": "integer"}}, {"description": "A filter on the data based on the maximum value of air temperature in millicelsius. The acceptable range for this value is between -20,000 and 50,000 millicelsius.", "in": "query", "name": "maxAirTemperatureMillicelsius", "schema": {"maximum": 50000, "minimum": -20000, "type": "integer"}}, {"description": "A filter on the data based on unknown air temperature value.", "in": "query", "name": "excludeEventsWithUnknownAirTemperature", "schema": {"default": false, "type": "boolean"}}, {"description": "A filter on the data based on the minimum value of Idling duration in milliseconds. The acceptable range for this value is between 2 minutes and 24 hours.", "in": "query", "name": "minDurationMilliseconds", "schema": {"maximum": 86400000, "minimum": 120000, "type": "integer"}}, {"description": "A filter on the data based on the maximum value of Idling duration in milliseconds. The acceptable range for this value is between 2 minutes and 24 hours.", "in": "query", "name": "maxDurationMilliseconds", "schema": {"maximum": 86400000, "minimum": 120000, "type": "integer"}}, {"description": "A filter on the data based on this comma-separated list of tag IDs. Tag IDs only include vehicle IDs at this time.", "explode": false, "in": "query", "name": "tagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of parent tag IDs. Parent tag IDs only include vehicle IDs at this time.", "explode": false, "in": "query", "name": "parentTagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": " If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "The limit for how many objects will be in the response. Default and max for this value is 200 objects.", "in": "query", "name": "limit", "schema": {"default": 200, "maximum": 200, "minimum": 1, "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdvancedIdlingGetIdlingEventsResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdvancedIdlingGetIdlingEventsUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdvancedIdlingGetIdlingEventsNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdvancedIdlingGetIdlingEventsMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdvancedIdlingGetIdlingEventsTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdvancedIdlingGetIdlingEventsInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdvancedIdlingGetIdlingEventsNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdvancedIdlingGetIdlingEventsBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdvancedIdlingGetIdlingEventsServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdvancedIdlingGetIdlingEventsGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdvancedIdlingGetIdlingEventsBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "[beta] Get idling events.", "tags": ["Beta APIs"]}}}, "tags": [{"name": "Safety"}]}