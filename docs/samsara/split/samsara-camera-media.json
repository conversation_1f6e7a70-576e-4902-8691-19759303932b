{"openapi": "3.0.1", "info": {"description": "Samsara API specification for camera media related endpoints and schemas.", "title": "Samsara API - Camera Media", "version": "2025-05-12"}, "servers": [{"url": "https://api.samsara.com/"}, {"url": "https://api.eu.samsara.com/"}], "security": [{"AccessTokenHeader": []}], "components": {"parameters": {"V1visionCameraIdParam": {"description": "The camera_id should be valid for the given accessToken.", "in": "path", "name": "camera_id", "required": true, "schema": {"format": "int64", "type": "integer"}}, "V1visionDurationMsParam": {"description": "DurationMs is a required param. This works with the EndMs parameter. Indicates the duration in which the visionRuns will be fetched", "in": "query", "name": "durationMs", "required": true, "schema": {"format": "int64", "type": "integer"}}, "V1visionEndMsParam": {"description": "EndMs is an optional param. It will default to the current time.", "in": "query", "name": "endMs", "schema": {"format": "int64", "type": "integer"}}, "V1visionIncludeParam": {"description": "Include is a filter parameter. Accepts 'pass', 'reject' or 'no_read'.", "in": "query", "name": "include", "schema": {"type": "string"}}, "V1visionLimitParam": {"description": "Limit is an integer value from 1 to 1,000.", "in": "query", "name": "limit", "schema": {"format": "int64", "type": "integer"}}, "V1visionProgramIdParam": {"description": "The configured program's ID on the camera.", "in": "query", "name": "program_id", "schema": {"format": "int64", "type": "integer"}}, "V1visionProgramIdPathParam": {"description": "The configured program's ID on the camera.", "in": "path", "name": "program_id", "required": true, "schema": {"format": "int64", "type": "integer"}}, "V1visionStartAtMsParam": {"description": "EndMs is an optional param. It will default to the current time.", "in": "query", "name": "startedAtMs", "schema": {"format": "int64", "type": "integer"}}, "V1visionStartAtMsPathParam": {"description": "Started_at_ms is a required param. Indicates the start time of the run to be fetched.", "in": "path", "name": "started_at_ms", "required": true, "schema": {"format": "int64", "type": "integer"}}}, "requestBodies": {}, "schemas": {"CameraConnectorDisconectedResponseBody": {"description": "Details specific to Camera Connector Disconnected.", "properties": {"driver": {"$ref": "#/components/schemas/alertObjectDriverResponseBody"}, "trailer": {"$ref": "#/components/schemas/alertObjectTrailerResponseBody"}, "vehicle": {"$ref": "#/components/schemas/alertObjectVehicleResponseBody"}}, "type": "object"}, "CameraDetailsResponseResponseBody": {"description": "Camera-specific health metadata details.", "properties": {"gatewayLastConnectedTime": {"description": "The timestamp when the gateway was last connected to the vehicle, in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "lastFiftyHoursUptimePercentage": {"description": "The percentage of successful recording time during the time when the vehicle is on an active trip over the past 50 hours, in percentage points.", "example": 95.5, "format": "double", "type": "number"}, "vehicleGatewaySerial": {"description": "The serial number of the vehicle gateway that the camera is connected to.", "example": "VG-1234-5678", "type": "string"}}, "type": "object"}, "CameraSerial": {"description": "The serial number of the camera installed in the vehicle", "example": "CNCK-VT8-XA8", "type": "string"}, "CameraStreamIssueResponseBody": {"description": "Details specific to Camera Stream Issue.", "properties": {"cameraDevice": {"$ref": "#/components/schemas/alertObjectWorkforceCameraDeviceResponseBody"}}, "type": "object"}, "FormsMediaRecordObjectResponseBody": {"description": "Forms media record object.", "properties": {"id": {"description": "ID of the media record.", "example": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac7", "format": "uuid", "type": "string"}, "processingStatus": {"description": "Status of the media record.  Valid values: `unknown`, `processing`, `finished`", "enum": ["unknown", "processing", "finished"], "example": "processing", "type": "string"}, "url": {"description": "URL containing a link to associated media content. Included if 'processingStatus' is 'finished'.", "example": "https://samsara-forms-submission-media-uploads.s3.us-west-2.amazonaws.com/123456", "format": "uri", "type": "string"}, "urlExpiresAt": {"description": "Expiration time of the media record 'url'. UTC timestamp in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}}, "required": ["id", "processingStatus"], "type": "object"}, "FormsMediaValueObjectResponseBody": {"description": "The value of a media form input field.", "properties": {"mediaList": {"description": "List of forms media record objects.", "items": {"$ref": "#/components/schemas/FormsMediaRecordObjectResponseBody"}, "type": "array"}}, "required": ["mediaList"], "type": "object"}, "GetMediaRetrievalObjectResponseBody": {"properties": {"media": {"description": "List of media retrieval objects.", "items": {"$ref": "#/components/schemas/MediaObjectResponseBody"}, "type": "array"}}, "required": ["media"], "type": "object"}, "ListUploadedMediaObjectResponseBody": {"properties": {"media": {"description": "List of media retrieval objects.", "items": {"$ref": "#/components/schemas/UploadedMediaObjectResponseBody"}, "type": "array"}}, "required": ["media"], "type": "object"}, "MediaObjectResponseBody": {"properties": {"availableAtTime": {"description": "Timestamp, in RFC 3339 format, at which the media item was made available. Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00", "example": "2019-11-11T14:00:12-04:00", "type": "string"}, "endTime": {"description": " An end time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}, "input": {"description": "Input type for this media.  Valid values: `dashcamDriverFacing`, `dashcamRoadFacing`", "enum": ["dashcamDriverFacing", "dashcamRoadFacing"], "example": "dashcamDriverFacing", "type": "string"}, "mediaType": {"description": "Type of media.  Valid values: `image`, `video`", "enum": ["image", "video"], "example": "image", "type": "string"}, "startTime": {"description": " A start time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}, "status": {"description": "Status of the media.  Valid values: `available`, `invalid`, `pending`, `failed`", "enum": ["available", "invalid", "pending", "failed"], "example": "available", "type": "string"}, "urlInfo": {"$ref": "#/components/schemas/UrlInfoObjectResponseBody"}, "vehicleId": {"description": "Vehicle ID for which this media was captured. Examples: 1234", "example": "1234", "type": "string"}}, "required": ["endTime", "input", "mediaType", "startTime", "status", "vehicleId"], "type": "object"}, "MediaRetrievalGetMediaRetrievalBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalGetMediaRetrievalBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalGetMediaRetrievalGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalGetMediaRetrievalInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalGetMediaRetrievalMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalGetMediaRetrievalNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalGetMediaRetrievalNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalGetMediaRetrievalResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/GetMediaRetrievalObjectResponseBody"}}, "required": ["data"], "type": "object"}, "MediaRetrievalGetMediaRetrievalServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalGetMediaRetrievalTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalGetMediaRetrievalUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalListUploadedMediaBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalListUploadedMediaBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalListUploadedMediaGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalListUploadedMediaInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalListUploadedMediaMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalListUploadedMediaNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalListUploadedMediaNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalListUploadedMediaResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/ListUploadedMediaObjectResponseBody"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "MediaRetrievalListUploadedMediaServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalListUploadedMediaTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalListUploadedMediaUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalPostMediaRetrievalBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalPostMediaRetrievalBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalPostMediaRetrievalGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalPostMediaRetrievalInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalPostMediaRetrievalMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalPostMediaRetrievalNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalPostMediaRetrievalNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalPostMediaRetrievalRequestBody": {"properties": {"endTime": {"description": "An end time in RFC 3339 format. If endTime is the same as startTime, an image will be captured at startTime. Must be 1 second or more after startTime and no more than 60 seconds after startTime (Examples: 2019-06-13T19:08:55Z, 2019-06-13T19:08:55.455Z, OR 2015-09-15T14:00:42-04:00).", "example": "2019-06-13T19:08:55Z", "type": "string"}, "inputs": {"description": "A list of desired camera inputs for which to capture media. Only media with valid inputs (e.g. device has that input stream and device was recording at the time) will be uploaded. An empty list is invalid.", "example": ["dashcamRoadFacing", "dashcamRoadFacing"], "items": {"description": "input  Valid values: `dashcamRoadFacing`, `dashcamDriverFacing`, `analog1`", "enum": ["dashcamRoadFacing", "dashcamDriverFacing", "analog1"], "example": "dashcamRoadFacing", "type": "string"}, "type": "array"}, "mediaType": {"description": "The desired media type. If a video is requested, endTime must be after startTime. If an image is requested, endTime must be the same as startTime. Must be one of: image, videoHighRes. Examples: image, videoHighRes.  Valid values: `image`, `videoHighRes`", "enum": ["image", "videoHighRes"], "example": "videoHighRes", "type": "string"}, "startTime": {"description": "A start time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}, "vehicleId": {"description": "Vehicle ID for which to initiate media capture. Examples: 1234", "example": "1234", "type": "string"}}, "required": ["endTime", "inputs", "mediaType", "startTime", "vehicleId"], "type": "object"}, "MediaRetrievalPostMediaRetrievalResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/PostMediaRetrievalObjectResponseBody"}}, "required": ["data"], "type": "object"}, "MediaRetrievalPostMediaRetrievalServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalPostMediaRetrievalTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "MediaRetrievalPostMediaRetrievalUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "PostMediaRetrievalObjectResponseBody": {"properties": {"quotaStatus": {"description": "Quota status for this media capture request. Examples: Current monthly usage is 80000.4 seconds of high-res video out of 900000.0 available.", "example": "Current monthly usage is 80000.4 seconds of high-res video out of 900000.0 available.", "type": "string"}, "retrievalId": {"description": "Retrieval ID associated with this media capture request. Examples: 2308cec4-82e0-46f1-8b3c-a3592e5cc21e", "example": "2308cec4-82e0-46f1-8b3c-a3592e5cc21e", "type": "string"}}, "required": ["quotaStatus", "retrievalId"], "type": "object"}, "UploadedMediaObjectResponseBody": {"properties": {"availableAtTime": {"description": "Timestamp, in RFC 3339 format, at which the media item was made available. Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00", "example": "2019-11-11T14:00:12-04:00", "type": "string"}, "endTime": {"description": " An end time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}, "input": {"description": "Input type for this media.  Valid values: `dashcamForwardFacing`, `dashcamInwardFacing`, `analog1`", "enum": ["dashcamForwardFacing", "dashcamInwardFacing", "analog1"], "example": "dashcamForwardFacing", "type": "string"}, "mediaType": {"description": "Type of media.  Valid values: `image`, `video`", "enum": ["image", "video"], "example": "image", "type": "string"}, "startTime": {"description": " A start time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}, "triggerReason": {"description": "Trigger reason for this media capture.  Valid values: `api`, `panicButton`, `periodicStill`, `rfidEvent`, `safetyEvent`, `tripEndStill`, `tripStartStill`, `videoRetrieval`", "enum": ["api", "panicButton", "periodicStill", "rfidEvent", "safetyEvent", "tripEndStill", "tripStartStill", "videoRetrieval"], "example": "api", "type": "string"}, "urlInfo": {"$ref": "#/components/schemas/UrlInfoObjectResponseBody"}, "vehicleId": {"description": "Vehicle ID for which this media was captured. Examples: 1234", "example": "1234", "type": "string"}}, "required": ["availableAtTime", "endTime", "input", "mediaType", "startTime", "triggerReason", "vehicleId"], "type": "object"}, "V1ProgramsForTheCameraResponse": {"description": "Contains the list of Programs configured on the given Camera.", "items": {"properties": {"programId": {"example": 1, "format": "int64", "type": "integer"}, "programName": {"example": "Barcode verification program", "type": "string"}}, "type": "object"}, "type": "array"}, "V1VisionCamerasResponse": {"description": "Contains the list of Cameras installed in the org.", "items": {"properties": {"cameraId": {"example": 1234512345999, "format": "int64", "type": "integer"}, "cameraName": {"example": "Line 32", "type": "string"}, "ethernetIp": {"example": "*********", "type": "string"}, "wifiIp": {"example": "*********", "type": "string"}}, "type": "object"}, "type": "array"}, "V1VisionRunByCameraResponse": {"properties": {"cameraId": {"example": 1234512345123, "format": "int64", "type": "integer"}, "endedAtMs": {"example": 0, "format": "int64", "type": "integer"}, "inspectionResults": {"items": {"$ref": "#/components/schemas/V1VisionRunByCameraResponse_inspectionResults"}, "type": "array"}, "isOngoing": {"example": true, "type": "boolean"}, "program": {"$ref": "#/components/schemas/V1VisionRunByCameraResponse_program"}, "runSummary": {"$ref": "#/components/schemas/V1VisionRunByCameraResponse_runSummary"}, "startedAtMs": {"example": 1553808606097, "format": "int64", "type": "integer"}}, "type": "object"}, "V1VisionRunByCameraResponse_inspectionResults": {"properties": {"captureAtMs": {"example": 1553811994730, "format": "float64", "type": "number"}, "result": {"example": "PASS", "type": "string"}, "stepResults": {"$ref": "#/components/schemas/V1VisionStepResults"}}, "type": "object"}, "V1VisionRunByCameraResponse_program": {"properties": {"id": {"example": 1, "format": "int64", "type": "integer"}, "name": {"example": "Barcode verification program", "type": "string"}}, "type": "object"}, "V1VisionRunByCameraResponse_runSummary": {"properties": {"itemsPerMinute": {"example": 0.1, "format": "float64", "type": "number"}, "noReadCount": {"example": 0, "format": "int64", "type": "integer"}, "rejectCount": {"example": 0, "format": "int64", "type": "integer"}, "successCount": {"example": 181, "format": "int64", "type": "integer"}}, "type": "object"}, "V1VisionRunsByCameraAndProgramResponse": {"properties": {"deviceId": {"example": 1234512345123, "format": "int64", "type": "integer"}, "endedAtMs": {"example": 0, "format": "int64", "type": "integer"}, "programId": {"example": 1, "format": "int64", "type": "integer"}, "reportMetadata": {"$ref": "#/components/schemas/V1VisionRunByCameraResponse_runSummary"}, "results": {"items": {"$ref": "#/components/schemas/V1VisionRunByCameraResponse_inspectionResults"}, "type": "array"}, "startedAtMs": {"example": 1553808606097, "format": "int64", "type": "integer"}}, "type": "object"}, "V1VisionRunsByCameraResponse": {"items": {"properties": {"deviceId": {"example": 1234512345123, "format": "int64", "type": "integer"}, "endedAtMs": {"example": 0, "format": "int64", "type": "integer"}, "program": {"properties": {"id": {"example": 1, "format": "int64", "type": "integer"}, "name": {"example": "Barcode verification program", "type": "string"}}, "type": "object"}, "reportMetadata": {"properties": {"itemsPerMinute": {"example": 0.1, "format": "float64", "type": "number"}, "noReadCount": {"example": 0, "format": "int64", "type": "integer"}, "rejectCount": {"example": 0, "format": "int64", "type": "integer"}, "successCount": {"example": 181, "format": "int64", "type": "integer"}}, "type": "object"}, "startedAtMs": {"example": 1553808606097, "format": "int64", "type": "integer"}}, "type": "object"}, "type": "array"}, "V1VisionRunsResponse": {"properties": {"visionRuns": {"items": {"$ref": "#/components/schemas/V1VisionRunsResponse_visionRuns"}, "type": "array"}}, "type": "object"}, "V1VisionRunsResponse_reportMetadata": {"description": "The response includes 4 additional fields that are now deprecated", "properties": {"itemsPerMinute": {"description": "Returns average scanned items per minute. Should be used instead of scanRate.", "example": 0.1, "format": "float64", "type": "number"}, "noReadCount": {"description": "Returns no read count for the run. Should be used instead of noReadScansCount", "example": 181, "format": "int64", "type": "integer"}, "rejectCount": {"description": "Returns reject count for the run. Should be used instead of failedScansCount", "example": 0, "format": "int64", "type": "integer"}, "successCount": {"description": "Returns success count for the run. Should be used instead of successfulScansCount", "example": 181, "format": "int64", "type": "integer"}}, "type": "object"}, "V1VisionRunsResponse_visionRuns": {"properties": {"deviceId": {"example": 1234512345123, "format": "int64", "type": "integer"}, "endedAtMs": {"example": 0, "format": "int64", "type": "integer"}, "programId": {"example": 1, "format": "int64", "type": "integer"}, "reportMetadata": {"$ref": "#/components/schemas/V1VisionRunsResponse_reportMetadata"}, "startedAtMs": {"example": 1553808606097, "format": "int64", "type": "integer"}}, "type": "object"}, "V1VisionStepResults": {"items": {"properties": {"angleCheck": {"properties": {"angleConfigured": {"description": "The configured angle allowance range (in degrees)", "properties": {"high": {"example": 150, "format": "int64", "type": "integer"}, "low": {"example": 90, "format": "int64", "type": "integer"}}, "type": "object"}, "angleFound": {"description": "The counter-clockwise angle detected from the first edge to the second edge", "example": 60, "format": "int64", "type": "integer"}, "endStepName": {"description": "The name of the second reference step used to check the angle", "example": "Edge 2", "type": "string"}, "startStepName": {"description": "The name of the first reference step used to check the angle", "example": "Edge 1", "type": "string"}}, "type": "object"}, "barcode": {"items": {"properties": {"contents": {"example": "10855639004823", "type": "string"}, "matchString": {"example": "10855639004823", "type": "string"}, "type": {"example": "EAN8", "type": "string"}}, "type": "object"}, "type": "array"}, "booleanLogic": {"properties": {"operator": {"example": "AND", "type": "string"}, "steps": {"items": {"properties": {"name": {"example": "Expiration Date", "type": "string"}, "result": {"example": "PASS", "type": "string"}}, "type": "object"}, "type": "array"}}, "type": "object"}, "caliper": {"properties": {"angleRange": {"description": "The configured angle allowance range", "properties": {"high": {"example": 360, "format": "int32", "type": "integer"}, "low": {"example": 0, "format": "int32", "type": "integer"}}, "type": "object"}, "contrastRange": {"description": "The configured contrast allowance range", "properties": {"high": {"example": 100, "format": "int64", "type": "integer"}, "low": {"example": 50, "format": "int64", "type": "integer"}}, "type": "object"}, "distanceFound": {"description": "The distance found between the found edges", "example": 555.55, "format": "float", "type": "number"}, "filterPolarity": {"default": "LIGHT TO DARK", "description": "The configured polarity for finding edges. Valid values: `LIGHT TO DARK`, `DARK TO LIGHT`.", "enum": ["LIGHT TO DARK", "DARK TO LIGHT"], "example": "LIGHT TO DARK", "type": "string"}, "maxDistance": {"description": "The maximum allowed distance threshold", "example": 1000, "format": "float", "type": "number"}, "minDistance": {"description": "The minumum allowed distance threshold", "example": 500, "format": "float", "type": "number"}, "sharpnessRange": {"description": "The configured sharpness allowance range", "properties": {"high": {"example": 100, "format": "int64", "type": "integer"}, "low": {"example": 80, "format": "int64", "type": "integer"}}, "type": "object"}, "straightnessRange": {"description": "The configured straightness allowance range", "properties": {"high": {"example": 100, "format": "int32", "type": "integer"}, "low": {"example": 80, "format": "int32", "type": "integer"}}, "type": "object"}, "unit": {"description": "The measurement unit of the distance found and the min and max distance threshold", "example": "Millimeters", "type": "string"}}, "type": "object"}, "contour": {"properties": {"angleDegrees": {"description": "The rotation angle found", "example": 0, "format": "int64", "type": "integer"}, "angleTolerance": {"description": "The rotation angle allowance", "example": 50, "format": "int64", "type": "integer"}, "matchPercentage": {"description": "The contour match percentage with the configured contour", "example": 46, "format": "int64", "type": "integer"}, "matchThreshold": {"description": "The configured match threshold for contours", "example": 18, "format": "int64", "type": "integer"}}, "type": "object"}, "distance": {"properties": {"distanceFound": {"description": "The distance found between the start and end references", "format": "float64", "type": "integer"}, "endStepName": {"description": "The name of the second reference step that we're checking the distances between", "example": "Bottom Right Square", "type": "string"}, "enforceOffsetAngleRange": {"description": "Whether an offset angle range is enforced", "example": true, "type": "boolean"}, "maxDistance": {"description": "The maximum allowed distance threshold", "example": 900, "format": "float64", "type": "integer"}, "maxOffsetAngle": {"description": "The maximum angle allowance (in degrees) if enforceOffsetAngleRange is true", "example": 90, "format": "int64", "type": "integer"}, "minDistance": {"description": "The minumum allowed distance threshold", "example": 1500, "format": "float64", "type": "integer"}, "minOffsetAngle": {"description": "The minimum angle allowance (in degrees) if enforceOffsetAngleRange is true", "example": 45, "format": "int64", "type": "integer"}, "offsetAngleFound": {"description": "The counter-clockwise angle (in degrees) found between the horizontal axis of the start reference step and the last", "example": 50, "format": "int64", "type": "integer"}, "startStepName": {"description": "The name of the first reference step that we're checking the distances between", "example": "Top Left Square", "type": "string"}, "unit": {"description": "The measurement unit of the distance found and the min and max distance threshold", "example": "Millimeters", "type": "string"}}, "type": "object"}, "expirationDate": {"properties": {"dateOffset": {"example": 1, "format": "int64", "type": "integer"}, "foundDate": {"example": "06/13/2019", "type": "string"}, "matchDate": {"example": "06/13/2019", "type": "string"}}, "type": "object"}, "findCopies": {"properties": {"angleTolerance": {"description": "The orientation angle tolerance (+/- °)", "example": 10, "format": "int64", "type": "integer"}, "foundCount": {"description": "The number of copies found", "example": 1, "format": "int64", "type": "integer"}, "maxCount": {"description": "The maximum number of copies allowed", "example": 5, "format": "int64", "type": "integer"}, "minCount": {"description": "The minimum number of copies allowed", "example": 1, "format": "int64", "type": "integer"}, "threshold": {"description": "The minimum required similarity (in %) of a found copy compared to the configured match region", "example": 70, "format": "int64", "type": "integer"}}, "type": "object"}, "findEdge": {"properties": {"angleFound": {"description": "The detected angle in degrees", "example": 90, "format": "float64", "type": "integer"}, "angleRange": {"description": "The configured angle allowance range", "properties": {"high": {"example": 360, "format": "int32", "type": "integer"}, "low": {"example": 0, "format": "int32", "type": "integer"}}, "type": "object"}, "contrastPercent": {"description": "The detected contrast percentage", "format": "float64", "type": "integer"}, "contrastRange": {"description": "The configured contrast allowance range", "properties": {"high": {"example": 100, "format": "int64", "type": "integer"}, "low": {"example": 50, "format": "int64", "type": "integer"}}, "type": "object"}, "filterPolarity": {"description": "The configured polarity for finding edges. Valid values: `LIGHT TO DARK`, `DARK TO LIGHT`.", "example": "ANY", "type": "string"}, "sharpnessPercent": {"description": "The detected sharpness percentage", "example": 95, "format": "float64", "type": "integer"}, "sharpnessRange": {"description": "The configured sharpness allowance range", "properties": {"high": {"example": 100, "format": "int64", "type": "integer"}, "low": {"example": 80, "format": "int64", "type": "integer"}}, "type": "object"}, "straightnessFound": {"description": "The detected straightness percentage", "example": 95, "format": "float64", "type": "integer"}, "straightnessRange": {"description": "The configured straightness allowance range", "properties": {"high": {"example": 100, "format": "int32", "type": "integer"}, "low": {"example": 80, "format": "int32", "type": "integer"}}, "type": "object"}}, "type": "object"}, "findShapes": {"properties": {"foundCount": {"example": 1, "format": "int32", "type": "integer"}, "maxCount": {"example": 5, "format": "int32", "type": "integer"}, "minCount": {"example": 1, "format": "int32", "type": "integer"}}, "type": "object"}, "fixture": {"properties": {"coordinates": {"properties": {"x": {"example": 10, "format": "int32", "type": "integer"}, "y": {"example": 10, "format": "int32", "type": "integer"}}, "type": "object"}, "found": {"example": true, "type": "boolean"}, "rotationDegrees": {"example": 90, "format": "int64", "type": "integer"}}, "type": "object"}, "labelMatch": {"properties": {"score": {"example": 10, "format": "int64", "type": "integer"}, "threshold": {"example": 10, "format": "int64", "type": "integer"}}, "type": "object"}, "name": {"example": "Chicken Caesar Label", "type": "string"}, "presenceAbsence": {"properties": {"blueRange": {"properties": {"high": {"example": 150, "format": "int64", "type": "integer"}, "low": {"example": 90, "format": "int64", "type": "integer"}}, "type": "object"}, "checkForAbsence": {"example": true, "type": "boolean"}, "grayscaleRange": {"properties": {"high": {"example": 100, "format": "int64", "type": "integer"}, "low": {"example": 20, "format": "int64", "type": "integer"}}, "type": "object"}, "greenRange": {"properties": {"high": {"example": 150, "format": "int64", "type": "integer"}, "low": {"example": 90, "format": "int64", "type": "integer"}}, "type": "object"}, "hueRange": {"properties": {"high": {"example": 100, "format": "int64", "type": "integer"}, "low": {"example": 20, "format": "int64", "type": "integer"}}, "type": "object"}, "redRange": {"properties": {"high": {"example": 150, "format": "int64", "type": "integer"}, "low": {"example": 90, "format": "int64", "type": "integer"}}, "type": "object"}, "saturationRange": {"properties": {"high": {"example": 50, "format": "int64", "type": "integer"}, "low": {"example": 10, "format": "int64", "type": "integer"}}, "type": "object"}, "score": {"example": 50, "format": "int64", "type": "integer"}, "threshold": {"example": 50, "format": "int64", "type": "integer"}, "valueRange": {"properties": {"high": {"example": 50, "format": "int64", "type": "integer"}, "low": {"example": 10, "format": "int64", "type": "integer"}}, "type": "object"}}, "type": "object"}, "result": {"example": "PASS", "type": "string"}, "textMatch": {"properties": {"foundText": {"example": "ABCD", "type": "string"}, "matchString": {"example": "ABCD", "type": "string"}}, "type": "object"}}, "type": "object"}, "type": "array"}, "alertObjectOnvifCameraStreamResponseBody": {"description": "A camera stream associated with the alert.", "properties": {"cameraDevice": {"$ref": "#/components/schemas/alertObjectWorkforceCameraDeviceResponseBody"}, "id": {"description": "The ID of the camera stream associated with the alert.", "example": "54321", "type": "string"}, "name": {"description": "The name of the camera stream.", "example": "Stream-123", "type": "string"}, "tags": {"description": "The list of [tags](https://kb.samsara.com/hc/en-us/articles/360026674631-Using-Tags-and-Tag-Nesting) associated with the camera stream.", "items": {"$ref": "#/components/schemas/GoaTagTinyResponseResponseBody"}, "type": "array"}}, "required": ["id"], "type": "object"}, "alertObjectWorkforceCameraDeviceResponseBody": {"description": "A camera device associated with the alert", "properties": {"id": {"description": "The ID of the camera device associated with the alert", "example": "22222", "type": "string"}, "name": {"description": "The name of the camera device", "example": "Camera-123", "type": "string"}, "sites": {"description": "The list of sites associated with the camera device.", "items": {"$ref": "#/components/schemas/alertObjectSitesResponseBody"}, "type": "array"}, "tags": {"description": "The list of [tags](https://kb.samsara.com/hc/en-us/articles/360026674631-Using-Tags-and-Tag-Nesting) associated with the camera device.", "items": {"$ref": "#/components/schemas/GoaTagTinyResponseResponseBody"}, "type": "array"}}, "required": ["id"], "type": "object"}}, "securitySchemes": {"AccessTokenHeader": {"type": "http", "scheme": "bearer"}}}, "paths": {"/cameras/media": {"get": {"description": "This endpoint returns a list of all uploaded media (video and still images) matching query parameters, with a maximum query range of one day. Additional media can be retrieved with the [Create a media retrieval request](https://developers.samsara.com/reference/postmediaretrieval) endpoint, and they will be included in the list after they are uploaded. Urls provided by this endpoint expire in 8 hours.\n\n <b>Rate limit:</b> 100 requests/min (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Read Media Retrieval** under the Safety & Cameras category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "listUploadedMedia", "parameters": [{"description": "A filter on the data based on this comma-separated list of vehicle IDs and externalIds. You can specify up to 20 vehicles. Example: `vehicleIds=1234,5678,samsara.vin:1HGBH41JXMN109186`", "in": "query", "name": "vehicleIds", "required": true, "schema": {"type": "string"}}, {"description": "An optional list of desired camera inputs for which to return captured media. If empty, media for all available inputs will be returned.", "explode": true, "in": "query", "name": "inputs", "schema": {"items": {"enum": ["dashcamRoadFacing", "dashcamDriverFacing", "analog1"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "An optional list of desired media types for which to return captured media. If empty, media for all available media types will be returned. Possible options include: image, videoHighRes.", "explode": true, "in": "query", "name": "mediaTypes", "schema": {"items": {"enum": ["image", "videoHighRes"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "An optional list of desired trigger reasons for which to return captured media. If empty, media for all available trigger reasons will be returned. Possible options include: api, panicButton, periodicStill, rfidEvent, safetyEvent, tripEndStill, tripStartStill, videoRetrieval. videoRetrieval represents media captured for a dashboard video retrieval request.", "explode": true, "in": "query", "name": "triggerReasons", "schema": {"items": {"enum": ["api", "panicButton", "periodicStill", "rfidEvent", "safetyEvent", "tripEndStill", "tripStartStill", "videoRetrieval"], "type": "string"}, "type": "array"}, "style": "form"}, {"description": "A start time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "startTime", "required": true, "schema": {"type": "string"}}, {"description": "An end time in RFC 3339 format. End time cannot be more than 24 hours after startTime. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "endTime", "required": true, "schema": {"type": "string"}}, {"description": "An optional timestamp in RFC 3339 format that can act as a cursor to track which media has previously been retrieved; only media whose availableAtTime comes after this parameter will be returned. Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00", "in": "query", "name": "availableAfterTime", "schema": {"type": "string"}}, {"description": " If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalListUploadedMediaResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalListUploadedMediaUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalListUploadedMediaNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalListUploadedMediaMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalListUploadedMediaTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalListUploadedMediaInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalListUploadedMediaNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalListUploadedMediaBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalListUploadedMediaServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalListUploadedMediaGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalListUploadedMediaBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "List uploaded media by time range.", "tags": ["Media"]}}, "/cameras/media/retrieval": {"get": {"description": "This endpoint returns media information corresponding to a retrieval ID. Retrieval IDs are associated to prior [media retrieval requests](https://developers.samsara.com/reference/postmediaretrieval). Urls provided by this endpoint expire in 8 hours.\n\n <b>Rate limit:</b> 100 requests/min (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Read Media Retrieval** under the Safety & Cameras category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "getMediaRetrieval", "parameters": [{"description": "Retrieval ID associated with this media capture request. Examples: 2308cec4-82e0-46f1-8b3c-a3592e5cc21e", "in": "query", "name": "retrievalId", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalGetMediaRetrievalResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalGetMediaRetrievalUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalGetMediaRetrievalNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalGetMediaRetrievalMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalGetMediaRetrievalTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalGetMediaRetrievalInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalGetMediaRetrievalNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalGetMediaRetrievalBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalGetMediaRetrievalServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalGetMediaRetrievalGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalGetMediaRetrievalBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "Get details for a media retrieval request", "tags": ["Media"]}, "post": {"description": "This endpoint creates an asynchronous request to upload certain media from a device. The closest available media to the requested timestamp is returned. Images and high-res videos are supported; other types of media (e.g. hyperlapse, low-res) are planned to be supported in the future. Currently, only unblurred media is supported. If a device is offline, the requested media will be uploaded once it comes back online. Quota limits are enforced for media retrievals made through the API. The Create a media retrieval request response includes information about the media retrieval quota remaining for the organization. The media retrieval quota for the organization is reset at the beginning of each month.The quota is expressed using seconds of High Resolution video. 10 still images are equivalent to a 1 second of High Resolution footage.\n\n <b>Rate limit:</b> 100 requests/min (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Write Media Retrieval** under the Safety & Cameras category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "postMediaRetrieval", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalPostMediaRetrievalRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalPostMediaRetrievalResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalPostMediaRetrievalUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalPostMediaRetrievalNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalPostMediaRetrievalMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalPostMediaRetrievalTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalPostMediaRetrievalInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalPostMediaRetrievalNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalPostMediaRetrievalBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalPostMediaRetrievalServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalPostMediaRetrievalGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MediaRetrievalPostMediaRetrievalBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "Create a media retrieval request", "tags": ["Media"], "x-codegen-request-body-name": "PostMediaRetrievalRequestBody"}}, "/v1/industrial/vision/cameras": {"get": {"description": "<n class=\"warning\">\n<nh>\n<i class=\"fa fa-exclamation-circle\"></i>\nThis endpoint is still on our legacy API.\n</nh>\n</n>\n\nFetch all cameras. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Industrial** under the Industrial category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "V1getCameras", "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1VisionCamerasResponse"}}}, "description": "Returns details about a camera."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "Fetch industrial cameras", "tags": ["Industrial"]}}, "/v1/industrial/vision/cameras/{camera_id}/programs": {"get": {"description": "<n class=\"warning\">\n<nh>\n<i class=\"fa fa-exclamation-circle\"></i>\nThis endpoint is still on our legacy API.\n</nh>\n</n>\n\n<PERSON>tch configured programs on the camera. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Industrial** under the Industrial category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "V1getVisionProgramsByCamera", "parameters": [{"description": "The camera_id should be valid for the given accessToken.", "in": "path", "name": "camera_id", "required": true, "schema": {"format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ProgramsForTheCameraResponse"}}}, "description": "Returns programs configured on the camera."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "Fetch industrial camera programs", "tags": ["Industrial"]}}, "/v1/industrial/vision/run/camera/{camera_id}": {"get": {"description": "Fetch the latest run for a camera or program by default. If startedAtMs is supplied, fetch the specific run that corresponds to that start time. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Industrial** under the Industrial category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "V1getVisionLatestRunCamera", "parameters": [{"description": "The camera_id should be valid for the given accessToken.", "in": "path", "name": "camera_id", "required": true, "schema": {"format": "int64", "type": "integer"}}, {"description": "The configured program's ID on the camera.", "in": "query", "name": "program_id", "schema": {"format": "int64", "type": "integer"}}, {"description": "EndMs is an optional param. It will default to the current time.", "in": "query", "name": "startedAtMs", "schema": {"format": "int64", "type": "integer"}}, {"description": "Include is a filter parameter. Accepts 'pass', 'reject' or 'no_read'.", "in": "query", "name": "include", "schema": {"type": "string"}}, {"description": "Limit is an integer value from 1 to 1,000.", "in": "query", "name": "limit", "schema": {"format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1VisionRunByCameraResponse"}}}, "description": "Returns the details for this run."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "Fetch the latest run for a camera or program", "tags": ["Industrial"]}}, "/v1/industrial/vision/runs": {"get": {"description": "<n class=\"warning\">\n<nh>\n<i class=\"fa fa-exclamation-circle\"></i>\nThis endpoint is still on our legacy API.\n</nh>\n</n>\n\nFetch runs. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Industrial** under the Industrial category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "V1getVisionRuns", "parameters": [{"description": "DurationMs is a required param. This works with the EndMs parameter. Indicates the duration in which the visionRuns will be fetched", "in": "query", "name": "durationMs", "required": true, "schema": {"format": "int64", "type": "integer"}}, {"description": "EndMs is an optional param. It will default to the current time.", "in": "query", "name": "endMs", "schema": {"format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1VisionRunsResponse"}}}, "description": "Return runs."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "<PERSON><PERSON> runs", "tags": ["Industrial"]}}, "/v1/industrial/vision/runs/{camera_id}": {"get": {"description": "<n class=\"warning\">\n<nh>\n<i class=\"fa fa-exclamation-circle\"></i>\nThis endpoint is still on our legacy API.\n</nh>\n</n>\n\nFetch runs by camera. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Industrial** under the Industrial category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "getVisionRunsByCamera", "parameters": [{"description": "The camera_id should be valid for the given accessToken.", "in": "path", "name": "camera_id", "required": true, "schema": {"format": "int64", "type": "integer"}}, {"description": "DurationMs is a required param. This works with the EndMs parameter. Indicates the duration in which the visionRuns will be fetched", "in": "query", "name": "durationMs", "required": true, "schema": {"format": "int64", "type": "integer"}}, {"description": "EndMs is an optional param. It will default to the current time.", "in": "query", "name": "endMs", "schema": {"format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1VisionRunsByCameraResponse"}}}, "description": "Return runs by cameraId."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "<PERSON><PERSON> runs by camera", "tags": ["Industrial"]}}, "/v1/industrial/vision/runs/{camera_id}/{program_id}/{started_at_ms}": {"get": {"description": "<n class=\"warning\">\n<nh>\n<i class=\"fa fa-exclamation-circle\"></i>\nThis endpoint is still on our legacy API.\n</nh>\n</n>\n\nFetch runs by camera and program. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Industrial** under the Industrial category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "V1getVisionRunsByCameraAndProgram", "parameters": [{"description": "The camera_id should be valid for the given accessToken.", "in": "path", "name": "camera_id", "required": true, "schema": {"format": "int64", "type": "integer"}}, {"description": "The configured program's ID on the camera.", "in": "path", "name": "program_id", "required": true, "schema": {"format": "int64", "type": "integer"}}, {"description": "Started_at_ms is a required param. Indicates the start time of the run to be fetched.", "in": "path", "name": "started_at_ms", "required": true, "schema": {"format": "int64", "type": "integer"}}, {"description": "Include is a filter parameter. Accepts 'pass', 'reject' or 'no_read'.", "in": "query", "name": "include", "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1VisionRunsByCameraAndProgramResponse"}}}, "description": "Return runs by camera ID and program ID."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "<PERSON><PERSON> runs by camera and program", "tags": ["Industrial"]}}}, "tags": [{"name": "Camera Media"}]}