{"openapi": "3.0.1", "info": {"description": "Samsara API specification for drivers related endpoints and schemas.", "title": "Samsara API - Drivers", "version": "2025-05-12"}, "servers": [{"url": "https://api.samsara.com/"}, {"url": "https://api.eu.samsara.com/"}], "security": [{"AccessTokenHeader": []}], "components": {"parameters": {"driverActivationStatusParam": {"description": "If value is `deactivated`, only drivers that are deactivated will appear in the response. This parameter will default to `active` if not provided (fetching only active drivers).", "in": "query", "name": "driverActivationStatus", "schema": {"enum": ["active", "deactivated"], "type": "string"}}}, "requestBodies": {"V1createMessagesParam": {"content": {"application/json": {"schema": {"properties": {"driverIds": {"description": "IDs of the drivers for whom the messages are sent to.", "example": [111, 222, 333], "items": {"example": 555, "format": "int64", "type": "number"}, "type": "array"}, "text": {"description": "The text sent in the message. Max 2500 characters allowed.", "example": "This is a message.", "type": "string"}}, "required": ["driverIds", "text"], "type": "object"}}}, "description": "Text to send to a list of driverIds.", "required": true}}, "schemas": {"CarrierProposedAssignmentDriver": {"allOf": [{"description": "The driver that this assignment is for.", "type": "object"}, {"$ref": "#/components/schemas/driverTinyResponse"}, {"$ref": "#/components/schemas/CarrierProposedAssignmentDriver_allOf"}]}, "CarrierProposedAssignmentDriverId": {"description": "<PERSON><PERSON><PERSON> ID for the driver that this assignment is for.", "example": "42", "type": "string"}, "CarrierProposedAssignmentDriver_allOf": {"properties": {"externalIds": {"$ref": "#/components/schemas/carrierProposedAssignmentDriverAllOf2ExternalIds"}}, "type": "object"}, "CoachAssignmentWithDriverExternalIdsResponseResponseBody": {"description": "Driver coach assignment object.", "properties": {"coachId": {"description": "Coach ID associated with coach assignment. Always returned.", "example": "45646", "type": "string"}, "createdAtTime": {"description": "Time coach assignment was created in UTC. Always returned.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "driver": {"$ref": "#/components/schemas/DriverWithExternalIdObjectResponseBody"}, "updatedAtTime": {"description": "Time coaching assignment was updated in UTC. Always returned.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}}, "required": ["coachId", "createdAtTime", "driver", "updatedAtTime"], "type": "object"}, "CoachAssignmentWithoutDriverExternalIdsResponseResponseBody": {"description": "Driver coach assignment object.", "properties": {"coachId": {"description": "Coach ID associated with coach assignment. Optional. Will be empty if no driver coach is assigned", "example": "45646", "type": "string"}, "createdAtTime": {"description": "Time coach assignment was created in UTC. Always returned.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "driverId": {"description": "Unique user ID for the driver of the driver coach assignment", "example": "45646", "type": "string"}, "updatedAtTime": {"description": "Time coaching assignment was updated in UTC. Always returned.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}}, "required": ["createdAtTime", "driverId", "updatedAtTime"], "type": "object"}, "CreateDriverRequest": {"description": "Driver that should be created.", "properties": {"attributes": {"items": {"$ref": "#/components/schemas/CreateDriverRequest_attributes"}, "type": "array"}, "carrierSettings": {"$ref": "#/components/schemas/DriverCarrierSettings"}, "currentIdCardCode": {"description": "The ID Card Code on the back of the physical card assigned to the driver.  Contact <PERSON><PERSON><PERSON> if you would like to enable this feature.", "example": "941767043", "type": "string"}, "eldAdverseWeatherExemptionEnabled": {"description": "Flag indicating this driver may use Adverse Weather exemptions in ELD logs.", "type": "boolean"}, "eldBigDayExemptionEnabled": {"description": "Flag indicating this driver may use Big Day exemption in ELD logs.", "type": "boolean"}, "eldDayStartHour": {"description": "`0` indicating midnight-to-midnight ELD driving hours, `12` to indicate noon-to-noon driving hours.", "type": "integer"}, "eldExempt": {"description": "Flag indicating this driver is exempt from the Electronic Logging Mandate.", "type": "boolean"}, "eldExemptReason": {"description": "Reason that this driver is exempt from the Electronic Logging Mandate (see eldExempt).", "example": "Bad driver", "type": "string"}, "eldPcEnabled": {"description": "Flag indicating this driver may select the Personal Conveyance duty status in ELD logs.", "type": "boolean"}, "eldYmEnabled": {"description": "Flag indicating this driver may select the Yard Move duty status in ELD logs.", "type": "boolean"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "The [external IDs](https://developers.samsara.com/docs/external-ids) for the given object.", "example": {"maintenanceId": "250020", "payrollId": "ABFS18600"}, "type": "object"}, "hasDrivingFeaturesHidden": {"$ref": "#/components/schemas/DriverHasDrivingFeaturesHidden"}, "hosSetting": {"$ref": "#/components/schemas/DriverHosSetting"}, "licenseNumber": {"description": "Driver's state issued license number. The combination of this number and `licenseState` must be unique.", "example": "********", "type": "string"}, "licenseState": {"description": "Abbreviation of US state, Canadian province, or US territory that issued driver's license.", "example": "CA", "type": "string"}, "locale": {"description": "Locale override (uncommon). These are specified by ISO 3166-2 country codes for supported locales. Valid values: `us`, `at`, `be`, `ca`, `gb`, `fr`, `de`, `ie`, `it`, `lu`, `mx`, `nl`, `es`, `ch`, `pr`.", "enum": ["us", "at", "be", "ca", "gb", "fr", "de", "ie", "it", "lu", "mx", "nl", "es", "ch", "pr"], "type": "string"}, "name": {"description": "Driver's name.", "example": "<PERSON>", "maxLength": 255, "minLength": 1, "type": "string"}, "notes": {"description": "Notes about the driver.", "example": "Also goes by the nickname <PERSON><PERSON>.", "maxLength": 4096, "type": "string"}, "password": {"description": "Password that the driver can use to login to the Samsara driver app.", "example": "aSecurePassword1234", "type": "string"}, "peerGroupTagId": {"description": "The peer group tag id this driver belong to, used for gamification.", "type": "string"}, "phone": {"description": "Phone number of the driver.", "example": "5558234327", "maxLength": 255, "type": "string"}, "staticAssignedVehicleId": {"description": "ID of vehicle that the driver is permanently assigned to. (uncommon).", "example": "456", "type": "string"}, "tachographCardNumber": {"description": "Driver's assigned tachograph card number (Europe specific)", "example": "1000000492436002", "type": "string"}, "tagIds": {"description": "IDs of tags the driver is associated with. If your access to the API is scoped by one or more tags, this field is required to pass in.", "items": {"example": "147", "type": "string"}, "type": "array"}, "timezone": {"description": "Home terminal timezone, in order to indicate what time zone should be used to calculate the ELD logs. Driver timezones use [IANA timezone database](https://www.iana.org/time-zones) keys (e.g. `America/Los_Angeles`, `America/New_York`, `Europe/London`, etc.). You can find a mapping of common timezone formats to IANA timezone keys [here](https://unicode.org/cldr/charts/latest/supplemental/zone_tzid.html).", "example": "America/Los_Angeles", "type": "string"}, "usDriverRulesetOverride": {"$ref": "#/components/schemas/UsDriverRulesetOverride"}, "username": {"description": "Driver's login username into the driver app. The username may not contain spaces or the '@' symbol. The username must be unique.", "example": "<PERSON><PERSON><PERSON>", "maxLength": 189, "minLength": 1, "type": "string"}, "vehicleGroupTagId": {"description": "Tag ID which determines which vehicles a driver will see when selecting vehicles.", "example": "342417", "type": "string"}, "waitingTimeDutyStatusEnabled": {"description": "Flag indicating this driver may select waiting time duty status in ELD logs", "type": "boolean"}}, "required": ["name", "password", "username"], "type": "object"}, "CreateDriverRequest_attributes": {"description": "A minified attribute.", "properties": {"id": {"description": "The samsara id of the attribute object.", "example": "123e4567-e89b-12d3-a456-426614174000", "type": "string"}, "name": {"description": "Name of attribute.", "example": "License Certifications", "type": "string"}, "numberValues": {"description": "Number values that are associated with this attribute.", "items": {"format": "double", "type": "number"}, "type": "array"}, "stringValues": {"description": "String values that are associated with this attribute.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Driver": {"description": "A driver object", "properties": {"attributes": {"description": "A minified attribute", "items": {"$ref": "#/components/schemas/attributeTiny"}, "type": "array"}, "carrierSettings": {"$ref": "#/components/schemas/DriverCarrierSettings"}, "createdAtTime": {"$ref": "#/components/schemas/DriverCreatedAtTime"}, "currentIdCardCode": {"$ref": "#/components/schemas/DriverIdCardCode"}, "driverActivationStatus": {"$ref": "#/components/schemas/DriverActivationStatus"}, "eldAdverseWeatherExemptionEnabled": {"$ref": "#/components/schemas/DriverEldAdverseWeatherExemptionEnabled"}, "eldBigDayExemptionEnabled": {"$ref": "#/components/schemas/DriverEldBigDayExemptionEnabled"}, "eldDayStartHour": {"$ref": "#/components/schemas/DriverEldDayStartHour"}, "eldExempt": {"$ref": "#/components/schemas/DriverEldExempt"}, "eldExemptReason": {"$ref": "#/components/schemas/DriverEldExemptReason"}, "eldPcEnabled": {"$ref": "#/components/schemas/DriverEldPcEnabled"}, "eldSettings": {"$ref": "#/components/schemas/DriverEldSettings"}, "eldYmEnabled": {"$ref": "#/components/schemas/DriverEldYmEnabled"}, "externalIds": {"$ref": "#/components/schemas/DriverExternalIds"}, "hasDrivingFeaturesHidden": {"$ref": "#/components/schemas/DriverHasDrivingFeaturesHidden"}, "id": {"$ref": "#/components/schemas/DriverId"}, "isDeactivated": {"$ref": "#/components/schemas/DriverIsDeactivated"}, "licenseNumber": {"$ref": "#/components/schemas/DriverLicenseNumber"}, "licenseState": {"$ref": "#/components/schemas/DriverLicenseState"}, "locale": {"$ref": "#/components/schemas/DriverLocale"}, "name": {"$ref": "#/components/schemas/DriverName"}, "notes": {"$ref": "#/components/schemas/DriverNotes"}, "peerGroupTag": {"$ref": "#/components/schemas/tagTinyResponse"}, "phone": {"$ref": "#/components/schemas/DriverPhone"}, "staticAssignedVehicle": {"$ref": "#/components/schemas/DriverStaticAssignedVehicle"}, "tachographCardNumber": {"$ref": "#/components/schemas/DriverTachographCardNumber"}, "tags": {"$ref": "#/components/schemas/DriverTags"}, "timezone": {"$ref": "#/components/schemas/DriverTimezone"}, "updatedAtTime": {"$ref": "#/components/schemas/DriverUpdatedAtTime"}, "usDriverRulesetOverride": {"$ref": "#/components/schemas/UsDriverRulesetOverride"}, "username": {"$ref": "#/components/schemas/DriverUsername"}, "vehicleGroupTag": {"$ref": "#/components/schemas/DriverVehicleGroupTag"}, "waitingTimeDutyStatusEnabled": {"$ref": "#/components/schemas/DriverWaitingTimeDutyStatusEnabled"}}, "type": "object"}, "DriverActivationStatus": {"description": "A value indicating whether the driver is active or deactivated. Valid values: `active`, `deactivated`.", "enum": ["active", "deactivated"], "type": "string"}, "DriverAppNotificationObjectRequestBody": {"description": "Driver app notification settings", "properties": {"inAppNotificationOptions": {"$ref": "#/components/schemas/InAppNotificationOptionsObjectRequestBody"}, "pushNotificationOptions": {"$ref": "#/components/schemas/PushNotificationOptionsObjectRequestBody"}}, "type": "object"}, "DriverAppNotificationObjectResponseBody": {"description": "Driver app notification settings", "properties": {"inAppNotificationOptions": {"$ref": "#/components/schemas/InAppNotificationOptionsObjectResponseBody"}, "pushNotificationOptions": {"$ref": "#/components/schemas/PushNotificationOptionsObjectResponseBody"}}, "type": "object"}, "DriverAppSettingsGamificationConfigTinyObjectRequestBody": {"description": "Gamification configuration for the Driver App.", "properties": {"anonymizeDriverNames": {"description": "Hide the names of other drivers when viewing the driver leaderboard in the mobile app.", "example": true, "type": "boolean"}}, "type": "object"}, "DriverAppSettingsGamificationConfigTinyObjectResponseBody": {"description": "Gamification configuration for the Driver App.", "properties": {"anonymizeDriverNames": {"description": "Hide the names of other drivers when viewing the driver leaderboard in the mobile app.", "example": false, "type": "boolean"}}, "type": "object"}, "DriverAppSettingsResponseObjectResponseBody": {"description": "The configuration settings for the Samsara Driver App. Can be set or updated through the Samsara Settings page or the API at any time.", "properties": {"driverFleetId": {"description": "Login user name for the fleet driver app", "example": "abc_trucking_co", "type": "string"}, "gamification": {"description": "Driver gamification feature. Enabling this will turn on the feature for all drivers using the mobile app. Drivers can be configured into peer groups within the Drivers Page. Unconfigured drivers will be grouped on an organization level.", "example": true, "type": "boolean"}, "gamificationConfig": {"$ref": "#/components/schemas/DriverAppSettingsGamificationConfigTinyObjectResponseBody"}, "orgVehicleSearch": {"description": "Allow drivers to search for vehicles outside of their selection tag when connected to the internet.", "example": false, "type": "boolean"}, "trailerSelection": {"description": "Allow drivers to see and select trailers in the Samsara Driver app. ", "example": false, "type": "boolean"}, "trailerSelectionConfig": {"$ref": "#/components/schemas/DriverAppSettingsTrailerSelectionConfigTinyObjectResponseBody"}}, "type": "object"}, "DriverAppSettingsTrailerSelectionConfigTinyObjectRequestBody": {"description": "Trailer selection setting configuration for the Driver App.", "properties": {"driverTrailerCreationEnabled": {"description": "Allow drivers to create new trailers in the Samsara Driver app.", "example": true, "type": "boolean"}, "maxNumOfTrailersSelected": {"description": "Trailer selection limit.", "maximum": 8, "type": "integer"}, "orgTrailerSearch": {"description": "Allow drivers to search for trailers outside of their selection tag when connected to the internet", "example": true, "type": "boolean"}}, "type": "object"}, "DriverAppSettingsTrailerSelectionConfigTinyObjectResponseBody": {"description": "Trailer selection setting configuration for the Driver App.", "properties": {"driverTrailerCreationEnabled": {"description": "Allow drivers to create new trailers in the Samsara Driver app.", "example": false, "type": "boolean"}, "maxNumOfTrailersSelected": {"description": "Trailer selection limit.", "maximum": 8, "type": "integer"}, "orgTrailerSearch": {"description": "Allow drivers to search for trailers outside of their selection tag when connected to the internet", "example": false, "type": "boolean"}}, "type": "object"}, "DriverAppSignInResponseBody": {"description": "Details specific to Driver App Sign In.", "properties": {"driver": {"$ref": "#/components/schemas/alertObjectDriverResponseBody"}}, "type": "object"}, "DriverAppSignOutResponseBody": {"description": "Details specific to Driver App Sign Out.", "properties": {"driver": {"$ref": "#/components/schemas/alertObjectDriverResponseBody"}}, "type": "object"}, "DriverAssignmentMetadataTinyObjectResponseBody": {"description": "Metadata object for external assignment source data.", "properties": {"sourceName": {"description": "Assigned source name from an external source.", "example": "ExternalSourceName", "type": "string"}}, "type": "object"}, "DriverAssignmentObjectResponseBody": {"properties": {"assignmentType": {"description": "Assignment type of the driver-vehicle assignment, indicating the provenance of the assignment. The only type of assignment supported right now is `driverApp` assignments. This list could change, so it is recommended that clients gracefully handle any types not enumerated in this list.  Valid values: `driverApp`", "enum": ["driverApp"], "example": "driverApp", "type": "string"}, "driver": {"$ref": "#/components/schemas/GoaDriverTinyResponseResponseBody"}, "endTime": {"description": " An end time in RFC 3339 format. Omitted if not applicable. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}, "isPassenger": {"description": "<PERSON><PERSON>an indicating whether the driver is a passenger.", "example": false, "type": "boolean"}, "startTime": {"description": " A start time in RFC 3339 format. Defaults to now if not provided. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}}, "type": "object"}, "DriverCarrierName": {"description": "Carrier for a given driver.", "example": "Acme Inc.", "maxLength": 255, "type": "string"}, "DriverCarrierSettings": {"description": "Carrier for a given driver. If the driver's carrier differs from the general organization's carrier settings, the override value is used. Updating this value only updates the override setting for this driver.", "properties": {"carrierName": {"description": "Carrier for a given driver.", "example": "Acme Inc.", "maxLength": 255, "type": "string"}, "dotNumber": {"description": "Carrier US DOT Number. If this differs from the general organization's settings, the override value is used. Updating this value only updates the override setting for this driver.", "example": 98231, "format": "int64", "type": "integer"}, "homeTerminalAddress": {"$ref": "#/components/schemas/DriverHomeTerminalAddress"}, "homeTerminalName": {"$ref": "#/components/schemas/DriverHomeTerminalName"}, "mainOfficeAddress": {"description": "Main office address for a given driver. If this differs from the general organization's settings, the override value is used. ", "example": "1234 Pear St., Scranton, PA 62814", "maxLength": 255, "type": "string"}}, "type": "object"}, "DriverCoachAssignmentsGetDriverCoachAssignmentBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCoachAssignmentsGetDriverCoachAssignmentBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCoachAssignmentsGetDriverCoachAssignmentGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCoachAssignmentsGetDriverCoachAssignmentInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCoachAssignmentsGetDriverCoachAssignmentMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCoachAssignmentsGetDriverCoachAssignmentNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCoachAssignmentsGetDriverCoachAssignmentNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCoachAssignmentsGetDriverCoachAssignmentResponseBody": {"properties": {"data": {"description": "List of driver coach assignment objects", "items": {"$ref": "#/components/schemas/CoachAssignmentWithDriverExternalIdsResponseResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "DriverCoachAssignmentsGetDriverCoachAssignmentServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCoachAssignmentsGetDriverCoachAssignmentTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCoachAssignmentsGetDriverCoachAssignmentUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCoachAssignmentsPutDriverCoachAssignmentBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCoachAssignmentsPutDriverCoachAssignmentBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCoachAssignmentsPutDriverCoachAssignmentGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCoachAssignmentsPutDriverCoachAssignmentInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCoachAssignmentsPutDriverCoachAssignmentMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCoachAssignmentsPutDriverCoachAssignmentNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCoachAssignmentsPutDriverCoachAssignmentNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCoachAssignmentsPutDriverCoachAssignmentResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/CoachAssignmentWithoutDriverExternalIdsResponseResponseBody"}}, "required": ["data"], "type": "object"}, "DriverCoachAssignmentsPutDriverCoachAssignmentServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCoachAssignmentsPutDriverCoachAssignmentTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCoachAssignmentsPutDriverCoachAssignmentUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverCreatedAtTime": {"description": "The date and time this driver was created in RFC 3339 format.", "example": "2019-05-18T20:27:35Z", "type": "string"}, "DriverDeactivatedAtTime": {"description": "The date and time this driver is considered to be deactivated in RFC 3339 format.", "example": "2019-05-18T20:27:35Z", "type": "string"}, "DriverDocumentSubmittedDetailsObjectRequestBody": {"description": "Details specific to Driver Document Submitted", "properties": {"templateIds": {"description": "Specific template IDs to be alerted on.", "example": ["23b78345-d098-3k4j-1pk3-4k5j6938j289", "23b78345-d098-3k4j-1pk3-4k5j6938j289"], "items": {"description": "The unique IDs of the template.", "format": "uuid", "type": "string"}, "type": "array"}}, "required": ["templateIds"], "type": "object"}, "DriverDocumentSubmittedDetailsObjectResponseBody": {"description": "Details specific to Driver Document Submitted", "properties": {"templateIds": {"description": "Specific template IDs to be alerted on.", "example": ["23b78345-d098-3k4j-1pk3-4k5j6938j289", "23b78345-d098-3k4j-1pk3-4k5j6938j289", "23b78345-d098-3k4j-1pk3-4k5j6938j289", "23b78345-d098-3k4j-1pk3-4k5j6938j289"], "items": {"description": "The unique IDs of the template.", "format": "uuid", "type": "string"}, "type": "array"}}, "required": ["templateIds"], "type": "object"}, "DriverDocumentSubmittedResponseBody": {"description": "Details specific to Driver Document Submitted.", "properties": {"document": {"$ref": "#/components/schemas/documentResponseObjectResponseBody"}}, "type": "object"}, "DriverDotNumber": {"description": "Carrier US DOT Number. If this differs from the general organization's settings, the override value is used. Updating this value only updates the override setting for this driver.", "example": 98231, "format": "int64", "type": "integer"}, "DriverEfficienciesResponse": {"description": "Summary of drivers' efficiencies over a time range.", "properties": {"data": {"$ref": "#/components/schemas/DriverEfficienciesResponse_data"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "type": "object"}, "DriverEfficienciesResponse_data": {"properties": {"driverSummaries": {"description": "A list of driver and associated vehicle efficiency data.", "items": {"$ref": "#/components/schemas/DriverEfficiency"}, "type": "array"}, "summaryEndTime": {"description": "End time of the window for which this efficiency report was computed. Will be a UTC timestamp in RFC 3339 format. For example: `2020-03-16T16:00:00Z`", "example": "2020-03-16T16:00:00Z", "format": "date-time", "type": "string"}, "summaryStartTime": {"description": "Start time of the window for which this efficiency report was computed. Will be a UTC timestamp in RFC 3339 format. For example: `2020-03-15T16:00:00Z`", "example": "2020-03-15T16:00:00Z", "format": "date-time", "type": "string"}}, "type": "object"}, "DriverEfficiency": {"description": "Summary of a driver's efficiency.", "properties": {"anticipationBrakeEventCount": {"$ref": "#/components/schemas/AnticipationBrakeEventCount"}, "coastingDurationMs": {"$ref": "#/components/schemas/CoastingDurationMs"}, "cruiseControlDurationMs": {"$ref": "#/components/schemas/CruiseControlDurationMs"}, "driver": {"$ref": "#/components/schemas/ExtendedDriverTinyResponse"}, "greenBandDrivingDurationMs": {"$ref": "#/components/schemas/GreenBandDrivingDurationMs"}, "highTorqueMs": {"$ref": "#/components/schemas/HighTorqueMs"}, "overSpeedMs": {"$ref": "#/components/schemas/OverSpeedMs"}, "totalBrakeEventCount": {"$ref": "#/components/schemas/TotalBrakeEventCount"}, "totalDistanceDrivenMeters": {"$ref": "#/components/schemas/DistanceDrivenMeters"}, "totalDriveTimeDurationMs": {"$ref": "#/components/schemas/DriveTimeDurationMs"}, "totalFuelConsumedMl": {"$ref": "#/components/schemas/FuelConsumedMl"}, "totalIdleTimeDurationMs": {"$ref": "#/components/schemas/IdleTimeDurationMs"}, "totalPowerTakeOffDurationMs": {"$ref": "#/components/schemas/PowerTakeOffDurationMs"}, "vehicleSummaries": {"description": "Summaries of vehicle efficiency for each vehicle the driver was driving during the given time period.", "items": {"$ref": "#/components/schemas/VehicleSummary"}, "type": "array"}}, "type": "object"}, "DriverEfficiencyDataObjectResponseBody": {"description": "Driver Efficiency score data. This object is returned by default or when the “score” format is specified in “dataFormats”.", "properties": {"anticipationScore": {"description": "Represents the anticipation score for the driver. The score will be in either number or letter format depending on the organisation config.", "example": "C", "type": "string"}, "coastingScore": {"description": "Represents the coasting score for the driver. The score will be in either number or letter format depending on the organisation config.", "example": "C", "type": "string"}, "cruiseControlScore": {"description": "Represents the cruise control score for the driver. The score will be in either number or letter format depending on the organisation config.", "example": "B", "type": "string"}, "greenBandScore": {"description": "Represents the green band score for the driver. The score will be in either number or letter format depending on the organisation config.", "example": "A", "type": "string"}, "highTorqueScore": {"description": "Represents the high torque score for the driver. The score will be in either number or letter format depending on the organisation config.", "example": "A", "type": "string"}, "idlingScore": {"description": "Represents the idling score for the driver.The score will be in either number or letter format depending on the organisation config.", "example": "B", "type": "string"}, "overSpeedScore": {"description": "Represents the over speed score for the driver. The score will be in either number or letter format depending on the organisation config.", "example": "B", "type": "string"}, "overallScore": {"description": "Represents the overall score for the driver. The score will be in either number (0-100) as a string or letter format (A-G) depending on the organisation config.", "example": "A", "type": "string"}, "wearFreeBrakeScore": {"description": "Represents the ware-free breaking score for the driver. The score will be in either number or letter format depending on the organisation config.", "example": "A", "type": "string"}}, "required": ["overallScore"], "type": "object"}, "DriverEfficiencyDifficultyScoreDataObjectResponseBody": {"description": "Difficulty score won't be available if there is no data to compute it against.", "properties": {"overallScore": {"description": "Represents the overall difficulty score. It has scores from 1 to 5.", "example": "4", "type": "string"}, "topographyScore": {"description": "Represents the topography difficulty score. It has scores from 1 to 5.", "example": "5", "type": "string"}, "vehicleWeightScore": {"description": " Represents the average vehicle weight score. It has scores from 1 to 5.", "example": "4", "type": "string"}}, "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByDriversBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByDriversBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByDriversGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByDriversInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByDriversMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByDriversNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByDriversNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByDriversResponseBody": {"properties": {"data": {"description": "List of driver efficiency data associated with drivers.", "items": {"$ref": "#/components/schemas/singleDriverEfficiencyByDriverDataObjectResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByDriversServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByDriversTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByDriversUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByVehiclesBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByVehiclesBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByVehiclesGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByVehiclesInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByVehiclesMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByVehiclesNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByVehiclesNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByVehiclesResponseBody": {"properties": {"data": {"description": "List of driver efficiency data associated with vehicles.", "items": {"$ref": "#/components/schemas/singleDriverEfficiencyByVehicleDataObjectResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByVehiclesServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByVehiclesTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyGetDriverEfficiencyByVehiclesUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverEfficiencyPercentageDataObjectResponseBody": {"description": "Driver Efficiency percentage data. This object is returned when the “percentage” format is specified in “dataFormats”.", "properties": {"anticipationPercentage": {"description": "Percentage of time a driver is in quickly breaking events vs total breaking events.", "example": 9.5, "format": "double", "type": "number"}, "coastingPercentage": {"description": "Percentage of time a driver is in coasting.", "example": 45.6, "format": "double", "type": "number"}, "cruiseControlPercentage": {"description": "Percentage of time a vehicle is in cruise control.", "example": 45.6, "format": "double", "type": "number"}, "greenBandPercentage": {"description": "Percentage of time a driver is driving within the green band.", "example": 78.9, "format": "double", "type": "number"}, "highGradeRoadDrivingPercentage": {"description": "Percentage of time a driver is driving on high-grade road.", "example": 15.3, "format": "double", "type": "number"}, "highTorquePercentage": {"description": "Percentage of time a driver is driving in high torque.", "example": 23.4, "format": "double", "type": "number"}, "idlingPercentage": {"description": "Percentage of time a driver is idling.", "example": 12.8, "format": "double", "type": "number"}, "overSpeedPercentage": {"description": "Percentage of time a driver is in over-speeding.", "example": 5.6, "format": "double", "type": "number"}, "wearFreeBrakePercentage": {"description": "Percentage of time a driver is in wear-free breaking.", "example": 88.2, "format": "double", "type": "number"}}, "required": ["idlingPercentage"], "type": "object"}, "DriverEfficiencyRawDataObjectResponseBody": {"description": "Driver Efficiency raw data. This object is returned when the “raw” format is specified in “dataFormats”.", "properties": {"anticipationBrakeEventCount": {"description": "Total number of quick braking events (less than one second after accelerating).", "example": 17, "format": "int64", "type": "integer"}, "averageVehicleWeightKg": {"description": "Average vehicle weight in kilograms.", "example": 14500, "format": "int64", "type": "integer"}, "coastingDurationMs": {"description": "Time spent without engaging the accelerator or brake in milliseconds.", "example": 1900800, "format": "int64", "type": "integer"}, "cruiseControlDurationMs": {"description": "Time spent in cruise control in milliseconds.", "example": 3283200, "format": "int64", "type": "integer"}, "driveTimeDurationMs": {"description": "Total driving time in milliseconds.", "example": 7200000, "format": "int64", "type": "integer"}, "engineOnDurationMs": {"description": "Total engine-on time in milliseconds.", "example": 7500000, "format": "int64", "type": "integer"}, "greenBandDurationMs": {"description": "Time spent driving within the configurable green band in milliseconds.", "example": 5683200, "format": "int64", "type": "integer"}, "highGradeRoadDrivingDurationMs": {"description": "Time spent driving on high-grade roads in milliseconds.", "example": 1108800, "format": "int64", "type": "integer"}, "highTorqueDurationMs": {"description": "Time the vehicle engine torque is greater than 90% in milliseconds. ", "example": 1684800, "format": "int64", "type": "integer"}, "idlingDurationMs": {"description": "Time spent idling in milliseconds.", "example": 921600, "format": "int64", "type": "integer"}, "overSpeedDurationMs": {"description": "Time spent over-speeding in milliseconds.", "example": 403200, "format": "int64", "type": "integer"}, "totalBrakeDurationMs": {"description": "Total breaking time in milliseconds.", "example": 1022400, "format": "int64", "type": "integer"}, "totalBrakeEventCount": {"description": "Total number of brake events.", "example": 85, "format": "int64", "type": "integer"}, "wearFreeBrakeDurationMs": {"description": "Time spent ware-free breaking in milliseconds.", "example": 6340800, "format": "int64", "type": "integer"}}, "required": ["driveTimeDurationMs", "engineOnDurationMs", "idlingDurationMs", "totalBrakeDurationMs"], "type": "object"}, "DriverEldAdverseWeatherExemptionEnabled": {"default": false, "description": "Flag indicating this driver may use Adverse Weather exemptions in ELD logs.", "type": "boolean"}, "DriverEldBigDayExemptionEnabled": {"default": false, "description": "Flag indicating this driver may use Big Day exemption in ELD logs.", "type": "boolean"}, "DriverEldDayStartHour": {"default": 0, "description": "`0` indicating midnight-to-midnight ELD driving hours, `12` to indicate noon-to-noon driving hours.", "type": "integer"}, "DriverEldExempt": {"default": false, "description": "Flag indicating this driver is exempt from the Electronic Logging Mandate.", "type": "boolean"}, "DriverEldExemptReason": {"description": "Reason that this driver is exempt from the Electronic Logging Mandate (see eldExempt).", "example": "Bad driver", "type": "string"}, "DriverEldPcEnabled": {"default": false, "description": "Flag indicating this driver may select the Personal Conveyance duty status in ELD logs.", "type": "boolean"}, "DriverEldRuleset": {"description": "An ELD ruleset for a driver.", "properties": {"break": {"$ref": "#/components/schemas/DriverEldRulesetRestBreak"}, "cycle": {"$ref": "#/components/schemas/DriverEldRulesetCycle"}, "jurisdiction": {"$ref": "#/components/schemas/DriverEldRulesetJurisdiction"}, "restart": {"$ref": "#/components/schemas/DriverEldRulesetRestart"}, "shift": {"$ref": "#/components/schemas/DriverEldRulesetShift"}}, "type": "object"}, "DriverEldRulesetCreatedAtTime": {"description": "The date and time this driver ELD ruleset was created in RFC 3339 format.", "example": "2019-05-18T20:27:35Z", "type": "string"}, "DriverEldRulesetCycle": {"description": "The cycle of the ELD ruleset applied to this driver. Valid values: `USA 60 hour / 7 day`, `USA 70 hour / 8 day`, `AK 80 hour / 8 day`, `AK 70 hour / 7 day`, `CA 80 hour / 8 day`, `CA 112 hour / 8 day`, `FL 80 hour / 8 day`, `FL 70 hour / 7 day`, `NE 80 hour / 8 day`, `NE 70 hour / 7 day`, `NC 80 hour / 8 day`, `NC 70 hour / 7 day`, `OK 70 hour / 8 day`, `OK 60 hour / 7 day`, `OR 80 hour / 8 day`, `OR 70 hour / 7 day`, `SC 80 hour / 8 day`, `SC 70 hour / 7 day`, `TX 70 hour / 7 day`, `WI 80 hour / 8 day`, `WI 70 hour / 7 day`, `Canada South Cycle 1 (70 hour / 7 day)`, `Canada South Cycle 2 (120 hour / 14 day)`, `Canada North Cycle 1 (80 hour / 7 day)`, `Canada North Cycle 2 (120 hour / 14 day)`.", "enum": ["USA 60 hour / 7 day", "USA 70 hour / 8 day", "AK 80 hour / 8 day", "AK 70 hour / 7 day", "CA 80 hour / 8 day", "CA 112 hour / 8 day", "FL 80 hour / 8 day", "FL 70 hour / 7 day", "NE 80 hour / 8 day", "NE 70 hour / 7 day", "NC 80 hour / 8 day", "NC 70 hour / 7 day", "OK 70 hour / 8 day", "OK 60 hour / 7 day", "OR 80 hour / 8 day", "OR 70 hour / 7 day", "SC 80 hour / 8 day", "SC 70 hour / 7 day", "TX 70 hour / 7 day", "WI 80 hour / 8 day", "WI 70 hour / 7 day", "Canada South Cycle 1 (70 hour / 7 day)", "Canada South Cycle 2 (120 hour / 14 day)", "Canada North Cycle 1 (80 hour / 7 day)", "Canada North Cycle 2 (120 hour / 14 day)"], "example": "USA 60 hour / 7 day", "type": "string"}, "DriverEldRulesetDailyOffDuty": {"description": "The daily off duty setting of the ELD ruleset applied to this driver.", "enum": ["Canada South", "Canada North"], "example": "Canada North", "type": "string"}, "DriverEldRulesetJurisdiction": {"description": "The jurisdiction of the ELD ruleset applied to this driver. These are specified by either `CS` or `CN` for Canada South and Canada North, respectively, or the ISO 3166-2 postal code for the supported state or territory.", "example": "AR", "type": "string"}, "DriverEldRulesetRestBreak": {"description": "The rest break required setting of the ELD ruleset applied to this driver. Valid values: `Property (off-duty/sleeper)`, `Explosives/HazMat (on-duty)`", "enum": ["Property (off-duty/sleeper)", "Explosives/HazMat (on-duty)"], "example": "Property (off-duty/sleeper)", "type": "string"}, "DriverEldRulesetRestart": {"description": "The restart of the ELD ruleset applied to this driver. Valid values: `None`, `34-hour Restart`, `24-hour Restart`, `36-hour Restart`, `72-hour Restart`.", "enum": ["None", "34-hour Restart", "24-hour Restart", "36-hour Restart", "72-hour Restart"], "example": "<PERSON><PERSON><PERSON>", "type": "string"}, "DriverEldRulesetShift": {"description": "The shift of the ELD ruleset applied to this driver. Valid values: `US Interstate Property`, `US Interstate Passenger`, `Texas Intrastate`.", "enum": ["US Interstate Property", "US Interstate Passenger", "Texas Intrastate"], "example": "US Interstate Property", "type": "string"}, "DriverEldRulesetUpdatedAtTime": {"description": "The date and time this driver ELD ruleset was last updated in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "type": "string"}, "DriverEldRulesetUsShortHaulType": {"description": "The US short haul ruleset type of the ELD ruleset applied to this driver.", "enum": ["100 Air-Mile (Passenger)", "100 Air-Mile (Property)", "150 Air-Mile (Non-CDL)"], "example": "100 Air-Mile (Passenger)", "type": "string"}, "DriverEldRulesets": {"description": "The driver's ELD rulesets and overrides. This is the full set of rulesets that may apply to the driver depending on their activity. If you wish to interface with the specific US driver override, use the usDriverRulesetOverride field.", "items": {"$ref": "#/components/schemas/DriverEldRuleset"}, "type": "array"}, "DriverEldSettings": {"description": "The driver's ELD settings.", "properties": {"rulesets": {"$ref": "#/components/schemas/DriverEldRulesets"}}, "type": "object"}, "DriverEldYmEnabled": {"default": false, "description": "Flag indicating this driver may select the Yard Move duty status in ELD logs.", "type": "boolean"}, "DriverExternalIds": {"description": "The [external IDs](https://developers.samsara.com/docs/external-ids) for the given object.", "example": {"maintenanceId": "250020", "payrollId": "ABFS18600"}, "type": "object"}, "DriverHasDrivingFeaturesHidden": {"description": "A boolean indicating whether the driver has driving-related features hidden in the Driver App, including Vehicle selection, HOS, Routing, Team Driving, Documents, and Trip Logs. Default value is false if omitted. Note: only available to customers of Connected Forms.", "example": true, "type": "boolean"}, "DriverHeavyHaulExemptionToggleEnabled": {"default": false, "description": "Flag indicating this driver may use the Heavy Haul exemption in ELD logs.", "type": "boolean"}, "DriverHomeTerminalName": {"description": "Name of the place of business at which a driver ordinarily reports for work.", "example": "Acme Inc.", "maxLength": 255, "type": "string"}, "DriverHosSetting": {"description": "Hos settings for a driver.", "properties": {"heavyHaulExemptionToggleEnabled": {"$ref": "#/components/schemas/DriverHeavyHaulExemptionToggleEnabled"}}, "type": "object"}, "DriverId": {"description": "<PERSON><PERSON><PERSON> ID for the driver.", "example": "123", "type": "string"}, "DriverIdCardCode": {"description": "The ID Card Code on the back of the physical card assigned to the driver.  Contact <PERSON><PERSON><PERSON> if you would like to enable this feature.", "example": "941767043", "type": "string"}, "DriverIsDeactivated": {"description": "[DEPRECATED] A boolean indicating whether or not the driver is deactivated. Use `driverActivationStatus` instead.", "example": false, "type": "boolean"}, "DriverLicenseNumber": {"description": "Driver's state issued license number. The combination of this number and `licenseState` must be unique.", "example": "********", "type": "string"}, "DriverLicenseState": {"description": "Abbreviation of US state, Canadian province, or US territory that issued driver's license.", "example": "CA", "type": "string"}, "DriverLocale": {"description": "Locale override (uncommon). These are specified by ISO 3166-2 country codes for supported locales. Valid values: `us`, `at`, `be`, `ca`, `gb`, `fr`, `de`, `ie`, `it`, `lu`, `mx`, `nl`, `es`, `ch`, `pr`.", "enum": ["us", "at", "be", "ca", "gb", "fr", "de", "ie", "it", "lu", "mx", "nl", "es", "ch", "pr"], "type": "string"}, "DriverMessageReceivedResponseBody": {"description": "Details specific to Driver Message Received.", "properties": {"driver": {"$ref": "#/components/schemas/alertObjectDriverResponseBody"}}, "type": "object"}, "DriverMessageSentResponseBody": {"description": "Details specific to Driver Message Sent.", "properties": {"driver": {"$ref": "#/components/schemas/alertObjectDriverResponseBody"}}, "type": "object"}, "DriverName": {"description": "Driver's name.", "example": "<PERSON>", "maxLength": 255, "minLength": 1, "type": "string"}, "DriverNotes": {"description": "Notes about the driver.", "example": "Also goes by the nickname <PERSON><PERSON>.", "maxLength": 4096, "type": "string"}, "DriverObjectResponseBody": {"description": "Current driver of the vehicle. Note: this parameter includes all assignment sources, not just static assignments.", "properties": {"id": {"description": "ID of the driver.", "example": "0987", "type": "string"}, "name": {"description": "Name of the driver.", "example": "Driver Name", "type": "string"}}, "type": "object"}, "DriverPassword": {"description": "Password that the driver can use to login to the Samsara driver app.", "example": "aSecurePassword1234", "type": "string"}, "DriverPhone": {"description": "Phone number of the driver.", "example": "5558234327", "maxLength": 255, "type": "string"}, "DriverQrCodesCreateDriverQrCodeBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesCreateDriverQrCodeBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesCreateDriverQrCodeGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesCreateDriverQrCodeInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesCreateDriverQrCodeMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesCreateDriverQrCodeNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesCreateDriverQrCodeNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesCreateDriverQrCodeRequestBody": {"description": "Driver to assign a new QR code to.", "properties": {"driverId": {"description": "Unique ID of the driver.", "example": 494123, "format": "int64", "type": "integer"}}, "required": ["driverId"], "type": "object"}, "DriverQrCodesCreateDriverQrCodeResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/QrCodeResponseObjectResponseBody"}}, "required": ["data"], "type": "object"}, "DriverQrCodesCreateDriverQrCodeServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesCreateDriverQrCodeTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesCreateDriverQrCodeUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesDeleteDriverQrCodeBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesDeleteDriverQrCodeBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesDeleteDriverQrCodeGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesDeleteDriverQrCodeInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesDeleteDriverQrCodeMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesDeleteDriverQrCodeNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesDeleteDriverQrCodeNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesDeleteDriverQrCodeRequestBody": {"description": "Driver to revoke active QR code from.", "properties": {"driverId": {"description": "Unique ID of the driver.", "example": 494123, "format": "int64", "type": "integer"}}, "required": ["driverId"], "type": "object"}, "DriverQrCodesDeleteDriverQrCodeServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesDeleteDriverQrCodeTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesDeleteDriverQrCodeUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesGetDriversQrCodesBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesGetDriversQrCodesBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesGetDriversQrCodesGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesGetDriversQrCodesInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesGetDriversQrCodesMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesGetDriversQrCodesNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesGetDriversQrCodesNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesGetDriversQrCodesResponseBody": {"properties": {"data": {"description": "List of driver QR codes.", "items": {"$ref": "#/components/schemas/QrCodeResponseObjectResponseBody"}, "type": "array"}}, "required": ["data"], "type": "object"}, "DriverQrCodesGetDriversQrCodesServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesGetDriversQrCodesTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverQrCodesGetDriversQrCodesUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverRecordedResponseBody": {"description": "Details specific to Driver Recorded.", "properties": {"driver": {"$ref": "#/components/schemas/alertObjectDriverResponseBody"}, "vehicle": {"$ref": "#/components/schemas/alertObjectVehicleResponseBody"}}, "type": "object"}, "DriverRemoteSignoutPostDriverRemoteSignoutBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverRemoteSignoutPostDriverRemoteSignoutBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverRemoteSignoutPostDriverRemoteSignoutGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverRemoteSignoutPostDriverRemoteSignoutInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverRemoteSignoutPostDriverRemoteSignoutMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverRemoteSignoutPostDriverRemoteSignoutNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverRemoteSignoutPostDriverRemoteSignoutNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverRemoteSignoutPostDriverRemoteSignoutRequestBody": {"description": "The driver of a vehicle.", "properties": {"driverId": {"description": "ID of the driver.", "example": "12434", "type": "string"}}, "required": ["driverId"], "type": "object"}, "DriverRemoteSignoutPostDriverRemoteSignoutResponseBody": {"properties": {"driverName": {"description": "The name of the driver", "example": "<PERSON>", "type": "string"}}, "type": "object"}, "DriverRemoteSignoutPostDriverRemoteSignoutServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverRemoteSignoutPostDriverRemoteSignoutTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverRemoteSignoutPostDriverRemoteSignoutUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverResponse": {"description": "A single driver.", "properties": {"data": {"$ref": "#/components/schemas/Driver"}}, "type": "object"}, "DriverRulesetObjectResponseBody": {"properties": {"break": {"description": "The rest break required setting of the ELD ruleset applied to this driver.  Valid values: `Property (off-duty/sleeper)`, `Explosives/HazMat (on-duty)`", "enum": ["Property (off-duty/sleeper)", "Explosives/HazMat (on-duty)"], "example": "Explosives/HazMat (on-duty)", "type": "string"}, "cycle": {"description": "The cycle of the ELD ruleset applied to this driver.  Valid values: `USA 60 hour / 7 day`, `USA 70 hour / 8 day`, `AK 80 hour / 8 day`, `AK 70 hour / 7 day`, `CA 80 hour / 8 day`, `CA 112 hour / 8 day`, `FL 80 hour / 8 day`, `FL 70 hour / 7 day`, `NE 80 hour / 8 day`, `NE 70 hour / 7 day`, `NC 80 hour / 8 day`, `NC 70 hour / 7 day`, `OK 70 hour / 8 day`, `OK 60 hour / 7 day`, `OR 80 hour / 8 day`, `OR 70 hour / 7 day`, `SC 80 hour / 8 day`, `SC 70 hour / 7 day`, `TX 70 hour / 7 day`, `WI 80 hour / 8 day`, `WI 70 hour / 7 day`, `Canada South Cycle 1 (70 hour / 7 day)`, `Canada South Cycle 2 (120 hour / 14 day)`, `Canada North Cycle 1 (80 hour / 7 day)`, `Canada North Cycle 2 (120 hour / 14 day)`", "enum": ["USA 60 hour / 7 day", "USA 70 hour / 8 day", "AK 80 hour / 8 day", "AK 70 hour / 7 day", "CA 80 hour / 8 day", "CA 112 hour / 8 day", "FL 80 hour / 8 day", "FL 70 hour / 7 day", "NE 80 hour / 8 day", "NE 70 hour / 7 day", "NC 80 hour / 8 day", "NC 70 hour / 7 day", "OK 70 hour / 8 day", "OK 60 hour / 7 day", "OR 80 hour / 8 day", "OR 70 hour / 7 day", "SC 80 hour / 8 day", "SC 70 hour / 7 day", "TX 70 hour / 7 day", "WI 80 hour / 8 day", "WI 70 hour / 7 day", "Canada South Cycle 1 (70 hour / 7 day)", "Canada South Cycle 2 (120 hour / 14 day)", "Canada North Cycle 1 (80 hour / 7 day)", "Canada North Cycle 2 (120 hour / 14 day)"], "example": "USA 60 hour / 7 day", "type": "string"}, "jurisdiction": {"description": "The jurisdiction of the ELD ruleset applied to this driver. These are specified by either `CS` or `CN` for Canada South and Canada North, respectively, or the ISO 3166-2 postal code for the supported state or territory.", "example": "AR", "type": "string"}, "restart": {"description": "The restart of the ELD ruleset applied to this driver.  Valid values: `34-hour Restart`, `24-hour Restart`, `36-hour Restart`, `72-hour Restart`", "enum": ["34-hour Restart", "24-hour Restart", "36-hour Restart", "72-hour Restart"], "example": "34-hour Restart", "type": "string"}, "shift": {"description": "The shift of the ELD ruleset applied to this driver.  Valid values: `US Interstate Property`, `US Interstate Passenger`", "enum": ["US Interstate Property", "US Interstate Passenger"], "example": "US Interstate Property", "type": "string"}}, "type": "object"}, "DriverStaticAssignedVehicle": {"description": "Vehicle assigned to the driver for static vehicle assignments. (uncommon).", "properties": {"id": {"description": "ID of the vehicle.", "example": "123456789", "type": "string"}, "name": {"description": "Name of the vehicle.", "example": "Midwest Truck #4", "type": "string"}}, "type": "object"}, "DriverStaticAssignedVehicleId": {"description": "ID of vehicle that the driver is permanently assigned to. (uncommon).", "example": "456", "type": "string"}, "DriverTachographActivityData": {"items": {"$ref": "#/components/schemas/TachographActivityListWrapper"}, "type": "array"}, "DriverTachographActivityResponse": {"description": "List of all driver tachograph activities in a specified time range.", "properties": {"data": {"$ref": "#/components/schemas/DriverTachographActivityData"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "type": "object"}, "DriverTachographCardNumber": {"description": "Driver's assigned tachograph card number (Europe specific)", "example": "1000000492436002", "type": "string"}, "DriverTagIds": {"description": "IDs of tags the driver is associated with.", "items": {"example": "147", "type": "string"}, "type": "array"}, "DriverTags": {"description": "The tags this driver belongs to.", "items": {"$ref": "#/components/schemas/tagTinyResponse"}, "type": "array"}, "DriverTimezone": {"default": "America/Los_Angeles", "description": "Home terminal timezone, in order to indicate what time zone should be used to calculate the ELD logs. Driver timezones use [IANA timezone database](https://www.iana.org/time-zones) keys (e.g. `America/Los_Angeles`, `America/New_York`, `Europe/London`, etc.). You can find a mapping of common timezone formats to IANA timezone keys [here](https://unicode.org/cldr/charts/latest/supplemental/zone_tzid.html).", "example": "America/Los_Angeles", "type": "string"}, "DriverUpdatedAtTime": {"description": "The date and time this driver was last updated in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "type": "string"}, "DriverUsername": {"description": "Driver's login username into the driver app. The username may not contain spaces or the '@' symbol. The username must be unique.", "example": "<PERSON><PERSON><PERSON>", "maxLength": 189, "minLength": 1, "type": "string"}, "DriverVehicleAssignmentV2ObjectResponseBody": {"description": "Object with driver assignment info and associated driver and vehicle info.", "properties": {"assignedAtTime": {"description": " An assigned at time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}, "assignmentType": {"description": "Name of the assigning source for the driver assignment record.  Valid values: `invalid`, `unknown`, `HOS`, `idCard`, `static`, `faceId`, `tachograph`, `safetyManual`, `RFID`, `trailer`, `external`, `qrCode`", "enum": ["invalid", "unknown", "HOS", "idCard", "static", "faceId", "tachograph", "safetyManual", "RFID", "trailer", "external", "qrCode"], "example": "idCard", "type": "string"}, "driver": {"$ref": "#/components/schemas/GoaDriverTinyResponseResponseBody"}, "endTime": {"description": " An end time in RFC 3339 format. Omitted if not applicable. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}, "isPassenger": {"description": "<PERSON><PERSON>an indicating whether the driver is a passenger.", "example": false, "type": "boolean"}, "metadata": {"$ref": "#/components/schemas/DriverAssignmentMetadataTinyObjectResponseBody"}, "startTime": {"description": " A start time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}, "vehicle": {"$ref": "#/components/schemas/GoaVehicleTinyResponseResponseBody"}}, "required": ["driver", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "startTime", "vehicle"], "type": "object"}, "DriverVehicleAssignmentsV2CreateDriverVehicleAssignmentBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2CreateDriverVehicleAssignmentBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2CreateDriverVehicleAssignmentGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2CreateDriverVehicleAssignmentInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2CreateDriverVehicleAssignmentMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2CreateDriverVehicleAssignmentNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2CreateDriverVehicleAssignmentNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2CreateDriverVehicleAssignmentRequestBody": {"description": "Driver Assignment to be created", "properties": {"assignedAtTime": {"description": "The time at which the assignment was made in RFC 3339 format. Defaults to now if not provided. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}, "driverId": {"description": "ID of the driver. This can be either a unique Samsara ID or an [external ID](https://developers.samsara.com/docs/external-ids) for the driver.", "example": "494123", "type": "string"}, "endTime": {"description": "The end time in RFC 3339 format. Defaults to max-time (meaning it's an ongoing assignment) if not provided. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}, "isPassenger": {"description": "Is this driver a passenger? Defaults to false if not provided", "example": true, "type": "boolean"}, "metadata": {"$ref": "#/components/schemas/PostDriverVehicleAssignmentsV2RequestBodyMetadataRequestBody"}, "startTime": {"description": "The start time in RFC 3339 format. Defaults to now if not provided. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}, "vehicleId": {"description": "ID of the vehicle. This can be either a unique Samsara ID or an [external ID](https://developers.samsara.com/docs/external-ids) for the vehicle.", "example": "281474978683353", "type": "string"}}, "required": ["driverId", "vehicleId"], "type": "object"}, "DriverVehicleAssignmentsV2CreateDriverVehicleAssignmentResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/PostDriverVehicleAssignmentsV2ResponseBodyResponseBody"}}, "required": ["data"], "type": "object"}, "DriverVehicleAssignmentsV2CreateDriverVehicleAssignmentServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2CreateDriverVehicleAssignmentTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2CreateDriverVehicleAssignmentUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2DeleteDriverVehicleAssignmentsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2DeleteDriverVehicleAssignmentsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2DeleteDriverVehicleAssignmentsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2DeleteDriverVehicleAssignmentsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2DeleteDriverVehicleAssignmentsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2DeleteDriverVehicleAssignmentsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2DeleteDriverVehicleAssignmentsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2DeleteDriverVehicleAssignmentsRequestBody": {"description": "Driver assignments to be deleted", "properties": {"assignedAtTime": {"description": " Assigned at time in RFC 3339 format. Defaults to now if not provided. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}, "endTime": {"description": " An end time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}, "isPassenger": {"description": "Indicates if assigned driver is passenger", "example": true, "type": "boolean"}, "startTime": {"description": " A start time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}, "vehicleId": {"description": "ID of the vehicle. This can be either a unique Samsara ID or an [external ID](https://developers.samsara.com/docs/external-ids) for the vehicle.", "example": "281474978683353", "type": "string"}}, "required": ["vehicleId"], "type": "object"}, "DriverVehicleAssignmentsV2DeleteDriverVehicleAssignmentsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2DeleteDriverVehicleAssignmentsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2DeleteDriverVehicleAssignmentsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2GetDriverVehicleAssignmentsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2GetDriverVehicleAssignmentsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2GetDriverVehicleAssignmentsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2GetDriverVehicleAssignmentsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2GetDriverVehicleAssignmentsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2GetDriverVehicleAssignmentsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2GetDriverVehicleAssignmentsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2GetDriverVehicleAssignmentsResponseBody": {"properties": {"data": {"description": "List of driver assignment objects and their respective vehcile and driver info.", "items": {"$ref": "#/components/schemas/DriverVehicleAssignmentV2ObjectResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "DriverVehicleAssignmentsV2GetDriverVehicleAssignmentsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2GetDriverVehicleAssignmentsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2GetDriverVehicleAssignmentsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2UpdateDriverVehicleAssignmentBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2UpdateDriverVehicleAssignmentBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2UpdateDriverVehicleAssignmentGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2UpdateDriverVehicleAssignmentInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2UpdateDriverVehicleAssignmentMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2UpdateDriverVehicleAssignmentNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2UpdateDriverVehicleAssignmentNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2UpdateDriverVehicleAssignmentRequestBody": {"description": "Updated Driver Assignment details", "properties": {"assignedAtTime": {"description": "The time at which the assignment was made in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}, "driverId": {"description": "ID of the driver. This can be either a unique Samsara ID or an [external ID](https://developers.samsara.com/docs/external-ids) for the driver.", "example": "494123", "type": "string"}, "endTime": {"description": "The end time in RFC 3339 format. To make this an ongoing assignment (ie. an assignment with no end time), provide an endTime value of 'null'. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}, "isPassenger": {"description": "Is this driver a passenger?", "example": true, "type": "boolean"}, "metadata": {"$ref": "#/components/schemas/PatchDriverVehicleAssignmentsV2RequestBodyMetadataRequestBody"}, "startTime": {"description": "The start time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}, "vehicleId": {"description": "ID of the vehicle. This can be either a unique Samsara ID or an [external ID](https://developers.samsara.com/docs/external-ids) for the vehicle.", "example": "281474978683353", "type": "string"}}, "required": ["driverId", "startTime", "vehicleId"], "type": "object"}, "DriverVehicleAssignmentsV2UpdateDriverVehicleAssignmentResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/PatchDriverVehicleAssignmentsV2ResponseBodyResponseBody"}}, "required": ["data"], "type": "object"}, "DriverVehicleAssignmentsV2UpdateDriverVehicleAssignmentServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2UpdateDriverVehicleAssignmentTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleAssignmentsV2UpdateDriverVehicleAssignmentUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriverVehicleGroupTag": {"description": "Tag which determines which vehicles a driver will see when selecting vehicles.", "properties": {"id": {"description": "ID of the tag.", "example": "3914", "type": "string"}, "name": {"description": "Name of the tag.", "example": "East Coast", "type": "string"}, "parentTagId": {"description": "If this tag is part a hierarchical tag tree, this is the ID of the parent tag, otherwise this will be omitted.", "example": "4815", "type": "string"}}, "type": "object"}, "DriverVehicleGroupTagId": {"description": "Tag ID which determines which vehicles a driver will see when selecting vehicles.", "example": "342417", "type": "string"}, "DriverWaitingTimeDutyStatusEnabled": {"default": false, "description": "Flag indicating this driver may select waiting time duty status in ELD logs.", "type": "boolean"}, "DriverWithExternalIdObjectResponseBody": {"description": "A driver object with an id and map of external ids.", "properties": {"driverId": {"description": "<PERSON><PERSON><PERSON> ID of the driver.", "example": "0987", "type": "string"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}}, "required": ["driverId"], "type": "object"}, "DriverWithTimezoneEldSettingsObjectResponseBody": {"description": "The driver the log applies to.", "properties": {"eldSettings": {"$ref": "#/components/schemas/EldSettingsObjectResponseBody"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "ID of the driver", "example": "45646", "type": "string"}, "name": {"description": "Name of the driver", "example": "Driver <PERSON>", "type": "string"}, "timezone": {"default": "America/Los_Angeles", "description": "Home terminal timezone, in order to indicate what time zone should be used to calculate the ELD logs. Driver timezones use [IANA timezone database](https://www.iana.org/time-zones) keys (e.g. `America/Los_Angeles`, `America/New_York`, `Europe/London`, etc.). You can find a mapping of common timezone formats to IANA timezone keys [here](https://unicode.org/cldr/charts/latest/supplemental/zone_tzid.html).", "example": "America/Los_Angeles", "type": "string"}}, "required": ["id", "name"], "type": "object"}, "DriversVehicleAssignmentsGetDriversVehicleAssignmentsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriversVehicleAssignmentsGetDriversVehicleAssignmentsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriversVehicleAssignmentsGetDriversVehicleAssignmentsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriversVehicleAssignmentsGetDriversVehicleAssignmentsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriversVehicleAssignmentsGetDriversVehicleAssignmentsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriversVehicleAssignmentsGetDriversVehicleAssignmentsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriversVehicleAssignmentsGetDriversVehicleAssignmentsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriversVehicleAssignmentsGetDriversVehicleAssignmentsResponseBody": {"properties": {"data": {"description": "List of driver and their vehicle assignments.", "items": {"$ref": "#/components/schemas/DriversVehicleAssignmentsObjectResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "DriversVehicleAssignmentsGetDriversVehicleAssignmentsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriversVehicleAssignmentsGetDriversVehicleAssignmentsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriversVehicleAssignmentsGetDriversVehicleAssignmentsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DriversVehicleAssignmentsObjectResponseBody": {"properties": {"driverActivationStatus": {"description": "A value indicating whether the driver is active or deactivated.  Valid values: `active`, `deactivated`", "enum": ["active", "deactivated"], "example": "active", "type": "string"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "ID of the driver.", "example": "494123", "type": "string"}, "name": {"description": "Name of the driver.", "example": "<PERSON>", "type": "string"}, "vehicleAssignments": {"description": "List of vehicle assignment objects.", "items": {"$ref": "#/components/schemas/VehicleAssignmentObjectResponseBody"}, "type": "array"}}, "required": ["driverActivationStatus", "id", "name", "vehicleAssignments"], "type": "object"}, "EldSettingsObjectResponseBody": {"description": "The driver's ELD settings.", "properties": {"rulesets": {"description": "The driver's ELD rulesets and overrides.", "items": {"$ref": "#/components/schemas/DriverRulesetObjectResponseBody"}, "type": "array"}}, "type": "object"}, "ExtendedDriverTinyResponse": {"description": "A minified driver object.", "properties": {"externalIds": {"description": "The [external IDs](https://developers.samsara.com/docs/external-ids) for the given object.", "example": {"maintenanceId": "250020", "payrollId": "ABFS18600"}, "properties": {}, "type": "object"}, "id": {"description": "ID of the driver.", "example": "88668", "type": "string"}, "name": {"description": "Name of the driver.", "example": "<PERSON>", "type": "string"}, "username": {"description": "<PERSON><PERSON><PERSON> of the driver.", "example": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}}, "type": "object"}, "FormSubmissionRequestFieldInputObjectRequestBody": {"description": "Forms input field request body object.", "properties": {"assetValue": {"$ref": "#/components/schemas/FormSubmissionRequestAssetValueObjectRequestBody"}, "checkBoxesValue": {"$ref": "#/components/schemas/FormSubmissionRequestCheckBoxesValueObjectRequestBody"}, "dateTimeValue": {"$ref": "#/components/schemas/FormSubmissionRequestDateTimeValueObjectRequestBody"}, "geofenceValue": {"$ref": "#/components/schemas/FormSubmissionRequestGeofenceValueObjectRequestBody"}, "id": {"description": "ID of the forms input field object.", "example": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac7", "format": "uuid", "type": "string"}, "multipleChoiceValue": {"$ref": "#/components/schemas/FormSubmissionRequestMultipleChoiceValueObjectRequestBody"}, "numberValue": {"$ref": "#/components/schemas/FormSubmissionRequestNumberValueObjectRequestBody"}, "personValue": {"$ref": "#/components/schemas/FormSubmissionRequestPersonValueObjectRequestBody"}, "tableValue": {"$ref": "#/components/schemas/FormSubmissionRequestTableValueObjectRequestBody"}, "textValue": {"$ref": "#/components/schemas/FormSubmissionRequestTextValueObjectRequestBody"}, "type": {"description": "Type of the field.  Valid values: `number`, `text`, `multiple_choice`, `check_boxes`, `datetime`, `asset`, `person`, `table`, `geofence`", "enum": ["number", "text", "multiple_choice", "check_boxes", "datetime", "asset", "person", "table", "geofence"], "example": "number", "type": "string"}}, "required": ["id", "type"], "type": "object"}, "FormsFieldDefinitionObjectResponseBody": {"description": "Forms field definition object.", "properties": {"allowManualEntry": {"description": "Indicates whether the field allows manual entry of a person. Only present for person fields.", "example": true, "type": "boolean"}, "allowedAssetTypes": {"description": "List of allowed asset types that can be selected for this field. Only present for asset fields.", "example": ["vehicle", "trailer"], "items": {"description": "  Valid values: `vehicle`, `trailer`, `equipment`, `unpoweredAsset`", "enum": ["vehicle", "trailer", "equipment", "unpoweredAsset"], "example": "vehicle", "type": "string"}, "type": "array"}, "allowedDateTimeValueType": {"description": "Type of date/time entry allowed for this question. Only present for datetime fields.  Valid values: `datetime`, `date`, `time`", "enum": ["datetime", "date", "time"], "example": "datetime", "type": "string"}, "autofillFromId": {"description": "Identifier of the field that will optionally autofill the current field. Only present for fields that have been configured to be autofilled by a source media field.", "example": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac7", "type": "string"}, "columns": {"description": "List of columns in the table field.", "items": {"$ref": "#/components/schemas/FormsTableFieldDefinitionObjectResponseBody"}, "type": "array"}, "conditionalActions": {"description": "List of conditional actions.", "items": {"$ref": "#/components/schemas/FormsConditionalActionObjectResponseBody"}, "type": "array"}, "filterByRoleIds": {"description": "List of role IDs to filter org users by, representing which roles are selectable people for this field. Only present for person fields.", "example": ["23d4d8d3-dc10-4e7a-a183-69968751f23e"], "items": {"example": "Laboriosam occaecati corrupti in dolor ad perspiciatis.", "type": "string"}, "type": "array"}, "id": {"description": "Identifier of the field.", "example": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac7", "format": "uuid", "type": "string"}, "includeDrivers": {"description": "Indicates whether the field includes drivers as selectable people. Only present for person fields.", "example": true, "type": "boolean"}, "includeUsers": {"description": "Indicates whether the field includes users as selectable people. Only present for person fields.", "example": true, "type": "boolean"}, "isAutofillSource": {"description": "Indicates whether the current field is enabled to autofill other fields. Only present for media fields that have autofill enabled.", "example": true, "type": "boolean"}, "isRequired": {"description": "Indicates whether the form field is required to be filled out by the user.", "example": true, "type": "boolean"}, "label": {"description": "Label of the field.", "example": "Engine Hours", "type": "string"}, "numDecimalPlaces": {"description": "Number of decimal places allowed. Only present for number fields.", "example": 2, "format": "int64", "type": "integer"}, "options": {"description": "List of select options for check boxes or multiple choice fields.", "items": {"$ref": "#/components/schemas/FormsSelectOptionObjectResponseBody"}, "type": "array"}, "questionWeight": {"description": "The maximum possible score weight for this field. For multiple choice fields, this number is the highest option score weight of the given options. For check boxes fields, this number is the sum of the score weights for all scored options. Only present for multiple choice or check boxes fields that have scoring.", "example": 5, "format": "int64", "type": "integer"}, "type": {"description": "Type of the field.  Valid values: `number`, `text`, `multiple_choice`, `check_boxes`, `media`, `datetime`, `signature`, `asset`, `person`, `geofence`, `instruction`, `media_instruction`, `table`", "enum": ["number", "text", "multiple_choice", "check_boxes", "media", "datetime", "signature", "asset", "person", "geofence", "instruction", "media_instruction", "table"], "example": "number", "type": "string"}}, "required": ["id", "isRequired", "label", "type"], "type": "object"}, "FormsFieldInputObjectResponseBody": {"description": "Forms input field object.", "properties": {"assetValue": {"$ref": "#/components/schemas/FormsAssetValueObjectResponseBody"}, "checkBoxesValue": {"$ref": "#/components/schemas/FormsCheckBoxesValueObjectResponseBody"}, "dateTimeValue": {"$ref": "#/components/schemas/FormsDateTimeValueObjectResponseBody"}, "geofenceValue": {"$ref": "#/components/schemas/FormsGeofenceValueObjectResponseBody"}, "id": {"description": "ID of the forms input field object.", "example": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac7", "format": "uuid", "type": "string"}, "issue": {"$ref": "#/components/schemas/FormsIssueCreatedByFieldObjectResponseBody"}, "label": {"description": "Forms input field label.", "example": "Engine Hours", "type": "string"}, "mediaList": {"description": "List of forms media record objects.", "items": {"$ref": "#/components/schemas/FormsMediaRecordObjectResponseBody"}, "type": "array"}, "mediaValue": {"$ref": "#/components/schemas/FormsMediaValueObjectResponseBody"}, "multipleChoiceValue": {"$ref": "#/components/schemas/FormsMultipleChoiceValueObjectResponseBody"}, "note": {"description": "A note attached to the field input.", "example": "Fire and oil can lead to an accident.", "type": "string"}, "numberValue": {"$ref": "#/components/schemas/FormsNumberValueObjectResponseBody"}, "personValue": {"$ref": "#/components/schemas/FormsPersonValueObjectResponseBody"}, "signatureValue": {"$ref": "#/components/schemas/FormsSignatureValueObjectResponseBody"}, "tableValue": {"$ref": "#/components/schemas/FormsTableValueObjectResponseBody"}, "textValue": {"$ref": "#/components/schemas/FormsTextValueObjectResponseBody"}, "type": {"description": "Type of the field.  Valid values: `number`, `text`, `multiple_choice`, `check_boxes`, `datetime`, `signature`, `media`, `asset`, `table`, `person`, `geofence`", "enum": ["number", "text", "multiple_choice", "check_boxes", "datetime", "signature", "media", "asset", "table", "person", "geofence"], "example": "number", "type": "string"}}, "required": ["id", "type"], "type": "object"}, "FormsIssueCreatedByFieldObjectResponseBody": {"description": "Issue created from this form input field input object.", "properties": {"externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "ID of the issue created from this form input field input object.", "example": "12345", "type": "string"}}, "required": ["id"], "type": "object"}, "FormsTableFieldDefinitionObjectResponseBody": {"description": "Forms table field definition object.", "properties": {"allowManualEntry": {"description": "Indicates whether the field allows manual entry of a person. Only present for person fields.", "example": true, "type": "boolean"}, "allowedDateTimeValueType": {"description": "Type of date/time entry allowed for this question. Only present for datetime fields.  Valid values: `datetime`, `date`, `time`", "enum": ["datetime", "date", "time"], "example": "datetime", "type": "string"}, "filterByRoleIds": {"description": "List of role IDs to filter org users by, representing which roles are selectable people for this field. Only present for person fields.", "example": ["23d4d8d3-dc10-4e7a-a183-69968751f23e"], "items": {"example": "Sunt harum quaerat sed et voluptate ullam.", "type": "string"}, "type": "array"}, "id": {"description": "Identifier of the field.", "example": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac7", "format": "uuid", "type": "string"}, "includeDrivers": {"description": "Indicates whether the field includes drivers as selectable people. Only present for person fields.", "example": true, "type": "boolean"}, "includeUsers": {"description": "Indicates whether the field includes users as selectable people. Only present for person fields.", "example": true, "type": "boolean"}, "label": {"description": "Label of the field.", "example": "Engine Hours", "type": "string"}, "numDecimalPlaces": {"description": "Number of decimal places allowed. Only present for number fields.", "example": 2, "format": "int64", "type": "integer"}, "options": {"description": "List of select options for check boxes or multiple choice fields.", "items": {"$ref": "#/components/schemas/FormsSelectOptionObjectResponseBody"}, "type": "array"}, "type": {"description": "Type of the field.  Valid values: `number`, `text`, `multiple_choice`, `check_boxes`, `media`, `datetime`, `signature`, `person`", "enum": ["number", "text", "multiple_choice", "check_boxes", "media", "datetime", "signature", "person"], "example": "number", "type": "string"}}, "required": ["id", "label", "type"], "type": "object"}, "FuelEnergyDriverReportDataObjectResponseBody": {"description": "Dictionary containing summarized driver report data.", "properties": {"driverReports": {"description": "List of summarized driver reports.", "items": {"$ref": "#/components/schemas/FuelEnergyDriverReportObjectResponseBody"}, "type": "array"}}, "required": ["driverReports"], "type": "object"}, "FuelEnergyDriverReportObjectResponseBody": {"description": "A summary of this driver's fuel and energy data.", "properties": {"distanceTraveledMeters": {"description": "Meters traveled over the given time range.", "example": 1384000, "format": "double", "type": "number"}, "driver": {"$ref": "#/components/schemas/GoaDriverTinyResponseResponseBody"}, "efficiencyMpge": {"description": "Efficiency in MPG or MPGE over the given time range. For fuel vehicles this will be provided in MPG, for hybrid and electric vehicles this will be provided in MPGE. MPG/MPGE values are provided based on US gallons.", "example": 21.4, "format": "double", "type": "number"}, "energyUsedKwh": {"description": "Kilowatt-hours of energy used over the given time range. Only provided for hybrid and electric vehicles.", "example": 73.2, "format": "double", "type": "number"}, "engineIdleTimeDurationMs": {"description": "Milliseconds of engine idle time over the given time range. Only provided for fuel and hybrid vehicles.", "example": 4320000, "format": "int64", "type": "integer"}, "engineRunTimeDurationMs": {"description": "Milliseconds of engine run time over the given time range. Only provided for fuel and hybrid vehicles.", "example": 576000, "format": "int64", "type": "integer"}, "estCarbonEmissionsKg": {"description": "Estimated kilograms of carbon emissions over the given time range. Only provided for fuel and hybrid vehicles.", "example": 22.7, "format": "double", "type": "number"}, "estFuelEnergyCost": {"$ref": "#/components/schemas/FuelEnergyCostResponseResponseBody"}, "fuelConsumedMl": {"description": "Milliliters of fuel consumed over the given time range. Only provided for fuel and hybrid vehicles.", "example": 162773, "format": "double", "type": "number"}}, "required": ["distanceTraveledMeters", "driver", "efficiencyMpge", "estFuelEnergyCost"], "type": "object"}, "FuelEnergyGetFuelEnergyDriverReportsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FuelEnergyGetFuelEnergyDriverReportsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FuelEnergyGetFuelEnergyDriverReportsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FuelEnergyGetFuelEnergyDriverReportsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FuelEnergyGetFuelEnergyDriverReportsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FuelEnergyGetFuelEnergyDriverReportsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FuelEnergyGetFuelEnergyDriverReportsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FuelEnergyGetFuelEnergyDriverReportsResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/FuelEnergyDriverReportDataObjectResponseBody"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "FuelEnergyGetFuelEnergyDriverReportsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FuelEnergyGetFuelEnergyDriverReportsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FuelEnergyGetFuelEnergyDriverReportsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "GetDriverTrailerAssignmentsResponseBodyResponseBody": {"description": "Object with driver assignment information and associated driver and trailer information.", "properties": {"createdAtTime": {"description": "Time when the driver trailer assignment was created, in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "type": "string"}, "driver": {"$ref": "#/components/schemas/DriverWithExternalIdObjectResponseBody"}, "endTime": {"description": "Time when the driver trailer assignment will end, in RFC 3339 format.", "example": "2019-06-13T20:10:25Z", "type": "string"}, "id": {"description": "<PERSON><PERSON><PERSON> ID of the driver trailer assignment.", "example": "08b3aeada5f4ab3010c0b4efa28d2d1890dbf8d48d2d6", "type": "string"}, "startTime": {"description": "Time when the driver trailer assignment starts, in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "type": "string"}, "trailer": {"$ref": "#/components/schemas/TrailerObjectResponseBody"}, "updatedAtTime": {"description": "Time when the driver trailer assignment was last updated, in RFC 3339 format.", "example": "2019-06-13T19:10:25Z", "type": "string"}}, "required": ["driver", "id", "startTime", "trailer"], "type": "object"}, "GoaDriverTinyResponseResponseBody": {"description": "A minified driver object. This object is only returned if the route is assigned to the driver.", "properties": {"externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "ID of the driver", "example": "45646", "type": "string"}, "name": {"description": "Name of the driver", "example": "Driver <PERSON>", "type": "string"}}, "required": ["id"], "type": "object"}, "HOSViolationTriggerDetailsObjectRequestBody": {"description": "Details specific to HOS Violation", "properties": {"maxUntilViolationMilliseconds": {"description": "Alert if driver has this specified time until driving causes an HOS violation.", "example": 600000, "format": "int64", "type": "integer"}, "violation": {"description": "The type of HOS violation.  Valid values: `CaliforniaMealbreakMissed`, `CycleHoursOn`, `DailyDrivingHours`, `DailyOnDutyHours`, `Invalid`, `RestbreakMissed`, `ShiftDrivingHours`, `ShiftHours`, `ShiftOnDutyHours`, `UnsubmittedLogs`", "enum": ["CaliforniaMealbreakMissed", "CycleHoursOn", "DailyDrivingHours", "DailyOnDutyHours", "Invalid", "RestbreakMissed", "ShiftDrivingHours", "ShiftHours", "ShiftOnDutyHours", "UnsubmittedLogs"], "example": "CaliforniaMealbreakMissed", "type": "string"}}, "required": ["maxUntilViolationMilliseconds", "violation"], "type": "object"}, "HOSViolationTriggerDetailsObjectResponseBody": {"description": "Details specific to HOS Violation", "properties": {"maxUntilViolationMilliseconds": {"description": "Alert if driver has this specified time until driving causes an HOS violation.", "example": 600000, "format": "int64", "type": "integer"}, "violation": {"description": "The type of HOS violation.  Valid values: `CaliforniaMealbreakMissed`, `CycleHoursOn`, `DailyDrivingHours`, `DailyOnDutyHours`, `Invalid`, `RestbreakMissed`, `ShiftDrivingHours`, `ShiftHours`, `ShiftOnDutyHours`, `UnsubmittedLogs`", "enum": ["CaliforniaMealbreakMissed", "CycleHoursOn", "DailyDrivingHours", "DailyOnDutyHours", "Invalid", "RestbreakMissed", "ShiftDrivingHours", "ShiftHours", "ShiftOnDutyHours", "UnsubmittedLogs"], "example": "CaliforniaMealbreakMissed", "type": "string"}}, "required": ["maxUntilViolationMilliseconds", "violation"], "type": "object"}, "HosBreak": {"description": "Remaining durations for the HOS rest break requirement.", "properties": {"timeUntilBreakDurationMs": {"description": "Time until the driver has a required break in milliseconds.", "example": 28800000, "type": "number"}}, "type": "object"}, "HosClocks": {"description": "Remaining durations and start times (where applicable) for various HOS rules. See [this page](https://www.samsara.com/fleet/eld-compliance/hours-of-service) for more information on HOS rules.", "properties": {"break": {"$ref": "#/components/schemas/HosBreak"}, "cycle": {"$ref": "#/components/schemas/HosCycle"}, "drive": {"$ref": "#/components/schemas/HosDrive"}, "shift": {"$ref": "#/components/schemas/HosShift"}}, "type": "object"}, "HosClocksForDriver": {"description": "HOS clock values for a specific driver, including remaining times and violations.", "properties": {"clocks": {"$ref": "#/components/schemas/HosClocks"}, "currentDutyStatus": {"$ref": "#/components/schemas/CurrentDutyStatus"}, "currentVehicle": {"$ref": "#/components/schemas/vehicleTinyResponse"}, "driver": {"$ref": "#/components/schemas/driverTinyResponse"}, "violations": {"$ref": "#/components/schemas/HosViolations"}}, "type": "object"}, "HosClocksForDrivers": {"description": "List of HOS clocks for the specified drivers.", "items": {"$ref": "#/components/schemas/HosClocksForDriver"}, "type": "array"}, "HosClocksResponse": {"description": "Drivers HOS clocks and pagination info.", "properties": {"data": {"$ref": "#/components/schemas/HosClocksForDrivers"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "required": ["data", "pagination"], "type": "object"}, "HosCycle": {"description": "Remaining durations and start time for the HOS driving cycle.", "properties": {"cycleRemainingDurationMs": {"description": "Remaining on duty or driving time the driver has in the current cycle in milliseconds. For property-carrying drivers, this is the amount of time the driver can be on duty or driving before hitting the 60/70-hour limit in 7/8 days.", "example": 252000000, "type": "number"}, "cycleStartedAtTime": {"$ref": "#/components/schemas/time"}, "cycleTomorrowDurationMs": {"description": "Remaining on duty or driving time the driver has available tomorrow in milliseconds. For property-carrying drivers this is calculated based on the 60/70-hour limit in 7/8 days rule.", "example": 252000000, "type": "number"}}, "type": "object"}, "HosDailyLogsGetHosDailyLogsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDailyLogsGetHosDailyLogsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDailyLogsGetHosDailyLogsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDailyLogsGetHosDailyLogsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDailyLogsGetHosDailyLogsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDailyLogsGetHosDailyLogsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDailyLogsGetHosDailyLogsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDailyLogsGetHosDailyLogsResponseBody": {"properties": {"data": {"description": "List of drivers and their HOS daily logs data.", "items": {"$ref": "#/components/schemas/HosDailyLogsObjectResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "HosDailyLogsGetHosDailyLogsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDailyLogsGetHosDailyLogsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDailyLogsGetHosDailyLogsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDailyLogsObjectResponseBody": {"properties": {"distanceTraveled": {"$ref": "#/components/schemas/DistanceTraveledObjectResponseBody"}, "driver": {"$ref": "#/components/schemas/DriverWithTimezoneEldSettingsObjectResponseBody"}, "dutyStatusDurations": {"$ref": "#/components/schemas/DutyStatusDurationObjectResponseBody"}, "endTime": {"description": "The end time of the daily log in RFC 3339 format. This will be calculated using timezone of the driver.", "example": "2019-06-20T19:08:25Z", "type": "string"}, "logMetaData": {"$ref": "#/components/schemas/LogMetaDataObjectResponseBody"}, "pendingDutyStatusDurations": {"$ref": "#/components/schemas/PendingDutyStatusDurationsObjectResponseBody"}, "startTime": {"description": "The start time of the daily log in RFC 3339 format. This will be calculated using timezone of the driver.", "example": "2019-06-13T19:08:25Z", "type": "string"}}, "required": ["driver", "endTime", "startTime"], "type": "object"}, "HosDailyLogsUpdateShippingDocsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDailyLogsUpdateShippingDocsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDailyLogsUpdateShippingDocsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDailyLogsUpdateShippingDocsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDailyLogsUpdateShippingDocsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDailyLogsUpdateShippingDocsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDailyLogsUpdateShippingDocsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDailyLogsUpdateShippingDocsRequestBody": {"description": "Update the shippingDocs field.", "properties": {"shippingDocs": {"description": "ShippingDocs associated with the driver for the day.", "example": "ShippingID1, ShippingID2", "type": "string"}}, "required": ["shippingDocs"], "type": "object"}, "HosDailyLogsUpdateShippingDocsResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/PatchShippingDocsResponseBodyResponseBody"}}, "required": ["data"], "type": "object"}, "HosDailyLogsUpdateShippingDocsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDailyLogsUpdateShippingDocsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDailyLogsUpdateShippingDocsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosDrive": {"description": "Remaining durations for the HOS driving shift limits.", "properties": {"driveRemainingDurationMs": {"description": "Remaining driving time the driver has in the current shift in milliseconds. For property-carrying drivers, this is the amount of time the driver can drive before hitting the 11-hour limit.", "example": 39600000, "type": "number"}}, "type": "object"}, "HosDriversEldEventObjectResponseBody": {"properties": {"driverActivationStatus": {"description": "A value indicating whether the driver is active or deactivated.  Valid values: `active`, `deactivated`", "enum": ["active", "deactivated"], "example": "active", "type": "string"}, "eldEvents": {"description": "List of ELD event objects.", "items": {"$ref": "#/components/schemas/HosEldEventObjectResponseBody"}, "type": "array"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "ID of the driver.", "example": "494123", "type": "string"}, "name": {"description": "Name of the driver.", "example": "<PERSON>", "type": "string"}}, "required": ["driverActivationStatus", "eldEvents", "id", "name"], "type": "object"}, "HosEldEventObjectResponseBody": {"description": "An event that refers to a discrete instance in time with various data elements. Depending on the type of event, not every field will be populated. For more information, see the ELD Mandate [section 3.1.2](https://www.ecfr.gov/cgi-bin/retrieveECFR?gp=1&ty=HTML&h=L&mc=true&=PART&n=pt49.5.395#ap49.5.395_138.a).", "properties": {"accumulatedVehicleMeters": {"description": "The accumulated meters in the given ignition power on cycle.", "example": 106, "format": "int64", "type": "integer"}, "elapsedEngineHours": {"description": "The elapsed time in the engine's operation in the given ignition power on cycle.", "example": 284.1, "format": "double", "type": "number"}, "eldEventCode": {"description": "A dependent attribute on `eldEventType` that specifies the nature of the event, as defined in the ELD Mandate [section 7.20](https://www.ecfr.gov/cgi-bin/retrieveECFR?gp=1&ty=HTML&h=L&mc=true&=PART&n=pt49.5.395#ap49.5.395_138.a).  Valid values: `1`, `2`, `3`, `4`, `5`, `6`, `7`, `8`, `9`", "example": 3, "maximum": 9, "minimum": 1, "type": "integer"}, "eldEventRecordOrigin": {"description": "An attribute for the event record indicating whether it is automatically recorded, or edited, entered or accepted by the driver, requested by another authenticated user, or assumed from unidentified driver profile, as defined in the ELD Mandate [section 7.22](https://www.ecfr.gov/cgi-bin/retrieveECFR?gp=1&ty=HTML&h=L&mc=true&=PART&n=pt49.5.395#ap49.5.395_138.a).  Valid values: `1`, `2`, `3`, `4`", "example": 2, "maximum": 4, "minimum": 1, "type": "integer"}, "eldEventRecordStatus": {"description": "An attribute for the event record indicating whether an event is active or inactive and further, if inactive, whether it is due to a change or lack of confirmation by the driver or due to a driver's rejection of change request, as defined in the ELD Mandate [section 7.23](https://www.ecfr.gov/cgi-bin/retrieveECFR?gp=1&ty=HTML&h=L&mc=true&=PART&n=pt49.5.395#ap49.5.395_138.a).  Valid values: `1`, `2`, `3`, `4`", "example": 2, "maximum": 4, "minimum": 1, "type": "integer"}, "eldEventType": {"description": "An attribute specifying the type of ELD event, as defined in the ELD Mandate [section 7.25](https://www.ecfr.gov/cgi-bin/retrieveECFR?gp=1&ty=HTML&h=L&mc=true&=PART&n=pt49.5.395#ap49.5.395_138.a).  Valid values: `1`, `2`, `3`, `4`, `5`, `6`, `7`", "example": 6, "maximum": 7, "minimum": 1, "type": "integer"}, "location": {"$ref": "#/components/schemas/HosEldEventLocationObjectResponseBody"}, "malfunctionDiagnosticCode": {"description": "A code that further specifies the underlying malfunction or data diagnostic event, as defined in the ELD Mandate [section 7.34](https://www.ecfr.gov/cgi-bin/retrieveECFR?gp=1&ty=HTML&h=L&mc=true&=PART&n=pt49.5.395#ap49.5.395_138.a).  Valid values: `P`, `E`, `T`, `L`, `R`, `S`, `O`, `1`, `2`, `3`, `4`, `5`, `6`", "enum": ["P", "E", "T", "L", "R", "S", "O", "1", "2", "3", "4", "5", "6"], "example": "P", "type": "string"}, "remark": {"$ref": "#/components/schemas/HosEldEventRemarkObjectResponseBody"}, "time": {"description": "A time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}, "totalEngineHours": {"description": "The aggregated time of a vehicle's engine's operation since its inception.", "example": 2894.1, "format": "double", "type": "number"}, "totalVehicleMeters": {"description": "The total meters recorded for the vehicle.", "example": 1004566, "format": "int64", "type": "integer"}, "vehicle": {"$ref": "#/components/schemas/GoaVehicleTinyResponseResponseBody"}}, "required": ["eldEventCode", "eldEventType", "time"], "type": "object"}, "HosEldEventRemarkObjectResponseBody": {"properties": {"comment": {"description": "The content of the remark.", "example": "Pre-Trip Inspection", "type": "string"}, "locationDescription": {"description": "The location description entered by the user", "example": "Near San Francisco", "type": "string"}, "time": {"description": "The time in RFC 3339 format at which the remark was created.", "example": "2019-06-13T19:08:25Z", "type": "string"}}, "required": ["comment", "locationDescription", "time"], "type": "object"}, "HosEldEventsGetHosEldEventsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosEldEventsGetHosEldEventsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosEldEventsGetHosEldEventsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosEldEventsGetHosEldEventsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosEldEventsGetHosEldEventsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosEldEventsGetHosEldEventsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosEldEventsGetHosEldEventsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosEldEventsGetHosEldEventsResponseBody": {"properties": {"data": {"description": "List of drivers and their ELD event objects data.", "items": {"$ref": "#/components/schemas/HosDriversEldEventObjectResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data"], "type": "object"}, "HosEldEventsGetHosEldEventsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosEldEventsGetHosEldEventsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosEldEventsGetHosEldEventsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosLogEntry": {"description": "A single HOS log entry.", "properties": {"codrivers": {"description": "The codriver information.", "items": {"$ref": "#/components/schemas/driverTinyResponse"}, "type": "array"}, "hosStatusType": {"description": "The Hours of Service status type. Valid values: `offDuty`, `sleeperBed`, `driving`, `onDuty`, `yardMove`, `personalConveyance`.", "enum": ["offDuty", "sleeperBed", "driving", "onDuty", "yardMove", "personalConveyance"], "example": "offDuty", "type": "string"}, "logEndTime": {"$ref": "#/components/schemas/time"}, "logRecordedLocation": {"$ref": "#/components/schemas/HosLogLocation"}, "logStartTime": {"$ref": "#/components/schemas/time"}, "remark": {"description": "Remark associated with the log entry.", "example": "Lunch Break", "type": "string"}, "vehicle": {"$ref": "#/components/schemas/vehicleTinyResponse"}}, "required": ["logStartTime"], "type": "object"}, "HosLogsForDriver": {"description": "List of HOS logs for a driver.", "properties": {"driver": {"$ref": "#/components/schemas/driverTinyResponse"}, "hosLogs": {"$ref": "#/components/schemas/HosLogsList"}}, "type": "object"}, "HosLogsForDrivers": {"description": "List of HOS logs for the specified drivers.", "items": {"$ref": "#/components/schemas/HosLogsForDriver"}, "type": "array"}, "HosLogsList": {"description": "List of HOS log entries.", "items": {"$ref": "#/components/schemas/HosLogEntry"}, "type": "array"}, "HosLogsResponse": {"description": "HOS logs and pagination info.", "properties": {"data": {"$ref": "#/components/schemas/HosLogsForDrivers"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "required": ["data", "pagination"], "type": "object"}, "HosShift": {"description": "Remaining durations and start time for the HOS on duty shift limits.", "properties": {"shiftRemainingDurationMs": {"description": "Remaining on duty or driving time the driver in the current shift in milliseconds. For property-carrying drivers, this is the amount of time the driver can be on duty or driving before hitting the 14-hour limit.", "example": 50400000, "type": "number"}}, "type": "object"}, "HosViolationDataResponseBody": {"description": "Details specific to Hos Violation.", "properties": {"driver": {"$ref": "#/components/schemas/alertObjectDriverResponseBody"}}, "type": "object"}, "HosViolationDayObjectResponseBody": {"properties": {"endTime": {"description": "The end time of the day on which the violation occurred in RFC 3339 format. This is determined by the driver's ELD start hour (00:00 or 12:00)", "example": "2019-06-14T12:00:00Z", "type": "string"}, "startTime": {"description": "The start time of the day on which the violation occurred in RFC 3339 format. This is determined by the driver's ELD start hour (00:00 or 12:00)", "example": "2019-06-13T12:00:00Z", "type": "string"}}, "required": ["endTime", "startTime"], "type": "object"}, "HosViolationObjectResponseBody": {"properties": {"day": {"$ref": "#/components/schemas/HosViolationDayObjectResponseBody"}, "description": {"description": "Description containing violation type, region, and other metadata. This field can assume the following formats for the following types:\ncaliforniaMealbreakMissed, restbreakMissed: \"[description] ([max on duty hours] hours)\"\ncycleHoursOn, dailyDrivingHours, dailyOffDutyNonResetHours, dailyOffDutyTotalHours, dailyOnDutyHours, shiftDrivingHours, shiftHours, shiftOnDutyHours: \"[description] ([region]-[max hours in duty status] hours)\"\ncycleOffHoursAfterOnDutyHours: \"[description] ([region]): [minimum hours consecutive rest] hours off duty required after [max hours before consecutive rest] hours on-duty time\"\ndailyOffDutyDeferralAddToDay2Consecutive, dailyOffDutyDeferralNotPartMandatory, dailyOffDutyDeferralTwoDayDrivingLimit, dailyOffDutyDeferralTwoDayOffDuty, mandatory24HoursOffDuty: \"[description] ([region])\"\nunsubmittedLogs: \"Missing Driver Certification\"", "example": "Daily Off-Duty Time (Canada South-10 hours)", "type": "string"}, "driver": {"$ref": "#/components/schemas/GoaDriverTinyResponseResponseBody"}, "durationMs": {"description": "Duration the driver was in violation in milliseconds. This is the time between the time the driver starts being in violation until the end of the time window for violations that have one (e.g. `shiftDrivingHours`) or until the end of the day. The duration of some violations may cover the whole day (e.g. `unsubmittedLogs`).", "example": 31970000, "format": "int64", "type": "integer"}, "type": {"description": "The string value of the violation type.  Valid values: `NONE`, `californiaMealbreakMissed`, `cycleHoursOn`, `cycleOffHoursAfterOnDutyHours`, `dailyDrivingHours`, `dailyOffDutyDeferralAddToDay2Consecutive`, `dailyOffDutyDeferralNotPartMandatory`, `dailyOffDutyDeferralTwoDayDrivingLimit`, `dailyOffDutyDeferralTwoDayOffDuty`, `dailyOffDutyNonResetHours`, `dailyOffDutyTotalHours`, `dailyOnDutyHours`, `mandatory24HoursOffDuty`, `restbreakMissed`, `shiftDrivingHours`, `shiftHours`, `shiftOnDutyHours`, `unsubmittedLogs`", "enum": ["NONE", "californiaMealbreakMissed", "cycleHoursOn", "cycleOffHoursAfterOnDutyHours", "dailyDrivingHours", "dailyOffDutyDeferralAddToDay2Consecutive", "dailyOffDutyDeferralNotPartMandatory", "dailyOffDutyDeferralTwoDayDrivingLimit", "dailyOffDutyDeferralTwoDayOffDuty", "dailyOffDutyNonResetHours", "dailyOffDutyTotalHours", "dailyOnDutyHours", "mandatory24HoursOffDuty", "restbreakMissed", "shiftDrivingHours", "shiftHours", "shiftOnDutyHours", "unsubmittedLogs"], "example": "shiftHours", "type": "string"}, "violationStartTime": {"description": "The start time of the violation in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "type": "string"}}, "required": ["day", "description", "driver", "durationMs", "type", "violationStartTime"], "type": "object"}, "HosViolations": {"description": "Durations the driver has been in violation of HOS rules. See [this page](https://www.samsara.com/fleet/eld-compliance/hours-of-service) for more information on HOS rules.", "properties": {"cycleViolationDurationMs": {"description": "Time since the driver has surpassed the driving cycle duration limit in milliseconds. For property-carrying drivers, this is the amount of time the driver has been on duty or driving past the 60/70-hour limit in 7/8 days.", "example": 39600000, "type": "number"}, "shiftDrivingViolationDurationMs": {"description": "Time since the driver has surpassed the driving shift duration limit in milliseconds. For property-carrying drivers, this is the amount of time the driver has been driving past the 11-hour limit.", "example": 39600000, "type": "number"}}, "type": "object"}, "HosViolationsGetHosViolationsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosViolationsGetHosViolationsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosViolationsGetHosViolationsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosViolationsGetHosViolationsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosViolationsGetHosViolationsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosViolationsGetHosViolationsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosViolationsGetHosViolationsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosViolationsGetHosViolationsResponseBody": {"properties": {"data": {"description": "List of violations data", "items": {"$ref": "#/components/schemas/HosViolationsObjectResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "HosViolationsGetHosViolationsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosViolationsGetHosViolationsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosViolationsGetHosViolationsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "HosViolationsObjectResponseBody": {"properties": {"violations": {"description": "List of violations and their associated drivers", "items": {"$ref": "#/components/schemas/HosViolationObjectResponseBody"}, "type": "array"}}, "required": ["violations"], "type": "object"}, "ListDriversResponse": {"description": "A list of drivers.", "properties": {"data": {"items": {"$ref": "#/components/schemas/Driver"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "type": "object"}, "PatchDriverTrailerAssignmentsResponseBodyResponseBody": {"description": "Response after successfully updating a Driver Trailer Assignment", "properties": {"createdAtTime": {"description": "Time when the driver trailer assignment was created, in RFC 3339 format", "example": "2019-06-13T19:08:25Z", "type": "string"}, "driverId": {"description": "<PERSON><PERSON><PERSON> ID for the driver that this assignment is for.", "example": "0987", "type": "string"}, "endTime": {"description": "Time when the driver trailer assignment ends, in RFC 3339 format", "example": "2019-06-13T19:08:25Z", "type": "string"}, "id": {"description": "<PERSON><PERSON><PERSON> ID for the assignment.", "example": "08b3aeada5f4ab3010c0b4efa28d2d1890dbf8d48d2d6", "type": "string"}, "startTime": {"description": "Time when the driver trailer assignment starts, in RFC 3339 format", "example": "2019-06-13T19:08:25Z", "type": "string"}, "trailerId": {"description": "Samsara ID of the trailer", "example": "494123", "type": "string"}, "updatedAtTime": {"description": "Time when the driver trailer assignment was last updated, in RFC 3339 format", "example": "2019-06-13T19:08:25Z", "type": "string"}}, "required": ["driverId", "endTime", "id", "startTime", "trailerId"], "type": "object"}, "PatchDriverVehicleAssignmentsV2RequestBodyMetadataRequestBody": {"description": "<PERSON><PERSON><PERSON> about this driver assignment", "properties": {"sourceName": {"description": "Describes where the external assignment is coming from", "example": "My custom assignment source", "maxLength": 100, "type": "string"}}, "type": "object"}, "PatchDriverVehicleAssignmentsV2ResponseBodyResponseBody": {"description": "Response after successfully updating a Driver Assignment", "properties": {"message": {"description": "A description of the outcome from updating Driver Assignment information", "example": "Driver assignment was successfully updated", "type": "string"}}, "type": "object"}, "PostDriverTrailerAssignmentsResponseBodyResponseBody": {"description": "Response after successfully submitting a Driver Trailer Assignment", "properties": {"createdAtTime": {"description": "Time when the driver trailer assignment was created, in RFC 3339 format", "example": "2019-06-13T19:08:25Z", "type": "string"}, "driverId": {"description": "<PERSON><PERSON><PERSON> ID for the driver that this assignment is for.", "example": "0987", "type": "string"}, "id": {"description": "<PERSON><PERSON><PERSON> ID for the assignment.", "example": "08b3aeada5f4ab3010c0b4efa28d2d1890dbf8d48d2d6", "type": "string"}, "startTime": {"description": "Time when the driver trailer assignment starts, in RFC 3339 format", "example": "2019-06-13T19:08:25Z", "type": "string"}, "trailerId": {"description": "Samsara ID of the trailer", "example": "494123", "type": "string"}, "updatedAtTime": {"description": "Time when the driver trailer assignment was last updated, in RFC 3339 format", "example": "2019-06-13T19:08:25Z", "type": "string"}}, "required": ["createdAtTime", "driverId", "id", "startTime", "trailerId", "updatedAtTime"], "type": "object"}, "PostDriverVehicleAssignmentsV2RequestBodyMetadataRequestBody": {"description": "<PERSON><PERSON><PERSON> about this driver assignment", "properties": {"sourceName": {"description": "Describes where the external assignment is coming from", "example": "My custom assignment source", "maxLength": 100, "type": "string"}}, "type": "object"}, "PostDriverVehicleAssignmentsV2ResponseBodyResponseBody": {"description": "Response after successfully submitting a Driver Assignment", "properties": {"message": {"description": "A description of the outcome from submitting Driver Assignment information", "example": "Driver assignment was successfully submitted", "type": "string"}}, "type": "object"}, "SafetyEventDriver": {"$ref": "#/components/schemas/driverTinyResponse"}, "SafetyEventDriverObjectResponseBody": {"description": "A minified driver object.", "properties": {"id": {"description": "ID of the driver.", "example": "0987", "type": "string"}}, "type": "object"}, "SettingsGetDriverAppSettingsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SettingsGetDriverAppSettingsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SettingsGetDriverAppSettingsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SettingsGetDriverAppSettingsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SettingsGetDriverAppSettingsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SettingsGetDriverAppSettingsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SettingsGetDriverAppSettingsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SettingsGetDriverAppSettingsResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/DriverAppSettingsResponseObjectResponseBody"}}, "required": ["data"], "type": "object"}, "SettingsGetDriverAppSettingsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SettingsGetDriverAppSettingsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SettingsGetDriverAppSettingsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SettingsPatchDriverAppSettingsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SettingsPatchDriverAppSettingsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SettingsPatchDriverAppSettingsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SettingsPatchDriverAppSettingsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SettingsPatchDriverAppSettingsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SettingsPatchDriverAppSettingsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SettingsPatchDriverAppSettingsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SettingsPatchDriverAppSettingsRequestBody": {"description": "The configuration settings for the Samsara Driver App. Can be set or updated through the Samsara Settings page or the API at any time.", "properties": {"driverFleetId": {"description": "Global login user name for the fleet driver app", "example": "abc-trucking-co", "pattern": "^[a-zA-Z0-9-:]*$", "type": "string"}, "gamification": {"description": "Driver gamification feature. Enabling this will turn on the feature for all drivers using the mobile app. Drivers can be configured into peer groups within the Drivers Page. Unconfigured drivers will be grouped on an organization level.", "example": false, "type": "boolean"}, "gamificationConfig": {"$ref": "#/components/schemas/DriverAppSettingsGamificationConfigTinyObjectRequestBody"}, "orgVehicleSearch": {"description": "Allow drivers to search for vehicles outside of their selection tag when connected to the internet.", "example": false, "type": "boolean"}, "trailerSelection": {"description": "Allow drivers to see and select trailers in the Samsara Driver app. ", "example": true, "type": "boolean"}, "trailerSelectionConfig": {"$ref": "#/components/schemas/DriverAppSettingsTrailerSelectionConfigTinyObjectRequestBody"}}, "type": "object"}, "SettingsPatchDriverAppSettingsResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/DriverAppSettingsResponseObjectResponseBody"}}, "required": ["data"], "type": "object"}, "SettingsPatchDriverAppSettingsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SettingsPatchDriverAppSettingsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SettingsPatchDriverAppSettingsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "SuddenFuelLevelDropResponseBody": {"description": "Details specific to Sudden Fuel Level Drop.", "properties": {"changeEndTime": {"description": "The end time of the fuel level change in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "format": "date-time", "type": "string"}, "changeStartTime": {"description": "The start time of the fuel level change in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "format": "date-time", "type": "string"}, "driver": {"$ref": "#/components/schemas/alertObjectDriverResponseBody"}, "fuelLevelAfterMillipercent": {"description": "The fuel level after the sudden fuel level drop in millipercents.", "example": 70000, "format": "int32", "type": "integer"}, "fuelLevelBeforeMillipercent": {"description": "The fuel level before the sudden fuel level drop in millipercents.", "example": 50000, "format": "int32", "type": "integer"}, "location": {"$ref": "#/components/schemas/EventLocationResponseBody"}, "vehicle": {"$ref": "#/components/schemas/alertObjectVehicleResponseBody"}}, "type": "object"}, "SuddenFuelLevelDropTriggerDetailsObjectRequestBody": {"description": "Details specific to Sudden Fuel Level Drop", "properties": {"minFuelLevelChangeInPercents": {"description": "The minimum fuel level change in percents to trigger on. Need to be between 5 to 100.", "example": 6, "format": "int64", "type": "integer"}}, "required": ["minFuelLevelChangeInPercents"], "type": "object"}, "SuddenFuelLevelDropTriggerDetailsObjectResponseBody": {"description": "Details specific to Sudden Fuel Level Drop", "properties": {"minFuelLevelChangeInPercents": {"description": "The minimum fuel level change in percents to trigger on. Need to be between 5 to 100.", "example": 6, "format": "int64", "type": "integer"}}, "required": ["minFuelLevelChangeInPercents"], "type": "object"}, "TachographDriverFile": {"description": "Tachograph driver file", "properties": {"cardNumber": {"description": "Tachograph card number associated with the file.", "example": "1000000492436002", "type": "string"}, "createdAtTime": {"description": "Creation time of files in RFC 3339 format. This is either the download time from the tachograph itself (for files downloaded via Samsara VG) or upload time (for files manually uploaded via Samsara UI).", "example": "2020-01-02T15:04:05Z07:00", "type": "string"}, "id": {"description": "ID of the file.", "example": "4aff772c-a7bb-45e6-8e41-6a53e34feb83", "type": "string"}, "url": {"description": "A temporary URL which can be used to download the file. The link can be used multiple times and expires after an hour.", "example": "https://samsara-tachograph-files.s3.us-west-2.amazonaws.com/123/456/789/4aff772c-a7bb-45e6-8e41-6a53e34feb83.ddd", "type": "string"}}, "type": "object"}, "TachographDriverFileData": {"items": {"$ref": "#/components/schemas/TachographDriverFileListWrapper"}, "type": "array"}, "TachographDriverFileList": {"description": "List of all tachograph driver files in a specified time range.", "items": {"$ref": "#/components/schemas/TachographDriverFile"}, "type": "array"}, "TachographDriverFileListWrapper": {"properties": {"driver": {"$ref": "#/components/schemas/driverTinyResponse"}, "files": {"$ref": "#/components/schemas/TachographDriverFileList"}}, "type": "object"}, "TachographDriverFilesResponse": {"description": "List of all driver tachograph files in a specified time range.", "properties": {"data": {"$ref": "#/components/schemas/TachographDriverFileData"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "type": "object"}, "TinyDriverObjectRequestBody": {"description": "The driver of a vehicle.", "properties": {"driverId": {"description": "ID of the driver.", "example": "12434", "type": "string"}}, "required": ["driverId"], "type": "object"}, "TinyDriverObjectResponseBody": {"description": "The driver of a vehicle.", "properties": {"driverId": {"description": "ID of the driver.", "example": "12434", "type": "string"}}, "required": ["driverId"], "type": "object"}, "TrailerAssignmentsCreateDriverTrailerAssignmentBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsCreateDriverTrailerAssignmentBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsCreateDriverTrailerAssignmentGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsCreateDriverTrailerAssignmentInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsCreateDriverTrailerAssignmentMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsCreateDriverTrailerAssignmentNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsCreateDriverTrailerAssignmentNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsCreateDriverTrailerAssignmentRequestBody": {"description": "Create a new driver-trailer assignment", "properties": {"driverId": {"description": "ID of the driver. This can be either a unique Samsara ID or an [external ID](https://developers.samsara.com/docs/external-ids) for the driver.", "example": "494123", "type": "string"}, "startTime": {"description": "The start time in RFC 3339 format. The time needs to be current or within the past 7 days. Defaults to now if not provided", "example": "2019-06-13T19:08:25Z", "type": "string"}, "trailerId": {"description": "ID of the trailer. This can be either a unique Samsara ID or an [external ID](https://developers.samsara.com/docs/external-ids) for the trailer.", "example": "12345", "type": "string"}}, "required": ["driverId", "trailerId"], "type": "object"}, "TrailerAssignmentsCreateDriverTrailerAssignmentResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/PostDriverTrailerAssignmentsResponseBodyResponseBody"}}, "required": ["data"], "type": "object"}, "TrailerAssignmentsCreateDriverTrailerAssignmentServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsCreateDriverTrailerAssignmentTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsCreateDriverTrailerAssignmentUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsGetDriverTrailerAssignmentsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsGetDriverTrailerAssignmentsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsGetDriverTrailerAssignmentsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsGetDriverTrailerAssignmentsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsGetDriverTrailerAssignmentsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsGetDriverTrailerAssignmentsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsGetDriverTrailerAssignmentsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsGetDriverTrailerAssignmentsResponseBody": {"properties": {"data": {"description": "List of driver trailer assignment objects and their respective driver and trailer info.", "items": {"$ref": "#/components/schemas/GetDriverTrailerAssignmentsResponseBodyResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "TrailerAssignmentsGetDriverTrailerAssignmentsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsGetDriverTrailerAssignmentsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsGetDriverTrailerAssignmentsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsUpdateDriverTrailerAssignmentBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsUpdateDriverTrailerAssignmentBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsUpdateDriverTrailerAssignmentGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsUpdateDriverTrailerAssignmentInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsUpdateDriverTrailerAssignmentMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsUpdateDriverTrailerAssignmentNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsUpdateDriverTrailerAssignmentNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsUpdateDriverTrailerAssignmentRequestBody": {"description": "Update a new driver-trailer assignment", "properties": {"endTime": {"description": "The end time in RFC 3339 format. The end time should not be in the future", "example": "2019-06-13T19:08:25Z", "type": "string"}}, "required": ["endTime"], "type": "object"}, "TrailerAssignmentsUpdateDriverTrailerAssignmentResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/PatchDriverTrailerAssignmentsResponseBodyResponseBody"}}, "required": ["data"], "type": "object"}, "TrailerAssignmentsUpdateDriverTrailerAssignmentServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsUpdateDriverTrailerAssignmentTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TrailerAssignmentsUpdateDriverTrailerAssignmentUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "UpdateDriverRequest": {"description": "Driver that should be updated.", "properties": {"attributes": {"items": {"$ref": "#/components/schemas/CreateDriverRequest_attributes"}, "type": "array"}, "carrierSettings": {"$ref": "#/components/schemas/DriverCarrierSettings"}, "currentIdCardCode": {"description": "The ID Card Code on the back of the physical card assigned to the driver.  Contact <PERSON><PERSON><PERSON> if you would like to enable this feature.", "example": "941767043", "type": "string"}, "deactivatedAtTime": {"description": "The date and time this driver is considered to be deactivated in RFC 3339 format.", "example": "2019-05-18T20:27:35Z", "type": "string"}, "driverActivationStatus": {"description": "A value indicating whether the driver is active or deactivated. Valid values: `active`, `deactivated`.", "enum": ["active", "deactivated"], "type": "string"}, "eldAdverseWeatherExemptionEnabled": {"description": "Flag indicating this driver may use Adverse Weather exemptions in ELD logs.", "type": "boolean"}, "eldBigDayExemptionEnabled": {"description": "Flag indicating this driver may use Big Day exemption in ELD logs.", "type": "boolean"}, "eldDayStartHour": {"description": "`0` indicating midnight-to-midnight ELD driving hours, `12` to indicate noon-to-noon driving hours.", "type": "integer"}, "eldExempt": {"description": "Flag indicating this driver is exempt from the Electronic Logging Mandate.", "type": "boolean"}, "eldExemptReason": {"description": "Reason that this driver is exempt from the Electronic Logging Mandate (see eldExempt).", "example": "Bad driver", "type": "string"}, "eldPcEnabled": {"description": "Flag indicating this driver may select the Personal Conveyance duty status in ELD logs.", "type": "boolean"}, "eldYmEnabled": {"description": "Flag indicating this driver may select the Yard Move duty status in ELD logs.", "type": "boolean"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "The [external IDs](https://developers.samsara.com/docs/external-ids) for the given object.", "example": {"maintenanceId": "250020", "payrollId": "ABFS18600"}, "type": "object"}, "hasDrivingFeaturesHidden": {"$ref": "#/components/schemas/DriverHasDrivingFeaturesHidden"}, "hosSetting": {"$ref": "#/components/schemas/UpdateDriverRequest_hosSetting"}, "licenseNumber": {"description": "Driver's state issued license number. The combination of this number and `licenseState` must be unique.", "example": "********", "type": "string"}, "licenseState": {"description": "Abbreviation of US state, Canadian province, or US territory that issued driver's license.", "example": "CA", "type": "string"}, "locale": {"description": "Locale override (uncommon). These are specified by ISO 3166-2 country codes for supported locales. Valid values: `us`, `at`, `be`, `ca`, `gb`, `fr`, `de`, `ie`, `it`, `lu`, `mx`, `nl`, `es`, `ch`, `pr`.", "enum": ["us", "at", "be", "ca", "gb", "fr", "de", "ie", "it", "lu", "mx", "nl", "es", "ch", "pr"], "type": "string"}, "name": {"description": "Driver's name.", "example": "<PERSON>", "maxLength": 255, "minLength": 1, "type": "string"}, "notes": {"description": "Notes about the driver.", "example": "Also goes by the nickname <PERSON><PERSON>.", "maxLength": 4096, "type": "string"}, "password": {"description": "Password that the driver can use to login to the Samsara driver app.", "example": "aSecurePassword1234", "type": "string"}, "peerGroupTagId": {"description": "The peer group tag id this driver belong to, leave blank to be in group with everyone, used for gamification.", "type": "string"}, "phone": {"description": "Phone number of the driver.", "example": "5558234327", "maxLength": 255, "type": "string"}, "staticAssignedVehicleId": {"description": "ID of vehicle that the driver is permanently assigned to. (uncommon).", "example": "456", "type": "string"}, "tachographCardNumber": {"description": "Driver's assigned tachograph card number (Europe specific)", "example": "1000000492436002", "type": "string"}, "tagIds": {"description": "IDs of tags the driver is associated with. If your access to the API is scoped by one or more tags, this field is required to pass in.", "items": {"example": "147", "type": "string"}, "type": "array"}, "timezone": {"description": "Home terminal timezone, in order to indicate what time zone should be used to calculate the ELD logs. Driver timezones use [IANA timezone database](https://www.iana.org/time-zones) keys (e.g. `America/Los_Angeles`, `America/New_York`, `Europe/London`, etc.). You can find a mapping of common timezone formats to IANA timezone keys [here](https://unicode.org/cldr/charts/latest/supplemental/zone_tzid.html).", "example": "America/Los_Angeles", "type": "string"}, "usDriverRulesetOverride": {"$ref": "#/components/schemas/UsDriverRulesetOverride"}, "username": {"description": "Driver's login username into the driver app. The username may not contain spaces or the '@' symbol. The username must be unique.", "example": "<PERSON><PERSON><PERSON>", "maxLength": 189, "minLength": 1, "type": "string"}, "vehicleGroupTagId": {"description": "Tag ID which determines which vehicles a driver will see when selecting vehicles.", "example": "342417", "type": "string"}, "waitingTimeDutyStatusEnabled": {"description": "Flag indicating this driver may select waiting time duty status in ELD logs", "type": "boolean"}}, "type": "object"}, "UpdateDriverRequest_hosSetting": {"description": "Hos settings for a driver.", "properties": {"heavyHaulExemptionToggleEnabled": {"$ref": "#/components/schemas/DriverHeavyHaulExemptionToggleEnabled"}}, "type": "object"}, "UsDriverRulesetOverride": {"description": "US Driver Ruleset override for a given driver. If the driver is operating under a ruleset different from the organization default, the override is used. Updating this value only updates the override setting for this driver. Explicitly setting this field to `null` will delete driver's ruleset override. If the driver does not have an override ruleset set, the response will not include any usDriverRulesetOverride information.", "properties": {"cycle": {"description": "The driver's working cycle. Valid values: `USA Property (8/70)`, `USA Property (7/60)`, `USA Passenger (8/70)`, `USA Passenger (7/60)`, `Alaska Property (8/80)`, `Alaska Property (7/70)`, `Alaska Passenger (8/80)`, `Alaska Passenger (7/70)`, `California School/FLV (8/80)`, `California Farm (8/112)`, `California Property (8/80)`, `California Flammable Liquid (8/80)`, `California Passenger (8/80)`, `California Motion Picture (8/80)`, `Florida (8/80)`, `Florida (7/70)`, `Nebraska (8/80)`, `Nebraska (7/70)`, `North Carolina (8/80)`, `North Carolina (7/70)`, `Oklahoma (8/70)`, `Oklahoma (7/60)`, `Oregon (8/80)`, `Oregon (7/70)`, `South Carolina (8/80)`, `South Carolina (7/70)`, `Texas (7/70)`, `Wisconsin (8/80)`, `Wisconsin (7/70)`", "enum": ["USA Property (8/70)", "USA Property (7/60)", "USA Passenger (8/70)", "USA Passenger (7/60)", "Alaska Property (8/80)", "Alaska Property (7/70)", "Alaska Passenger (8/80)", "Alaska Passenger (7/70)", "California School/FLV (8/80)", "California Farm (8/112)", "California Property (8/80)", "California Flammable Liquid (8/80)", "California Passenger (8/80)", "California Motion Picture (8/80)", "Florida (8/80)", "Florida (7/70)", "Nebraska (8/80)", "Nebraska (7/70)", "North Carolina (8/80)", "North Carolina (7/70)", "Oklahoma (8/70)", "Oklahoma (7/60)", "Oregon (8/80)", "Oregon (7/70)", "South Carolina (8/80)", "South Carolina (7/70)", "Texas (7/70)", "Wisconsin (8/80)", "Wisconsin (7/70)"], "example": "TX 70 hour / 7 day", "type": "string"}, "restart": {"description": "Amount of time necessary for the driver to be resting in order to restart their cycle. Valid values: `34-hour Restart`, `24-hour Restart`, `36-hour Restart`, `72-hour Restart`, `None`.", "enum": ["34-hour Restart", "24-hour Restart", "36-hour Restart", "72-hour Restart", "None"], "example": "34-hour Restart", "type": "string"}, "restbreak": {"description": "The restbreak required for this driver. Valid values: `Property (off-duty/sleeper)`, `California Mealbreak (off-duty/sleeper)`, `None`.", "enum": ["Property (off-duty/sleeper)", "California Mealbreak (off-duty/sleeper)", "None"], "example": "None", "type": "string"}, "usStateToOverride": {"description": "The jurisdiction of the ruleset applied to this driver. These are specified by either the ISO 3166-2 postal code for the supported US states, or empty string '' for US Federal Ruleset jurisdiction. Valid values: ``, `AK`, `CA`, `FL`, `NE`, `NC`, `OK`, `OR`, `SC`, `TX`, `WI`.", "enum": ["", "AK", "CA", "FL", "NE", "NC", "OK", "OR", "SC", "TX", "WI"], "example": "TX", "type": "string"}}, "required": ["cycle", "restart", "restbreak", "usStateToOverride"], "type": "object"}, "V1DocumentConditionalFields": {"description": "An array of objects that describe a set of fields conditionally shown by a multiple choice value.", "example": [{"conditionalFieldFirstIndex": 1, "conditionalFieldLastIndex": 4, "triggeringFieldIndex": 0, "triggeringFieldValue": "Yes"}], "items": {"properties": {"conditionalFieldIndexFirst": {"description": "Start index of the conditional fields set", "type": "number"}, "conditionalFieldIndexLast": {"description": "End index of the conditional fields set", "type": "number"}, "triggeringFieldIndex": {"description": "Index of the multiple choice field that triggers the conditional fields", "type": "number"}, "triggeringFieldValue": {"description": "Multiple choice value that triggers the conditional fields", "type": "string"}}, "type": "object"}, "type": "array"}, "V1DocumentCreateFields": {"description": "List of fields for the document. The fields must be listed in the order that that they appear in the document type. Values of fields can be set to null or omitted if the document state is set to \"Required\".", "example": [{"label": "Example number field label", "numberValue": 999, "valueType": "ValueType_Number"}, {"label": "text", "stringValue": "example string", "valueType": "ValueType_String"}, {"label": "Example multi choice label", "multipleChoiceValue": [{"selected": false, "value": "option 1"}, {"selected": true, "value": "option 2"}], "valueType": "ValueType_MultipleChoice"}, {"dateTimeValue": {"dateTimeMs": 10855639004823}, "label": "Example date time label", "valueType": "ValueType_DateTime"}, {"label": "Example photo label", "valueType": "ValueType_Photo"}, {"label": "Example signature label", "valueType": "ValueType_Signature"}], "items": {"description": "Field item parameters.", "properties": {"dateTimeValue": {"description": "The date time value to populate the field. Use with ValueType_DateTime. Cannot be ommited or set to null", "properties": {"dateTimeMs": {"type": "number"}}, "type": "object"}, "label": {"description": "label of the field", "type": "string"}, "multipleChoiceValue": {"description": "The multiple choice value to populate the field. Use with ValueType_MultipleChoice. Cannot be ommited or set to null", "items": {"description": "An option item for a multiple choice field", "properties": {"selected": {"type": "boolean"}, "value": {"type": "string"}}, "type": "object"}, "type": "array"}, "numberValue": {"description": "The number value to populate the field. Use with ValueType_Number. Can be ommited or set to null", "type": "number"}, "stringValue": {"description": "The string value to populate the field. Use with ValueType_String. Can be ommited or set to null", "type": "string"}, "value": {"description": "DEPRECATED. Please use one of the type-specific parameters.", "type": "object"}, "valueType": {"description": "The valueType of the field. Use with the corresponding {type of field}Value attribute. Signature and photo fields do not support values added via API. Their values must be omitted or set to null. For example:\n\n```\n\n\"fields\": [\n\n  {\n\n    \"label\": \"Signature\",\n\n    \"valueType\": \"ValueType_Signature\"\n\n  },\n\n  ... more fields\n\n]\n\n```", "type": "string"}}, "required": ["label", "valueType"], "type": "object"}, "type": "array"}, "V1DocumentFieldType": {"properties": {"label": {"description": "Name of this field type.", "example": "Fuel Cost ($)", "type": "string"}, "multipleChoiceValueTypeMetadata": {"$ref": "#/components/schemas/V1DocumentFieldType_multipleChoiceValueTypeMetadata"}, "numberValueTypeMetadata": {"$ref": "#/components/schemas/V1DocumentFieldType_numberValueTypeMetadata"}, "signatureValueTypeMetadata": {"$ref": "#/components/schemas/V1DocumentFieldType_signatureValueTypeMetadata"}, "valueType": {"description": "The type of value this field can have.\nValid values: `ValueType_Number`, `ValueType_String`, `ValueType_Photo`, `ValueType_MultipleChoice`, `ValueType_Signature`, `ValueType_DateTime`.", "example": "ValueType_Number", "type": "string"}}, "required": ["label", "valueType"], "type": "object"}, "V1DocumentFieldType_multipleChoiceValueTypeMetadata": {"description": "Metadata about the multiple choice field. Only present for value type `ValueType_MultipleChoice`", "properties": {"multipleChoiceOptionLabels": {"description": "Array of the multiple choice option labels for the field", "items": {"$ref": "#/components/schemas/V1DocumentFieldType_multipleChoiceValueTypeMetadata_multipleChoiceOptionLabels"}, "type": "array"}}, "type": "object"}, "V1DocumentFieldType_multipleChoiceValueTypeMetadata_multipleChoiceOptionLabels": {"properties": {"label": {"example": "option 1", "type": "string"}}, "type": "object"}, "V1DocumentFieldType_numberValueTypeMetadata": {"description": "Metadata about the number field. Only present for value type `ValueType_Number`", "properties": {"numDecimalPlaces": {"description": "The number of decimal places allowed for this number field", "example": 2, "format": "int64", "type": "number"}}, "type": "object"}, "V1DocumentFieldType_signatureValueTypeMetadata": {"description": "Metadata about the signature field. Only present for value type `ValueType_Signature`", "properties": {"legalText": {"description": "Legal text displayed alongside signature", "example": "I consent on behalf of myself and my employer to using electronic signatures in this transaction. I understand that I can request a copy of the signed documentation from the party requesting my signature.", "type": "string"}}, "type": "object"}, "V1DocumentFields": {"description": "An array of field objects associated with a document.", "example": [{"label": "Was there an accident?", "multipleChoiceValue": [{"selected": false, "value": "Yes"}, {"selected": true, "value": "No"}], "value": [{"selected": false, "value": "Yes"}, {"selected": true, "value": "No"}], "valueType": "ValueType_MultipleChoice"}, {"label": "Number of cars involved", "numberValue": 123, "value": 123, "valueType": "ValueType_Number"}, {"label": "Description of Accident", "stringValue": "Example text value", "value": "Example text value", "valueType": "ValueType_String"}, {"label": "Images of the damaged cars", "photoValue": [{"url": "https://samsara-driver-media-upload.s3.us-west-2.amazonaws.com/123456"}], "value": [{"url": "https://samsara-driver-media-upload.s3.us-west-2.amazonaws.com/123456", "uuid": "f5271458-21f9-4a9f-a290-780c6d8840ff"}], "valueType": "ValueType_Photo"}, {"barcodeValue": [{"barcodeType": "org.gs1.EAN-13", "barcodeValue": "0853883003114"}, {"barcodeType": "org.gs1.EAN-13", "barcodeValue": "0036000318036"}], "label": "Accident Ticket Barcode", "value": [{"barcodeType": "org.gs1.EAN-13", "barcodeValue": "0853883003114"}, {"barcodeType": "org.gs1.EAN-13", "barcodeValue": "0036000318036"}], "valueType": "ValueType_Barcode"}, {"dateTimeValue": {"dateTimeMs": 1596681182972}, "label": "Today's Date", "value": {"dateTimeMs": 1596681182972}, "valueType": "ValueType_DateTime"}, {"label": "Sign Signature", "signatureValue": {"name": "<PERSON>", "signedAtMs": 1596681191327, "url": "https://samsara-driver-media-upload.s3.us-west-2.amazonaws.com/123456"}, "value": {"name": "<PERSON>", "signedAtMs": 1596681191327, "url": "https://samsara-driver-media-upload.s3.us-west-2.amazonaws.com/123456", "uuid": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac7"}, "valueType": "ValueType_Signature"}], "items": {"properties": {"barcodeValue": {"description": "The value of a barcode scanning field. Only present for barcode scanning fields", "items": {"properties": {"barcodeType": {"description": "The barcode type that was scanned", "type": "string"}, "barcodeValue": {"description": "The captured barcode value", "type": "string"}}, "type": "object"}, "type": "array"}, "dateTimeValue": {"description": "The value of a date time field. Only present for date time fields.", "properties": {"dateTimeMs": {"description": "Date time value in milliseconds.", "type": "number"}}, "type": "object"}, "label": {"description": "The name of a field.", "type": "string"}, "multipleChoiceValue": {"description": "The value of a multiple choice field. Only present for multiple choice fields.", "items": {"properties": {"selected": {"description": "<PERSON><PERSON><PERSON> representing if the choice has been selected.", "type": "boolean"}, "value": {"description": "Description of the choice.", "type": "string"}}, "type": "object"}, "type": "array"}, "numberValue": {"description": "The value of a number field. Only present for number fields.", "format": "float", "type": "number"}, "photoValue": {"description": "The value of a photo or document scanning field. Only present for photo or document scanning fields.", "items": {"properties": {"url": {"description": "Url of the photo.", "type": "string"}, "uuid": {"description": "<PERSON><PERSON> of the photo.", "type": "string"}}, "type": "object"}, "type": "array"}, "signatureValue": {"description": "The value of a signature field. Only present for signature fields.", "properties": {"name": {"description": "Name of the signee for a signature field", "type": "string"}, "signedAtMs": {"description": "Date time value in milliseconds of the time a signature was captured.", "type": "number"}, "url": {"description": "Url of a signature field's PNG signature image.", "type": "string"}, "uuid": {"description": "Uuid of a signature field", "type": "string"}}, "type": "object"}, "stringValue": {"description": "The value of a string field. Only present for string fields.", "type": "string"}, "value": {"description": "The value of a field. Type varies by field type.", "type": "object"}, "valueType": {"description": "The value type of a field.", "type": "string"}}, "type": "object"}, "type": "array"}, "V1DriverDailyLogResponse": {"properties": {"days": {"items": {"$ref": "#/components/schemas/V1DriverDailyLogResponse_days"}, "type": "array"}}, "type": "object"}, "V1DriverDailyLogResponse_days": {"properties": {"activeHours": {"description": "Hours spent on duty or driving, rounded to two decimal places.", "example": 5.4, "format": "double", "type": "number"}, "activeMs": {"description": "Milliseconds spent on duty or driving.", "example": 691200, "format": "int64", "type": "integer"}, "certified": {"description": "Whether this HOS day chart was certified by the driver.", "type": "boolean"}, "certifiedAtMs": {"description": "Unix epoch time (in ms) of time when this chart was certified. If this chart is uncertified, 0.", "format": "int64", "type": "number"}, "distanceMiles": {"description": "Distance driven in miles, rounded to two decimal places.", "example": 123.24, "format": "double", "type": "number"}, "endMs": {"description": "End of the HOS day, specified in milliseconds UNIX time.", "example": 1473836400000, "type": "integer"}, "shippingDocIds": {"description": "List of customer shipping document IDs associated with the driver for the day.", "example": ["14334", "Shipping Document ID 1"], "items": {"type": "string"}, "type": "array"}, "startMs": {"description": "Start of the HOS day, specified in milliseconds UNIX time.", "example": 1473750000000, "type": "integer"}, "trailerIds": {"description": "List of trailer ID's associated with the driver for the day.", "example": ["10293", "Trailer ID 1"], "items": {"type": "string"}, "type": "array"}, "vehicleIds": {"description": "List of vehicle ID's associated with the driver for the day.", "example": [192319, 12958], "properties": {}, "type": "object"}}, "type": "object"}, "V1DriverSafetyScoreResponse": {"description": "Safety score details for a driver", "properties": {"crashCount": {"description": "Crash event count", "example": 0, "type": "integer"}, "driverId": {"description": "Driver ID", "example": 1234, "type": "integer"}, "harshAccelCount": {"description": "Harsh acceleration event count", "example": 1, "type": "integer"}, "harshBrakingCount": {"description": "Harsh braking event count", "example": 2, "type": "integer"}, "harshEvents": {"items": {"$ref": "#/components/schemas/V1SafetyReportHarshEvent"}, "type": "array"}, "harshTurningCount": {"description": "Harsh turning event count", "example": 0, "type": "integer"}, "safetyScore": {"description": "The driver’s Safety Score for the requested period. Note that if the driver has zero drive time in this period, the Safety Score will be returned as 100.", "example": 97, "type": "integer"}, "safetyScoreRank": {"description": "Safety Score Rank", "example": "26", "type": "string"}, "timeOverSpeedLimitMs": {"description": "Amount of time driven over the speed limit in milliseconds", "example": 3769, "type": "integer"}, "totalDistanceDrivenMeters": {"description": "Total distance driven in meters", "example": 291836, "type": "integer"}, "totalHarshEventCount": {"description": "Total harsh event count", "example": 3, "type": "integer"}, "totalTimeDrivenMs": {"description": "Amount of time driven in milliseconds", "example": 19708293, "type": "integer"}}, "type": "object"}, "V1DvirBase_nextDriverSignature": {"description": "The next driver signature for the DVIR.", "properties": {"driverId": {"description": "ID of the driver who signed the DVIR", "example": 2581, "format": "int64", "type": "integer"}, "email": {"description": "Email of the  driver who signed the next DVIR on this vehicle.", "example": "<EMAIL>", "type": "string"}, "name": {"description": "The name of the driver who signed the next DVIR on this vehicle.", "example": "<PERSON>", "type": "string"}, "signedAt": {"description": "The time in millis when the next driver signed the DVIR on this vehicle.", "example": 12535500000, "format": "int64", "type": "integer"}, "type": {"description": "Type corresponds to driver.", "example": "driver", "type": "string"}, "username": {"description": "<PERSON><PERSON><PERSON> of the  driver who signed the next DVIR on this vehicle.", "example": "jsmith", "type": "string"}}, "type": "object"}, "V1HosAuthenticationLogsResponse": {"properties": {"authenticationLogs": {"items": {"$ref": "#/components/schemas/V1HosAuthenticationLogsResponse_authenticationLogs"}, "type": "array"}}, "type": "object"}, "V1HosAuthenticationLogsResponse_authenticationLogs": {"properties": {"actionType": {"description": "The log type - one of 'signin' or 'signout'", "example": "signin", "type": "string"}, "address": {"description": "DEPRECATED: THIS FIELD MAY NOT BE POPULATED", "example": "THIS FIELD MAY NOT BE POPULATED", "type": "string"}, "addressName": {"description": "DEPRECATED: THIS FIELD MAY NOT BE POPULATED", "example": "THIS FIELD MAY NOT BE POPULATED", "type": "string"}, "city": {"description": "DEPRECATED: THIS FIELD MAY NOT BE POPULATED", "example": "THIS FIELD MAY NOT BE POPULATED", "type": "string"}, "happenedAtMs": {"description": "The time at which the event was recorded in UNIX milliseconds.", "example": 1462881998034, "format": "int64", "type": "integer"}, "state": {"description": "DEPRECATED: THIS FIELD MAY NOT BE POPULATED", "example": "THIS FIELD MAY NOT BE POPULATED", "type": "string"}}, "type": "object"}, "V1HosLogsResponse": {"properties": {"logs": {"items": {"$ref": "#/components/schemas/V1HosLogsResponse_logs"}, "type": "array"}}, "type": "object"}, "V1HosLogsResponse_logs": {"properties": {"codriverIds": {"items": {"description": "Ids of codrivers during any of the logged trips", "example": 445, "type": "number"}, "type": "array"}, "driverId": {"description": "ID of the driver.", "example": 444, "format": "int64", "type": "integer"}, "groupId": {"description": "Deprecated.", "example": 101, "format": "int64", "type": "integer"}, "hosStatusType": {"description": "The Hours of Service status type. One of `OFF_DUTY`, `SLEEPER_BED`, `DRIVING`, `ON_DUTY`, `YARD_MOVE`, `PERSO<PERSON>L_CONVEYANCE`.", "example": "OFF_DUTY", "type": "string"}, "locCity": {"description": "City in which the log was recorded.", "example": "Ahwatukee", "type": "string"}, "locLat": {"description": "Latitude at which the log was recorded.", "example": 23.413702, "format": "float", "type": "number"}, "locLng": {"description": "Longitude at which the log was recorded.", "example": -98.50289, "format": "float", "type": "number"}, "locName": {"description": "Name of location at which the log was recorded.", "example": "McLean Site A", "type": "string"}, "locState": {"description": "State in which the log was recorded.", "example": "Arizona", "type": "string"}, "logStartMs": {"description": "The time at which the log/HOS status started in UNIX milliseconds.", "example": 1462881998034, "format": "int64", "type": "integer"}, "remark": {"description": "Remark associated with the log entry.", "example": "Lunch Break", "type": "string"}, "vehicleId": {"description": "ID of the vehicle.", "example": 112, "format": "int64", "type": "integer"}}, "type": "object"}, "VehicleStaticAssignedDriver": {"$ref": "#/components/schemas/driverTinyResponse"}, "VehicleStaticAssignedDriverId": {"description": "ID for the static assigned driver of the vehicle.", "example": "123", "type": "string"}, "VehicleStatsFaultCodesValue_j1939_vendorSpecificFields": {"description": "Vendor specific data for J1939 vehicles.", "properties": {"dtcDescription": {"description": "The DTC description, if available.", "example": "false", "type": "string"}, "repairInstructionsUrl": {"description": "A link to vendor repair instructions, if available.", "example": "false", "type": "string"}}, "type": "object"}, "VehicleStatsFaultCodesVendorSpecificFields": {"description": "Vendor specific data for J1939 vehicles.", "properties": {"dtcDescription": {"description": "The DTC description, if available.", "example": "false", "type": "string"}, "repairInstructionsUrl": {"description": "A link to vendor repair instructions, if available.", "example": "false", "type": "string"}}, "type": "object"}, "VehicleStatsResponse_seatbeltDriver": {"description": "Seatbelt Driver Status as read from the vehicle. `Buckled` or `Unbuckled`.", "properties": {"time": {"description": "UTC timestamp in RFC 3339 format. Example: `2020-01-27T07:06:25Z`.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "Seatbelt Driver Status as read from the vehicle. `Buckled` or `Unbuckled`.", "enum": ["Buckled", "Unbuckled"], "example": "Buckled", "type": "string"}}, "required": ["time", "value"], "type": "object"}, "VehicleStatsSeatbeltDriver": {"description": "Seatbelt Driver Status as read from the vehicle. `Buckled` or `Unbuckled`.", "properties": {"time": {"$ref": "#/components/schemas/time"}, "value": {"description": "Seatbelt Driver Status as read from the vehicle. `Buckled` or `Unbuckled`.", "enum": ["Buckled", "Unbuckled"], "example": "Buckled", "type": "string"}}, "required": ["time", "value"], "type": "object"}, "VehiclesDriverAssignmentsGetVehiclesDriverAssignmentsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "VehiclesDriverAssignmentsGetVehiclesDriverAssignmentsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "VehiclesDriverAssignmentsGetVehiclesDriverAssignmentsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "VehiclesDriverAssignmentsGetVehiclesDriverAssignmentsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "VehiclesDriverAssignmentsGetVehiclesDriverAssignmentsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "VehiclesDriverAssignmentsGetVehiclesDriverAssignmentsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "VehiclesDriverAssignmentsGetVehiclesDriverAssignmentsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "VehiclesDriverAssignmentsGetVehiclesDriverAssignmentsResponseBody": {"properties": {"data": {"description": "List of vehicles and their driver assignments.", "items": {"$ref": "#/components/schemas/VehiclesDriverAssignmentsObjectResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "VehiclesDriverAssignmentsGetVehiclesDriverAssignmentsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "VehiclesDriverAssignmentsGetVehiclesDriverAssignmentsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "VehiclesDriverAssignmentsGetVehiclesDriverAssignmentsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "VehiclesDriverAssignmentsObjectResponseBody": {"properties": {"driverAssignments": {"description": "List of driver assignment objects.", "items": {"$ref": "#/components/schemas/DriverAssignmentObjectResponseBody"}, "type": "array"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "ID of the vehicle.", "example": "494123", "type": "string"}, "name": {"description": "Name of the vehicle.", "example": "Bus-123", "type": "string"}}, "required": ["driverAssignments", "id"], "type": "object"}, "alertObjectDriverResponseBody": {"description": "A driver associated with the alert", "properties": {"attributes": {"description": "List of attributes associated with the entity", "items": {"$ref": "#/components/schemas/GoaAttributeTinyResponseBody"}, "type": "array"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "The ID of the driver", "example": "45646", "type": "string"}, "name": {"description": "The name of the driver.", "example": "Driver <PERSON>", "type": "string"}, "tags": {"description": "The list of [tags](https://kb.samsara.com/hc/en-us/articles/360026674631-Using-Tags-and-Tag-Nesting) associated with the driver.", "items": {"$ref": "#/components/schemas/GoaTagTinyResponseResponseBody"}, "type": "array"}}, "required": ["id"], "type": "object"}, "carrierProposedAssignmentDriverAllOf2ExternalIds": {"additionalProperties": {"type": "string"}, "description": "The [external IDs](https://developers.samsara.com/docs/external-ids) for the given object.", "example": {"maintenanceId": "250020", "payrollId": "ABFS18600"}, "type": "object", "x-go-gen-location": "models"}, "conditionalFieldSectionObjectResponseBody": {"properties": {"conditionalFieldFirstIndex": {"description": "The index of the first conditional field associated with the triggeringFieldValue in the fieldTypes list.", "example": 1442355805701948200, "format": "int64", "type": "integer"}, "conditionalFieldLastIndex": {"description": "The index of the last conditional field associated with the triggeringFieldValue in the fieldTypes list.", "example": 8587732987390647000, "format": "int64", "type": "integer"}, "triggeringFieldIndex": {"description": "The index of the multiple choice field in the fieldTypes list that triggers one or more conditional fields.", "example": 7703795094721638000, "format": "int64", "type": "integer"}, "triggeringFieldValue": {"description": "The multiple choice option value that triggers the conditional fields.", "example": "Optiona 1", "type": "string"}}, "type": "object"}, "driverTinyResponse": {"description": "A minified driver object.", "properties": {"id": {"description": "ID of the driver.", "example": "88668", "type": "string"}, "name": {"description": "Name of the driver.", "example": "<PERSON>", "type": "string"}}, "type": "object"}, "fieldObjectPostRequestBody": {"properties": {"label": {"description": "The name of the field.", "example": "Load weight", "type": "string"}, "type": {"description": "The type of field.  Valid values: `photo`, `string`, `number`, `multipleChoice`, `signature`, `dateTime`, `scannedDocument`, `barcode`", "enum": ["photo", "string", "number", "multipleChoice", "signature", "dateTime", "scannedDocument", "barcode"], "example": "photo", "type": "string"}, "value": {"$ref": "#/components/schemas/fieldObjectValueRequestBody"}}, "required": ["label", "type"], "type": "object"}, "fieldObjectResponseBody": {"properties": {"label": {"description": "The name of the field.", "example": "Load weight", "type": "string"}, "type": {"description": "The type of field.  Valid values: `photo`, `string`, `number`, `multipleChoice`, `signature`, `dateTime`, `scannedDocument`, `barcode`", "enum": ["photo", "string", "number", "multipleChoice", "signature", "dateTime", "scannedDocument", "barcode"], "example": "photo", "type": "string"}, "value": {"$ref": "#/components/schemas/fieldObjectValueResponseBody"}}, "required": ["label", "type", "value"], "type": "object"}, "fieldObjectValueRequestBody": {"description": "The value of the document field. The shape of value depends on the type.", "properties": {"barcodeValue": {"description": "The value of a barcode scanning field. Only present for barcode scanning fields.", "items": {"$ref": "#/components/schemas/barcodeValueObjectRequestBody"}, "type": "array"}, "dateTimeValue": {"$ref": "#/components/schemas/dateTimeValueObjectRequestBody"}, "multipleChoiceValue": {"description": "The value of a multiple choice field. Only present for multiple choice fields.", "items": {"$ref": "#/components/schemas/multipleChoiceValueObjectRequestBody"}, "type": "array"}, "numberValue": {"description": "The value of a number field. Only present for number fields.", "example": 123.456, "format": "double", "type": "number"}, "photoValue": {"description": "The value of a photo field. Only present for photo fields.", "items": {"$ref": "#/components/schemas/photoValueObjectRequestBody"}, "type": "array"}, "scannedDocumentValue": {"description": "The value of a scanned document field. Only present for scanned document fields.", "items": {"$ref": "#/components/schemas/scannedDocumentValueObjectRequestBody"}, "type": "array"}, "signatureValue": {"$ref": "#/components/schemas/signatureValueObjectRequestBody"}, "stringValue": {"description": "The value of a string field. Only present for string fields.", "example": "Red Truck", "type": "string"}}, "type": "object"}, "fieldObjectValueResponseBody": {"description": "The value of the document field. The shape of value depends on the type.", "properties": {"barcodeValue": {"description": "The value of a barcode scanning field. Only present for barcode scanning fields.", "items": {"$ref": "#/components/schemas/barcodeValueObjectResponseBody"}, "type": "array"}, "dateTimeValue": {"$ref": "#/components/schemas/dateTimeValueObjectResponseBody"}, "multipleChoiceValue": {"description": "The value of a multiple choice field. Only present for multiple choice fields.", "items": {"$ref": "#/components/schemas/multipleChoiceValueObjectResponseBody"}, "type": "array"}, "numberValue": {"description": "The value of a number field. Only present for number fields.", "example": 123.456, "format": "double", "type": "number"}, "photoValue": {"description": "The value of a photo field. Only present for photo fields.", "items": {"$ref": "#/components/schemas/photoValueObjectResponseBody"}, "type": "array"}, "scannedDocumentValue": {"description": "The value of a scanned document field. Only present for scanned document fields.", "items": {"$ref": "#/components/schemas/scannedDocumentValueObjectResponseBody"}, "type": "array"}, "signatureValue": {"$ref": "#/components/schemas/signatureValueObjectResponseBody"}, "stringValue": {"description": "The value of a string field. Only present for string fields.", "example": "Red Truck", "type": "string"}}, "type": "object"}, "fieldTypesObjectResponseBody": {"properties": {"fieldType": {"description": "The type of value this field can have.  Valid values: `photo`, `string`, `number`, `multipleChoice`, `signature`, `dateTime`, `scannedDocument`, `barcode`", "enum": ["photo", "string", "number", "multipleChoice", "signature", "dateTime", "scannedDocument", "barcode"], "example": "photo", "type": "string"}, "label": {"description": "The name of the field type.", "example": "Receipts", "type": "string"}, "multipleChoiceFieldTypeMetaData": {"description": "A list of the multiple choice field option labels.", "items": {"$ref": "#/components/schemas/multipleChoiceFieldTypeMetaDataObjectResponseBody"}, "type": "array"}, "numberFieldTypeMetaData": {"$ref": "#/components/schemas/numberFieldTypeMetaDataObjectResponseBody"}, "requiredField": {"description": "The indicator that states if the field is required.", "example": true, "type": "boolean"}, "signatureFieldTypeMetaData": {"$ref": "#/components/schemas/signatureFieldTypeMetaDataObjectResponseBody"}}, "required": ["fieldType", "label", "requiredField"], "type": "object"}, "multipleChoiceFieldTypeMetaDataObjectResponseBody": {"properties": {"label": {"description": "The option choice label.", "example": "Fuel Receipt", "type": "string"}}, "type": "object"}, "numberFieldTypeMetaDataObjectResponseBody": {"description": "The number field metadata.", "properties": {"numberOfDecimalPlaces": {"description": "The number of decimal places allowed for the field.", "example": 3167430815212634600, "format": "int64", "type": "integer"}}, "type": "object"}, "signatureFieldTypeMetaDataObjectResponseBody": {"description": "The signature field metadata.", "properties": {"legalText": {"description": "The signature field legal text.", "example": "Verified by signee.", "type": "string"}}, "type": "object"}, "singleDriverEfficiencyByDriverDataObjectResponseBody": {"description": "singleDriverEfficiencyByDriverDataObject", "properties": {"difficultyScore": {"$ref": "#/components/schemas/DriverEfficiencyDifficultyScoreDataObjectResponseBody"}, "driverId": {"description": "ID of the driver.", "example": "driver_001", "type": "string"}, "percentageData": {"$ref": "#/components/schemas/DriverEfficiencyPercentageDataObjectResponseBody"}, "rawData": {"$ref": "#/components/schemas/DriverEfficiencyRawDataObjectResponseBody"}, "scoreData": {"$ref": "#/components/schemas/DriverEfficiencyDataObjectResponseBody"}}, "required": ["driverId"], "type": "object"}, "singleDriverEfficiencyByVehicleDataObjectResponseBody": {"description": "singleDriverEfficiencyByVehicleDataObject", "properties": {"difficultyScore": {"$ref": "#/components/schemas/DriverEfficiencyDifficultyScoreDataObjectResponseBody"}, "percentageData": {"$ref": "#/components/schemas/DriverEfficiencyPercentageDataObjectResponseBody"}, "rawData": {"$ref": "#/components/schemas/DriverEfficiencyRawDataObjectResponseBody"}, "scoreData": {"$ref": "#/components/schemas/DriverEfficiencyDataObjectResponseBody"}, "vehicleId": {"description": "ID of the vehicle.", "example": "vehicle_001", "type": "string"}}, "required": ["vehicleId"], "type": "object"}}, "securitySchemes": {"AccessTokenHeader": {"type": "http", "scheme": "bearer"}}}, "paths": {"/beta/fleet/drivers/efficiency": {"get": {"description": "Get all driver and associated vehicle efficiency data. \n\n This is a legacy endpoint, consider using this endpoint [/driver-efficiency/drivers](https://developers.samsara.com/reference/getdriverefficiencybydrivers) instead. The endpoint will continue to function as documented. \n\n <b>Rate limit:</b> 50 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>). \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Fuel & Energy** under the Fuel & Energy category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "getDriverEfficiency", "parameters": [{"description": "If value is `deactivated`, only drivers that are deactivated will appear in the response. This parameter will default to `active` if not provided (fetching only active drivers).", "in": "query", "name": "driverActivationStatus", "schema": {"enum": ["active", "deactivated"], "type": "string"}}, {"description": "A filter on the data based on this comma-separated list of driver IDs. Cannot be used with tag filtering or driver status. Example: `driverIds=1234,5678`", "explode": false, "in": "query", "name": "driverIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "Filters summary to drivers based on this comma-separated list of tag IDs. Data from all the drivers' respective vehicles will be included in the summary, regardless of which tag the vehicle is associated with. Should not be provided in addition to `driverIds`. Example: driverTagIds=1234,5678", "explode": false, "in": "query", "name": "driverTagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "Filters like `driverTagIds` but includes descendants of all the given parent tags. Should not be provided in addition to `driverIds`. Example: `driverParentTagIds=1234,5678`", "explode": false, "in": "query", "name": "driverParentTagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A start time in RFC 3339 format. The results will be truncated to the hour mark for the provided time. For example, if `startTime` is 2020-03-17T12:06:19Z then the results will include data starting from 2020-03-17T12:00:00Z. The provided start time cannot be in the future. Start time can be at most 31 days before the end time. If the start time is within the last hour, the results will be empty. Default: 24 hours prior to endTime.\n\nNote that the most recent 72 hours of data may still be processing and is subject to change and latency, so it is not recommended to request data for the most recent 72 hours.", "in": "query", "name": "startTime", "schema": {"format": "date-time", "type": "string"}}, {"description": "An end time in RFC 3339 format. The results will be truncated to the hour mark for the provided time. For example, if `endTime` is 2020-03-17T12:06:19Z then the results will include data up until 2020-03-17T12:00:00Z. The provided end time cannot be in the future. End time can be at most 31 days after the start time. Default: The current time truncated to the hour mark.\n\nNote that the most recent 72 hours of data may still be processing and is subject to change and latency, so it is not recommended to request data for the most recent 72 hours", "in": "query", "name": "endTime", "schema": {"format": "date-time", "type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverEfficienciesResponse"}}}, "description": "List of all driver and associated vehicle efficiency data"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "[beta] List driver efficiency", "tags": ["Beta APIs"]}}, "/beta/fleet/hos/drivers/eld-events": {"get": {"description": "Get all HOS ELD events in a time range, grouped by driver. Attributes will be populated depending on which ELD Event Type is being returned.\n\n <b>Rate limit:</b> 5 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Read ELD Compliance Settings (US)** under the Compliance category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "getHosEldEvents", "parameters": [{"description": " A start time in RFC 3339 format. Defaults to now if not provided. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "startTime", "required": true, "schema": {"type": "string"}}, {"description": " An end time in RFC 3339 format. Defaults to now if not provided. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "endTime", "required": true, "schema": {"type": "string"}}, {"description": " A filter on the data based on this comma-separated list of driver IDs and externalIds. Example: `driverIds=1234,5678,payroll:4841`", "explode": false, "in": "query", "name": "driverIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": " A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "in": "query", "name": "tagIds", "schema": {"type": "string"}}, {"description": " A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "in": "query", "name": "parentTagIds", "schema": {"type": "string"}}, {"description": "If value is `deactivated`, only drivers that are deactivated will appear in the response. This parameter will default to `active` if not provided (fetching only active drivers).  Valid values: `active`, `deactivated`", "in": "query", "name": "driverActivationStatus", "schema": {"default": "active", "enum": ["active", "deactivated"], "type": "string"}}, {"description": " If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "The limit for how many objects will be in the response. Default and max for this value is 25 objects.", "in": "query", "name": "limit", "schema": {"default": 25, "maximum": 25, "minimum": 1, "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosEldEventsGetHosEldEventsResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosEldEventsGetHosEldEventsUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosEldEventsGetHosEldEventsNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosEldEventsGetHosEldEventsMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosEldEventsGetHosEldEventsTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosEldEventsGetHosEldEventsInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosEldEventsGetHosEldEventsNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosEldEventsGetHosEldEventsBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosEldEventsGetHosEldEventsServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosEldEventsGetHosEldEventsGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosEldEventsGetHosEldEventsBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "[beta] Get driver HOS ELD events", "tags": ["Beta APIs"]}}, "/driver-efficiency/drivers": {"get": {"description": "This endpoint will return driver efficiency data that has been collected for your organization and grouped by drivers based on the time parameters passed in. Results are paginated. \n\n**Note:** The data from this endpoint comes from the Driver Efficiency (Eco-Driving) Report. The existing [/fleet/drivers/efficiency](https://developers.samsara.com/reference/getdriverefficiency) endpoint has now been moved to Legacy.\n\n <b>Rate limit:</b> 10 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Read Driver Efficiency** under the Fuel & Energy category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "getDriverEfficiencyByDrivers", "parameters": [{"description": "A start time in RFC 3339 format. Must be in multiple of hours and at least 1 day before endTime. Timezones are supported. Note that the most recent 72 hours of data may still be processing and is subject to change and latency, so it is not recommended to request data for the most recent 72 hours. (Examples: 2019-06-11T19:00:00Z, 2015-09-12T14:00:00-04:00).", "in": "query", "name": "startTime", "required": true, "schema": {"type": "string"}}, {"description": "An end time in RFC 3339 format. Must be in multiple of hours and no later than 3 hours before the current time. Timezones are supported. Note that the most recent 72 hours of data may still be processing and is subject to change and latency, so it is not recommended to request data for the most recent 72 hours. (Examples: 2019-06-13T19:00:00Z, 2015-09-15T14:00:00-04:00).", "in": "query", "name": "endTime", "required": true, "schema": {"type": "string"}}, {"description": " A filter on the data based on this comma-separated list of driver IDs and externalIds. Example: `driverIds=1234,5678,payroll:4841`", "explode": false, "in": "query", "name": "driverIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A comma-separated list of data formats you want to fetch. Valid values: `score`, `raw` and `percentage`. The default data format is `score`. Example: `dataFormats=raw,score`", "explode": false, "in": "query", "name": "dataFormats", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": " A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "in": "query", "name": "tagIds", "schema": {"type": "string"}}, {"description": " A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "in": "query", "name": "parentTagIds", "schema": {"type": "string"}}, {"description": " If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverEfficiencyGetDriverEfficiencyByDriversResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverEfficiencyGetDriverEfficiencyByDriversUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverEfficiencyGetDriverEfficiencyByDriversNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverEfficiencyGetDriverEfficiencyByDriversMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverEfficiencyGetDriverEfficiencyByDriversTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverEfficiencyGetDriverEfficiencyByDriversInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverEfficiencyGetDriverEfficiencyByDriversNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverEfficiencyGetDriverEfficiencyByDriversBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverEfficiencyGetDriverEfficiencyByDriversServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverEfficiencyGetDriverEfficiencyByDriversGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverEfficiencyGetDriverEfficiencyByDriversBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "[beta] Get Driver efficiency data grouped by drivers.", "tags": ["Beta APIs"]}}, "/drivers/qr-codes": {"delete": {"description": "Revoke requested driver's currently active QR code.\n\n <b>Rate limit:</b> 100 requests/min (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Write Drivers** under the Drivers category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "deleteDriverQrCode", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesDeleteDriverQrCodeRequestBody"}}}, "required": true}, "responses": {"204": {"content": {}, "description": "No Content response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesDeleteDriverQrCodeUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesDeleteDriverQrCodeNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesDeleteDriverQrCodeMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesDeleteDriverQrCodeTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesDeleteDriverQrCodeInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesDeleteDriverQrCodeNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesDeleteDriverQrCodeBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesDeleteDriverQrCodeServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesDeleteDriverQrCodeGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesDeleteDriverQrCodeBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "Revoke driver's QR code", "tags": ["Driver QR Codes"], "x-codegen-request-body-name": "DeleteDriverQrCodeRequestBody"}, "get": {"description": "Get details for requested driver(s) QR code, used for driver trip assignment.\n\n <b>Rate limit:</b> 5 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Read Drivers** under the Drivers category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "getDriversQrCodes", "parameters": [{"description": "String of comma separated driver IDs. List of driver - QR codes for specified driver(s) will be returned.", "explode": false, "in": "query", "name": "driverIds", "required": true, "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesGetDriversQrCodesResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesGetDriversQrCodesUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesGetDriversQrCodesNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesGetDriversQrCodesMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesGetDriversQrCodesTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesGetDriversQrCodesInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesGetDriversQrCodesNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesGetDriversQrCodesBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesGetDriversQrCodesServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesGetDriversQrCodesGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesGetDriversQrCodesBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "Get driver QR codes", "tags": ["Driver QR Codes"]}, "post": {"description": "Assign a new QR code for the requested driver. Return error if an active QR code already exists.\n\n <b>Rate limit:</b> 100 requests/min (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Write Drivers** under the Drivers category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "createDriverQrCode", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesCreateDriverQrCodeRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesCreateDriverQrCodeResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesCreateDriverQrCodeUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesCreateDriverQrCodeNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesCreateDriverQrCodeMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesCreateDriverQrCodeTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesCreateDriverQrCodeInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesCreateDriverQrCodeNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesCreateDriverQrCodeBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesCreateDriverQrCodeServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesCreateDriverQrCodeGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverQrCodesCreateDriverQrCodeBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "Create new QR code for driver", "tags": ["Driver QR Codes"], "x-codegen-request-body-name": "CreateDriverQrCodeRequestBody"}}, "/fleet/drivers": {"get": {"description": "Get all drivers in organization. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Drivers** under the Drivers category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "listDrivers", "parameters": [{"description": "If value is `deactivated`, only drivers that are deactivated will appear in the response. This parameter will default to `active` if not provided (fetching only active drivers).", "in": "query", "name": "driverActivationStatus", "schema": {"enum": ["active", "deactivated"], "type": "string"}}, {"description": "The limit for how many objects will be in the response. Default and max for this value is 512 objects.", "in": "query", "name": "limit", "schema": {"format": "int64", "maximum": 512, "minimum": 1, "type": "integer"}}, {"description": "If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "explode": false, "in": "query", "name": "parentTagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "explode": false, "in": "query", "name": "tagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of attribute value IDs. Only entities associated with ALL of the referenced values will be returned (i.e. the intersection of the sets of entities with each value). Example: `attributeValueIds=076efac2-83b5-47aa-ba36-18428436dcac,6707b3f0-23b9-4fe3-b7be-11be34aea544`", "explode": false, "in": "query", "name": "attributeValueIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on data to have an updated at time after or equal to this specified time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "updatedAfterTime", "schema": {"type": "string"}}, {"description": "A filter on data to have a created at time after or equal to this specified time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "createdAfterTime", "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListDriversResponse"}}}, "description": "List of all driver objects."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "List all drivers", "tags": ["Drivers"]}, "post": {"description": "Add a driver to the organization. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Drivers** under the Drivers category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "createDriver", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDriverRequest"}}}, "description": "The driver to create.", "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverResponse"}}}, "description": "Newly created driver object, with Samsara-generated ID."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Create a driver", "tags": ["Drivers"], "x-codegen-request-body-name": "driver"}}, "/fleet/drivers/remote-sign-out": {"post": {"description": "Sign out a driver from the Samsara Driver App\n\nTo access this endpoint, your organization must have the Samsara Platform Premier license.\n\nNote: Sign out requests made while a logged-in driver does not have internet connection will not log the driver out. A success response will still be provided and the driver will be logged out once they have internet connection.\n\n <b>Rate limit:</b> 100 requests/min (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Write Driver Remote Signout** under the Closed Beta category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "postDriverRemoteSignout", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverRemoteSignoutPostDriverRemoteSignoutRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverRemoteSignoutPostDriverRemoteSignoutResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverRemoteSignoutPostDriverRemoteSignoutUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverRemoteSignoutPostDriverRemoteSignoutNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverRemoteSignoutPostDriverRemoteSignoutMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverRemoteSignoutPostDriverRemoteSignoutTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverRemoteSignoutPostDriverRemoteSignoutInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverRemoteSignoutPostDriverRemoteSignoutNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverRemoteSignoutPostDriverRemoteSignoutBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverRemoteSignoutPostDriverRemoteSignoutServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverRemoteSignoutPostDriverRemoteSignoutGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverRemoteSignoutPostDriverRemoteSignoutBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "[beta] Sign out a driver", "tags": ["Beta APIs"], "x-codegen-request-body-name": "PostDriverRemoteSignoutRequestBody"}}, "/fleet/drivers/tachograph-activity/history": {"get": {"description": "Returns all known tachograph activity for all specified drivers in the time range. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Tachograph (EU)** under the Compliance category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "getDriverTachographActivity", "parameters": [{"description": "If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "A start time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "startTime", "required": true, "schema": {"type": "string"}}, {"description": "An end time in RFC 3339 format. It can't be more than 30 days past startTime. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "endTime", "required": true, "schema": {"type": "string"}}, {"description": "A filter on the data based on this comma-separated list of driver IDs. Example: `driverIds=1234,5678`", "explode": false, "in": "query", "name": "driverIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "explode": false, "in": "query", "name": "parentTagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "explode": false, "in": "query", "name": "tagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverTachographActivityResponse"}}}, "description": "List of all driver tachograph activities in a specified time range."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Get driver tachograph activity", "tags": ["Tachograph (EU Only)"]}}, "/fleet/drivers/tachograph-files/history": {"get": {"description": "Returns all known tachograph files for all specified drivers in the time range. \n\n <b>Rate limit:</b> 50 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>). \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Tachograph (EU)** under the Compliance category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "getDriverTachographFiles", "parameters": [{"description": "If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "A start time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "startTime", "required": true, "schema": {"type": "string"}}, {"description": "An end time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "endTime", "required": true, "schema": {"type": "string"}}, {"description": "A filter on the data based on this comma-separated list of driver IDs. Example: `driverIds=1234,5678`", "explode": false, "in": "query", "name": "driverIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "explode": false, "in": "query", "name": "parentTagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "explode": false, "in": "query", "name": "tagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TachographDriverFilesResponse"}}}, "description": "List of all driver tachograph files in a specified time range."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Get tachograph driver files", "tags": ["Tachograph (EU Only)"]}}, "/fleet/drivers/vehicle-assignments": {"get": {"description": "**Note: This is a legacy endpoint, consider using [this endpoint](https://developers.samsara.com/reference/getdrivervehicleassignments) instead. The endpoint will continue to function as documented.** Get all vehicle assignments for the requested drivers in the requested time range. The only type of assignment supported right now are assignments created through the driver app.\n\n <b>Rate limit:</b> 25 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Read Assignments** under the Assignments category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "getDriversVehicleAssignments", "parameters": [{"description": " A filter on the data based on this comma-separated list of driver IDs and externalIds. Example: `driverIds=1234,5678,payroll:4841`", "explode": false, "in": "query", "name": "driverIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": " A start time in RFC 3339 format. Defaults to now if not provided. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00). The maximum allowed startTime-endTime range is 7 days.", "in": "query", "name": "startTime", "schema": {"type": "string"}}, {"description": " An end time in RFC 3339 format. Defaults to now if not provided. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00). The maximum allowed startTime-endTime range is 7 days.", "in": "query", "name": "endTime", "schema": {"type": "string"}}, {"description": " A filter on the data based on this comma-separated list of driver tag IDs. Example: `tagIds=1234,5678`", "in": "query", "name": "tagIds", "schema": {"type": "string"}}, {"description": " A filter on the data based on this comma-separated list of driver parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "in": "query", "name": "parentTagIds", "schema": {"type": "string"}}, {"description": "If value is `deactivated`, only drivers that are deactivated will appear in the response. This parameter will default to `active` if not provided (fetching only active drivers).  Valid values: `active`, `deactivated`", "in": "query", "name": "driverActivationStatus", "schema": {"default": "active", "enum": ["active", "deactivated"], "type": "string"}}, {"description": " If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriversVehicleAssignmentsGetDriversVehicleAssignmentsResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriversVehicleAssignmentsGetDriversVehicleAssignmentsUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriversVehicleAssignmentsGetDriversVehicleAssignmentsNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriversVehicleAssignmentsGetDriversVehicleAssignmentsMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriversVehicleAssignmentsGetDriversVehicleAssignmentsTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriversVehicleAssignmentsGetDriversVehicleAssignmentsInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriversVehicleAssignmentsGetDriversVehicleAssignmentsNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriversVehicleAssignmentsGetDriversVehicleAssignmentsBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriversVehicleAssignmentsGetDriversVehicleAssignmentsServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriversVehicleAssignmentsGetDriversVehicleAssignmentsGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriversVehicleAssignmentsGetDriversVehicleAssignmentsBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "[legacy] Get all vehicles assigned to a set of drivers", "tags": ["Legacy APIs"]}}, "/fleet/drivers/{id}": {"get": {"description": "Get information about a driver. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Drivers** under the Drivers category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "getDriver", "parameters": [{"description": "ID of the driver. This can either be the Samsara-specified ID, or an external ID. External IDs are customer specified key-value pairs created in the POST or PATCH requests of this resource. To specify an external ID as part of a path parameter, use the following format: `key:value`. For example, `payrollId:ABFS18600`", "in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverResponse"}}}, "description": "Returns the specified driver."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Retrieve a driver", "tags": ["Drivers"]}, "patch": {"description": "Update a specific driver's information. This can also be used to activate or de-activate a given driver by setting the driverActivationStatus field. If the driverActivationStatus field is 'deactivated' then the user can also specify the deactivatedAtTime. The deactivatedAtTime cannot be more than 6 months in the past and must not come before the dirver's latest active HOS log. It will be considered an error if deactivatedAtTime is provided with a driverActivationStatus of active. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Drivers** under the Drivers category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "updateDriver", "parameters": [{"description": "ID of the driver. This can either be the Samsara-specified ID, or an external ID. External IDs are customer specified key-value pairs created in the POST or PATCH requests of this resource. To specify an external ID as part of a path parameter, use the following format: `key:value`. For example, `payrollId:ABFS18600`", "in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDriverRequest"}}}, "description": "Updates to the driver properties.", "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DriverResponse"}}}, "description": "Updated driver object, with ID."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Update a driver", "tags": ["Drivers"], "x-codegen-request-body-name": "driver"}}, "/fleet/hos/clocks": {"get": {"description": "Get the current HOS status for all drivers. Note that this includes inactive as well as active drivers. The legacy version of this endpoint can be found at [samsara.com/api-legacy](https://www.samsara.com/api-legacy#operation/getFleetHosLogsSummary). \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read ELD Compliance Settings (US)** under the Compliance category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "getHosClocks", "parameters": [{"description": "A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "explode": false, "in": "query", "name": "tagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "explode": false, "in": "query", "name": "parentTagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of driver IDs. Example: `driverIds=1234,5678`", "explode": false, "in": "query", "name": "driverIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "The limit for how many objects will be in the response. Default and max for this value is 512 objects.", "in": "query", "name": "limit", "schema": {"format": "int64", "maximum": 512, "minimum": 1, "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosClocksResponse"}}}, "description": "List of current HOS clock information for the specified drivers."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Get HOS clocks", "tags": ["Hours of Service"]}}, "/fleet/hos/daily-logs": {"get": {"description": "Get summarized daily Hours of Service charts for the specified drivers.\n\nThe time range for a log is defined by the `driver`'s `eldDayStartHour`. This value is configurable per driver.\n\nThe `startDate` and `endDate` parameters indicate the date range you'd like to retrieve daily logs for. A daily log will be returned if its `startTime` is on any of the days within in this date range (inclusive of `startDate` and `endDate`).\n\n**Note:** If data is still being uploaded from the Samsara Driver App, it may not be completely reflected in the response from this endpoint. The best practice is to wait a couple of days before querying this endpoint to make sure that all data from the Samsara Driver App has been uploaded.\n\nIf you are using the legacy version of this endpoint and looking for its documentation, you can find it [here](https://www.samsara.com/api-legacy#operation/getFleetDriversHosDailyLogs).\n\n <b>Rate limit:</b> 5 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Read ELD Compliance Settings (US)** under the Compliance category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "getHosDailyLogs", "parameters": [{"description": " A filter on the data based on this comma-separated list of driver IDs and externalIds. Example: `driverIds=1234,5678,payroll:4841`", "explode": false, "in": "query", "name": "driverIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": " A start date in YYYY-MM-DD. This is a date only without an associated time. Example: `2019-06-13`. This is a required field", "in": "query", "name": "startDate", "schema": {"type": "string"}}, {"description": " An end date in YYYY-MM-DD. This is a date only without an associated time. Must be greater than or equal to the start date. Example: `2019-07-21`. This is a required field", "in": "query", "name": "endDate", "schema": {"type": "string"}}, {"description": " A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "in": "query", "name": "tagIds", "schema": {"type": "string"}}, {"description": " A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "in": "query", "name": "parentTagIds", "schema": {"type": "string"}}, {"description": "If value is `deactivated`, only drivers that are deactivated will appear in the response. This parameter will default to `active` if not provided (fetching only active drivers).  Valid values: `active`, `deactivated`", "in": "query", "name": "driverActivationStatus", "schema": {"default": "active", "enum": ["active", "deactivated"], "type": "string"}}, {"description": " If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "Expands the specified value(s) in the response object. Expansion populates additional fields in an object, if supported. Unsupported fields are ignored. To expand multiple fields, input a comma-separated list.\n\nValid value: `vehicle`  Valid values: `vehicle`", "in": "query", "name": "expand", "schema": {"enum": ["vehicle"], "type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsGetHosDailyLogsResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsGetHosDailyLogsUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsGetHosDailyLogsNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsGetHosDailyLogsMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsGetHosDailyLogsTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsGetHosDailyLogsInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsGetHosDailyLogsNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsGetHosDailyLogsBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsGetHosDailyLogsServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsGetHosDailyLogsGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsGetHosDailyLogsBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "Get all driver HOS daily logs", "tags": ["Hours of Service"]}}, "/fleet/hos/logs": {"get": {"description": "Returns HOS logs between a given `startTime` and `endTime`. The logs can be further filtered using tags or by providing a list of driver IDs (including external IDs). The legacy version of this endpoint can be found at [samsara.com/api-legacy](https://www.samsara.com/api-legacy#operation/getFleetHosLogs).\n\n**Note:** If data is still being uploaded from the Samsara Driver App, it may not be completely reflected in the response from this endpoint. The best practice is to wait a couple of days before querying this endpoint to make sure that all data from the Samsara Driver App has been uploaded. \n\n <b>Rate limit:</b> 5 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>). \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read ELD Compliance Settings (US)** under the Compliance category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "getHosLogs", "parameters": [{"description": "A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "explode": false, "in": "query", "name": "tagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "explode": false, "in": "query", "name": "parentTagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of driver IDs. Example: `driverIds=1234,5678`", "explode": false, "in": "query", "name": "driverIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A start time in RFC 3339 format. Defaults to now if not provided. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "startTime", "schema": {"type": "string"}}, {"description": "An end time in RFC 3339 format. Defaults to now if not provided. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "endTime", "schema": {"type": "string"}}, {"description": "If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosLogsResponse"}}}, "description": "List of the last known HOS log entries for the specified drivers."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Get HOS logs", "tags": ["Hours of Service"]}}, "/fleet/hos/violations": {"get": {"description": "Get active Hours of Service violations for the specified drivers.\n\nThe day object time range for a violation is defined by the `driver`'s `eldDayStartHour`. This value is configurable per driver.\n\nThe `startTime` and `endTime` parameters indicate the datetime range you'd like to retrieve violations for. A violation will be returned if its `violationStartTime` falls within this datetime range (inclusive of `startTime` and `endTime`) \n\n**Note:** The following are all the violation types with a short explanation about what each of them means: `californiaMealbreakMissed` (Missed California Meal Break), `cycleHoursOn` (Cycle Limit), `cycleOffHoursAfterOnDutyHours` (Cycle 2 Limit), `dailyDrivingHours` (Daily Driving Limit), `dailyOffDutyDeferralAddToDay2Consecutive` (Daily Off-Duty Deferral: Add To Day2 Consecutive), `dailyOffDutyDeferralNotPartMandatory` (Daily Off-Duty Deferral: Not Part Of Mandatory), `dailyOffDutyDeferralTwoDayDrivingLimit` (Daily Off-Duty Deferral: 2 Day Driving Limit), `dailyOffDutyDeferralTwoDayOffDuty` (Daily Off-Duty Deferral: 2 Day Off Duty), `dailyOffDutyNonResetHours` (Daily Off-Duty Time: Non-Reset), `dailyOffDutyTotalHours` (Daily Off-Duty Time), `dailyOnDutyHours` (Daily On-Duty Limit), `mandatory24HoursOffDuty` (24 Hours of Off Duty required), `restbreakMissed` (Missed Rest Break), `shiftDrivingHours` (Shift Driving Limit), `shiftHours` (Shift Duty Limit), `shiftOnDutyHours` (Shift On-Duty Limit), `unsubmittedLogs` (Missing Driver Certification)\n\n <b>Rate limit:</b> 5 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Read ELD Compliance Settings (US)** under the Compliance category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "getHosViolations", "parameters": [{"description": " A filter on the data based on this comma-separated list of driver IDs and externalIds. Example: `driverIds=1234,5678,payroll:4841`", "explode": false, "in": "query", "name": "driverIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": " A start time in RFC 3339 format. Defaults to now if not provided. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "startTime", "schema": {"type": "string"}}, {"description": " An end time in RFC 3339 format. Defaults to now if not provided. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "endTime", "schema": {"type": "string"}}, {"description": " A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "in": "query", "name": "tagIds", "schema": {"type": "string"}}, {"description": " A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "in": "query", "name": "parentTagIds", "schema": {"type": "string"}}, {"description": "A filter on violations data based on the violation type enum. Supported types: `NONE, californiaMealbreakMissed, cycleHoursOn, cycleOffHoursAfterOnDutyHours, dailyDrivingHours, dailyOffDutyDeferralAddToDay2Consecutive, dailyOffDutyDeferralNotPartMandatory, dailyOffDutyDeferralTwoDayDrivingLimit, dailyOffDutyDeferralTwoDayOffDuty, dailyOffDutyNonResetHours, dailyOffDutyTotalHours, dailyOnDutyHours, mandatory24HoursOffDuty, restbreakMissed, shiftDrivingHours, shiftHours, shiftOnDutyHours, unsubmittedLogs`", "explode": false, "in": "query", "name": "types", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": " If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosViolationsGetHosViolationsResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosViolationsGetHosViolationsUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosViolationsGetHosViolationsNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosViolationsGetHosViolationsMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosViolationsGetHosViolationsTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosViolationsGetHosViolationsInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosViolationsGetHosViolationsNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosViolationsGetHosViolationsBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosViolationsGetHosViolationsServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosViolationsGetHosViolationsGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosViolationsGetHosViolationsBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "Get all driver HOS violations", "tags": ["Hours of Service"]}}, "/fleet/reports/drivers/fuel-energy": {"get": {"description": "Get fuel and energy efficiency driver reports for the requested time range.\n\n <b>Rate limit:</b> 5 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Read Fuel & Energy** under the Fuel & Energy category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "getFuelEnergyDriverReports", "parameters": [{"description": "A start date in RFC 3339 format. This parameter ignores everything (i.e. hour, minutes, seconds, nanoseconds, etc.) besides the date and timezone. If no time zone is passed in, then the UTC time zone will be used. This parameter is inclusive, so data on the date specified will be considered. Note that the most recent 72 hours of data may still be processing and is subject to change and latency, so it is not recommended to request data for the most recent 72 hours. For example, 2022-07-13T14:20:50.52-07:00 is a time in Pacific Daylight Time.", "in": "query", "name": "startDate", "required": true, "schema": {"type": "string"}}, {"description": "An end date in RFC 3339 format. This parameter ignores everything (i.e. hour, minutes, seconds, nanoseconds, etc.) besides the date and timezone. If no time zone is passed in, then the UTC time zone will be used. This parameter is inclusive, so data on the date specified will be considered. Note that the most recent 72 hours of data may still be processing and is subject to change and latency, so it is not recommended to request data for the most recent 72 hours. For example, 2022-07-13T14:20:50.52-07:00 is a time in Pacific Daylight Time.", "in": "query", "name": "endDate", "required": true, "schema": {"type": "string"}}, {"description": " A filter on the data based on this comma-separated list of driver IDs and externalIds. Example: `driverIds=1234,5678,payroll:4841`", "explode": false, "in": "query", "name": "driverIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": " A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "in": "query", "name": "tagIds", "schema": {"type": "string"}}, {"description": " A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "in": "query", "name": "parentTagIds", "schema": {"type": "string"}}, {"description": " If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FuelEnergyGetFuelEnergyDriverReportsResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FuelEnergyGetFuelEnergyDriverReportsUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FuelEnergyGetFuelEnergyDriverReportsNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FuelEnergyGetFuelEnergyDriverReportsMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FuelEnergyGetFuelEnergyDriverReportsTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FuelEnergyGetFuelEnergyDriverReportsInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FuelEnergyGetFuelEnergyDriverReportsNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FuelEnergyGetFuelEnergyDriverReportsBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FuelEnergyGetFuelEnergyDriverReportsServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FuelEnergyGetFuelEnergyDriverReportsGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FuelEnergyGetFuelEnergyDriverReportsBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "Get fuel and energy efficiency driver reports.", "tags": ["Fuel and Energy"]}}, "/hos/daily-logs/log-meta-data": {"patch": {"description": "Update the shippingDocs field of an existing assignment.\n\n <b>Rate limit:</b> 5 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Write ELD Hours of Service (US)** under the Compliance category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "updateShippingDocs", "parameters": [{"description": "A start date in yyyy-mm-dd format. Required.", "in": "query", "name": "hosDate", "required": true, "schema": {"type": "string"}}, {"description": "ID of the driver for whom the duty status is being set.", "in": "query", "name": "driverID", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsUpdateShippingDocsRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsUpdateShippingDocsResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsUpdateShippingDocsUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsUpdateShippingDocsNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsUpdateShippingDocsMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsUpdateShippingDocsTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsUpdateShippingDocsInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsUpdateShippingDocsNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsUpdateShippingDocsBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsUpdateShippingDocsServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsUpdateShippingDocsGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HosDailyLogsUpdateShippingDocsBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "[beta] Update the shippingDocs field of an existing assignment.", "tags": ["Beta APIs"], "x-codegen-request-body-name": "UpdateShippingDocsRequestBody"}}, "/v1/fleet/drivers/{driverId}/safety/score": {"get": {"description": "<n class=\"warning\">\n<nh>\n<i class=\"fa fa-exclamation-circle\"></i>\nThis endpoint is still on our legacy API.\n</nh>\n</n>\n\nFetch the safety score for the driver.\n\n <b>Rate limit:</b> 5 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>). \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Safety Events & Scores** under the Safety & Cameras category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "V1getDriverSafetyScore", "parameters": [{"description": "ID of the driver. Must contain only digits 0-9.", "in": "path", "name": "driverId", "required": true, "schema": {"format": "int64", "type": "integer"}}, {"description": "Timestamp in milliseconds representing the start of the period to fetch, inclusive. Used in combination with endMs. Total duration (endMs - startMs) must be greater than or equal to 1 hour.", "in": "query", "name": "startMs", "required": true, "schema": {"format": "int64", "type": "integer"}}, {"description": "Timestamp in milliseconds representing the end of the period to fetch, inclusive. Used in combination with startMs. Total duration (endMs - startMs) must be greater than or equal to 1 hour.", "in": "query", "name": "endMs", "required": true, "schema": {"format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1DriverSafetyScoreResponse"}}}, "description": "Safety score details."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "Fetch driver safety score", "tags": ["Safety"]}}, "/v1/fleet/drivers/{driver_id}/hos/duty_status": {"post": {"description": "<n class=\"warning\">\n<nh>\n<i class=\"fa fa-exclamation-circle\"></i>\nThis endpoint is still on our legacy API.\n</nh>\n</n>\n\nSet an individual driver’s current duty status to 'On Duty' or 'Off Duty'.\n\n To ensure compliance with the ELD Mandate, only  authenticated drivers can make direct duty status changes on their own logbook. Any system external to the Samsara Driver App using this endpoint to trigger duty status changes must ensure that such changes are only triggered directly by the driver in question and that the driver has been properly authenticated. This endpoint should not be used to algorithmically trigger duty status changes nor should it be used by personnel besides the driver to trigger duty status changes on the driver’s behalf. Carriers and their drivers are ultimately responsible for maintaining accurate logs and should confirm that their use of the endpoint is compliant with the ELD Mandate. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write ELD Hours of Service (US)** under the Compliance category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "setCurrentDutyStatus", "parameters": [{"description": "ID of the driver for whom the duty status is being set.", "in": "path", "name": "driver_id", "required": true, "schema": {"format": "int64", "type": "integer"}}], "requestBody": {"$ref": "#/components/requestBodies/inline_object_1", "content": {"application/json": {"schema": {"properties": {"duty_status": {"description": "Duty status to set the driver to. The only supported values are 'ON_DUTY' and 'OFF_DUTY'.", "example": "ON_DUTY", "type": "string"}, "location": {"description": "Location to associate the duty status change with.", "example": "Loading dock", "type": "string"}, "remark": {"description": "Remark to associate the duty status change with.", "example": "Beginning On Duty Shift", "type": "string"}, "status_change_at_ms": {"description": "Timestamp that the duty status will begin at specified in milliseconds UNIX time. Defaults to the current time if left blank. This can only be set to up to 8 hours in the past.", "example": 1580834793568, "format": "int64", "type": "number"}, "vehicle_id": {"description": "Vehicle ID to associate the duty status change with.", "example": 1234, "format": "int64", "type": "number"}}, "required": ["duty_status"], "type": "object"}}}, "required": false}, "responses": {"200": {"content": {}, "description": "Successfully changed duty status. No response body is returned."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "Set a duty status for a specific driver", "tags": ["Hours of Service"], "x-codegen-request-body-name": "dutyStatusParams"}}, "/v1/fleet/hos_authentication_logs": {"get": {"description": "<n class=\"warning\">\n<nh>\n<i class=\"fa fa-exclamation-circle\"></i>\nThis endpoint is still on our legacy API.\n</nh>\n</n>\n\nGet the HOS (hours of service) signin and signout logs for the specified driver. The response includes 4 fields that are now deprecated.\n\n**Note:** If data is still being uploaded from the Samsara Driver App, it may not be completely reflected in the response from this endpoint. The best practice is to wait a couple of days before querying this endpoint to make sure that all data from the Samsara Driver App has been uploaded. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read ELD Hours of Service (US)** under the Compliance category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "V1getFleetHosAuthenticationLogs", "parameters": [{"description": "Driver ID to query.", "in": "query", "name": "driverId", "required": true, "schema": {"format": "int64", "type": "integer"}}, {"description": "Beginning of the time range, specified in milliseconds UNIX time.", "in": "query", "name": "startMs", "required": true, "schema": {"format": "int64", "type": "integer"}}, {"description": "End of the time range, specified in milliseconds UNIX time.", "in": "query", "name": "endMs", "required": true, "schema": {"format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1HosAuthenticationLogsResponse"}}}, "description": "HOS authentication logs for the specified driver."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "Get HOS signin and signout", "tags": ["Hours of Service"]}}}, "tags": [{"name": "Drivers"}, {"name": "Driver Vehicle Assignments"}, {"name": "Vehicle Driver Assignments"}]}