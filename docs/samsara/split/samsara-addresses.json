{"openapi": "3.0.1", "info": {"description": "Samsara API specification for addresses related endpoints and schemas.", "title": "Samsara API - Addresses", "version": "2025-05-12"}, "servers": [{"url": "https://api.samsara.com/"}, {"url": "https://api.eu.samsara.com/"}], "security": [{"AccessTokenHeader": []}], "components": {"parameters": {}, "requestBodies": {}, "schemas": {"Address": {"description": "An Address object.", "properties": {"addressTypes": {"description": "Reporting location type associated with the address (used for ELD reporting purposes). Valid values: `yard`, `shortHaul`, `workforceSite`, `riskZone`, `industrialSite`, `alertsOnly`, `agricultureSource`.", "items": {"enum": ["yard", "<PERSON><PERSON><PERSON>", "workforceSite", "riskZone", "industrialSite", "alertsOnly", "agricultureSource"], "example": "yard", "type": "string"}, "type": "array"}, "contacts": {"description": "An array Contact mini-objects that are associated the Address.", "items": {"$ref": "#/components/schemas/contactTinyResponse"}, "type": "array"}, "createdAtTime": {"description": "The date and time this address was created in RFC 3339 format.", "example": "2019-05-18T20:27:35Z", "format": "date-time", "type": "string"}, "externalIds": {"description": "The [external IDs](https://developers.samsara.com/docs/external-ids) for the given object.", "example": {"maintenanceId": "250020", "payrollId": "ABFS18600"}, "properties": {}, "type": "object"}, "formattedAddress": {"description": "The full street address for this address/geofence, as it might be recognized by Google Maps.", "example": "350 Rhode Island St, San Francisco, CA", "maxLength": 1024, "type": "string"}, "geofence": {"$ref": "#/components/schemas/AddressGeofence"}, "id": {"description": "ID of the Address.", "example": "22408", "type": "string"}, "latitude": {"description": "Latitude of the address. Will be geocoded from `formattedAddress` if not provided.", "example": 37.765363, "format": "double", "type": "number"}, "longitude": {"description": "Longitude of the address. Will be geocoded from `formattedAddress` if not provided.", "example": -122.4029238, "format": "double", "type": "number"}, "name": {"description": "Name of the address.", "example": "Samsara HQ", "maxLength": 255, "type": "string"}, "notes": {"description": "Notes about the address.", "example": "Hours of operation: 8am - 6pm; Truck entrance on the Rhode Island street side.", "maxLength": 280, "type": "string"}, "tags": {"description": "An array of all tag mini-objects that are associated with the given address entry.", "items": {"$ref": "#/components/schemas/tagTinyResponse"}, "type": "array"}}, "required": ["formattedAddress", "geofence", "id", "name"], "type": "object"}, "AddressGeofence": {"description": "The geofence that defines this address and its bounds. This can either be a circle or a polygon, but not both.", "properties": {"circle": {"$ref": "#/components/schemas/AddressGeofence_circle"}, "polygon": {"$ref": "#/components/schemas/AddressGeofence_polygon"}, "settings": {"$ref": "#/components/schemas/AddressGeofence_settings"}}, "type": "object"}, "AddressGeofence_circle": {"description": "Information about a circular geofence. This field is only needed if the geofence is a circle.", "properties": {"latitude": {"description": "Latitude of the address. Will be geocoded from `formattedAddress` if not provided.", "example": 37.765363, "format": "double", "type": "number"}, "longitude": {"description": "Longitude of the address. Will be geocoded from `formattedAddress` if not provided.", "example": -122.4029238, "format": "double", "type": "number"}, "radiusMeters": {"description": "The radius of the circular geofence in meters.", "example": 25, "format": "int64", "type": "integer"}}, "required": ["radiusMeters"], "type": "object"}, "AddressGeofence_polygon": {"description": "Information about a polygon geofence. This field is only needed if the geofence is a polygon.", "properties": {"vertices": {"description": "The vertices of the polygon geofence. These geofence vertices describe the perimeter of the polygon, and must consist of at least 3 vertices and less than 40.", "example": [{"latitude": 37.765363, "longitude": -122.403098}, {"latitude": 38.765363, "longitude": -122.403098}, {"latitude": 37.765363, "longitude": -123.403098}], "items": {"$ref": "#/components/schemas/AddressGeofence_polygon_vertices"}, "maxItems": 40, "minItems": 3, "type": "array"}}, "required": ["vertices"], "type": "object"}, "AddressGeofence_polygon_vertices": {"properties": {"latitude": {"description": "The latitude of a geofence vertex in decimal degrees.", "format": "double", "type": "number"}, "longitude": {"description": "The longitude of a geofence vertex in decimal degrees.", "format": "double", "type": "number"}}, "required": ["latitude", "longitude"], "type": "object"}, "AddressGeofence_settings": {"description": "Information about a geofence's settings.", "properties": {"showAddresses": {"description": "If this property is set to true, then underlying geofence addresses will be shown in reports instead of a geofence's name.", "example": false, "type": "boolean"}}, "type": "object"}, "AddressResponse": {"description": "An Address response body.", "properties": {"data": {"$ref": "#/components/schemas/Address"}}, "required": ["data"], "type": "object"}, "AddressResponseResponseBody": {"description": "Closest address that the GPS latitude and longitude match to.", "properties": {"city": {"description": "The name of the city", "example": "New York", "type": "string"}, "country": {"description": "The country", "example": "USA", "type": "string"}, "neighborhood": {"description": "The name of the neighborhood if one exists", "example": "Mission District", "type": "string"}, "pointOfInterest": {"description": "Point that may be of interest to the user", "example": "The Eiffel Tower", "type": "string"}, "postalCode": {"description": "The zip code", "example": "10010", "type": "string"}, "state": {"description": "The name of the state", "example": "New York", "type": "string"}, "street": {"description": "The street name", "example": "Main Street", "type": "string"}, "streetNumber": {"description": "Street number of the address", "example": "16", "type": "string"}}, "type": "object"}, "AssetLocation": {"description": "For locationType \"point\", latitude and longitude are required. For \"address\", formattedAddress must be provided, and lat/long can be optionally included for displaying a dot on the assets map. For \"dataInput\", this object should not be passed in.", "properties": {"formattedAddress": {"description": "Formatted address of the location", "example": "350 Rhode Island St, San Francisco CA, 94103", "type": "string"}, "latitude": {"$ref": "#/components/schemas/latitude"}, "longitude": {"$ref": "#/components/schemas/longitude"}}, "type": "object"}, "AssetLocationHeading": {"description": "Heading of the asset in degrees.", "example": 120, "format": "double", "type": "number"}, "AssetLocationSpeed": {"description": "GPS speed of the asset in miles per hour.", "example": 48.3, "format": "double", "type": "number"}, "AssetResponse_locationDataInput": {"description": "The associated location data input (only applicable when locationType is \"dataInput\").", "properties": {"id": {"description": "Id of the data input", "type": "string"}}, "required": ["id"], "type": "object"}, "AssetsLocationLinkConfigAddressDetailsObject": {"description": "Location object that indicates what address information (destination point and/or ETA) will be shown by Live Sharing Link.", "properties": {"formattedAddress": {"description": "Formatted address of a location", "example": "1990 Alameda Street, San Francisco, CA 94103", "type": "string"}, "latitude": {"description": "Latitude of a location", "example": 37.456345, "format": "double", "type": "number"}, "longitude": {"description": "Longitude of a location", "example": 34.5633749, "format": "double", "type": "number"}, "name": {"description": "Name of a location", "example": "Suburbs", "type": "string"}}, "required": ["formattedAddress", "latitude", "longitude", "name"], "type": "object"}, "AssetsLocationLinkConfigAddressDetailsObjectResponseBody": {"description": "Location object that indicates what address information (destination point and/or ETA) will be shown by Live Sharing Link.", "properties": {"formattedAddress": {"description": "Formatted address of a location", "example": "1990 Alameda Street, San Francisco, CA 94103", "type": "string"}, "latitude": {"description": "Latitude of a location", "example": 37.456345, "format": "double", "type": "number"}, "longitude": {"description": "Longitude of a location", "example": 34.5633749, "format": "double", "type": "number"}, "name": {"description": "Name of a location", "example": "Suburbs", "type": "string"}}, "required": ["formattedAddress", "latitude", "longitude", "name"], "type": "object"}, "AssetsLocationLinkConfigObject": {"description": "Configuration details specific to the 'By Asset' Live Sharing Link.", "properties": {"assetId": {"description": "Unique assets ID that Live Sharing Link will show.", "example": "1234", "type": "string"}, "location": {"$ref": "#/components/schemas/AssetsLocationLinkConfigAddressDetailsObject"}}, "required": ["assetId"], "type": "object"}, "AssetsLocationLinkConfigObjectResponseBody": {"description": "Configuration details specific to the 'By Asset' Live Sharing Link.", "properties": {"assetId": {"description": "Unique assets ID that Live Sharing Link will show.", "example": "1234", "type": "string"}, "location": {"$ref": "#/components/schemas/AssetsLocationLinkConfigAddressDetailsObjectResponseBody"}}, "required": ["assetId"], "type": "object"}, "AssetsNearLocationLinkConfigObject": {"description": "Configuration details specific to the 'By Location' Live Sharing Link.", "properties": {"addressId": {"description": "ID of the address. Can be either a unique Samsara ID or an external ID for the address.", "example": "1234", "type": "string"}}, "required": ["addressId"], "type": "object"}, "AssetsNearLocationLinkConfigObjectResponseBody": {"description": "Configuration details specific to the 'By Location' Live Sharing Link.", "properties": {"addressId": {"description": "ID of the address. Can be either a unique Samsara ID or an external ID for the address.", "example": "1234", "type": "string"}}, "required": ["addressId"], "type": "object"}, "CreateAddressRequest": {"description": "A request body to create an Address.", "properties": {"addressTypes": {"description": "Reporting location type associated with the address (used for ELD reporting purposes). Valid values: `yard`, `shortHaul`, `workforceSite`, `riskZone`, `industrialSite`, `alertsOnly`, `agricultureSource`.", "items": {"enum": ["yard", "<PERSON><PERSON><PERSON>", "workforceSite", "riskZone", "industrialSite", "alertsOnly", "agricultureSource"], "example": "yard", "type": "string"}, "type": "array"}, "contactIds": {"description": "An array of Contact IDs associated with this Address.", "items": {"example": "22408", "type": "string"}, "type": "array"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "The [external IDs](https://developers.samsara.com/docs/external-ids) for the given object.", "example": {"maintenanceId": "250020", "payrollId": "ABFS18600"}, "type": "object"}, "formattedAddress": {"description": "The full street address for this address/geofence, as it might be recognized by Google Maps.", "example": "350 Rhode Island St, San Francisco, CA", "maxLength": 1024, "type": "string"}, "geofence": {"$ref": "#/components/schemas/CreateAddressRequest_geofence"}, "latitude": {"description": "Latitude of the address. Will be geocoded from `formattedAddress` if not provided.", "example": 37.765363, "format": "double", "type": "number"}, "longitude": {"description": "Longitude of the address. Will be geocoded from `formattedAddress` if not provided.", "example": -122.4029238, "format": "double", "type": "number"}, "name": {"description": "Name of the address.", "example": "Samsara HQ", "maxLength": 255, "type": "string"}, "notes": {"description": "Notes about the address.", "example": "Hours of operation: 8am - 6pm; Truck entrance on the Rhode Island street side.", "maxLength": 280, "type": "string"}, "tagIds": {"description": "An array of IDs of tags to associate with this address.", "items": {"example": "3914", "type": "string"}, "type": "array"}}, "required": ["formattedAddress", "geofence", "name"], "type": "object"}, "CreateAddressRequest_geofence": {"description": "The geofence that defines this address and its bounds. This can either be a circle or a polygon, but not both.", "properties": {"circle": {"$ref": "#/components/schemas/AddressGeofence_circle"}, "polygon": {"$ref": "#/components/schemas/AddressGeofence_polygon"}, "settings": {"$ref": "#/components/schemas/CreateAddressRequest_geofence_settings"}}, "type": "object"}, "CreateAddressRequest_geofence_settings": {"description": "Information about a geofence's settings.", "properties": {"showAddresses": {"description": "If this property is set to true, then underlying geofence addresses will be shown in reports instead of a geofence's name.", "example": true, "type": "boolean"}}, "type": "object"}, "DriverHomeTerminalAddress": {"description": "Address of the place of business at which a driver ordinarily reports for work.", "example": "1234 Pear St., Scranton, PA 62814", "maxLength": 255, "type": "string"}, "DriverMainOfficeAddress": {"description": "Main office address for a given driver. If this differs from the general organization's settings, the override value is used. ", "example": "1234 Pear St., Scranton, PA 62814", "maxLength": 255, "type": "string"}, "DvirLocation": {"description": "Optional string if your jurisdiction requires a location of the DVIR.", "example": "350 Rhode Island St Ste. 400S, San Francisco, CA 94103", "type": "string"}, "EquipmentLocation": {"description": "Location reading.", "properties": {"heading": {"description": "Heading of the unit of equipment in degrees.", "example": 120, "format": "double", "type": "number"}, "latitude": {"description": "GPS latitude represented in degrees", "example": 122.142, "format": "double", "type": "number"}, "longitude": {"description": "GPS longitude represented in degrees", "example": -93.343, "format": "double", "type": "number"}, "speed": {"description": "GPS speed of the unit of equipment in miles per hour.", "example": 48.3, "format": "double", "type": "number"}, "time": {"$ref": "#/components/schemas/time"}}, "required": ["latitude", "longitude", "time"], "type": "object"}, "EquipmentLocationsListResponse": {"description": "A time-series of equipment locations and pagination information", "properties": {"data": {"description": "Time-series of locations for the specified units of equipment.", "items": {"$ref": "#/components/schemas/EquipmentLocationsListResponse_data"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "required": ["data", "pagination"], "type": "object"}, "EquipmentLocationsListResponse_data": {"description": "A unit of equipment and its time-series of location events.", "properties": {"id": {"$ref": "#/components/schemas/EquipmentId"}, "locations": {"description": "A time-series of location events for the given unit of equipment.", "items": {"$ref": "#/components/schemas/EquipmentLocation"}, "type": "array"}, "name": {"$ref": "#/components/schemas/EquipmentName"}}, "required": ["id", "locations", "name"], "type": "object"}, "EquipmentLocationsResponse": {"description": "The most recent equipment locations and pagination information", "properties": {"data": {"description": "List of the most recent locations for the specified units of equipment.", "items": {"$ref": "#/components/schemas/EquipmentLocationsResponse_data"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "required": ["data", "pagination"], "type": "object"}, "EquipmentLocationsResponse_data": {"description": "A unit of equipment and its most recent location.", "properties": {"id": {"$ref": "#/components/schemas/EquipmentId"}, "location": {"$ref": "#/components/schemas/EquipmentLocation"}, "name": {"$ref": "#/components/schemas/EquipmentName"}}, "required": ["id", "location", "name"], "type": "object"}, "EventLocationResponseBody": {"description": "Information about a location when the vehicle was stopped.", "properties": {"latitude": {"description": "Latitude of the event.", "example": 37.7749, "format": "double", "type": "number"}, "longitude": {"description": "Longitude of the event.", "example": 137.7749, "format": "double", "type": "number"}}, "type": "object"}, "FormSubmissionRequestGeofenceObjectRequestBody": {"description": "Geofence object.", "properties": {"id": {"description": "Samsara ID of the geofence.", "example": "285909128147498", "type": "string"}}, "required": ["id"], "type": "object"}, "FormSubmissionRequestGeofenceValueObjectRequestBody": {"description": "The value of a geofence form input field. Only valid for geofence form input fields.", "properties": {"geofence": {"$ref": "#/components/schemas/FormSubmissionRequestGeofenceObjectRequestBody"}}, "required": ["geofence"], "type": "object"}, "FormsGeofenceObjectResponseBody": {"description": "Tracked or untracked (i.e. manually entered) geofence object.", "properties": {"address": {"description": "Address of the geofence. Included if 'entryType' is `tracked`.", "example": "123 Main St, Anytown, USA 12345", "type": "string"}, "entryType": {"description": "The type of entry for the geofence.  Valid values: `tracked`, `untracked`", "enum": ["tracked", "untracked"], "example": "tracked", "type": "string"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "ID of the tracked geofence. Included if 'entryType' is `tracked`.", "example": "1234567890", "type": "string"}, "name": {"description": "Name of an untracked (i.e. manually entered) geofence.", "example": "Geofence Name", "type": "string"}}, "required": ["entryType"], "type": "object"}, "FormsGeofenceValueObjectResponseBody": {"description": "The value of a geofence form input field.", "properties": {"geofence": {"$ref": "#/components/schemas/FormsGeofenceObjectResponseBody"}}, "required": ["geofence"], "type": "object"}, "FormsLocationObjectResponseBody": {"description": "Form template location object.", "properties": {"latitude": {"description": "Latitude of a location.", "example": 12333122.3, "format": "double", "type": "number"}, "longitude": {"description": "Longitude of a location.", "example": 1233331.4, "format": "double", "type": "number"}}, "required": ["latitude", "longitude"], "type": "object"}, "GeofenceEntryTriggerDetailsObjectRequestBody": {"description": "Details specific to Geofence Entry", "properties": {"location": {"$ref": "#/components/schemas/LocationObjectRequestBody"}}, "required": ["location"], "type": "object"}, "GeofenceEntryTriggerDetailsObjectResponseBody": {"description": "Details specific to Geofence Entry", "properties": {"location": {"$ref": "#/components/schemas/LocationObjectResponseBody"}}, "required": ["location"], "type": "object"}, "GeofenceExitTriggerDetailsObjectRequestBody": {"description": "Details specific to Geofence Exit", "properties": {"location": {"$ref": "#/components/schemas/LocationObjectRequestBody"}}, "required": ["location"], "type": "object"}, "GeofenceExitTriggerDetailsObjectResponseBody": {"description": "Details specific to Geofence Exit", "properties": {"location": {"$ref": "#/components/schemas/LocationObjectResponseBody"}}, "required": ["location"], "type": "object"}, "GeofenceResponseResponseBody": {"description": "Closest geofence based on 1000 meter radial search.", "properties": {"externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "Unique ID of the geofence object.", "example": "12345", "type": "string"}}, "type": "object"}, "GoaAddressTinyResponseResponseBody": {"description": "A minified Address object", "properties": {"externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "Id of the address", "example": "494123", "type": "string"}, "name": {"description": "Name of the address", "example": "Company Office #1", "type": "string"}}, "required": ["id", "name"], "type": "object"}, "HosEldEventLocationObjectResponseBody": {"properties": {"city": {"description": "The best effort city for the latitude and longitude.", "example": "San Francisco", "type": "string"}, "eldLocation": {"description": "Relative location to the city, village, or town with population of 5,000 or greater.", "example": "3.1 mi WSW San Francisco, CA", "type": "string"}, "latitude": {"description": "The latitude of the location.", "example": 123.456, "format": "double", "type": "number"}, "longitude": {"description": "The longitude of the location.", "example": 37.459, "format": "double", "type": "number"}, "state": {"description": "The best effort state for the latitude and longitude.", "example": "CA", "type": "string"}}, "type": "object"}, "HosLogLocation": {"description": "Location associated with the duty status change", "properties": {"latitude": {"description": "GPS latitude represented in degrees", "example": 122.142, "format": "double", "type": "number"}, "longitude": {"description": "GPS longitude represented in degrees", "example": -93.343, "format": "double", "type": "number"}}, "required": ["latitude", "longitude"], "type": "object"}, "IdlingReportEventAddressResponseBody": {"description": "Address where the idling event took place.", "properties": {"formatted": {"description": "The formatted address of the idling location.", "example": "123 Main Street Atlanta, GA 30307", "type": "string"}, "latitude": {"description": "The latitude of the idling location.", "example": 34.654567, "format": "double", "type": "number"}, "longitude": {"description": "The longitude of the idling location.", "example": 34.654567, "format": "double", "type": "number"}}, "required": ["formatted", "latitude", "longitude"], "type": "object"}, "InsideGeofenceDataResponseBody": {"description": "Details specific to Inside Geofence.", "properties": {"driver": {"$ref": "#/components/schemas/alertObjectDriverResponseBody"}, "trailer": {"$ref": "#/components/schemas/alertObjectTrailerResponseBody"}, "vehicle": {"$ref": "#/components/schemas/alertObjectVehicleResponseBody"}}, "type": "object"}, "InsideGeofenceTriggerDetailsObjectRequestBody": {"description": "Details specific to Inside Geofence", "properties": {"location": {"$ref": "#/components/schemas/LocationObjectRequestBody"}, "minDurationMilliseconds": {"description": "The number of milliseconds the trigger needs to stay active before alerting.", "example": 600000, "format": "int64", "type": "integer"}}, "required": ["location", "minDurationMilliseconds"], "type": "object"}, "InsideGeofenceTriggerDetailsObjectResponseBody": {"description": "Details specific to Inside Geofence", "properties": {"location": {"$ref": "#/components/schemas/LocationObjectResponseBody"}, "minDurationMilliseconds": {"description": "The number of milliseconds the trigger needs to stay active before alerting.", "example": 600000, "format": "int64", "type": "integer"}}, "required": ["location", "minDurationMilliseconds"], "type": "object"}, "LastKnownLocationResponseResponseBody": {"description": "The most recent location information for the device.", "properties": {"id": {"description": "The unique ID of the address.", "example": 12345, "format": "int64", "type": "integer"}, "latitude": {"description": "Latitude of a location.", "example": 12333122.3, "format": "double", "type": "number"}, "longitude": {"description": "Longitude of a location.", "example": 1233331.4, "format": "double", "type": "number"}}, "type": "object"}, "ListAddressesResponse": {"description": "A list of Addresses and pagination information.", "properties": {"data": {"description": "A list of Addresses.", "items": {"$ref": "#/components/schemas/Address"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "required": ["data", "pagination"], "type": "object"}, "LocationAndSpeedGetLocationAndSpeedBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "LocationAndSpeedGetLocationAndSpeedBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "LocationAndSpeedGetLocationAndSpeedGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "LocationAndSpeedGetLocationAndSpeedInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "LocationAndSpeedGetLocationAndSpeedMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "LocationAndSpeedGetLocationAndSpeedNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "LocationAndSpeedGetLocationAndSpeedNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "LocationAndSpeedGetLocationAndSpeedResponseBody": {"properties": {"data": {"description": "List of location and speed objects.", "items": {"$ref": "#/components/schemas/LocationAndSpeedResponseResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationWithTokensResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "LocationAndSpeedGetLocationAndSpeedServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "LocationAndSpeedGetLocationAndSpeedTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "LocationAndSpeedGetLocationAndSpeedUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "LocationAndSpeedResponseResponseBody": {"description": "Full location and speed objects.", "properties": {"asset": {"$ref": "#/components/schemas/AssetResponseResponseBody"}, "happenedAtTime": {"description": "UTC timestamp in RFC 3339 format of the event.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "location": {"$ref": "#/components/schemas/LocationResponseResponseBody"}, "speed": {"$ref": "#/components/schemas/SpeedResponseResponseBody"}}, "required": ["asset", "happenedAtTime", "location"], "type": "object"}, "LocationDataPoint": {"description": "A single location data point of a data input.", "properties": {"gpsLocation": {"$ref": "#/components/schemas/LocationDataPoint_gpsLocation"}, "time": {"$ref": "#/components/schemas/time"}}, "type": "object"}, "LocationDataPoint_gpsLocation": {"description": "GPS location information of the data input's datapoint.", "properties": {"formattedAddress": {"description": "Formatted address of the location", "example": "350 Rhode Island St, San Francisco CA, 94103", "format": "string", "type": "string"}, "gpsMetersPerSecond": {"description": "Speed of GPS (meters per second)", "example": 35.5, "format": "double", "type": "number"}, "headingDegrees": {"description": "Heading degrees", "example": 91.2, "format": "double", "type": "number"}, "latitude": {"description": "Latitude of the location", "example": 42.44817, "format": "double", "type": "number"}, "longitude": {"description": "Longitude of the location", "example": -71.224716, "format": "double", "type": "number"}, "place": {"$ref": "#/components/schemas/LocationDataPoint_gpsLocation_place"}}, "type": "object"}, "LocationDataPoint_gpsLocation_place": {"description": "Address of the location", "properties": {"city": {"description": "City", "example": "San Francisco", "format": "string", "type": "string"}, "houseNumber": {"description": "House number", "example": "350", "format": "string", "type": "string"}, "neighborhood": {"description": "Neighborhood", "example": "<PERSON>", "format": "string", "type": "string"}, "poi": {"description": "POI", "example": "400", "format": "string", "type": "string"}, "postcode": {"description": "Postcode", "example": "94103", "format": "string", "type": "string"}, "state": {"description": "State", "example": "CA", "format": "string", "type": "string"}, "street": {"description": "Street", "example": "Rhode Island", "format": "string", "type": "string"}}, "type": "object"}, "LocationObjectRequestBody": {"description": "A location. Polygon and Circle is deprecated, but may be set for old Alerts. At least one location must be selected.", "properties": {"addressIds": {"description": "All locations with selected address IDs will trigger.", "example": ["<PERSON><PERSON><PERSON> do<PERSON>.", "Quisquam rerum dolorum et unde.", "In culpa voluptas ab.", "Repellendus vel fugit iure."], "items": {"description": "Id of the address.", "example": "Rerum consectetur ut et.", "type": "string"}, "type": "array"}, "addressTypes": {"description": "All locations with the selected address types will trigger.", "example": ["yard", "undefined"], "items": {"description": "Type of the address.  Valid values: `agricultureSource`, `alertsOnly`, `industrialSite`, `riskZone`, `shortHaul`, `undefined`, `workforceSite`, `yard`", "enum": ["agricultureSource", "alertsOnly", "industrialSite", "riskZone", "<PERSON><PERSON><PERSON>", "undefined", "workforceSite", "yard"], "example": "<PERSON><PERSON><PERSON>", "type": "string"}, "type": "array"}, "circle": {"$ref": "#/components/schemas/CircleRequestBody"}, "polygon": {"$ref": "#/components/schemas/PolygonRequestBody"}, "tagIds": {"description": "All locations with selected tag will trigger.", "example": ["4815", "4815", "4815"], "items": {"description": "Id of the tag.", "example": "4815", "type": "string"}, "type": "array"}}, "type": "object"}, "LocationObjectResponseBody": {"description": "A location. Polygon and Circle is deprecated, but may be set for old Alerts. At least one location must be selected.", "properties": {"addressIds": {"description": "All locations with selected address IDs will trigger.", "example": ["Voluptas provident nihil vitae modi accusamus.", "Eum eos dolor magni mollitia.", "A sequi.", "Sunt molestiae fuga quisquam distinctio nostrum."], "items": {"description": "Id of the address.", "example": "Quia quam.", "type": "string"}, "type": "array"}, "addressTypes": {"description": "All locations with the selected address types will trigger.", "example": ["yard", "riskZone", "industrialSite", "workforceSite"], "items": {"description": "Type of the address.  Valid values: `agricultureSource`, `alertsOnly`, `industrialSite`, `riskZone`, `shortHaul`, `undefined`, `workforceSite`, `yard`", "enum": ["agricultureSource", "alertsOnly", "industrialSite", "riskZone", "<PERSON><PERSON><PERSON>", "undefined", "workforceSite", "yard"], "example": "agricultureSource", "type": "string"}, "type": "array"}, "circle": {"$ref": "#/components/schemas/CircleResponseBody"}, "polygon": {"$ref": "#/components/schemas/PolygonResponseBody"}, "tagIds": {"description": "All locations with selected tag will trigger.", "example": ["4815", "4815", "4815", "4815"], "items": {"description": "Id of the tag.", "example": "4815", "type": "string"}, "type": "array"}}, "type": "object"}, "LocationResponseBody": {"description": "Equipment location.", "properties": {"Latitude": {"description": "Location latitude.", "example": 12.34, "format": "double", "type": "number"}, "Longitude": {"description": "Location longitude.", "example": 12.34, "format": "double", "type": "number"}, "datetime": {"description": "Date time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}}, "type": "object"}, "LocationResponseResponseBody": {"description": "Location object.", "properties": {"accuracyMeters": {"description": "Radial accuracy of gps location in meters. This will only return if strong GPS is not available.", "example": 5.801, "format": "double", "type": "number"}, "address": {"$ref": "#/components/schemas/AddressResponseResponseBody"}, "geofence": {"$ref": "#/components/schemas/GeofenceResponseResponseBody"}, "headingDegrees": {"description": "Heading of the asset in degrees. May be 0 if the asset is not moving.", "example": 120, "format": "int64", "type": "integer"}, "latitude": {"description": "Latitude of the location of the asset.", "example": 37.7749, "format": "double", "type": "number"}, "longitude": {"description": "Longitude of the location of the asset.", "example": 137.2719, "format": "double", "type": "number"}}, "required": ["headingDegrees", "latitude", "longitude"], "type": "object"}, "LocationType": {"description": "The format of the location. This field is required if a location is provided. Valid values: `point`, `address`, `dataInput`.", "enum": ["point", "address", "dataInput"], "type": "string"}, "OutsideGeofenceDataResponseBody": {"description": "Details specific to Outside Geofence.", "properties": {"driver": {"$ref": "#/components/schemas/alertObjectDriverResponseBody"}, "trailer": {"$ref": "#/components/schemas/alertObjectTrailerResponseBody"}, "vehicle": {"$ref": "#/components/schemas/alertObjectVehicleResponseBody"}}, "type": "object"}, "OutsideGeofenceTriggerDetailsObjectRequestBody": {"description": "Details specific to Outside Geofence", "properties": {"location": {"$ref": "#/components/schemas/LocationObjectRequestBody"}, "minDurationMilliseconds": {"description": "The number of milliseconds the trigger needs to stay active before alerting.", "example": 600000, "format": "int64", "type": "integer"}}, "required": ["location", "minDurationMilliseconds"], "type": "object"}, "OutsideGeofenceTriggerDetailsObjectResponseBody": {"description": "Details specific to Outside Geofence", "properties": {"location": {"$ref": "#/components/schemas/LocationObjectResponseBody"}, "minDurationMilliseconds": {"description": "The number of milliseconds the trigger needs to stay active before alerting.", "example": 600000, "format": "int64", "type": "integer"}}, "required": ["location", "minDurationMilliseconds"], "type": "object"}, "PatchJobObjectjobLocationRequestObjectRequestBody": {"description": "A location object for the job", "properties": {"address": {"description": "Address of a location", "example": "1990 Alameda st, San Francisco, Ca 94103", "type": "string"}, "latitude": {"description": "Latitude of a location", "example": 37.456345, "format": "double", "type": "number"}, "longitude": {"description": "Longitude of a location", "example": 34.5633749, "format": "double", "type": "number"}, "name": {"description": "Name of the location", "example": "Worksite #1", "type": "string"}}, "required": ["address", "latitude", "longitude", "name"], "type": "object"}, "PostJobObjectjobLocationRequestObjectRequestBody": {"description": "A location object for the job", "properties": {"address": {"description": "Address of a location", "example": "1990 Alameda st, San Francisco, Ca 94103", "type": "string"}, "latitude": {"description": "Latitude of a location", "example": 37.456345, "format": "double", "type": "number"}, "longitude": {"description": "Longitude of a location", "example": 34.5633749, "format": "double", "type": "number"}, "name": {"description": "Name of the location", "example": "Worksite #1", "type": "string"}}, "required": ["address", "latitude", "longitude", "name"], "type": "object"}, "RoutesSingleUseAddressObjectRequestBody": {"description": "This field is used to indicate stops along the route for which an address has not been persisted. This field is mutually exclusive with addressId.", "properties": {"address": {"description": "Address of the stop.", "example": "1234 Main St, San Jose, CA", "maxLength": 255, "type": "string"}, "latitude": {"description": "The latitude of the location", "example": 123.456, "format": "double", "type": "number"}, "longitude": {"description": "The longitude of the location", "example": 37.459, "format": "double", "type": "number"}}, "required": ["latitude", "longitude"], "type": "object"}, "RoutesSingleUseAddressObjectResponseBody": {"description": "This field is used to indicate stops along the route for which an address has not been persisted. This field is mutually exclusive with addressId.", "properties": {"address": {"description": "Address of the stop.", "example": "1234 Main St, San Jose, CA", "maxLength": 255, "type": "string"}, "latitude": {"description": "The latitude of the location", "example": 123.456, "format": "double", "type": "number"}, "longitude": {"description": "The longitude of the location", "example": 37.459, "format": "double", "type": "number"}}, "required": ["latitude", "longitude"], "type": "object"}, "SafetyEventLocation": {"$ref": "#/components/schemas/location"}, "SpeedingIntervalLocationResponseResponseBody": {"description": "Location object of the closest location point to the interval.", "properties": {"accuracyMeters": {"description": "Radial accuracy of gps location in meters. This will only return if strong GPS is not available.", "example": 5.801, "format": "double", "type": "number"}, "address": {"$ref": "#/components/schemas/AddressResponseResponseBody"}, "headingDegrees": {"description": "Heading of the asset in degrees. May be 0 if the asset is not moving.", "example": 120, "format": "int64", "type": "integer"}, "latitude": {"description": "Latitude of the closest location point to the interval.", "example": 37.7749, "format": "double", "type": "number"}, "longitude": {"description": "Longitude of the closest location point to the interval.", "example": 137.2719, "format": "double", "type": "number"}}, "required": ["address", "headingDegrees", "latitude", "longitude"], "type": "object"}, "UpdateAddressRequest": {"description": "A request body to update an Address.", "properties": {"addressTypes": {"description": "Reporting location type associated with the address (used for ELD reporting purposes). Valid values: `yard`, `shortHaul`, `workforceSite`, `riskZone`, `industrialSite`, `alertsOnly`, `agricultureSource`.", "items": {"enum": ["yard", "<PERSON><PERSON><PERSON>", "workforceSite", "riskZone", "industrialSite", "alertsOnly", "agricultureSource"], "example": "yard", "type": "string"}, "type": "array"}, "contactIds": {"description": "An array of Contact IDs associated with this Address.", "items": {"example": "22408", "type": "string"}, "type": "array"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "The [external IDs](https://developers.samsara.com/docs/external-ids) for the given object.", "example": {"maintenanceId": "250020", "payrollId": "ABFS18600"}, "type": "object"}, "formattedAddress": {"description": "The full street address for this address/geofence, as it might be recognized by Google Maps.", "example": "350 Rhode Island St, San Francisco, CA", "maxLength": 1024, "type": "string"}, "geofence": {"$ref": "#/components/schemas/UpdateAddressRequest_geofence"}, "latitude": {"description": "Latitude of the address. Will be geocoded from `formattedAddress` if not provided.", "example": 37.765363, "format": "double", "type": "number"}, "longitude": {"description": "Longitude of the address. Will be geocoded from `formattedAddress` if not provided.", "example": -122.4029238, "format": "double", "type": "number"}, "name": {"description": "Name of the address.", "example": "Samsara HQ", "maxLength": 255, "type": "string"}, "notes": {"description": "Notes about the address.", "example": "Hours of operation: 8am - 6pm; Truck entrance on the Rhode Island street side.", "maxLength": 280, "type": "string"}, "tagIds": {"description": "An array of IDs of tags to associate with this address.", "items": {"example": "3914", "type": "string"}, "type": "array"}}, "type": "object"}, "UpdateAddressRequest_geofence": {"description": "The geofence that defines this address and its bounds. This can either be a circle or a polygon, but not both.", "properties": {"circle": {"$ref": "#/components/schemas/AddressGeofence_circle"}, "polygon": {"$ref": "#/components/schemas/AddressGeofence_polygon"}, "settings": {"$ref": "#/components/schemas/AddressGeofence_settings"}}, "type": "object"}, "V1AssetCurrentLocation": {"description": "Current location of an asset", "properties": {"latitude": {"description": "The latitude of the location in degrees.", "example": 37, "type": "number"}, "location": {"description": "The best effort (street,city,state) for the latitude and longitude.", "example": "525 York, San Francisco, CA", "type": "string"}, "longitude": {"description": "The longitude of the location in degrees.", "example": -122.7, "type": "number"}, "speedMilesPerHour": {"description": "The speed calculated from GPS that the asset was traveling at in miles per hour.", "example": 35, "type": "number"}, "timeMs": {"description": "Time in Unix milliseconds since epoch when the asset was at the location.", "example": 12314151, "type": "number"}}, "type": "object"}, "V1AssetCurrentLocationsResponse": {"description": "Basic information of an asset", "properties": {"assetSerialNumber": {"description": "Asset serial number", "example": "8dka2810", "type": "string"}, "cable": {"$ref": "#/components/schemas/V1Asset_cable"}, "engineHours": {"description": "Engine hours", "example": 104, "type": "integer"}, "id": {"description": "Asset ID", "example": 1, "format": "int64", "type": "integer"}, "location": {"description": "Current location of an asset", "items": {"$ref": "#/components/schemas/V1AssetCurrentLocation"}, "type": "array"}, "name": {"description": "Asset name", "example": "Trailer 123", "type": "string"}}, "required": ["id"], "type": "object"}, "V1AssetLocationResponse": {"description": "A list of historical asset locations.", "items": {"description": "Asset location details.", "properties": {"latitude": {"description": "The latitude of the location in degrees.", "example": 37, "type": "number"}, "location": {"description": "The best effort (street,city,state) for the latitude and longitude.", "example": "525 York, San Francisco, CA", "type": "string"}, "longitude": {"description": "The longitude of the location in degrees.", "example": -122.7, "type": "number"}, "speedMilesPerHour": {"description": "The speed calculated from GPS that the asset was traveling at in miles per hour.", "example": 35, "type": "number"}, "time": {"description": "Time in Unix milliseconds since epoch when the asset was at the location.", "example": 12314151, "type": "number"}}, "type": "object"}, "type": "array"}, "V1FleetVehicleLocation": {"description": "Contains the location and speed of a vehicle at a particular time", "properties": {"latitude": {"description": "The latitude of the location in degrees.", "example": 37.2, "format": "double", "type": "number"}, "location": {"description": "The best effort (street,city,state) for the latitude and longitude.", "example": "525 York, San Francisco, CA", "type": "string"}, "longitude": {"description": "The longitude of the location in degrees.", "example": -122.5, "format": "double", "type": "number"}, "speedMilesPerHour": {"description": "The speed calculated from GPS that the asset was traveling at in miles per hour.", "example": 35.2, "format": "double", "type": "number"}, "timeMs": {"description": "Time in Unix milliseconds since epoch when the asset was at the location.", "example": 1535586471332, "format": "int64", "type": "number"}}, "type": "object"}, "V1FleetVehicleLocations": {"items": {"$ref": "#/components/schemas/V1FleetVehicleLocation"}, "type": "array"}, "V1FleetVehiclesLocations": {"items": {"properties": {"id": {"description": "ID of the vehicle.", "example": 112, "format": "int64", "type": "integer"}, "locations": {"$ref": "#/components/schemas/V1FleetVehicleLocations"}, "name": {"description": "Name of the vehicle.", "example": "Truck A7", "type": "string"}}, "type": "object"}, "type": "array"}, "V1TripResponse_endAddress": {"description": "Text representation of nearest identifiable location to the end (latitude, longitude) coordinates.", "properties": {"address": {"description": "The formatted address", "example": "123 Main St, Sunnyvale, CA 94089", "type": "string"}, "id": {"description": "The ID of the address", "example": 581, "format": "int64", "type": "number"}, "name": {"description": "The name of the address", "example": "<PERSON><PERSON>", "type": "string"}}, "type": "object"}, "V1TripResponse_startAddress": {"description": "Text representation of nearest identifiable location to the start (latitude, longitude) coordinates.", "properties": {"address": {"description": "The formatted address", "example": "123 Main St, Sunnyvale, CA 94089", "type": "string"}, "id": {"description": "The ID of the address", "example": 581, "format": "int64", "type": "number"}, "name": {"description": "The name of the address", "example": "<PERSON><PERSON>", "type": "string"}}, "type": "object"}, "V1VehicleHarshEventResponse_location": {"properties": {"address": {"description": "Address of location where the harsh event occurred", "example": "350 Rhode Island St, San Francisco, CA", "type": "string"}, "latitude": {"description": "Latitude of location where the harsh event occurred", "example": 33.07614328, "type": "number"}, "longitude": {"description": "Longitude of location where the harsh event occurred", "example": -96.14907287, "type": "number"}}, "type": "object"}, "V1VehicleLocation": {"description": "Contains the location, in latitude and longitude, of a vehicle.", "properties": {"driverId": {"description": "The ID of the driver currently assigned to this vehicle.", "example": 1, "type": "integer"}, "heading": {"description": "Heading in degrees.", "example": 246.42, "format": "double", "type": "number"}, "id": {"description": "ID of the vehicle.", "example": 112, "format": "int64", "type": "integer"}, "latitude": {"description": "Latitude in decimal degrees.", "example": 123.456, "format": "double", "type": "number"}, "location": {"description": "Text representation of nearest identifiable location to (latitude, longitude) coordinates.", "example": "1 Main St, Dallas, TX", "type": "string"}, "longitude": {"description": "Longitude in decimal degrees.", "example": 32.897, "format": "double", "type": "number"}, "name": {"description": "Name of the vehicle.", "example": "Truck A7", "type": "string"}, "odometerMeters": {"description": "The number of meters reported by the odometer.", "example": 71774705, "format": "int64", "type": "integer"}, "odometerType": {"description": "The source of data for odometerMeters. Will be either GPS or OBD", "enum": ["GPS", "OBD"], "example": "GPS", "type": "string"}, "onTrip": {"description": "Whether or not a trip is currently in progress for this vehicle. More information available via /fleet/trips endpoint.", "example": true, "type": "boolean"}, "routeIds": {"description": "A list of currently active route IDs that the vehicle is in.", "example": [2244514, 2311654], "items": {"format": "int64", "type": "integer"}, "type": "array"}, "speed": {"description": "Speed in miles per hour.", "example": 64.37, "format": "double", "type": "number"}, "time": {"description": "The time the reported location was logged, reported as a UNIX timestamp in milliseconds.", "example": 1462881998034, "type": "integer"}, "vin": {"description": "Vehicle Identification Number (VIN) of the vehicle.", "example": "JTNBB46KX73011966", "type": "string"}}, "required": ["id"], "type": "object"}, "VehicleLocation": {"description": "Vehicle location event.", "properties": {"heading": {"$ref": "#/components/schemas/VehicleLocationHeading"}, "latitude": {"$ref": "#/components/schemas/VehicleLocationLatitude"}, "longitude": {"$ref": "#/components/schemas/VehicleLocationLongitude"}, "reverseGeo": {"$ref": "#/components/schemas/reverseGeo"}, "speed": {"$ref": "#/components/schemas/VehicleLocationSpeed"}, "time": {"$ref": "#/components/schemas/time"}}, "required": ["latitude", "longitude", "time"], "type": "object"}, "VehicleLocationAddress": {"description": "Address that the location is in.", "properties": {"id": {"description": "Id of the address.", "example": "1234", "type": "string"}, "name": {"description": "Name of address.", "example": "Address 1", "type": "string"}}, "type": "object"}, "VehicleLocationHeading": {"description": "Heading of the vehicle in degrees.", "example": 120, "format": "double", "type": "number"}, "VehicleLocationIsEcuSpeed": {"description": "True if the speed value is reported from the ECU. Speed value is reported from GPS otherwise.", "example": true, "type": "boolean"}, "VehicleLocationLatitude": {"description": "GPS latitude represented in degrees", "example": 122.142, "format": "double", "type": "number"}, "VehicleLocationLongitude": {"description": "GPS longitude represented in degrees", "example": -93.343, "format": "double", "type": "number"}, "VehicleLocationReverseGeo": {"$ref": "#/components/schemas/reverseGeo"}, "VehicleLocationSpeed": {"description": "GPS speed of the vehicle in miles per hour. See `isEcuSpeed` to determine speed data source.", "example": 48.3, "format": "double", "type": "number"}, "VehicleLocationTime": {"$ref": "#/components/schemas/time"}, "VehicleLocationsListResponse": {"description": "List of vehicle location events and pagination info.", "properties": {"data": {"description": "A list of vehicles and an array of location events for each vehicle.", "items": {"$ref": "#/components/schemas/VehicleLocationsListResponse_data"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "required": ["data", "pagination"], "type": "object"}, "VehicleLocationsListResponse_data": {"description": "A vehicle and its list of location events.", "properties": {"id": {"$ref": "#/components/schemas/VehicleId"}, "locations": {"description": "A list of location events for the given vehicle.", "items": {"$ref": "#/components/schemas/VehicleLocation"}, "type": "array"}, "name": {"$ref": "#/components/schemas/VehicleName"}}, "required": ["id", "locations", "name"], "type": "object"}, "VehicleLocationsResponse": {"description": "Most recent vehicle locations and pagination info.", "properties": {"data": {"description": "List of the most recent locations for the specified vehicles.", "items": {"$ref": "#/components/schemas/VehicleLocationsResponse_data"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "required": ["data", "pagination"], "type": "object"}, "VehicleLocationsResponse_data": {"description": "A vehicle and its most recent location.", "properties": {"id": {"$ref": "#/components/schemas/VehicleId"}, "location": {"$ref": "#/components/schemas/VehicleLocation"}, "name": {"$ref": "#/components/schemas/VehicleName"}}, "required": ["id", "location", "name"], "type": "object"}, "WorkflowAddressEventWithGeofenceObjectResponseBody": {"description": "A minimal Address object representation used in AddressEventObject objects", "properties": {"externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "formattedAddress": {"description": "The full street address for this address/geofence, as it might be recognized by Google Maps.", "example": "350 Rhode Island St, San Francisco, CA", "type": "string"}, "geofence": {"$ref": "#/components/schemas/WorkflowGeofenceResponseBody"}, "id": {"description": "Id of the address", "example": "494123", "type": "string"}, "name": {"description": "Name of the address", "example": "Company Office #1", "type": "string"}}, "required": ["formattedAddress", "id", "name"], "type": "object"}, "WorkflowGeofenceEventResponseObjectResponseBody": {"properties": {"address": {"$ref": "#/components/schemas/WorkflowAddressEventWithGeofenceObjectResponseBody"}, "fuelVolume": {"$ref": "#/components/schemas/fuelVolumeResponseBody"}, "vehicle": {"$ref": "#/components/schemas/VehicleWithGatewayTinyResponseResponseBody"}}, "type": "object"}, "WorkflowGeofenceResponseBody": {"description": "The geofence that defines this address and its bounds. This can either be a circle or a polygon, but not both.", "properties": {"circle": {"$ref": "#/components/schemas/WorkflowCircleResponseBody"}, "polygon": {"$ref": "#/components/schemas/WorkflowPolygonResponseBody"}, "settings": {"$ref": "#/components/schemas/SettingsResponseBody"}}, "type": "object"}, "addressTinyResponse": {"description": "Address book entry, if one exists", "properties": {"id": {"description": "Address book identifier", "example": "123", "type": "string"}, "name": {"description": "Name of this address book entry", "example": "Main Distribution Warehouse", "type": "string"}}, "type": "object"}, "jobLocationResponseObjectResponseBody": {"description": "jobLocation object", "properties": {"address": {"description": "Address of a location", "example": "1990 Alameda st, San Francisco, Ca 94103", "type": "string"}, "latitude": {"description": "Latitude of a location", "example": 37.456345, "format": "double", "type": "number"}, "longitude": {"description": "Longitude of a location", "example": 34.5633749, "format": "double", "type": "number"}, "name": {"description": "Name of a location", "example": "Worksite #1", "type": "string"}}, "required": ["address", "latitude", "longitude", "name"], "type": "object"}, "location": {"description": "Location object", "properties": {"latitude": {"description": "GPS latitude represented in degrees", "example": 122.142, "format": "double", "type": "number"}, "longitude": {"description": "GPS longitude represented in degrees", "example": -93.343, "format": "double", "type": "number"}}, "required": ["latitude", "longitude"], "type": "object"}}, "securitySchemes": {"AccessTokenHeader": {"type": "http", "scheme": "bearer"}}}, "paths": {"/addresses": {"get": {"description": "Returns a list of all addresses in an organization. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Addresses** under the Addresses category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "listAddresses", "parameters": [{"description": "The limit for how many objects will be in the response. Default and max for this value is 512 objects.", "in": "query", "name": "limit", "schema": {"format": "int64", "maximum": 512, "minimum": 1, "type": "integer"}}, {"description": "If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "explode": false, "in": "query", "name": "parentTagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "explode": false, "in": "query", "name": "tagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on data to have a created at time after or equal to this specified time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "createdAfterTime", "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"example": {"data": [{"addressTypes": ["yard"], "contacts": [{"firstName": "<PERSON>", "id": "22408", "lastName": "<PERSON>"}], "createdAtTime": "2019-05-18T20:27:35Z", "externalIds": {"maintenanceId": "250020", "payrollId": "ABFS18600"}, "formattedAddress": "350 Rhode Island St, San Francisco, CA", "geofence": {"circle": {"latitude": 37.765363, "longitude": -122.4029238, "radiusMeters": 25}, "polygon": {"vertices": [{"latitude": 37.765363, "longitude": -122.403098}, {"latitude": 38.765363, "longitude": -122.403098}, {"latitude": 37.765363, "longitude": -123.403098}]}}, "id": "22408", "latitude": 37.765363, "longitude": -122.4029238, "name": "Samsara HQ", "notes": "Hours of operation: 8am - 6pm; Truck entrance on the Rhode Island street side.", "tags": [{"id": "3914", "name": "East Coast", "parentTagId": "4815"}]}], "pagination": {"endCursor": "MjkY", "hasNextPage": true}}, "schema": {"$ref": "#/components/schemas/ListAddressesResponse"}}}, "description": "List of all addresses in the organization"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "List all addresses", "tags": ["Addresses"]}, "post": {"description": "Creates a new address in the organization. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Addresses** under the Addresses category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "createAddress", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAddressRequest"}}}, "description": "The address to create.", "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddressResponse"}}}, "description": "Newly created address object with ID."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Create an address", "tags": ["Addresses"], "x-codegen-request-body-name": "address"}}, "/addresses/{id}": {"delete": {"description": "Delete a specific address. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Addresses** under the Addresses category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "deleteAddress", "parameters": [{"description": "ID of the Address. This can either be the Samsara-provided ID or an external ID. External IDs are customer-specified key-value pairs created in the POST or PATCH requests of this resource. To specify an external ID as part of a path parameter, use the following format: `key:value`. For example, `crmId:abc123`", "in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardDeleteResponse"}}}, "description": "A successful DELETE response is a 204 with no content."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "Delete an address", "tags": ["Addresses"]}, "get": {"description": "Returns a specific address. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Addresses** under the Addresses category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "get<PERSON><PERSON><PERSON>", "parameters": [{"description": "ID of the Address. This can either be the Samsara-provided ID or an external ID. External IDs are customer-specified key-value pairs created in the POST or PATCH requests of this resource. To specify an external ID as part of a path parameter, use the following format: `key:value`. For example, `crmId:abc123`", "in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddressResponse"}}}, "description": "An Address."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Retrieve an address", "tags": ["Addresses"]}, "patch": {"description": "Update a specific address. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Addresses** under the Addresses category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "updateAddress", "parameters": [{"description": "ID of the Address. This can either be the Samsara-provided ID or an external ID. External IDs are customer-specified key-value pairs created in the POST or PATCH requests of this resource. To specify an external ID as part of a path parameter, use the following format: `key:value`. For example, `crmId:abc123`", "in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAddressRequest"}}}, "description": "The address fields to update.", "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddressResponse"}}}, "description": "Updated address object with ID."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Update an address", "tags": ["Addresses"], "x-codegen-request-body-name": "address"}}}, "tags": [{"name": "Addresses"}]}