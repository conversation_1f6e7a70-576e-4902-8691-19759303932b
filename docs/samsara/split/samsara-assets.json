{"openapi": "3.0.1", "info": {"description": "Samsara API specification for assets related endpoints and schemas.", "title": "Samsara API - Assets", "version": "2025-05-12"}, "servers": [{"url": "https://api.samsara.com/"}, {"url": "https://api.eu.samsara.com/"}], "security": [{"AccessTokenHeader": []}], "components": {"parameters": {"V1assetHistoryEndTimeParam": {"description": "Timestamp in milliseconds representing the end of the period to fetch, inclusive. Used in combination with startMs.", "in": "query", "name": "endMs", "required": true, "schema": {"format": "int64", "type": "integer"}}, "V1assetHistoryStartTimeParam": {"description": "Timestamp in milliseconds representing the start of the period to fetch, inclusive. Used in combination with endMs.", "in": "query", "name": "startMs", "required": true, "schema": {"format": "int64", "type": "integer"}}, "V1assetIdParam": {"description": "ID of the asset. Must contain only digits 0-9.", "in": "path", "name": "asset_id", "required": true, "schema": {"format": "int64", "type": "integer"}}, "equipmentStatTypes": {"description": "The types of equipment stats you want to query. Currently, you may submit up to 4 types.\r\n\r\n- `engineRpm`: The revolutions per minute of the engine.\r\n- `fuelPercents`: The percent of fuel in the unit of equipment.\r\n- `obdEngineSeconds`: The number of seconds the engine has been running as reported directly from on-board diagnostics. This is supported with the following hardware configurations: AG24/AG26 + AOPEN/A9PIN/ACT9/ACT14.\r\n- `gatewayEngineSeconds`: An approximation of the number of seconds the engine has been running since it was new, based on the amount of time the asset gateway has been receiving power with an offset provided manually through the Samsara cloud dashboard. This is supported with the following hardware configurations: \r\n  - AG24/AG26/AG46P + APWR cable ([Auxiliary engine configuration](https://kb.samsara.com/hc/en-us/articles/************-Auxiliary-Inputs#UUID-d514abff-d10a-efaf-35d9-e10fa6c4888d) required) \r\n  - AG52 + BPWR/BEQP cable ([Auxiliary engine configuration](https://kb.samsara.com/hc/en-us/articles/************-Auxiliary-Inputs#UUID-d514abff-d10a-efaf-35d9-e10fa6c4888d) required). \r\n- `gatewayJ1939EngineSeconds`: An approximation of the number of seconds the engine has been running since it was new, based on the amount of time the AG26 device is receiving power via J1939/CAT cable and an offset provided manually through the Samsara cloud dashboard.\r\n- `obdEngineStates`: The state of the engine read from on-board diagnostics. Can be `Off`, `On`, or `Idle`.\r\n- `gatewayEngineStates`: An approximation of engine state based on readings the AG26 receives from the aux/digio cable. Can be `Off` or `On`.\r\n- `gpsOdometerMeters`: An approximation of odometer reading based on GPS calculations since the AG26 was activated, and a manual odometer offset provided in the Samsara cloud dashboard. Valid values: `Off`, `On`.\r\n- `gps`: GPS data including lat/long, heading, speed, address book entry (if exists), and a reverse geocoded address. \r\n- `engineTotalIdleTimeMinutes`: Total time in minutes that the engine has been idling.", "explode": false, "in": "query", "name": "types", "required": true, "schema": {"items": {"enum": ["gatewayEngineStates", "obdEngineStates", "fuelPercents", "engineRpm", "gatewayEngineSeconds", "obdEngineSeconds", "gatewayJ1939EngineSeconds", "gpsOdometerMeters", "gps", "engineTotalIdleTimeMinutes"], "format": "string", "type": "string"}, "type": "array"}, "style": "form"}}, "requestBodies": {}, "schemas": {"AempEquipmentGetAempEquipmentListBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AempEquipmentGetAempEquipmentListBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AempEquipmentGetAempEquipmentListGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AempEquipmentGetAempEquipmentListInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AempEquipmentGetAempEquipmentListMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AempEquipmentGetAempEquipmentListNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AempEquipmentGetAempEquipmentListNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AempEquipmentGetAempEquipmentListResponseBody": {"properties": {"Fleet": {"$ref": "#/components/schemas/AempFleetListResponseBody"}}, "required": ["Fleet"], "type": "object"}, "AempEquipmentGetAempEquipmentListServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AempEquipmentGetAempEquipmentListTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AempEquipmentGetAempEquipmentListUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AempEquipmentWithAdditionalFieldsResponseBody": {"description": "Contains equipment fields.", "properties": {"CumulativeOperatingHours": {"$ref": "#/components/schemas/CumulativeOperatingHoursResponseBody"}, "DEFRemaining": {"$ref": "#/components/schemas/DEFRemainingResponseBody"}, "Distance": {"$ref": "#/components/schemas/DistanceResponseBody"}, "EngineStatus": {"$ref": "#/components/schemas/EngineStatusResponseBody"}, "EquipmentHeader": {"$ref": "#/components/schemas/EquipmentHeaderWithAdditionalFieldsResponseBody"}, "FuelRemaining": {"$ref": "#/components/schemas/FuelRemainingResponseBody"}, "Location": {"$ref": "#/components/schemas/LocationResponseBody"}}, "required": ["EquipmentHeader", "Location"], "type": "object"}, "AempFleetListResponseBody": {"description": "Contains a list of equipment objects and links", "properties": {"Equipment": {"description": "The list of Equipment objects.", "items": {"$ref": "#/components/schemas/AempEquipmentWithAdditionalFieldsResponseBody"}, "type": "array"}, "Links": {"description": "The list of links associated with the current API request.", "items": {"$ref": "#/components/schemas/AempLinkResponseBody"}, "type": "array"}, "snapshotTime": {"description": "Date and time at which the snapshot of the fleet was created in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "type": "string"}, "version": {"description": "The version of the ISO/TS 15143-3 standard", "example": "1", "type": "string"}}, "required": ["Equipment", "Links", "snapshotTime", "version"], "type": "object"}, "AempLinkResponseBody": {"description": "Contains a list of relevant links", "properties": {"href": {"description": "The hyperlink of the relationship.", "example": "https://api.samsara.com/aemp/Fleet/1", "type": "string"}, "rel": {"description": "The link relationship to the current call.", "example": "self", "type": "string"}}, "required": ["href", "rel"], "type": "object"}, "AssetCreate": {"description": "The asset creation arguments", "properties": {"customMetadata": {"$ref": "#/components/schemas/CustomMetadata"}, "location": {"$ref": "#/components/schemas/AssetLocation"}, "locationDataInputId": {"description": "Required if locationType is \"dataInput\". Specifies the id of a location data input which will determine the asset's location. **The data input will be moved to the new asset.**", "example": "12345", "type": "string"}, "locationType": {"$ref": "#/components/schemas/LocationType"}, "name": {"$ref": "#/components/schemas/AssetName"}, "parentId": {"$ref": "#/components/schemas/ParentId"}, "runningStatusDataInputId": {"description": "The asset's isRunning status will be true when the associated data input's value is 1. Data input cannot be of location format. **The data input will be moved to the new asset.**", "example": "67890", "type": "string"}, "tagIds": {"$ref": "#/components/schemas/TagIds"}}, "required": ["name"], "type": "object"}, "AssetDataInput": {"properties": {"dataGroup": {"description": "Name of the data group that the data input is associated with", "example": "Pressure", "type": "string"}, "id": {"description": "ID of the data input", "example": "123456", "type": "string"}, "lastPoint": {"$ref": "#/components/schemas/AssetDataInput_lastPoint"}, "name": {"description": "Name of the data input", "example": "Digital Input 1", "type": "string"}, "units": {"description": "Units of data for this data input", "example": "PSI", "type": "string"}}, "type": "object"}, "AssetDataInput_lastPoint": {"description": "The last reported point of a data input.", "properties": {"time": {"$ref": "#/components/schemas/time"}, "value": {"description": "Numeric value of the data point.", "example": 1992.0506, "format": "double", "type": "number"}}, "type": "object"}, "AssetDataOutput": {"properties": {"dataGroup": {"description": "Name of the data group that the data output is associated with", "example": "Control Pressure", "type": "string"}, "dataInput": {"$ref": "#/components/schemas/AssetDataInput"}, "deviceId": {"description": "ID of the device that the data output is configured on", "example": "123", "type": "string"}, "id": {"description": "ID of the data output", "example": "3fa85f64-5717-4562-b3fc-2c963f66afa6", "type": "string"}, "name": {"description": "Name of the data output", "example": "Digital Output 1", "type": "string"}}, "type": "object"}, "AssetDataOutputsPatchAssetDataOutputsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetDataOutputsPatchAssetDataOutputsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetDataOutputsPatchAssetDataOutputsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetDataOutputsPatchAssetDataOutputsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetDataOutputsPatchAssetDataOutputsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetDataOutputsPatchAssetDataOutputsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetDataOutputsPatchAssetDataOutputsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetDataOutputsPatchAssetDataOutputsRequestBody": {"properties": {"values": {"description": "A map of data output IDs to values. All data outputs must belong to the same asset. Only the specified IDs will be written to.", "example": "", "type": "object"}}, "required": ["values"], "type": "object"}, "AssetDataOutputsPatchAssetDataOutputsResponseBody": {"properties": {"data": {"description": "List of responses for each data output from the original request.", "items": {"$ref": "#/components/schemas/PatchAssetDataOutputsSingleResponseResponseBody"}, "type": "array"}}, "required": ["data"], "type": "object"}, "AssetDataOutputsPatchAssetDataOutputsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetDataOutputsPatchAssetDataOutputsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetDataOutputsPatchAssetDataOutputsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetName": {"description": "The name of the asset.", "type": "string"}, "AssetPatch": {"description": "The asset creation arguments", "properties": {"customMetadata": {"$ref": "#/components/schemas/CustomMetadata"}, "location": {"$ref": "#/components/schemas/AssetLocation"}, "locationDataInputId": {"description": "Required if locationType is \"dataInput\". Specifies the id of a location data input which will determine the asset's location. The data input must be in the asset.", "example": "12345", "type": "string"}, "locationType": {"$ref": "#/components/schemas/LocationType"}, "name": {"$ref": "#/components/schemas/AssetName"}, "parentId": {"description": "The id of the parent asset that the asset belongs to. Pass in an empty string to remove the child from the parent.", "example": "", "type": "string"}, "runningStatusDataInputId": {"description": "The asset's isRunning status will be true when the associated data input's value is 1. Data input cannot be of location format. The data input must be in the asset.", "example": "67890", "type": "string"}, "tagIds": {"$ref": "#/components/schemas/TagIds"}}, "type": "object"}, "AssetResponse": {"description": "<PERSON><PERSON>", "properties": {"customMetadata": {"$ref": "#/components/schemas/CustomMetadata"}, "dataOutputs": {"description": "The list of data outputs configured on the asset.", "items": {"$ref": "#/components/schemas/AssetDataOutput"}, "type": "array"}, "id": {"description": "The id of the asset", "example": "123abcde-4567-8910-1112-fghi1314jklm", "type": "string"}, "isRunning": {"description": "The running status of the asset. Returns True for On, and False for Off.", "type": "boolean"}, "location": {"$ref": "#/components/schemas/AssetLocation"}, "locationDataInput": {"$ref": "#/components/schemas/AssetResponse_locationDataInput"}, "locationType": {"$ref": "#/components/schemas/LocationType"}, "name": {"$ref": "#/components/schemas/AssetName"}, "parentAsset": {"$ref": "#/components/schemas/AssetResponse_parentAsset"}, "runningStatusDataInput": {"$ref": "#/components/schemas/AssetResponse_runningStatusDataInput"}, "tags": {"description": "The list of [tags](https://kb.samsara.com/hc/en-us/articles/360026674631-Using-Tags-and-Tag-Nesting) associated with the Industrial Asset. **By default**: empty. Can be set or updated through the Samsara Dashboard or the API at any time.", "items": {"$ref": "#/components/schemas/tagTinyResponse"}, "type": "array"}}, "required": ["id", "isRunning", "name"], "type": "object"}, "AssetResponseBody": {"description": "Representation of a vehicle trailer or other equipment to be tracked.", "properties": {"createdAtTime": {"description": "The time the asset was created in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "The unique ID of the asset.", "example": "12345", "type": "string"}, "licensePlate": {"description": "The license plate of the asset.", "example": "XHK1234", "type": "string"}, "make": {"description": "The OEM/manufacturer of the asset. Updates to this field are restricted.", "example": "Bobcat", "type": "string"}, "model": {"description": "The model of the asset. Updates to this field are restricted.", "example": "S630 T4", "type": "string"}, "name": {"description": "The human-readable name of the asset. This is set by a fleet administrator and will appear in both Samsara’s cloud dashboard as well as the Samsara Driver mobile app. By default, this name is the serial number of the Samsara Asset Gateway. It can be set or updated through the Samsara Dashboard or through the API at any time.", "example": "MyAsset-1234", "type": "string"}, "notes": {"description": "These are generic notes about the asset. Can be set or updated through the Samsara Dashboard or the API at any time.", "example": "These are notes.", "type": "string"}, "regulationMode": {"description": "Whether or not the asset is regulated, unregulated (non-CMV), or a mixed use unregulated asset. Primarily used with vehicles.  Valid values: `mixed`, `regulated`, `unregulated`", "enum": ["mixed", "regulated", "unregulated"], "example": "mixed", "type": "string"}, "serialNumber": {"description": "The serial number of the asset. This can be an internal serial number or used to hold legacy VIN/PIN numbers such as ones of shorter lengths.", "example": "LN016251", "type": "string"}, "tags": {"description": "The list of [tags](https://kb.samsara.com/hc/en-us/articles/360026674631-Using-Tags-and-Tag-Nesting) associated with the Asset.", "items": {"$ref": "#/components/schemas/GoaTagTinyResponseResponseBody"}, "type": "array"}, "type": {"default": "uncategorized", "description": "The operational context in which the asset interacts with the Samsara system. Examples: Vehicle (eg: truck, bus...), Trailer (eg: dry van, reefer, flatbed...), Powered Equipment (eg: dozer, crane...), Unpowered Equipment (eg: container, dumpster...), or Uncategorized.  Valid values: `uncategorized`, `trailer`, `equipment`, `unpowered`, `vehicle`", "enum": ["uncategorized", "trailer", "equipment", "unpowered", "vehicle"], "example": "trailer", "type": "string"}, "updatedAtTime": {"description": "The time the asset was last updated in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "vin": {"description": "The unique 17-digit VIN (Vehicle Identification Number) or PIN (Product Identification Number) of the asset.", "example": "1FUJBBCKXCLBZ1234", "type": "string"}, "year": {"description": "The model year of the asset. Updates to this field are restricted.", "example": 2015, "format": "int64", "type": "integer"}}, "required": ["createdAtTime", "id", "updatedAtTime"], "type": "object"}, "AssetResponseResponseBody": {"description": "Asset that the location readings are tied to.", "properties": {"externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "ID of the asset", "example": "12345", "type": "string"}}, "required": ["id"], "type": "object"}, "AssetResponse_parentAsset": {"description": "The asset's parent", "properties": {"id": {"$ref": "#/components/schemas/ParentId"}, "name": {"$ref": "#/components/schemas/AssetName"}}, "required": ["id", "name"], "type": "object"}, "AssetResponse_runningStatusDataInput": {"description": "The associated running status data input. isRunning will be true when the data input's value is 1.", "properties": {"id": {"description": "Id of the data input", "example": "12345", "type": "string"}}, "required": ["id"], "type": "object"}, "AssetsCreateAssetBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsCreateAssetBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsCreateAssetGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsCreateAssetInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsCreateAssetMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsCreateAssetNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsCreateAssetNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsCreateAssetRequestBody": {"description": "Representation of a vehicle trailer or other equipment to be tracked.", "properties": {"externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "licensePlate": {"description": "The license plate of the asset.", "example": "XHK1234", "type": "string"}, "make": {"description": "The OEM/manufacturer of the asset. Updates to this field are restricted.", "example": "Bobcat", "type": "string"}, "model": {"description": "The model of the asset. Updates to this field are restricted.", "example": "S630 T4", "type": "string"}, "name": {"description": "The human-readable name of the asset. This is set by a fleet administrator and will appear in both Samsara’s cloud dashboard as well as the Samsara Driver mobile app. By default, this name is the serial number of the Samsara Asset Gateway. It can be set or updated through the Samsara Dashboard or through the API at any time.", "example": "MyAsset-1234", "type": "string"}, "notes": {"description": "These are generic notes about the asset. Can be set or updated through the Samsara Dashboard or the API at any time.", "example": "These are notes.", "type": "string"}, "regulationMode": {"description": "Whether or not the asset is regulated, unregulated (non-CMV), or a mixed use unregulated asset. Primarily used with vehicles.  Valid values: `mixed`, `regulated`, `unregulated`", "enum": ["mixed", "regulated", "unregulated"], "example": "mixed", "type": "string"}, "serialNumber": {"description": "The serial number of the asset. This can be an internal serial number or used to hold legacy VIN/PIN numbers such as ones of shorter lengths.", "example": "LN016251", "type": "string"}, "type": {"default": "uncategorized", "description": "The operational context in which the asset interacts with the Samsara system. Examples: Vehicle (eg: truck, bus...), Trailer (eg: dry van, reefer, flatbed...), Powered Equipment (eg: dozer, crane...), Unpowered Equipment (eg: container, dumpster...), or Uncategorized.  Valid values: `uncategorized`, `trailer`, `equipment`, `unpowered`, `vehicle`", "enum": ["uncategorized", "trailer", "equipment", "unpowered", "vehicle"], "example": "trailer", "type": "string"}, "vin": {"description": "The unique 17-digit VIN (Vehicle Identification Number) or PIN (Product Identification Number) of the asset.", "example": "1FUJBBCKXCLBZ1234", "type": "string"}, "year": {"description": "The model year of the asset. Updates to this field are restricted.", "example": 2015, "format": "int64", "type": "integer"}}, "type": "object"}, "AssetsCreateAssetResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/AssetResponseBody"}}, "required": ["data"], "type": "object"}, "AssetsCreateAssetServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsCreateAssetTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsCreateAssetUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsDeleteAssetBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsDeleteAssetBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsDeleteAssetGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsDeleteAssetInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsDeleteAssetMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsDeleteAssetNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsDeleteAssetNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsDeleteAssetServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsDeleteAssetTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsDeleteAssetUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsInputsAssetResponseResponseBody": {"description": "Asset that the input data is from.", "properties": {"attributes": {"description": "List of attributes associated with the entity", "items": {"$ref": "#/components/schemas/GoaAttributeTinyResponseBody"}, "type": "array"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "ID of the asset", "example": "12345", "type": "string"}, "tags": {"description": "The array of [tags](https://kb.samsara.com/hc/en-us/articles/360026674631-Using-Tags-and-Tag-Nesting) associated with the Asset.", "items": {"$ref": "#/components/schemas/GoaTagTinyResponseResponseBody"}, "type": "array"}}, "required": ["id"], "type": "object"}, "AssetsInputsGetAssetsInputsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsInputsGetAssetsInputsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsInputsGetAssetsInputsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsInputsGetAssetsInputsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsInputsGetAssetsInputsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsInputsGetAssetsInputsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsInputsGetAssetsInputsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsInputsGetAssetsInputsResponseBody": {"properties": {"data": {"description": "Array of assets inputs objects.", "items": {"$ref": "#/components/schemas/assetsInputsResponseResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "AssetsInputsGetAssetsInputsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsInputsGetAssetsInputsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsInputsGetAssetsInputsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsListAssetsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsListAssetsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsListAssetsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsListAssetsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsListAssetsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsListAssetsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsListAssetsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsListAssetsResponseBody": {"properties": {"data": {"description": "List of assets", "items": {"$ref": "#/components/schemas/AssetResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "AssetsListAssetsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsListAssetsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsListAssetsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsOnRouteLinkConfigObject": {"description": "Configuration details specific to the 'By Recurring Route' Live Sharing Link.", "properties": {"recurringRouteId": {"description": "Samsara ID of the recurring route.", "example": "1234", "type": "string"}}, "required": ["recurringRouteId"], "type": "object"}, "AssetsOnRouteLinkConfigObjectResponseBody": {"description": "Configuration details specific to the 'By Recurring Route' Live Sharing Link.", "properties": {"recurringRouteId": {"description": "Samsara ID of the recurring route.", "example": "1234", "type": "string"}}, "required": ["recurringRouteId"], "type": "object"}, "AssetsUpdateAssetBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsUpdateAssetBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsUpdateAssetGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsUpdateAssetInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsUpdateAssetMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsUpdateAssetNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsUpdateAssetNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsUpdateAssetRequestBody": {"description": "Representation of a vehicle trailer or other equipment to be tracked.", "properties": {"externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "licensePlate": {"description": "The license plate of the asset.", "example": "XHK1234", "type": "string"}, "make": {"description": "The OEM/manufacturer of the asset. Updates to this field are restricted.", "example": "Bobcat", "type": "string"}, "model": {"description": "The model of the asset. Updates to this field are restricted.", "example": "S630 T4", "type": "string"}, "name": {"description": "The human-readable name of the asset. This is set by a fleet administrator and will appear in both Samsara’s cloud dashboard as well as the Samsara Driver mobile app. By default, this name is the serial number of the Samsara Asset Gateway. It can be set or updated through the Samsara Dashboard or through the API at any time.", "example": "MyAsset-1234", "type": "string"}, "notes": {"description": "These are generic notes about the asset. Can be set or updated through the Samsara Dashboard or the API at any time.", "example": "These are notes.", "type": "string"}, "regulationMode": {"description": "Whether or not the asset is regulated, unregulated (non-CMV), or a mixed use unregulated asset. Primarily used with vehicles.  Valid values: `mixed`, `regulated`, `unregulated`", "enum": ["mixed", "regulated", "unregulated"], "example": "mixed", "type": "string"}, "serialNumber": {"description": "The serial number of the asset. This can be an internal serial number or used to hold legacy VIN/PIN numbers such as ones of shorter lengths.", "example": "LN016251", "type": "string"}, "type": {"description": "The operational context in which the asset interacts with the Samsara system. Examples: Vehicle (eg: truck, bus...), Trailer (eg: dry van, reefer, flatbed...), Powered Equipment (eg: dozer, crane...), Unpowered Equipment (eg: container, dumpster...), or Uncategorized.  Valid values: `uncategorized`, `trailer`, `equipment`, `unpowered`, `vehicle`", "enum": ["uncategorized", "trailer", "equipment", "unpowered", "vehicle"], "example": "trailer", "type": "string"}, "vin": {"description": "The unique 17-digit VIN (Vehicle Identification Number) or PIN (Product Identification Number) of the asset.", "example": "1FUJBBCKXCLBZ1234", "type": "string"}, "year": {"description": "The model year of the asset. Updates to this field are restricted.", "example": 2015, "format": "int64", "type": "integer"}}, "type": "object"}, "AssetsUpdateAssetResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/AssetResponseBody"}}, "required": ["data"], "type": "object"}, "AssetsUpdateAssetServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsUpdateAssetTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "AssetsUpdateAssetUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DeviceAssetResponseResponseBody": {"description": "Asset that the device is tied to.", "properties": {"id": {"description": "The unique ID of the asset.", "example": "281474982859091", "type": "string"}, "name": {"description": "The human-readable name of the asset. This is set by a fleet administrator and will appear in both Samsara’s cloud dashboard as well as the Samsara Driver mobile app. By default, this name is the serial number of the Samsara Asset Gateway. It can be set or updated through the Samsara Dashboard or through the API at any time.", "example": "MyAsset-1234", "type": "string"}}, "required": ["id", "name"], "type": "object"}, "Equipment": {"description": "An equipment object.", "properties": {"assetSerial": {"description": "An equipment identification number.", "example": "1FUJA6BD31LJ09646", "type": "string"}, "externalIds": {"$ref": "#/components/schemas/carrierProposedAssignmentDriverAllOf2ExternalIds"}, "id": {"$ref": "#/components/schemas/EquipmentId"}, "installedGateway": {"$ref": "#/components/schemas/Equipment_installedGateway"}, "name": {"$ref": "#/components/schemas/EquipmentName"}, "notes": {"description": "Notes about a piece of equipment. Samsara supports a maximum of 255 chars.", "example": "These are notes about this given equipment.", "maxLength": 255, "type": "string"}, "tags": {"description": "An array of all tag mini-objects that are associated with the given equipment.", "items": {"$ref": "#/components/schemas/tagTinyResponse"}, "type": "array"}}, "required": ["id"], "type": "object"}, "EquipmentEngineRpm": {"description": "Engine RPM reading.", "properties": {"time": {"$ref": "#/components/schemas/EquipmentTime"}, "value": {"description": "The revolutions per minute of the engine.", "example": 1800, "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "EquipmentEngineSeconds": {"description": "[DEPRECATED] Please use either `gatewayEngineSeconds` or `obdEngineSeconds`.", "properties": {"time": {"$ref": "#/components/schemas/EquipmentTime"}, "value": {"description": "An approximation of the number of seconds the engine has been running since it was new, based on the amount of time the AG26 device is receiving power and an offset provided manually through the Samsara cloud dashboard.", "example": 22374000, "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "EquipmentEngineState": {"description": "[DEPRECATED] Please use either `gatewayEngineStates` or `obdEngineStates`.", "properties": {"time": {"$ref": "#/components/schemas/EquipmentTime"}, "value": {"description": "An approximation of engine state based on readings the AG26 receives from the aux/digio cable. Valid values: `Off`, `On`.", "enum": ["Off", "On"], "example": "On", "type": "string"}}, "required": ["time", "value"], "type": "object"}, "EquipmentEngineTotalIdleTimeMinutes": {"description": "A time-series of engine total idle time minutes. (Beta)", "properties": {"time": {"$ref": "#/components/schemas/EquipmentTime"}, "value": {"description": "Total time in minutes that the engine has been idling since the device was installed. This value is calculated based on the time the engine has been running while the vehicle is not in motion.", "example": 1000, "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "EquipmentFuelPercent": {"description": "Fuel percent reading.", "properties": {"time": {"$ref": "#/components/schemas/EquipmentTime"}, "value": {"description": "The percent of fuel in the unit of equipment.", "example": 54, "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "EquipmentGatewayEngineSeconds": {"description": "Engine seconds reading from the aux/digio cable.", "properties": {"time": {"$ref": "#/components/schemas/EquipmentTime"}, "value": {"description": "The number of seconds an engine has been running as detected via engine state. Used in combination with an offset provided manually through the Samsara cloud dashboard. Useful for when assets do not report engine hours over the J1939 network. The Engine Speed SPN must be available from the ECU for this parameter to properly calculate seconds. This is supported with the following hardware configurations: AG24/AG26 + AOPEN/A9PIN/ACT9/ACT14.", "example": 22374000, "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "EquipmentGatewayEngineState": {"description": "Engine state reading from the aux/digio cable.", "properties": {"time": {"$ref": "#/components/schemas/EquipmentTime"}, "value": {"description": "An approximation of engine state based on readings the AG26 receives from the aux/digio cable. Valid values: `Off`, `On`.", "enum": ["Off", "On"], "example": "On", "type": "string"}}, "required": ["time", "value"], "type": "object"}, "EquipmentGatewayJ1939EngineSeconds": {"description": "Engine seconds reading from the J1939/CAT cable.", "properties": {"time": {"$ref": "#/components/schemas/EquipmentTime"}, "value": {"description": "The number of seconds an engine has been running as detected via aux input 1 active state. Used in combination with an offset provided manually through the Samsara cloud dashboard. This is supported with the following hardware configurations: AG24/AG26/AG46P + APWR cable (Auxiliary engine configuration required) AG52/AG53 + BPWR/BEQP cable (Auxiliary engine configuration required).", "example": 22374000, "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "EquipmentGpsOdometerMeters": {"description": "GPS odometer reading.", "properties": {"time": {"$ref": "#/components/schemas/EquipmentTime"}, "value": {"description": "An approximation of odometer reading based on GPS calculations since the AG26 was activated, and a manual odometer offset provided in the Samsara cloud dashboard.", "example": 3200, "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "EquipmentHeaderWithAdditionalFieldsResponseBody": {"description": "Equipment header fields.", "properties": {"EquipmentID": {"description": "The unique Samsara ID of the equipment. This is automatically generated when the Equipment object is created. It cannot be changed.", "example": "494123", "type": "string"}, "Model": {"description": "The model of the equipment.", "example": "S630 T4", "type": "string"}, "OEMName": {"description": "The make of the equipment.", "example": "Bobcat", "type": "string"}, "PIN": {"description": "The PIN number of the equipment.", "example": "8V8WD530FLN016251", "type": "string"}, "SerialNumber": {"description": "The serial number of the equipment.", "example": "8V8WD530FLN016251", "type": "string"}, "UnitInstallDateTime": {"description": "Telematics unit install date in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "example": "2019-06-13T19:08:25Z", "type": "string"}}, "type": "object"}, "EquipmentId": {"description": "Unique Samsara ID for the equipment.", "example": "112", "type": "string"}, "EquipmentListResponse": {"description": "List of all equipment objects, and pagination information.", "properties": {"data": {"description": "List of equipment objects.", "items": {"$ref": "#/components/schemas/Equipment"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "required": ["data", "pagination"], "type": "object"}, "EquipmentName": {"description": "Name of the equipment.", "example": "Crane A7", "type": "string"}, "EquipmentObdEngineSeconds": {"description": "Engine seconds reading from on-board diagnostics.", "properties": {"time": {"$ref": "#/components/schemas/EquipmentTime"}, "value": {"description": "The number of seconds the engine has been running as reported directly from on-board diagnostics. This is supported with the following hardware configurations: AG24/AG26 + AOPEN/A9PIN/ACT9/ACT14", "example": 22374000, "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "EquipmentObdEngineState": {"description": "Engine state reading from on-board diagnostics.", "properties": {"time": {"$ref": "#/components/schemas/EquipmentTime"}, "value": {"description": "The state of the engine read from on-board diagnostics. Valid values: `Off`, `On`, `Idle`.", "enum": ["Off", "On", "Idle"], "example": "On", "type": "string"}}, "required": ["time", "value"], "type": "object"}, "EquipmentPatchEquipmentBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "EquipmentPatchEquipmentBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "EquipmentPatchEquipmentGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "EquipmentPatchEquipmentInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "EquipmentPatchEquipmentMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "EquipmentPatchEquipmentNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "EquipmentPatchEquipmentNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "EquipmentPatchEquipmentRequestBody": {"properties": {"attributes": {"description": "List of attributes associated with the entity", "items": {"$ref": "#/components/schemas/GoaAttributeTiny"}, "type": "array"}, "engineHours": {"description": "When you provide a manual engine hours override, <PERSON><PERSON><PERSON> will begin updating a equipment's engine hours used since this override was set.", "example": 1234, "format": "int64", "type": "integer"}, "equipmentSerialNumber": {"description": "The serial number of the equipment.", "example": "8V8WD530FLN016251", "type": "string"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "The unique Samsara ID of the Equipment. This is automatically generated when the Equipment object is created. It cannot be changed.", "example": "494123", "type": "string"}, "name": {"description": "The human-readable name of the Equipment. This is set by a fleet administrator and will appear in both Samsara’s cloud dashboard as well as the Samsara Driver mobile app. By default, this name is the serial number of the Samsara Asset Gateway. It can be set or updated through the Samsara Dashboard or through the API at any time.", "example": "Equipment-123", "type": "string"}, "notes": {"description": "These are generic notes about the Equipment. Empty by default. Can be set or updated through the Samsara Dashboard or the API at any time.", "example": "These are my equipment notes", "maxLength": 255, "type": "string"}, "odometerMeters": {"description": "When you provide a manual odometer override, <PERSON><PERSON><PERSON> will begin updating a equipment's odometer using GPS distance traveled since this override was set.", "example": 1234, "format": "int64", "type": "integer"}, "tagIds": {"description": "An array of IDs of tags to associate with this equipment. If your access to the API is scoped by one or more tags, this field is required to pass in. ", "example": ["Commodi esse aliquam.", "Qui delectus dolore corporis."], "items": {"example": "Voluptas enim non aliquam praesentium et.", "type": "string"}, "type": "array"}}, "type": "object"}, "EquipmentPatchEquipmentResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/EquipmentWithAttributesResponseObjectResponseBody"}}, "required": ["data"], "type": "object"}, "EquipmentPatchEquipmentServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "EquipmentPatchEquipmentTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "EquipmentPatchEquipmentUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "EquipmentResponse": {"description": "A response containing an equipment object.", "properties": {"data": {"$ref": "#/components/schemas/Equipment"}}, "required": ["data"], "type": "object"}, "EquipmentStatsGps": {"description": "GPS location of equipment.", "properties": {"address": {"$ref": "#/components/schemas/addressTinyResponse"}, "headingDegrees": {"$ref": "#/components/schemas/AssetLocationHeading"}, "latitude": {"description": "GPS latitude represented in degrees", "example": 122.142, "format": "double", "type": "number"}, "longitude": {"description": "GPS longitude represented in degrees", "example": -93.343, "format": "double", "type": "number"}, "reverseGeo": {"$ref": "#/components/schemas/reverseGeo"}, "speedMilesPerHour": {"$ref": "#/components/schemas/AssetLocationSpeed"}, "time": {"$ref": "#/components/schemas/time"}}, "required": ["latitude", "longitude", "time"], "type": "object"}, "EquipmentStatsListResponse": {"description": "A time-series of equipment stats and pagination information", "properties": {"data": {"description": "Time-series of stats for the specified units of equipment and stat types.", "items": {"$ref": "#/components/schemas/EquipmentStatsListResponse_data"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "required": ["data", "pagination"], "type": "object"}, "EquipmentStatsListResponse_data": {"description": "A unit of equipment and its time-series of stats events.", "properties": {"engineRpm": {"description": "A time-series of engine RPM readings for the given unit of equipment.", "items": {"$ref": "#/components/schemas/EquipmentEngineRpm"}, "type": "array"}, "engineSeconds": {"description": "[DEPRECATED] Please use either `gatewayEngineSeconds`, `obdEngineSeconds`, or `gatewayJ1939EngineSeconds`.", "items": {"$ref": "#/components/schemas/EquipmentEngineSeconds"}, "type": "array"}, "engineStates": {"description": "[DEPRECATED] Please use either `gatewayEngineStates` or `obdEngineStates`.", "items": {"$ref": "#/components/schemas/EquipmentEngineState"}, "type": "array"}, "engineTotalIdleTimeMinutes": {"description": "A time-series of engine total idle time minutes. (Beta)", "items": {"$ref": "#/components/schemas/EquipmentEngineTotalIdleTimeMinutes"}, "type": "array"}, "fuelPercents": {"description": "A time-series of fuel percent level changes for the given unit of equipment.", "items": {"$ref": "#/components/schemas/EquipmentFuelPercent"}, "type": "array"}, "gatewayEngineSeconds": {"description": "A time-series of engine seconds readings for the given unit of equipment as an approximate based on readings from the AG26's aux/digio cable.", "items": {"$ref": "#/components/schemas/EquipmentGatewayEngineSeconds"}, "type": "array"}, "gatewayEngineStates": {"description": "A time-series of engine state changes (as read from the AG26's aux/digio cable) for the given unit of equipment.", "items": {"$ref": "#/components/schemas/EquipmentGatewayEngineState"}, "type": "array"}, "gatewayJ1939EngineSeconds": {"description": "A time-series of engine seconds readings for the given unit of equipment as an approximate based on readings from the AG26's CAT/J1939 cable.", "items": {"$ref": "#/components/schemas/EquipmentGatewayJ1939EngineSeconds"}, "type": "array"}, "gps": {"description": "A time-series of GPS locations.", "items": {"$ref": "#/components/schemas/EquipmentStatsGps"}, "type": "array"}, "gpsOdometerMeters": {"description": "A time-series of GPS odometer readings for the given unit of equipment.", "items": {"$ref": "#/components/schemas/EquipmentGpsOdometerMeters"}, "type": "array"}, "id": {"$ref": "#/components/schemas/EquipmentId"}, "name": {"$ref": "#/components/schemas/EquipmentName"}, "obdEngineSeconds": {"description": "A time-series of engine seconds readings for the given unit of equipment directly from on-board diagnostics.", "items": {"$ref": "#/components/schemas/EquipmentObdEngineSeconds"}, "type": "array"}, "obdEngineStates": {"description": "A time-series of engine state changes (as read from on-board diagnostics) for the given unit of equipment.", "items": {"$ref": "#/components/schemas/EquipmentObdEngineState"}, "type": "array"}}, "required": ["id", "name"], "type": "object"}, "EquipmentStatsResponse": {"description": "The most recent equipment stats and pagination information", "properties": {"data": {"description": "List of the most recent stats for the specified units of equipment and stat types.", "items": {"$ref": "#/components/schemas/EquipmentStatsResponse_data"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "required": ["data", "pagination"], "type": "object"}, "EquipmentStatsResponse_data": {"description": "A unit of equipment and its most recent stat.", "properties": {"engineRpm": {"$ref": "#/components/schemas/EquipmentEngineRpm"}, "engineSeconds": {"$ref": "#/components/schemas/EquipmentEngineSeconds"}, "engineState": {"$ref": "#/components/schemas/EquipmentEngineState"}, "engineTotalIdleTimeMinutes": {"$ref": "#/components/schemas/EquipmentEngineTotalIdleTimeMinutes"}, "fuelPercent": {"$ref": "#/components/schemas/EquipmentFuelPercent"}, "gatewayEngineSeconds": {"$ref": "#/components/schemas/EquipmentGatewayEngineSeconds"}, "gatewayEngineState": {"$ref": "#/components/schemas/EquipmentGatewayEngineState"}, "gps": {"$ref": "#/components/schemas/EquipmentStatsGps"}, "gpsOdometerMeters": {"$ref": "#/components/schemas/EquipmentGpsOdometerMeters"}, "id": {"$ref": "#/components/schemas/EquipmentId"}, "name": {"$ref": "#/components/schemas/EquipmentName"}, "obdEngineSeconds": {"$ref": "#/components/schemas/EquipmentObdEngineSeconds"}, "obdEngineState": {"$ref": "#/components/schemas/EquipmentObdEngineState"}}, "required": ["id", "name"], "type": "object"}, "EquipmentTime": {"description": "UTC timestamp of the time the data point was generated by the equipment, in RFC3339 format.", "example": "2019-05-03T04:30:31Z", "type": "string"}, "EquipmentWithAttributesResponseObjectResponseBody": {"description": "The equipment object.", "properties": {"attributes": {"description": "List of attributes associated with the entity", "items": {"$ref": "#/components/schemas/GoaAttributeTinyResponseBody"}, "type": "array"}, "equipmentSerialNumber": {"description": "The serial number of the equipment.", "example": "8V8WD530FLN016251", "type": "string"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "The unique Samsara ID of the Equipment. This is automatically generated when the Equipment object is created. It cannot be changed.", "example": "494123", "type": "string"}, "installedGateway": {"$ref": "#/components/schemas/GoaGatewayTinyResponseResponseBody"}, "name": {"description": "The human-readable name of the Equipment. This is set by a fleet administrator and will appear in both Samsara’s cloud dashboard as well as the Samsara Driver mobile app. By default, this name is the serial number of the Samsara Asset Gateway. It can be set or updated through the Samsara Dashboard or through the API at any time.", "example": "Equipment-123", "type": "string"}, "notes": {"description": "These are generic notes about the Equipment. Empty by default. Can be set or updated through the Samsara Dashboard or the API at any time.", "example": "These are my equipment notes", "maxLength": 255, "type": "string"}, "tags": {"description": "The list of [tags](https://kb.samsara.com/hc/en-us/articles/360026674631-Using-Tags-and-Tag-Nesting) associated with the Equipment.", "items": {"$ref": "#/components/schemas/GoaTagTinyResponseResponseBody"}, "type": "array"}}, "type": "object"}, "Equipment_installedGateway": {"properties": {"model": {"description": "The model of the installed Samsara gateway.", "example": "Ag26", "type": "string"}, "serial": {"description": "The serial of the installed Samsara gateway.", "type": "string"}}, "type": "object"}, "FormSubmissionRequestAssetObjectRequestBody": {"description": "Asset object.", "properties": {"id": {"description": "Samsara ID of the asset.", "example": "281474982859091", "type": "string"}}, "required": ["id"], "type": "object"}, "FormSubmissionRequestAssetValueObjectRequestBody": {"description": "The value of an asset form input field. Only valid for asset form input fields.", "properties": {"asset": {"$ref": "#/components/schemas/FormSubmissionRequestAssetObjectRequestBody"}}, "required": ["asset"], "type": "object"}, "FormsAssetObjectResponseBody": {"description": "Tracked or untracked (i.e. manually entered) asset object.", "properties": {"entryType": {"description": "The type of entry for the asset.  Valid values: `tracked`, `untracked`", "enum": ["tracked", "untracked"], "example": "tracked", "type": "string"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "ID of a tracked asset. Included if 'entryType' is `tracked`.", "example": "281474982859091", "type": "string"}, "name": {"description": "Name of an untracked (i.e. manually entered) asset.", "example": "trailer 123", "type": "string"}}, "required": ["entryType"], "type": "object"}, "FormsAssetValueObjectResponseBody": {"description": "The value of an asset form input field.", "properties": {"asset": {"$ref": "#/components/schemas/FormsAssetObjectResponseBody"}}, "required": ["asset"], "type": "object"}, "GatewayAssetResponseObjectResponseBody": {"description": "An object containing information about the asset the gateway is installed on", "properties": {"externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "The unique Samsara ID of the asset where the gateway is installed. This is automatically generated when the asset is created and cannot be changed. Use this ID on PATCH vehicle, equipment, or trailer endpoints to update the asset", "example": "8393848111", "type": "string"}}, "type": "object"}, "ListIndustrialAssetsResponse": {"properties": {"data": {"items": {"$ref": "#/components/schemas/AssetResponse"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "type": "object"}, "PatchAssetDataOutputsSingleResponseResponseBody": {"description": "A response that corresponds to an element in the original request body.", "properties": {"errorMessage": {"description": "If the request failed, this displays the error message.", "example": "Failed to write to <PERSON><PERSON>: GFRV-43N-VGX", "type": "string"}, "id": {"description": "The data output ID.", "example": "8a9371af-82d1-4158-bf91-4ecc8d3a114c", "type": "string"}, "statusCode": {"description": "The status code of the request. 200 indicates the request succeeded for this data output. 500 indicates an internal server error.", "example": 200, "format": "int64", "type": "integer"}}, "required": ["id", "statusCode"], "type": "object"}, "TinyAssetObjectRequestBody": {"description": "Vehicle, trailer or other equipment to be tracked.", "properties": {"assetId": {"description": "ID of the asset.", "example": "12443", "type": "string"}, "assetType": {"default": "uncategorized", "description": "The operational context in which the asset interacts with the Samsara system. Examples: Vehicle (eg: truck, bus...), Trailer (eg: dry van, reefer, flatbed...), Powered Equipment (eg: dozer, crane...), Unpowered Equipment (eg: container, dumpster...), or Uncategorized.  Valid values: `uncategorized`, `trailer`, `equipment`, `unpowered`, `vehicle`", "enum": ["uncategorized", "trailer", "equipment", "unpowered", "vehicle"], "example": "trailer", "type": "string"}}, "required": ["assetId", "assetType"], "type": "object"}, "TinyAssetObjectResponseBody": {"description": "Vehicle, trailer or other equipment to be tracked.", "properties": {"assetId": {"description": "ID of the asset.", "example": "12443", "type": "string"}, "assetType": {"default": "uncategorized", "description": "The operational context in which the asset interacts with the Samsara system. Examples: Vehicle (eg: truck, bus...), Trailer (eg: dry van, reefer, flatbed...), Powered Equipment (eg: dozer, crane...), Unpowered Equipment (eg: container, dumpster...), or Uncategorized.  Valid values: `uncategorized`, `trailer`, `equipment`, `unpowered`, `vehicle`", "enum": ["uncategorized", "trailer", "equipment", "unpowered", "vehicle"], "example": "trailer", "type": "string"}}, "required": ["assetId", "assetType"], "type": "object"}, "TripAssetResponseBody": {"description": "Asset that the location readings are tied to", "properties": {"id": {"description": "Unique ID for the asset object that is reporting the location.", "example": "12345", "type": "string"}, "name": {"description": "Name for the asset object that is reporting the location. Only returns when `includeAsset` is set to `true`.", "example": "MyAsset-1234", "type": "string"}, "type": {"description": "Type for the asset object that is reporting the location. Only returns when `includeAsset` is set to `true`.  Valid values: `uncategorized`, `trailer`, `equipment`, `unpowered`, `vehicle`", "enum": ["uncategorized", "trailer", "equipment", "unpowered", "vehicle"], "example": "trailer", "type": "string"}, "vin": {"description": "VIN for the asset object that is reporting the location. Only returns when `includeAsset` is set to `true`.", "example": "1GBJ6P1B2HV112765", "type": "string"}}, "required": ["id"], "type": "object"}, "V1Asset": {"description": "Basic information of an asset", "properties": {"assetSerialNumber": {"description": "Serial number of the host asset", "example": "SNTEST123", "type": "string"}, "cable": {"$ref": "#/components/schemas/V1Asset_cable"}, "engineHours": {"description": "Engine hours", "example": 104, "type": "integer"}, "id": {"description": "Asset ID", "example": 1, "format": "int64", "type": "integer"}, "name": {"description": "Asset name", "example": "Trailer 123", "type": "string"}, "vehicleId": {"description": "The ID of the Vehicle associated to the Asset (if present)", "example": 2, "format": "int64", "type": "integer"}}, "required": ["id"], "type": "object"}, "V1AssetReeferResponse": {"description": "Reefer-specific asset details", "properties": {"assetType": {"description": "Asset type", "example": "Thermo King", "type": "string"}, "id": {"description": "Asset ID", "example": 1, "type": "integer"}, "name": {"description": "Asset name", "example": "Reefer 123", "type": "string"}, "reeferStats": {"$ref": "#/components/schemas/V1AssetReeferResponse_reeferStats"}}, "type": "object"}, "V1AssetReeferResponse_reeferStats": {"properties": {"alarms": {"description": "Reefer alarms", "items": {"$ref": "#/components/schemas/V1AssetReeferResponse_reeferStats_alarms_1"}, "type": "array"}, "engineHours": {"description": "Engine hours of the reefer", "items": {"$ref": "#/components/schemas/V1AssetReeferResponse_reeferStats_engineHours"}, "type": "array"}, "fuelPercentage": {"description": "Fuel percentage of the reefer", "items": {"$ref": "#/components/schemas/V1AssetReeferResponse_reeferStats_fuelPercentage"}, "type": "array"}, "powerStatus": {"description": "Power status of the reefer", "items": {"$ref": "#/components/schemas/V1AssetReeferResponse_reeferStats_powerStatus"}, "type": "array"}, "returnAirTemp": {"description": "Return air temperature of the reefer", "items": {"$ref": "#/components/schemas/V1AssetReeferResponse_reeferStats_returnAirTemp"}, "type": "array"}, "setPoint": {"description": "Set point temperature of the reefer", "items": {"$ref": "#/components/schemas/V1AssetReeferResponse_reeferStats_setPoint"}, "type": "array"}}, "type": "object"}, "V1AssetReeferResponse_reeferStats_alarms": {"properties": {"alarmCode": {"description": "ID of the alarm", "example": 102, "format": "int64", "type": "integer"}, "description": {"description": "Description of the alarm", "example": "Check Return Air Sensor", "type": "string"}, "operatorAction": {"description": "Recommended operator action", "example": "Check and repair at end of trip", "type": "string"}, "severity": {"description": "Severity of the alarm: 1: OK to run, 2: Check as specified, 3: Take immediate action", "example": 1, "format": "int64", "type": "integer"}}, "type": "object"}, "V1AssetReeferResponse_reeferStats_alarms_1": {"properties": {"alarms": {"items": {"$ref": "#/components/schemas/V1AssetReeferResponse_reeferStats_alarms"}, "type": "array"}, "changedAtMs": {"description": "Timestamp when the alarms were reported, in Unix milliseconds since epoch", "example": 1453449599999, "format": "int64", "type": "integer"}}, "type": "object"}, "V1AssetReeferResponse_reeferStats_engineHours": {"properties": {"changedAtMs": {"description": "Timestamp in Unix milliseconds since epoch.", "example": 1453449599999, "format": "int64", "type": "integer"}, "engineHours": {"description": "Engine hours of the reefer.", "example": 1200, "format": "int64", "type": "integer"}}, "type": "object"}, "V1AssetReeferResponse_reeferStats_fuelPercentage": {"properties": {"changedAtMs": {"description": "Timestamp in Unix milliseconds since epoch.", "example": 1453449599999, "format": "int64", "type": "integer"}, "fuelPercentage": {"description": "Fuel percentage of the reefer.", "example": 99, "format": "int64", "type": "integer"}}, "type": "object"}, "V1AssetReeferResponse_reeferStats_powerStatus": {"properties": {"changedAtMs": {"description": "Timestamp in Unix milliseconds since epoch.", "example": 1453449599999, "format": "int64", "type": "integer"}, "status": {"description": "Power status of the reefer. Valid values: `Off`, `Active`, `Active (Start/Stop)`, `Active (Continuous)`.", "example": "Active (Continuous)", "type": "string"}}, "type": "object"}, "V1AssetReeferResponse_reeferStats_returnAirTemp": {"properties": {"changedAtMs": {"description": "Timestamp in Unix milliseconds since epoch.", "example": 1453449599999, "format": "int64", "type": "integer"}, "tempInMilliC": {"description": "Return air temperature in millidegree Celsius.", "example": 31110, "format": "int64", "type": "integer"}}, "type": "object"}, "V1AssetReeferResponse_reeferStats_setPoint": {"properties": {"changedAtMs": {"description": "Timestamp in Unix milliseconds since epoch.", "example": 1453449599999, "format": "int64", "type": "integer"}, "tempInMilliC": {"description": "Set point temperature in millidegree Celsius.", "example": 31110, "format": "int64", "type": "integer"}}, "type": "object"}, "V1Asset_cable": {"description": "The cable connected to the asset", "properties": {"assetType": {"description": "Asset type", "example": "Thermo King", "type": "string"}}, "type": "object"}, "V1AssetsReefer": {"description": "Reefer-specific details", "properties": {"assetType": {"description": "Asset type", "example": "Thermo King", "type": "string"}, "id": {"description": "Asset ID", "example": 1, "type": "integer"}, "name": {"description": "Asset name", "example": "Reefer 123", "type": "string"}, "reeferStats": {"$ref": "#/components/schemas/V1AssetsReefer_reeferStats"}}, "type": "object"}, "V1AssetsReefer_reeferStats": {"description": "Contains all the state changes of the reefer for the included stat types. Each state change is recorded independently, so the number of records in each array may differ depending on when that stat changed state. Stat types with a continuous value (such as temperature) will be recorded at different rates depending on the reefer, but generally readings have a frequency on the order of seconds.", "properties": {"ambientAirTemperature": {"description": "Ambient temperature of the reefer. This is the temperature of the air around the Samsara Asset Gateway.", "items": {"$ref": "#/components/schemas/V1AssetsReefer_reeferStats_ambientAirTemperature"}, "type": "array"}, "dischargeAirTemperature": {"description": "Discharge air temperature of the reefer. This is the temperature of the air as it leaves the cooling unit.", "items": {"$ref": "#/components/schemas/V1AssetsReefer_reeferStats_dischargeAirTemperature"}, "type": "array"}, "engineHours": {"description": "Engine hours of the reefer", "items": {"$ref": "#/components/schemas/V1AssetReeferResponse_reeferStats_engineHours"}, "type": "array"}, "fuelPercentage": {"description": "Fuel percentage of the reefer", "items": {"$ref": "#/components/schemas/V1AssetReeferResponse_reeferStats_fuelPercentage"}, "type": "array"}, "powerStatus": {"description": "Power status of the reefer", "items": {"$ref": "#/components/schemas/V1AssetsReefer_reeferStats_powerStatus"}, "type": "array"}, "reeferAlarms": {"description": "Reefer alarms", "items": {"$ref": "#/components/schemas/V1AssetReeferResponse_reeferStats_alarms_1"}, "type": "array"}, "returnAirTemperature": {"description": "Return air temperature of the reefer. This is the temperature read by the reefer module (Carrier, Thermo King) that shows the temperature of the air as it enters the system.", "items": {"$ref": "#/components/schemas/V1AssetReeferResponse_reeferStats_returnAirTemp"}, "type": "array"}, "setPoint": {"description": "Set point temperature of the reefer", "items": {"$ref": "#/components/schemas/V1AssetReeferResponse_reeferStats_setPoint"}, "type": "array"}}, "type": "object"}, "V1AssetsReefer_reeferStats_ambientAirTemperature": {"properties": {"changedAtMs": {"description": "Timestamp in Unix milliseconds since epoch.", "example": 1453449599999, "format": "int64", "type": "integer"}, "tempInMilliC": {"description": "Ambient temperature in millidegree Celsius.", "example": 31110, "format": "int64", "type": "integer"}}, "type": "object"}, "V1AssetsReefer_reeferStats_dischargeAirTemperature": {"properties": {"changedAtMs": {"description": "Timestamp in Unix milliseconds since epoch.", "example": 1453449599999, "format": "int64", "type": "integer"}, "tempInMilliC": {"description": "Discharge temperature in millidegree Celsius.", "example": 31110, "format": "int64", "type": "integer"}}, "type": "object"}, "V1AssetsReefer_reeferStats_powerStatus": {"properties": {"changedAtMs": {"description": "Timestamp in Unix milliseconds since epoch.", "example": 1453449599999, "format": "int64", "type": "integer"}, "status": {"description": "Power status of the reefer. Valid values: `Off`, `Active`, `Active (Start/Stop)`, `Active (Continuous)`.", "enum": ["Off", "Active", "Active (Start/Stop)", "Active (Continuous)"], "example": "Active (Continuous)", "type": "string"}}, "type": "object"}, "alertObjectAssetResponseBody": {"description": "The Asset associated with the alert.", "properties": {"attributes": {"description": "List of attributes associated with the entity", "items": {"$ref": "#/components/schemas/GoaAttributeTinyResponseBody"}, "type": "array"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "The ID of the asset.", "example": "494123", "type": "string"}, "name": {"description": "The name of the asset.", "example": "Fleet Truck #1", "type": "string"}, "serial": {"description": "The serial number of the gateway installed on the asset.", "example": "GFRV-43N-VGX", "type": "string"}, "tags": {"description": "The list of [tags](https://kb.samsara.com/hc/en-us/articles/360026674631-Using-Tags-and-Tag-Nesting) associated with the asset.", "items": {"$ref": "#/components/schemas/GoaTagTinyResponseResponseBody"}, "type": "array"}, "type": {"default": "uncategorized", "description": "The operational context in which the asset interacts with the Samsara system. Examples: Vehicle (eg: truck, bus...), Trailer (eg: dry van, reefer, flatbed...), Powered Equipment (eg: dozer, crane...), Unpowered Equipment (eg: container, dumpster...), or Uncategorized.  Valid values: `uncategorized`, `trailer`, `equipment`, `unpowered`, `vehicle`", "enum": ["uncategorized", "trailer", "equipment", "unpowered", "vehicle"], "example": "trailer", "type": "string"}}, "required": ["id", "serial", "type"], "type": "object"}, "assetsInputsAuxInputResponseBody": {"description": "Auxiliary input metadata", "properties": {"name": {"description": "Name of the auxiliary input", "example": "PTO", "type": "string"}}, "required": ["name"], "type": "object"}, "assetsInputsResponseResponseBody": {"description": "Full assets inputs objects.", "properties": {"asset": {"$ref": "#/components/schemas/AssetsInputsAssetResponseResponseBody"}, "auxInput": {"$ref": "#/components/schemas/assetsInputsAuxInputResponseBody"}, "happenedAtTime": {"description": "UTC timestamp in RFC 3339 format of the event.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "units": {"description": "Units of the values in the returned data.  Valid values: `boolean`, `millivolts`, `microamps`", "enum": ["boolean", "millivolts", "microamps"], "example": "boolean", "type": "string"}, "value": {"description": "Value of the data point.", "example": "1", "type": "string"}}, "required": ["asset", "happenedAtTime", "units", "value"], "type": "object"}, "industrialAssetObjectResponseBody": {"description": "industrialAssetObject", "properties": {"id": {"description": "Id of the device", "example": "8d218e6c-7a16-4f9f-90f7-cc1d93b9e596", "type": "string"}, "name": {"description": "Name of the industrial asset", "example": "My asset", "type": "string"}}, "required": ["id", "name"], "type": "object"}}, "securitySchemes": {"AccessTokenHeader": {"type": "http", "scheme": "bearer"}}}, "paths": {"/assets": {"delete": {"description": "Delete an existing asset.\n\n <b>Rate limit:</b> 100 requests/min (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Write Assets** under the Assets category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "deleteAsset", "parameters": [{"description": "A filter selecting a single asset by id.", "in": "query", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"content": {}, "description": "No Content response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsDeleteAssetUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsDeleteAssetNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsDeleteAssetMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsDeleteAssetTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsDeleteAssetInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsDeleteAssetNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsDeleteAssetBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsDeleteAssetServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsDeleteAssetGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsDeleteAssetBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "[beta] Delete an existing asset.", "tags": ["Beta APIs"]}, "get": {"description": "List all assets. Up to 300 assets will be returned per page.\n\n <b>Rate limit:</b> 5 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Read Assets** under the Assets category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "listAssets", "parameters": [{"description": "The operational context in which the asset interacts with the Samsara system. Examples: Vehicle (eg: truck, bus...), Trailer (eg: dry van, reefer, flatbed...), Powered Equipment (eg: dozer, crane...), Unpowered Equipment (eg: container, dumpster...), or Uncategorized.  Valid values: `uncategorized`, `trailer`, `equipment`, `unpowered`, `vehicle`", "in": "query", "name": "type", "schema": {"enum": ["uncategorized", "trailer", "equipment", "unpowered", "vehicle"], "type": "string"}}, {"description": " If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": " A filter on data to have an updated at time after or equal to this specified time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "updatedAfterTime", "schema": {"type": "string"}}, {"description": "Optional boolean indicating whether to return external IDs on supported entities", "in": "query", "name": "includeExternalIds", "schema": {"type": "boolean"}}, {"description": "Optional boolean indicating whether to return tags on supported entities", "in": "query", "name": "includeTags", "schema": {"type": "boolean"}}, {"description": " A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "in": "query", "name": "tagIds", "schema": {"type": "string"}}, {"description": " A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "in": "query", "name": "parentTagIds", "schema": {"type": "string"}}, {"description": "A filter on the data based on this comma-separated list of asset IDs and External IDs.", "explode": false, "in": "query", "name": "ids", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of attribute value IDs. Only entities associated with ALL of the referenced values will be returned (i.e. the intersection of the sets of entities with each value). Example: `attributeValueIds=076efac2-83b5-47aa-ba36-18428436dcac,6707b3f0-23b9-4fe3-b7be-11be34aea544`", "in": "query", "name": "attributeValueIds", "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsListAssetsResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsListAssetsUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsListAssetsNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsListAssetsMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsListAssetsTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsListAssetsInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsListAssetsNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsListAssetsBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsListAssetsServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsListAssetsGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsListAssetsBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "[beta] List all assets.", "tags": ["Beta APIs"]}, "patch": {"description": "Update an existing asset.\n\n <b>Rate limit:</b> 100 requests/min (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Write Assets** under the Assets category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "updateAsset", "parameters": [{"description": "A filter selecting a single asset by id.", "in": "query", "name": "id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsUpdateAssetRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsUpdateAssetResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsUpdateAssetUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsUpdateAssetNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsUpdateAssetMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsUpdateAssetTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsUpdateAssetInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsUpdateAssetNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsUpdateAssetBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsUpdateAssetServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsUpdateAssetGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsUpdateAssetBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "[beta] Update an existing asset.", "tags": ["Beta APIs"], "x-codegen-request-body-name": "UpdateAssetRequestBody"}, "post": {"description": "Create a new asset.\n\n <b>Rate limit:</b> 100 requests/min (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Write Assets** under the Assets category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "createAsset", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsCreateAssetRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsCreateAssetResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsCreateAssetUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsCreateAssetNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsCreateAssetMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsCreateAssetTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsCreateAssetInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsCreateAssetNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsCreateAssetBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsCreateAssetServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsCreateAssetGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsCreateAssetBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "[beta] Create a new asset.", "tags": ["Beta APIs"], "x-codegen-request-body-name": "CreateAssetRequestBody"}}, "/assets/inputs/stream": {"get": {"description": "This endpoint will return data collected from the inputs of your organization's assets based on the time parameters passed in. Results are paginated. If you include an endTime, the endpoint will return data up until that point. If you don’t include an endTime, you can continue to poll the API real-time with the pagination cursor that gets returned on every call. The endpoint will only return data up until the endTime that has been processed by the server at the time of the original request. You will need to request the same [startTime, endTime) range again to receive data for assets processed after the original request time. This endpoint sorts data by time ascending.\n\n <b>Rate limit:</b> 10 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Read Assets** under the Assets category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "getAssetsInputs", "parameters": [{"description": "Comma-separated list of asset IDs. Limited to 100 ID's for each request.", "explode": false, "in": "query", "name": "ids", "required": true, "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "Input stat type to query for.  Valid values: `auxInput1`, `auxInput2`, `auxInput3`, `auxInput4`, `auxInput5`, `auxInput6`, `auxInput7`, `auxInput8`, `auxInput9`, `auxInput10`, `auxInput11`, `auxInput12`, `auxInput13`, `analogInput1Voltage`, `analogInput2Voltage`, `analogInput1Current`, `analogInput2Current`, `batteryVoltage`", "in": "query", "name": "type", "required": true, "schema": {"enum": ["auxInput1", "auxInput2", "auxInput3", "auxInput4", "auxInput5", "auxInput6", "auxInput7", "auxInput8", "auxInput9", "auxInput10", "auxInput11", "auxInput12", "auxInput13", "analogInput1Voltage", "analogInput2Voltage", "analogInput1Current", "analogInput2Current", "batteryVoltage"], "type": "string"}}, {"description": " If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "A start time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "startTime", "required": true, "schema": {"type": "string"}}, {"description": " An end time in RFC 3339 format. Defaults to never if not provided; if not provided then pagination will not cease, and a valid pagination cursor will always be returned. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "endTime", "schema": {"type": "string"}}, {"description": "Optional boolean indicating whether to return external IDs on supported entities", "in": "query", "name": "includeExternalIds", "schema": {"type": "boolean"}}, {"description": "Optional boolean indicating whether to return tags on supported entities", "in": "query", "name": "includeTags", "schema": {"type": "boolean"}}, {"description": "Optional boolean indicating whether to return attributes on supported entities", "in": "query", "name": "includeAttributes", "schema": {"type": "boolean"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsInputsGetAssetsInputsResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsInputsGetAssetsInputsUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsInputsGetAssetsInputsNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsInputsGetAssetsInputsMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsInputsGetAssetsInputsTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsInputsGetAssetsInputsInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsInputsGetAssetsInputsNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsInputsGetAssetsInputsBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsInputsGetAssetsInputsServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsInputsGetAssetsInputsGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetsInputsGetAssetsInputsBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "[beta] List asset inputs data in an organization.", "tags": ["Beta APIs"]}}, "/assets/location-and-speed/stream": {"get": {"description": "This endpoint will return asset locations and speed data that has been collected for your organization based on the time parameters passed in. Results are paginated. If you include an endTime, the endpoint will return data up until that point. If you don’t include an endTime, you can continue to poll the API real-time with the pagination cursor that gets returned on every call. The endpoint will only return data up until the endTime that has been processed by the server at the time of the original request. You will need to request the same [startTime, endTime) range again to receive data for assets processed after the original request time. This endpoint sorts the time-series data by device.\n\n <b>Rate limit:</b> 10 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Read Vehicles** under the Vehicles category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "getLocationAndSpeed", "parameters": [{"description": " If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "The limit for how many objects will be in the response. Default and max for this value is 512 objects.", "in": "query", "name": "limit", "schema": {"default": 512, "maximum": 512, "minimum": 1, "type": "integer"}}, {"description": " A start time in RFC 3339 format. Defaults to now if not provided. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "startTime", "schema": {"type": "string"}}, {"description": " An end time in RFC 3339 format. Defaults to never if not provided; if not provided then pagination will not cease, and a valid pagination cursor will always be returned. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "endTime", "schema": {"type": "string"}}, {"description": "Comma-separated list of asset IDs.", "explode": false, "in": "query", "name": "ids", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "Optional boolean indicating whether or not to return the 'speed' object", "in": "query", "name": "includeSpeed", "schema": {"type": "boolean"}}, {"description": "Optional boolean indicating whether or not to return the 'address' object", "in": "query", "name": "includeReverseGeo", "schema": {"type": "boolean"}}, {"description": "Optional boolean indicating whether or not to return the 'geofence' object", "in": "query", "name": "includeGeofenceLookup", "schema": {"type": "boolean"}}, {"description": "Optional boolean indicating whether to return external IDs on supported entities", "in": "query", "name": "includeExternalIds", "schema": {"type": "boolean"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationAndSpeedGetLocationAndSpeedResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationAndSpeedGetLocationAndSpeedUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationAndSpeedGetLocationAndSpeedNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationAndSpeedGetLocationAndSpeedMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationAndSpeedGetLocationAndSpeedTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationAndSpeedGetLocationAndSpeedInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationAndSpeedGetLocationAndSpeedNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationAndSpeedGetLocationAndSpeedBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationAndSpeedGetLocationAndSpeedServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationAndSpeedGetLocationAndSpeedGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationAndSpeedGetLocationAndSpeedBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "List asset location and speed data in an organization.", "tags": ["Location and Speed"]}}, "/beta/fleet/equipment/{id}": {"patch": {"description": "Update an equipment.  **Note** this implementation of patch uses [the JSON merge patch](https://tools.ietf.org/html/rfc7396) proposed standard.\n This means that any fields included in the patch request will _overwrite_ fields which exist on the target resource.\n For arrays, this means any array included in the request will _replace_ the array that exists at the specified path, it will not _add_ to the existing array\n\n <b>Rate limit:</b> 100 requests/min (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Write Equipment** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "patchEquipment", "parameters": [{"description": "The unique Samsara ID of the Equipment. This is automatically generated when the Equipment object is created. It cannot be changed.", "in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentPatchEquipmentRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentPatchEquipmentResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentPatchEquipmentUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentPatchEquipmentNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentPatchEquipmentMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentPatchEquipmentTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentPatchEquipmentInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentPatchEquipmentNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentPatchEquipmentBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentPatchEquipmentServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentPatchEquipmentGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentPatchEquipmentBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "[beta] Update an equipment", "tags": ["Beta APIs"], "x-codegen-request-body-name": "PatchEquipmentRequestBody"}}, "/fleet/equipment": {"get": {"description": "Returns a list of all equipment in an organization.", "operationId": "listEquipment", "parameters": [{"description": "The limit for how many objects will be in the response. Default and max for this value is 512 objects.", "in": "query", "name": "limit", "schema": {"format": "int64", "maximum": 512, "minimum": 1, "type": "integer"}}, {"description": "If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "explode": false, "in": "query", "name": "parentTagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "explode": false, "in": "query", "name": "tagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentListResponse"}}}, "description": "List of all equipment objects, and pagination information"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "List all equipment", "tags": ["Equipment"]}}, "/fleet/equipment/locations": {"get": {"description": "Returns last known locations for all equipment. This can be optionally filtered by tags or specific equipment IDs. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Equipment Statistics** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "getEquipmentLocations", "parameters": [{"description": "If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "explode": false, "in": "query", "name": "parentTagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "explode": false, "in": "query", "name": "tagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of equipment IDs. Example: `equipmentIds=1234,5678`", "explode": false, "in": "query", "name": "equipmentIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentLocationsResponse"}}}, "description": "The most recent equipment locations and pagination information"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Get most recent locations for all equipment", "tags": ["Equipment"]}}, "/fleet/equipment/locations/feed": {"get": {"description": "Follow a continuous feed of all equipment locations.\n\nYour first call to this endpoint will provide you with the most recent location for each unit of equipment and a `pagination` object that contains an `endCursor`.\n\nYou can provide the `endCursor` to subsequent calls via the `after` parameter. The response will contain any equipment location updates since that `endCursor`.\n\nIf `hasNextPage` is `false`, no updates are readily available yet. We'd suggest waiting a minimum of 5 seconds before requesting updates. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Equipment Statistics** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "getEquipmentLocationsFeed", "parameters": [{"description": "If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "explode": false, "in": "query", "name": "parentTagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "explode": false, "in": "query", "name": "tagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of equipment IDs. Example: `equipmentIds=1234,5678`", "explode": false, "in": "query", "name": "equipmentIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentLocationsListResponse"}}}, "description": "The feed of equipment locations and pagination information"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Follow feed of equipment locations", "tags": ["Equipment"]}}, "/fleet/equipment/locations/history": {"get": {"description": "Returns historical equipment locations during the given time range. This can be optionally filtered by tags or specific equipment IDs. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Equipment Statistics** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "getEquipmentLocationsHistory", "parameters": [{"description": "If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "A start time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "startTime", "required": true, "schema": {"type": "string"}}, {"description": "An end time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "endTime", "required": true, "schema": {"type": "string"}}, {"description": "A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "explode": false, "in": "query", "name": "parentTagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "explode": false, "in": "query", "name": "tagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of equipment IDs. Example: `equipmentIds=1234,5678`", "explode": false, "in": "query", "name": "equipmentIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentLocationsListResponse"}}}, "description": "Historical equipment locations and pagination information"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Get historical equipment locations", "tags": ["Equipment"]}}, "/fleet/equipment/stats": {"get": {"description": "Returns the last known stats for all equipment. This can be optionally filtered by tags or specific equipment IDs. \n\n <b>Rate limit:</b> 150 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>). \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Equipment Statistics** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "getEquipmentStats", "parameters": [{"description": "If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "explode": false, "in": "query", "name": "parentTagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "explode": false, "in": "query", "name": "tagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of equipment IDs. Example: `equipmentIds=1234,5678`", "explode": false, "in": "query", "name": "equipmentIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "The types of equipment stats you want to query. Currently, you may submit up to 4 types.\r\n\r\n- `engineRpm`: The revolutions per minute of the engine.\r\n- `fuelPercents`: The percent of fuel in the unit of equipment.\r\n- `obdEngineSeconds`: The number of seconds the engine has been running as reported directly from on-board diagnostics. This is supported with the following hardware configurations: AG24/AG26 + AOPEN/A9PIN/ACT9/ACT14.\r\n- `gatewayEngineSeconds`: An approximation of the number of seconds the engine has been running since it was new, based on the amount of time the asset gateway has been receiving power with an offset provided manually through the Samsara cloud dashboard. This is supported with the following hardware configurations: \r\n  - AG24/AG26/AG46P + APWR cable ([Auxiliary engine configuration](https://kb.samsara.com/hc/en-us/articles/************-Auxiliary-Inputs#UUID-d514abff-d10a-efaf-35d9-e10fa6c4888d) required) \r\n  - AG52 + BPWR/BEQP cable ([Auxiliary engine configuration](https://kb.samsara.com/hc/en-us/articles/************-Auxiliary-Inputs#UUID-d514abff-d10a-efaf-35d9-e10fa6c4888d) required). \r\n- `gatewayJ1939EngineSeconds`: An approximation of the number of seconds the engine has been running since it was new, based on the amount of time the AG26 device is receiving power via J1939/CAT cable and an offset provided manually through the Samsara cloud dashboard.\r\n- `obdEngineStates`: The state of the engine read from on-board diagnostics. Can be `Off`, `On`, or `Idle`.\r\n- `gatewayEngineStates`: An approximation of engine state based on readings the AG26 receives from the aux/digio cable. Can be `Off` or `On`.\r\n- `gpsOdometerMeters`: An approximation of odometer reading based on GPS calculations since the AG26 was activated, and a manual odometer offset provided in the Samsara cloud dashboard. Valid values: `Off`, `On`.\r\n- `gps`: GPS data including lat/long, heading, speed, address book entry (if exists), and a reverse geocoded address. \r\n- `engineTotalIdleTimeMinutes`: Total time in minutes that the engine has been idling.", "explode": false, "in": "query", "name": "types", "required": true, "schema": {"items": {"enum": ["gatewayEngineStates", "obdEngineStates", "fuelPercents", "engineRpm", "gatewayEngineSeconds", "obdEngineSeconds", "gatewayJ1939EngineSeconds", "gpsOdometerMeters", "gps", "engineTotalIdleTimeMinutes"], "format": "string", "type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentStatsResponse"}}}, "description": "The most recent equipment stats and pagination information"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Get most recent stats for all equipment", "tags": ["Equipment"]}}, "/fleet/equipment/stats/feed": {"get": {"description": "Follow a continuous feed of all equipment stats.\n\nYour first call to this endpoint will provide you with the most recent stats for each unit of equipment and a `pagination` object that contains an `endCursor`.\n\nYou can provide the `endCursor` to subsequent calls via the `after` parameter. The response will contain any equipment stats updates since that `endCursor`.\n\nIf `hasNextPage` is `false`, no updates are readily available yet. Each stat type has a different refresh rate, but in general we'd suggest waiting a minimum of 5 seconds before requesting updates. \n\n <b>Rate limit:</b> 150 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>). \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Equipment Statistics** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "getEquipmentStatsFeed", "parameters": [{"description": "If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "explode": false, "in": "query", "name": "parentTagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "explode": false, "in": "query", "name": "tagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of equipment IDs. Example: `equipmentIds=1234,5678`", "explode": false, "in": "query", "name": "equipmentIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "The types of equipment stats you want to query. Currently, you may submit up to 4 types.\r\n\r\n- `engineRpm`: The revolutions per minute of the engine.\r\n- `fuelPercents`: The percent of fuel in the unit of equipment.\r\n- `obdEngineSeconds`: The number of seconds the engine has been running as reported directly from on-board diagnostics. This is supported with the following hardware configurations: AG24/AG26 + AOPEN/A9PIN/ACT9/ACT14.\r\n- `gatewayEngineSeconds`: An approximation of the number of seconds the engine has been running since it was new, based on the amount of time the asset gateway has been receiving power with an offset provided manually through the Samsara cloud dashboard. This is supported with the following hardware configurations: \r\n  - AG24/AG26/AG46P + APWR cable ([Auxiliary engine configuration](https://kb.samsara.com/hc/en-us/articles/************-Auxiliary-Inputs#UUID-d514abff-d10a-efaf-35d9-e10fa6c4888d) required) \r\n  - AG52 + BPWR/BEQP cable ([Auxiliary engine configuration](https://kb.samsara.com/hc/en-us/articles/************-Auxiliary-Inputs#UUID-d514abff-d10a-efaf-35d9-e10fa6c4888d) required). \r\n- `gatewayJ1939EngineSeconds`: An approximation of the number of seconds the engine has been running since it was new, based on the amount of time the AG26 device is receiving power via J1939/CAT cable and an offset provided manually through the Samsara cloud dashboard.\r\n- `obdEngineStates`: The state of the engine read from on-board diagnostics. Can be `Off`, `On`, or `Idle`.\r\n- `gatewayEngineStates`: An approximation of engine state based on readings the AG26 receives from the aux/digio cable. Can be `Off` or `On`.\r\n- `gpsOdometerMeters`: An approximation of odometer reading based on GPS calculations since the AG26 was activated, and a manual odometer offset provided in the Samsara cloud dashboard. Valid values: `Off`, `On`.\r\n- `gps`: GPS data including lat/long, heading, speed, address book entry (if exists), and a reverse geocoded address. \r\n- `engineTotalIdleTimeMinutes`: Total time in minutes that the engine has been idling.", "explode": false, "in": "query", "name": "types", "required": true, "schema": {"items": {"enum": ["gatewayEngineStates", "obdEngineStates", "fuelPercents", "engineRpm", "gatewayEngineSeconds", "obdEngineSeconds", "gatewayJ1939EngineSeconds", "gpsOdometerMeters", "gps", "engineTotalIdleTimeMinutes"], "format": "string", "type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentStatsListResponse"}}}, "description": "The feed of equipment stats and pagination information"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Follow a feed of equipment stats", "tags": ["Equipment"]}}, "/fleet/equipment/stats/history": {"get": {"description": "Returns historical equipment status during the given time range. This can be optionally filtered by tags or specific equipment IDs. \n\n <b>Rate limit:</b> 150 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>). \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Equipment Statistics** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "getEquipmentStatsHistory", "parameters": [{"description": "If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "A start time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "startTime", "required": true, "schema": {"type": "string"}}, {"description": "An end time in RFC 3339 format. Millisecond precision and timezones are supported. (Examples: 2019-06-13T19:08:25Z, 2019-06-13T19:08:25.455Z, OR 2015-09-15T14:00:12-04:00).", "in": "query", "name": "endTime", "required": true, "schema": {"type": "string"}}, {"description": "A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "explode": false, "in": "query", "name": "parentTagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "explode": false, "in": "query", "name": "tagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of equipment IDs. Example: `equipmentIds=1234,5678`", "explode": false, "in": "query", "name": "equipmentIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "The types of equipment stats you want to query. Currently, you may submit up to 4 types.\r\n\r\n- `engineRpm`: The revolutions per minute of the engine.\r\n- `fuelPercents`: The percent of fuel in the unit of equipment.\r\n- `obdEngineSeconds`: The number of seconds the engine has been running as reported directly from on-board diagnostics. This is supported with the following hardware configurations: AG24/AG26 + AOPEN/A9PIN/ACT9/ACT14.\r\n- `gatewayEngineSeconds`: An approximation of the number of seconds the engine has been running since it was new, based on the amount of time the asset gateway has been receiving power with an offset provided manually through the Samsara cloud dashboard. This is supported with the following hardware configurations: \r\n  - AG24/AG26/AG46P + APWR cable ([Auxiliary engine configuration](https://kb.samsara.com/hc/en-us/articles/************-Auxiliary-Inputs#UUID-d514abff-d10a-efaf-35d9-e10fa6c4888d) required) \r\n  - AG52 + BPWR/BEQP cable ([Auxiliary engine configuration](https://kb.samsara.com/hc/en-us/articles/************-Auxiliary-Inputs#UUID-d514abff-d10a-efaf-35d9-e10fa6c4888d) required). \r\n- `gatewayJ1939EngineSeconds`: An approximation of the number of seconds the engine has been running since it was new, based on the amount of time the AG26 device is receiving power via J1939/CAT cable and an offset provided manually through the Samsara cloud dashboard.\r\n- `obdEngineStates`: The state of the engine read from on-board diagnostics. Can be `Off`, `On`, or `Idle`.\r\n- `gatewayEngineStates`: An approximation of engine state based on readings the AG26 receives from the aux/digio cable. Can be `Off` or `On`.\r\n- `gpsOdometerMeters`: An approximation of odometer reading based on GPS calculations since the AG26 was activated, and a manual odometer offset provided in the Samsara cloud dashboard. Valid values: `Off`, `On`.\r\n- `gps`: GPS data including lat/long, heading, speed, address book entry (if exists), and a reverse geocoded address. \r\n- `engineTotalIdleTimeMinutes`: Total time in minutes that the engine has been idling.", "explode": false, "in": "query", "name": "types", "required": true, "schema": {"items": {"enum": ["gatewayEngineStates", "obdEngineStates", "fuelPercents", "engineRpm", "gatewayEngineSeconds", "obdEngineSeconds", "gatewayJ1939EngineSeconds", "gpsOdometerMeters", "gps", "engineTotalIdleTimeMinutes"], "format": "string", "type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentStatsListResponse"}}}, "description": "Historical equipment stats and pagination information"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Get historical equipment stats", "tags": ["Equipment"]}}, "/fleet/equipment/{id}": {"get": {"description": "Retrieves the unit of equipment with the given Samsara ID. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Equipment** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "getEquipment", "parameters": [{"description": "Samsara ID of the Equipment.", "in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentResponse"}}}, "description": "The specified equipment object"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Retrieve a unit of equipment", "tags": ["Equipment"]}}, "/industrial/assets": {"get": {"description": "List all assets in the organization. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Equipment** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "getIndustrialAssets", "parameters": [{"description": "The limit for how many objects will be in the response. Default and max for this value is 512 objects.", "in": "query", "name": "limit", "schema": {"format": "int64", "maximum": 512, "minimum": 1, "type": "integer"}}, {"description": "If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "A filter on the data based on this comma-separated list of parent tag IDs, for use by orgs with tag hierarchies. Specifying a parent tag will implicitly include all descendent tags of the parent tag. Example: `parentTagIds=345,678`", "explode": false, "in": "query", "name": "parentTagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A filter on the data based on this comma-separated list of tag IDs. Example: `tagIds=1234,5678`", "explode": false, "in": "query", "name": "tagIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}, {"description": "A comma-separated list of industrial asset UUIDs. Example: `assetIds=076efac2-83b5-47aa-ba36-18428436dcac,6707b3f0-23b9-4fe3-b7be-11be34aea544`", "explode": false, "in": "query", "name": "assetIds", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListIndustrialAssetsResponse"}}}, "description": "Assets in the organization."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "List all assets", "tags": ["Industrial"]}, "post": {"description": "Create an asset with optional configuration parameters. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Equipment** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "createIndustrialAsset", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetCreate"}}}, "description": "The asset to create", "required": false}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/inline_response_200"}}}, "description": "Newly created asset object"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Create an asset", "tags": ["Industrial"], "x-codegen-request-body-name": "asset"}}, "/industrial/assets/{id}": {"delete": {"description": "Delete asset. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Equipment** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "deleteIndustrialAsset", "parameters": [{"description": "Id of the asset to be deleted.", "in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardDeleteResponse"}}}, "description": "A successful DELETE response is a 204 with no content."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Delete an existing asset", "tags": ["Industrial"]}, "patch": {"description": "Update an existing asset. Only the provided fields will be updated. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Equipment** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "patchIndustrialAsset", "parameters": [{"description": "Id of the asset to be updated", "in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetPatch"}}}, "description": "The updated asset fields", "required": false}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/inline_response_200"}}}, "description": "The updated asset"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Update an asset", "tags": ["Industrial"], "x-codegen-request-body-name": "asset"}}, "/industrial/assets/{id}/data-outputs": {"patch": {"description": "Writes values to multiple data outputs on an asset simultaneously. Only the provided data outputs will be updated.\n\n <b>Rate limit:</b> 100 requests/min (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Write Equipment Statistics** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "patchAssetDataOutputs", "parameters": [{"description": "Asset ID", "in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetDataOutputsPatchAssetDataOutputsRequestBody"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetDataOutputsPatchAssetDataOutputsResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetDataOutputsPatchAssetDataOutputsUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetDataOutputsPatchAssetDataOutputsNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetDataOutputsPatchAssetDataOutputsMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetDataOutputsPatchAssetDataOutputsTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetDataOutputsPatchAssetDataOutputsInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetDataOutputsPatchAssetDataOutputsNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetDataOutputsPatchAssetDataOutputsBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetDataOutputsPatchAssetDataOutputsServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetDataOutputsPatchAssetDataOutputsGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetDataOutputsPatchAssetDataOutputsBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "Writes to data outputs on an asset", "tags": ["Industrial"], "x-codegen-request-body-name": "PatchAssetDataOutputsRequestBody"}}, "/v1/fleet/assets": {"get": {"description": "<n class=\"warning\">\n<nh>\n<i class=\"fa fa-exclamation-circle\"></i>\nThis endpoint is still on our legacy API.\n</nh>\n</n>\n\nFetch all of the assets. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Equipment** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "V1getAllAssets", "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/inline_response_200_1"}}}, "description": "List of assets."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "List all assets", "tags": ["Assets"]}}, "/v1/fleet/assets/locations": {"get": {"description": "<n class=\"warning\">\n<nh>\n<i class=\"fa fa-exclamation-circle\"></i>\nThis endpoint is still on our legacy API.\n</nh>\n</n>\n\nFetch current locations of all assets. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Equipment Statistics** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "V1getAllAssetCurrentLocations", "parameters": [{"description": "Pagination parameter indicating the cursor position to continue returning results after. Used in conjunction with the 'limit' parameter. Mutually exclusive with 'endingBefore' parameter.", "in": "query", "name": "startingAfter", "schema": {"type": "string"}}, {"description": "Pagination parameter indicating the cursor position to return results before. Used in conjunction with the 'limit' parameter. Mutually exclusive with 'startingAfter' parameter.", "in": "query", "name": "endingBefore", "schema": {"type": "string"}}, {"description": "Pagination parameter indicating the number of results to return in this request. Used in conjunction with either 'startingAfter' or 'endingBefore'.", "in": "query", "name": "limit", "schema": {"format": "int64", "type": "number"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/inline_response_200_2"}}}, "description": "List of assets and their current locations."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "List current location for all assets", "tags": ["Assets"]}}, "/v1/fleet/assets/reefers": {"get": {"description": "<n class=\"warning\">\n<nh>\n<i class=\"fa fa-exclamation-circle\"></i>\nThis endpoint is still on our legacy API.\n</nh>\n</n>\n\nFetches all reefers and reefer-specific stats. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Trailers** under the Trailers category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "V1getAssetsReefers", "parameters": [{"description": "Timestamp in milliseconds representing the start of the period to fetch, inclusive. Used in combination with endMs.", "in": "query", "name": "startMs", "required": true, "schema": {"format": "int64", "type": "integer"}}, {"description": "Timestamp in milliseconds representing the end of the period to fetch, inclusive. Used in combination with startMs.", "in": "query", "name": "endMs", "required": true, "schema": {"format": "int64", "type": "integer"}}, {"description": "Pagination parameter indicating the cursor position to continue returning results after. Used in conjunction with the 'limit' parameter. Mutually exclusive with 'endingBefore' parameter.", "in": "query", "name": "startingAfter", "schema": {"type": "string"}}, {"description": "Pagination parameter indicating the cursor position to return results before. Used in conjunction with the 'limit' parameter. Mutually exclusive with 'startingAfter' parameter.", "in": "query", "name": "endingBefore", "schema": {"type": "string"}}, {"description": "Pagination parameter indicating the number of results to return in this request. Used in conjunction with either 'startingAfter' or 'endingBefore'.", "in": "query", "name": "limit", "schema": {"format": "int64", "type": "number"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/inline_response_200_3"}}}, "description": "All org reefers and reefer-specific details."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "List stats for all reefers", "tags": ["Assets"]}}, "/v1/fleet/assets/{asset_id}/locations": {"get": {"description": "<n class=\"warning\">\n<nh>\n<i class=\"fa fa-exclamation-circle\"></i>\nThis endpoint is still on our legacy API.\n</nh>\n</n>\n\nList historical locations for a given asset. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Equipment Statistics** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "V1getAssetLocation", "parameters": [{"description": "ID of the asset. Must contain only digits 0-9.", "in": "path", "name": "asset_id", "required": true, "schema": {"format": "int64", "type": "integer"}}, {"description": "Timestamp in milliseconds representing the start of the period to fetch, inclusive. Used in combination with endMs.", "in": "query", "name": "startMs", "required": true, "schema": {"format": "int64", "type": "integer"}}, {"description": "Timestamp in milliseconds representing the end of the period to fetch, inclusive. Used in combination with startMs.", "in": "query", "name": "endMs", "required": true, "schema": {"format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1AssetLocationResponse"}}}, "description": "Asset location details."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "List historical locations for a given asset", "tags": ["Assets"]}}, "/v1/fleet/assets/{asset_id}/reefer": {"get": {"description": "<n class=\"warning\">\n<nh>\n<i class=\"fa fa-exclamation-circle\"></i>\nThis endpoint is still on our legacy API.\n</nh>\n</n>\n\nFetch the reefer-specific stats of an asset. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Trailers** under the Trailers category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "V1getAssetReefer", "parameters": [{"description": "ID of the asset. Must contain only digits 0-9.", "in": "path", "name": "asset_id", "required": true, "schema": {"format": "int64", "type": "integer"}}, {"description": "Timestamp in milliseconds representing the start of the period to fetch, inclusive. Used in combination with endMs.", "in": "query", "name": "startMs", "required": true, "schema": {"format": "int64", "type": "integer"}}, {"description": "Timestamp in milliseconds representing the end of the period to fetch, inclusive. Used in combination with startMs.", "in": "query", "name": "endMs", "required": true, "schema": {"format": "int64", "type": "integer"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1AssetReeferResponse"}}}, "description": "Reefer-specific asset details."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "List stats for a given reefer", "tags": ["Assets"]}}}, "tags": [{"name": "Assets"}, {"name": "Equipment"}]}