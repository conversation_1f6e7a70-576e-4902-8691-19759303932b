{"openapi": "3.0.1", "info": {"description": "Samsara API specification for attributes related endpoints and schemas.", "title": "Samsara API - Attributes", "version": "2025-05-12"}, "servers": [{"url": "https://api.samsara.com/"}, {"url": "https://api.eu.samsara.com/"}], "security": [{"AccessTokenHeader": []}], "components": {"parameters": {}, "requestBodies": {}, "schemas": {"Attribute": {"properties": {"attributeType": {"default": "string", "description": "Denotes the data type of the attribute's values. Valid values: `string`, `number`.", "enum": ["string", "number"], "example": "string", "type": "string"}, "attributeValueQuantity": {"default": "multi", "description": "Defines whether or not this attribute can be used on the same entity many times (with different values). Valid values: `single`, `multi`.", "enum": ["single", "multi"], "example": "multi", "type": "string"}, "entityType": {"description": "Denotes the type of entity, driver or asset. Valid values: `driver`, `asset`.", "enum": ["driver", "asset"], "example": "asset", "type": "string"}, "id": {"description": "The samsara id of the attribute object.", "example": "123e4567-e89b-12d3-a456-426614174000", "type": "string"}, "name": {"description": "Name of attribute.", "example": "License Certifications", "type": "string"}, "numberValues": {"description": "Number values that can be associated with this attribute", "items": {"format": "double", "type": "number"}, "type": "array"}, "stringValues": {"description": "String values that can be associated with this attribute", "items": {"type": "string"}, "type": "array"}, "values": {"description": "Representation of values that includes ids.", "items": {"$ref": "#/components/schemas/attributeValueTiny"}, "type": "array"}}, "type": "object"}, "AttributeEntity": {"properties": {"entityId": {"format": "int64", "type": "integer"}, "externalIds": {"description": "The [external IDs](https://developers.samsara.com/docs/external-ids) for the given object.", "example": {"maintenanceId": "250020", "payrollId": "ABFS18600"}, "properties": {}, "type": "object"}, "name": {"type": "string"}, "numberValues": {"description": "Number values that are associated with this attribute.", "items": {"format": "double", "type": "number"}, "type": "array"}, "stringValues": {"description": "String values that are associated with this attribute.", "items": {"type": "string"}, "type": "array"}, "values": {"description": "Representation of values that includes ids.", "items": {"$ref": "#/components/schemas/attributeValueTiny"}, "type": "array"}}, "type": "object"}, "AttributeExpanded": {"allOf": [{"$ref": "#/components/schemas/Attribute"}, {"$ref": "#/components/schemas/AttributeExpanded_allOf"}]}, "AttributeExpandedResponse": {"properties": {"data": {"$ref": "#/components/schemas/AttributeExpanded"}}, "type": "object"}, "AttributeExpanded_allOf": {"properties": {"entities": {"description": "Entities that this attribute is applied onto", "items": {"$ref": "#/components/schemas/AttributeEntity"}, "type": "array"}}, "required": ["entities"], "type": "object"}, "AttributeResponse": {"properties": {"data": {"$ref": "#/components/schemas/Attribute"}}, "type": "object"}, "Contact": {"description": "Information about a notification contact for alerts.", "properties": {"email": {"$ref": "#/components/schemas/ContactEmail"}, "firstName": {"$ref": "#/components/schemas/ContactFirstName"}, "id": {"$ref": "#/components/schemas/ContactId"}, "lastName": {"$ref": "#/components/schemas/ContactLastName"}, "phone": {"$ref": "#/components/schemas/ContactPhone"}}, "required": ["email", "firstName", "id", "lastName", "phone"], "type": "object"}, "ContactEmail": {"description": "Email address of the contact.", "example": "<EMAIL>", "maxLength": 255, "type": "string"}, "ContactFirstName": {"description": "First name of the contact.", "example": "<PERSON>", "maxLength": 255, "type": "string"}, "ContactId": {"description": "ID of the contact.", "example": "22408", "type": "string"}, "ContactLastName": {"description": "Last name of the contact.", "example": "<PERSON>", "maxLength": 255, "type": "string"}, "ContactPhone": {"description": "Phone number of the contact.", "example": "************", "maxLength": 255, "type": "string"}, "ContactResponse": {"description": "A single contact.", "properties": {"data": {"$ref": "#/components/schemas/Contact"}}, "type": "object"}, "CreateAttributeRequest": {"description": "A request body to create an Attribute.", "properties": {"attributeType": {"default": "string", "description": "Denotes the data type of the attribute's values. Valid values: `string`, `number`.", "enum": ["string", "number"], "example": "string", "type": "string"}, "attributeValueQuantity": {"default": "multi", "description": "Defines whether or not this attribute can be used on the same entity many times (with different values). Valid values: `single`, `multi`.", "enum": ["single", "multi"], "example": "multi", "type": "string"}, "entities": {"description": "Entities that will be applied to this attribute", "items": {"$ref": "#/components/schemas/CreateAttributeRequest_entities"}, "type": "array"}, "entityType": {"description": "Denotes the type of entity, driver or asset.", "enum": ["driver", "asset"], "example": "asset", "type": "string"}, "name": {"description": "Name", "example": "License Certifications", "type": "string"}, "numberValues": {"description": "Number values that can be associated with this attribute", "items": {"format": "double", "type": "number"}, "type": "array"}, "stringValues": {"description": "String values that can be associated with this attribute", "items": {"type": "string"}, "type": "array"}}, "required": ["attributeType", "attributeValueQuantity", "entityType", "name"], "type": "object"}, "CreateAttributeRequest_entities": {"properties": {"entityId": {"description": "Entity id, based on the entity type.", "type": "string"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "The [external IDs](https://developers.samsara.com/docs/external-ids) for the given object.", "example": {"maintenanceId": "250020", "payrollId": "ABFS18600"}, "type": "object"}, "numberValues": {"description": "Number values that can be associated with this attribute", "items": {"format": "double", "type": "number"}, "type": "array"}, "stringValues": {"description": "String values that can be associated with this attribute", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "CreateContactRequest": {"description": "Information for adding a notification contact for alerts.", "properties": {"email": {"description": "Email address of the contact.", "example": "<EMAIL>", "maxLength": 255, "type": "string"}, "firstName": {"description": "First name of the contact.", "example": "<PERSON>", "maxLength": 255, "type": "string"}, "lastName": {"description": "Last name of the contact.", "example": "<PERSON>", "maxLength": 255, "type": "string"}, "phone": {"description": "Phone number of the contact.", "example": "************", "maxLength": 255, "type": "string"}}, "type": "object"}, "CreateTagRequest": {"properties": {"addresses": {"description": "The addresses that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObjectId"}, "type": "array"}, "assets": {"description": "The trailers, unpowered, and powered assets that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObjectId"}, "type": "array"}, "drivers": {"description": "The drivers that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObjectId"}, "type": "array"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "The [external IDs](https://developers.samsara.com/docs/external-ids) for the given object.", "example": {"maintenanceId": "250020", "payrollId": "ABFS18600"}, "type": "object"}, "machines": {"description": "The machines that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObjectId"}, "type": "array"}, "name": {"description": "Name of this tag.", "example": "California", "maxLength": 191, "minLength": 1, "type": "string"}, "parentTagId": {"description": "If this tag is part a hierarchical tag tree, this is the ID of the parent tag, otherwise this will be omitted.", "example": "4815", "type": "string"}, "sensors": {"description": "The sensors that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObjectId"}, "type": "array"}, "vehicles": {"description": "The vehicles that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObjectId"}, "type": "array"}}, "required": ["name"], "type": "object"}, "FuelLevelPercentageResponseBody": {"description": "Details specific to Fuel Level Percentage.", "properties": {"driver": {"$ref": "#/components/schemas/alertObjectDriverResponseBody"}, "trailer": {"$ref": "#/components/schemas/alertObjectTrailerResponseBody"}, "vehicle": {"$ref": "#/components/schemas/alertObjectVehicleResponseBody"}}, "type": "object"}, "GetAttributesByEntityTypeResponse": {"properties": {"data": {"items": {"$ref": "#/components/schemas/Attribute"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "type": "object"}, "GoaAttributeTiny": {"description": "Attribute properties.", "properties": {"id": {"description": "Id of the attribute", "example": "494123", "type": "string"}, "name": {"description": "Name of the attribute", "example": "Compliance/ELD", "type": "string"}, "numberValues": {"description": "List of number values associated with the attribute", "example": [867, 5309], "items": {"example": 0.5914724497111786, "format": "double", "type": "number"}, "type": "array"}, "stringValues": {"description": "List of string values associated with the attribute.", "example": ["HQ", "Leased"], "items": {"example": "Libero molestias porro dolor.", "type": "string"}, "type": "array"}}, "type": "object"}, "GoaAttributeTinyRequestBody": {"description": "Attribute properties.", "properties": {"id": {"description": "Id of the attribute", "example": "494123", "type": "string"}, "name": {"description": "Name of the attribute", "example": "Compliance/ELD", "type": "string"}, "numberValues": {"description": "List of number values associated with the attribute", "example": [867, 5309], "items": {"example": 0.755796096168875, "format": "double", "type": "number"}, "type": "array"}, "stringValues": {"description": "List of string values associated with the attribute.", "example": ["HQ", "Leased"], "items": {"example": "Eligendi vel ab ut exercitationem cumque.", "type": "string"}, "type": "array"}}, "type": "object"}, "GoaAttributeTinyResponseBody": {"description": "Attribute properties.", "properties": {"id": {"description": "Id of the attribute", "example": "494123", "type": "string"}, "name": {"description": "Name of the attribute", "example": "Compliance/ELD", "type": "string"}, "numberValues": {"description": "List of number values associated with the attribute", "example": [867, 5309], "items": {"example": 0.9846720235535413, "format": "double", "type": "number"}, "type": "array"}, "stringValues": {"description": "List of string values associated with the attribute.", "example": ["HQ", "Leased"], "items": {"example": "Placeat amet autem neque ea architecto.", "type": "string"}, "type": "array"}}, "type": "object"}, "GoaTagTinyResponseRequestBody": {"description": "A minified tag object", "properties": {"id": {"description": "ID of the tag", "example": "3914", "type": "string"}, "name": {"description": "Name of the tag.", "example": "East Coast", "type": "string"}, "parentTagId": {"description": "If this tag is part a hierarchical tag tree, this is the ID of the parent tag, otherwise this will be omitted.", "example": "4815", "type": "string"}}, "required": ["id", "name"], "type": "object"}, "GoaTagTinyResponseResponseBody": {"description": "A minified tag object", "properties": {"id": {"description": "ID of the tag", "example": "3914", "type": "string"}, "name": {"description": "Name of the tag.", "example": "East Coast", "type": "string"}, "parentTagId": {"description": "If this tag is part a hierarchical tag tree, this is the ID of the parent tag, otherwise this will be omitted.", "example": "4815", "type": "string"}}, "required": ["id", "name"], "type": "object"}, "IFTAGetIftaDetailJobBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "IFTAGetIftaDetailJobBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "IFTAGetIftaDetailJobGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "IFTAGetIftaDetailJobInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "IFTAGetIftaDetailJobMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "IFTAGetIftaDetailJobNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "IFTAGetIftaDetailJobNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "IFTAGetIftaDetailJobResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/IftaDetailJobResponseBody"}}, "required": ["data"], "type": "object"}, "IFTAGetIftaDetailJobServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "IFTAGetIftaDetailJobTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "IFTAGetIftaDetailJobUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "IFTAGetIftaJurisdictionReportsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "IFTAGetIftaJurisdictionReportsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "IFTAGetIftaJurisdictionReportsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "IFTAGetIftaJurisdictionReportsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "IFTAGetIftaJurisdictionReportsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "IFTAGetIftaJurisdictionReportsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "IFTAGetIftaJurisdictionReportsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "IFTAGetIftaJurisdictionReportsResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/IftaJurisdictionReportDataObjectResponseBody"}}, "required": ["data"], "type": "object"}, "IFTAGetIftaJurisdictionReportsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "IFTAGetIftaJurisdictionReportsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "IFTAGetIftaJurisdictionReportsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "ListContactsResponse": {"description": "A list of contacts.", "properties": {"data": {"items": {"$ref": "#/components/schemas/Contact"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "type": "object"}, "ListTagsResponse": {"description": "A list of tags.", "properties": {"data": {"items": {"$ref": "#/components/schemas/Tag"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "type": "object"}, "ParentTag": {"description": "If this tag is part a hierarchical tag tree, this is the parent tag, otherwise this will be omitted.", "properties": {"id": {"$ref": "#/components/schemas/TaggedObjectId"}, "name": {"$ref": "#/components/schemas/ParentTagName"}}, "required": ["id"], "type": "object"}, "ParentTagId": {"description": "If this tag is part a hierarchical tag tree, this is the ID of the parent tag, otherwise this will be omitted.", "example": "4815", "type": "string"}, "ParentTagName": {"description": "The tag name.", "example": "US West Vehicles", "type": "string"}, "PatchTagRequest": {"properties": {"addresses": {"description": "The addresses that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObjectId"}, "type": "array"}, "assets": {"description": "The trailers, unpowered, and powered assets that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObjectId"}, "type": "array"}, "drivers": {"description": "The drivers that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObjectId"}, "type": "array"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "The [external IDs](https://developers.samsara.com/docs/external-ids) for the given object.", "example": {"maintenanceId": "250020", "payrollId": "ABFS18600"}, "type": "object"}, "machines": {"description": "The machines that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObjectId"}, "type": "array"}, "name": {"description": "Name of this tag.", "example": "California", "maxLength": 191, "minLength": 1, "type": "string"}, "parentTagId": {"description": "If this tag is part a hierarchical tag tree, this is the ID of the parent tag, otherwise this will be omitted.", "example": "4815", "type": "string"}, "sensors": {"description": "The sensors that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObjectId"}, "type": "array"}, "vehicles": {"description": "The vehicles that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObjectId"}, "type": "array"}}, "type": "object"}, "PeerGroupTagId": {"description": "The tag id of the peer group this driver belong to, used for gamification, leave blank for group with everyone.", "example": "1234", "type": "string"}, "ReplaceTagRequest": {"properties": {"addresses": {"description": "The addresses that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObjectId"}, "type": "array"}, "assets": {"description": "The trailers, unpowered, and powered assets that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObjectId"}, "type": "array"}, "drivers": {"description": "The drivers that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObjectId"}, "type": "array"}, "machines": {"description": "The machines that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObjectId"}, "type": "array"}, "name": {"description": "Name of this tag.", "example": "California", "maxLength": 191, "minLength": 1, "type": "string"}, "parentTagId": {"description": "If this tag is part a hierarchical tag tree, this is the ID of the parent tag, otherwise this will be omitted.", "example": "4815", "type": "string"}, "sensors": {"description": "The sensors that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObjectId"}, "type": "array"}, "vehicles": {"description": "The vehicles that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObjectId"}, "type": "array"}}, "type": "object"}, "Tag": {"allOf": [{"$ref": "#/components/schemas/TinyTag"}, {"$ref": "#/components/schemas/Tag_allOf"}]}, "TagId": {"description": "Unique Samsara ID of this tag.", "example": "342417", "type": "string"}, "TagIds": {"description": "The ids of the tags that the asset should belong to.", "items": {"example": "123", "type": "string"}, "type": "array"}, "TagName": {"description": "Name of this tag.", "example": "California", "maxLength": 191, "minLength": 1, "type": "string"}, "TagResponse": {"description": "A single tag.", "properties": {"data": {"$ref": "#/components/schemas/Tag"}}, "type": "object"}, "Tag_allOf": {"properties": {"addresses": {"description": "The addresses that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObject"}, "type": "array"}, "assets": {"description": "The trailers, unpowered, and powered assets that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObject"}, "type": "array"}, "drivers": {"description": "The drivers that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObject"}, "type": "array"}, "externalIds": {"description": "The [external IDs](https://developers.samsara.com/docs/external-ids) for the given object.", "example": {"maintenanceId": "250020", "payrollId": "ABFS18600"}, "properties": {}, "type": "object"}, "machines": {"description": "The machines that belong to thistag.", "items": {"$ref": "#/components/schemas/TaggedObject"}, "type": "array"}, "parentTag": {"$ref": "#/components/schemas/ParentTag"}, "sensors": {"description": "The sensors that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObject"}, "type": "array"}, "vehicles": {"description": "The vehicles that belong to this tag.", "items": {"$ref": "#/components/schemas/TaggedObject"}, "type": "array"}}, "type": "object"}, "TaggedObject": {"properties": {"id": {"$ref": "#/components/schemas/TaggedObjectId"}, "name": {"$ref": "#/components/schemas/TaggedObjectName"}}, "required": ["id"], "type": "object"}, "TaggedObjectId": {"description": "The object ID.", "example": "23502866574", "type": "string"}, "TaggedObjectName": {"description": "The object name.", "example": "Driver Don", "type": "string"}, "TinyTag": {"properties": {"id": {"$ref": "#/components/schemas/TagId"}, "name": {"$ref": "#/components/schemas/TagName"}, "parentTagId": {"$ref": "#/components/schemas/ParentTagId"}}, "type": "object"}, "TrailerWithAttributesResponseObjectResponseBody": {"properties": {"attributes": {"description": "List of attributes associated with the entity", "items": {"$ref": "#/components/schemas/GoaAttributeTinyResponseBody"}, "type": "array"}, "enabledForMobile": {"description": "Indicates if the trailer is visible on the Samsara mobile apps.", "example": true, "type": "boolean"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "The unique Samsara ID of the Trailer. This is automatically generated when the Trailer object is created. It cannot be changed.", "example": "494123", "type": "string"}, "installedGateway": {"$ref": "#/components/schemas/TrailerGoaGatewayTinyResponseResponseBody"}, "licensePlate": {"description": "The license plate of the Trailer. **By default**: empty. Can be set or updated through the Samsara Dashboard or the API at any time.", "example": "7TYP290", "maxLength": 12, "type": "string"}, "name": {"description": "The human-readable name of the <PERSON><PERSON>. This is set by a fleet administrator and will appear in both Samsara’s cloud dashboard as well as the Samsara Driver mobile app. By default, this name is the serial number of the Samsara Asset Gateway. It can be set or updated through the Samsara Dashboard or through the API at any time.", "example": "Trailer-123", "type": "string"}, "notes": {"description": "These are generic notes about the Trailer. Empty by default. Can be set or updated through the Samsara Dashboard or the API at any time.", "example": "These are my trailer notes", "maxLength": 255, "type": "string"}, "tags": {"description": "The list of [tags](https://kb.samsara.com/hc/en-us/articles/360026674631-Using-Tags-and-Tag-Nesting) associated with the Trailer.", "items": {"$ref": "#/components/schemas/GoaTagTinyResponseResponseBody"}, "type": "array"}, "trailerSerialNumber": {"description": "The serial number of the trailer.", "example": "8V8WD530FLN016251", "type": "string"}}, "required": ["id"], "type": "object"}, "UpdateAttributeRequest": {"description": "A request body to update an Attribute.", "properties": {"attributeType": {"default": "string", "description": "Denotes the data type of the attribute's values. Valid values: `string`, `number`.", "enum": ["string", "number"], "example": "string", "type": "string"}, "attributeValueQuantity": {"default": "multi", "description": "Defines whether or not this attribute can be used on the same entity many times (with different values). Denotes the type of entity, driver or asset. Valid values: `driver`, `asset`.", "enum": ["single", "multi"], "example": "multi", "type": "string"}, "entities": {"description": "Entities that will be applied to this attribute", "items": {"$ref": "#/components/schemas/CreateAttributeRequest_entities"}, "type": "array"}, "entityType": {"description": "Denotes the type of entity, driver or asset.", "enum": ["driver", "asset"], "example": "asset", "type": "string"}, "name": {"description": "Name", "example": "License Certifications", "type": "string"}, "numberValues": {"description": "Number values that can be associated with this attribute", "items": {"format": "double", "type": "number"}, "type": "array"}, "stringValues": {"description": "String values that can be associated with this attribute", "items": {"type": "string"}, "type": "array"}}, "required": ["entityType"], "type": "object"}, "UpdateContactRequest": {"description": "Information for adding a notification contact for alerts.", "properties": {"email": {"description": "Email address of the contact.", "example": "<EMAIL>", "maxLength": 255, "type": "string"}, "firstName": {"description": "First name of the contact.", "example": "<PERSON>", "maxLength": 255, "type": "string"}, "lastName": {"description": "Last name of the contact.", "example": "<PERSON>", "maxLength": 255, "type": "string"}, "phone": {"description": "Phone number of the contact.", "example": "************", "maxLength": 255, "type": "string"}}, "type": "object"}, "attributeTiny": {"description": "A minified attribute.", "properties": {"id": {"description": "The samsara id of the attribute object.", "example": "123e4567-e89b-12d3-a456-426614174000", "type": "string"}, "name": {"description": "Name of attribute.", "example": "License Certifications", "type": "string"}, "numberValues": {"description": "Number values that are associated with this attribute.", "items": {"format": "double", "type": "number"}, "type": "array"}, "stringValues": {"description": "String values that are associated with this attribute.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "attributeValueTiny": {"description": "A minified attribute value", "properties": {"id": {"description": "The samsara id of this value object.", "example": "123e4567-e89b-12d3-a456-426614174000", "type": "string"}, "stringValue": {"description": "The human-readable string for this value.", "example": "CDL", "type": "string"}}, "type": "object"}, "contactTinyResponse": {"description": "A minified contact object", "properties": {"firstName": {"description": "First name of the contact.", "example": "<PERSON>", "type": "string"}, "id": {"description": "ID of the contact.", "example": "22408", "type": "string"}, "lastName": {"description": "Last name of the contact.", "example": "<PERSON>", "type": "string"}}, "type": "object"}, "tagTinyResponse": {"description": "A minified tag object", "properties": {"id": {"description": "ID of the tag.", "example": "3914", "type": "string"}, "name": {"description": "Name of the tag.", "example": "East Coast", "type": "string"}, "parentTagId": {"description": "If this tag is part a hierarchical tag tree, this is the ID of the parent tag, otherwise this will be omitted.", "example": "4815", "type": "string"}}, "type": "object"}}, "securitySchemes": {"AccessTokenHeader": {"type": "http", "scheme": "bearer"}}}, "paths": {"/contacts": {"get": {"description": "Returns a list of all contacts in an organization. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Alert Contacts** under the Setup & Administration category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "listContacts", "parameters": [{"description": "The limit for how many objects will be in the response. Default and max for this value is 512 objects.", "in": "query", "name": "limit", "schema": {"format": "int64", "maximum": 512, "minimum": 1, "type": "integer"}}, {"description": "If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListContactsResponse"}}}, "description": "List of all contacts"}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "List all contacts", "tags": ["Contacts"]}, "post": {"description": "Add a contact to the organization. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Alert Contacts** under the Setup & Administration category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "createContact", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateContactRequest"}}}, "description": "The contact create parameters.", "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactResponse"}}}, "description": "Contact was successfully added."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Create a contact", "tags": ["Contacts"], "x-codegen-request-body-name": "contact"}}, "/contacts/{id}": {"delete": {"description": "Delete the given contact. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Alert Contacts** under the Setup & Administration category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "deleteContact", "parameters": [{"description": "Unique identifier for the contact.", "in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardDeleteResponse"}}}, "description": "A successful DELETE response is a 204 with no content."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Delete a contact", "tags": ["Contacts"]}, "get": {"description": "Get a specific contact's information. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Alert Contacts** under the Setup & Administration category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "getContact", "parameters": [{"description": "Unique identifier for the contact.", "in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactResponse"}}}, "description": "Returns the specified contact."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Retrieve a contact", "tags": ["Contacts"]}, "patch": {"description": "Update a specific contact's information. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Alert Contacts** under the Setup & Administration category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "updateContact", "parameters": [{"description": "Unique identifier for the contact.", "in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateContactRequest"}}}, "description": "Updates to the contact.", "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactResponse"}}}, "description": "Updated contact object with given ID."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Update a contact", "tags": ["Contacts"], "x-codegen-request-body-name": "contact"}}, "/tags": {"get": {"description": "Return all of the tags for an organization. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Tags** under the Setup & Administration category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "listTags", "parameters": [{"description": "The limit for how many objects will be in the response. Default and max for this value is 512 objects.", "in": "query", "name": "limit", "schema": {"format": "int64", "maximum": 512, "minimum": 1, "type": "integer"}}, {"description": "If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListTagsResponse"}}}, "description": "List of tags."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "List all tags", "tags": ["Tags"]}, "post": {"description": "Create a new tag for the organization. This may include up to 20,000 tagged entities. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Tags** under the Setup & Administration category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "createTag", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTagRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagResponse"}}}, "description": "Newly created tag object, including the new tag ID."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Create a tag", "tags": ["Tags"], "x-codegen-request-body-name": "tag"}}, "/tags/{id}": {"delete": {"description": "Permanently deletes a tag. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Tags** under the Setup & Administration category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "deleteTag", "parameters": [{"description": "ID of the Tag. This can either be the Samsara-provided ID or an external ID. External IDs are customer-specified key-value pairs created in the POST or PATCH requests of this resource. To specify an external ID as part of a path parameter, use the following format: `key:value`. For example, `crmId:abc123`. Automatically populated external IDs are prefixed with `samsara.`. For example, `samsara.name:ELD-exempt`.", "in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardDeleteResponse"}}}, "description": "A successful DELETE response is a 204 with no content."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Delete a tag", "tags": ["Tags"]}, "get": {"description": "Fetch a tag by id. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Read Tags** under the Setup & Administration category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "getTag", "parameters": [{"description": "ID of the Tag. This can either be the Samsara-provided ID or an external ID. External IDs are customer-specified key-value pairs created in the POST or PATCH requests of this resource. To specify an external ID as part of a path parameter, use the following format: `key:value`. For example, `crmId:abc123`. Automatically populated external IDs are prefixed with `samsara.`. For example, `samsara.name:ELD-exempt`.", "in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagResponse"}}}, "description": "The tag corresponding to request id."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Retrieve a tag", "tags": ["Tags"]}, "patch": {"description": "Update an existing tag. **Note** this implementation of patch uses [the JSON merge patch](https://tools.ietf.org/html/rfc7396) proposed standard. \n\n This means that any fields included in the patch request will _overwrite_ fields which exist on the target resource. \n\n For arrays, this means any array included in the request will _replace_ the array that exists at the specified path, it will not _add_ to the existing array. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Tags** under the Setup & Administration category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "patchTag", "parameters": [{"description": "ID of the Tag. This can either be the Samsara-provided ID or an external ID. External IDs are customer-specified key-value pairs created in the POST or PATCH requests of this resource. To specify an external ID as part of a path parameter, use the following format: `key:value`. For example, `crmId:abc123`. Automatically populated external IDs are prefixed with `samsara.`. For example, `samsara.name:ELD-exempt`.", "in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchTagRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagResponse"}}}, "description": "Returns updated tag object."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Update a tag", "tags": ["Tags"], "x-codegen-request-body-name": "tag"}, "put": {"description": "Update a tag with a new name and new members. This API call would replace all old members of a tag with new members specified in the request body. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Tags** under the Setup & Administration category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "replaceTag", "parameters": [{"description": "ID of the Tag. This can either be the Samsara-provided ID or an external ID. External IDs are customer-specified key-value pairs created in the POST or PATCH requests of this resource. To specify an external ID as part of a path parameter, use the following format: `key:value`. For example, `crmId:abc123`. Automatically populated external IDs are prefixed with `samsara.`. For example, `samsara.name:ELD-exempt`.", "in": "path", "name": "id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReplaceTagRequest"}}}, "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagResponse"}}}, "description": "The updated tag data."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/standardErrorResponse"}}}, "description": "Error response"}}, "summary": "Update a tag", "tags": ["Tags"], "x-codegen-request-body-name": "tag"}}}, "tags": [{"name": "Contacts"}, {"name": "Tags"}]}