{"openapi": "3.0.1", "info": {"description": "Samsara API specification for routes related endpoints and schemas.", "title": "Samsara API - Routes", "version": "2025-05-12"}, "servers": [{"url": "https://api.samsara.com/"}, {"url": "https://api.eu.samsara.com/"}], "security": [{"AccessTokenHeader": []}], "components": {"parameters": {"V1DispatchRouteIdOrExternalIdParam": {"description": "ID of the route. This can either be the Samsara-specified ID, or an external ID. External IDs are customer specified key-value pairs created in the POST or PATCH requests of this resource. To specify an external ID as part of a path parameter, use the following format: `key:value`. For example, `payrollId:ABFS18600`", "in": "path", "name": "route_id_or_external_id", "required": true, "schema": {"type": "string"}}, "V1routeDurationParam": {"description": "Time in milliseconds that represents the duration before end_time to query. Defaults to 24 hours.", "in": "query", "name": "duration", "schema": {"format": "int64", "type": "integer"}}, "V1routeEndTimeParam": {"description": "Time in unix milliseconds that represents the end time of the requested time interval. See above for a description of how routes are returned. Defaults to now.", "in": "query", "name": "end_time", "schema": {"format": "int64", "type": "integer"}}, "V1routeHistoryEndTimeParam": {"description": "Timestamp representing the end of the period to fetch, inclusive. Used in combination with start_time. Defaults to nowMs.", "in": "query", "name": "end_time", "schema": {"format": "int64", "type": "integer"}}, "V1routeHistoryStartTimeParam": {"description": "Timestamp representing the start of the period to fetch, inclusive. Used in combination with end_time. Defaults to 0.", "in": "query", "name": "start_time", "schema": {"format": "int64", "type": "integer"}}}, "requestBodies": {"V1DispatchRouteApplyToFutureRoutesParam": {"content": {"application/json": {"schema": {"properties": {"apply_to_future_routes": {"description": "This is only for a recurring route.  If set to true, delete all following runs of the route.  If set to false, only delete the current route.", "example": true, "type": "boolean"}}, "type": "object"}}}, "required": false}}, "schemas": {"CreateRoutesStopRequestObjectRequestBody": {"properties": {"addressId": {"description": "ID of the address. An address [externalId](https://developers.samsara.com/docs/external-ids#using-external-ids) can also be used interchangeably here.", "example": "45934", "type": "string"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "name": {"description": "Name of the stop", "example": "Stop #1", "type": "string"}, "notes": {"description": "Notes for the stop", "example": "These are my notes", "maxLength": 2000, "type": "string"}, "ontimeWindowAfterArrivalMs": {"description": "Specifies the time window (in milliseconds) after a stop's scheduled arrival time during which the stop is considered 'on-time'.", "example": 300000, "format": "int64", "type": "integer"}, "ontimeWindowBeforeArrivalMs": {"description": "Specifies the time window (in milliseconds) before a stop's scheduled arrival time during which the stop is considered 'on-time'.", "example": 300000, "format": "int64", "type": "integer"}, "scheduledArrivalTime": {"description": "This is a required field for all stops EXCEPT the start and end, based on route start and stop settings selected.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "scheduledDepartureTime": {"description": "This is a required field for all stops EXCEPT the start and end, based on route start and stop settings selected.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "singleUseLocation": {"$ref": "#/components/schemas/RoutesSingleUseAddressObjectRequestBody"}}, "type": "object"}, "GoaRouteStopTinyResponseResponseBody": {"description": "A minified route stop object", "properties": {"externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "Id of the route stop", "example": "494123", "type": "string"}, "name": {"description": "Name of the route stop", "example": "Company Warehouse #1", "type": "string"}}, "type": "object"}, "GoaRouteTinyResponseResponseBody": {"description": "A minified representation of a single route.", "properties": {"externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "Unique identifier for the route.", "example": "131313", "type": "string"}, "name": {"description": "Name of the route.", "example": "Pineapple delivery", "maxLength": 255, "type": "string"}}, "required": ["id"], "type": "object"}, "MinimalRouteAuditLogsResponseBody": {"description": "A single route. Only the fields that have changed are present in the response. All other fields, including the route id, will not be present in the response. For now, only routeStops are included since only Route Tracking updates are supported.", "properties": {"stops": {"description": "The route stops in the route. Only stops that have been updated will be included in the response.", "items": {"$ref": "#/components/schemas/MinimalRouteStopAuditLogsResponseBody"}, "type": "array"}}, "type": "object"}, "MinimalRouteStopAuditLogsResponseBody": {"description": "A single route stop for a route.", "properties": {"actualArrivalTime": {"description": "Actual arrival time, if it exists, for the route stop in RFC 3339 format.", "example": "2006-01-02T15:04:05+07:00", "format": "date-time", "type": "string"}, "actualDepartureTime": {"description": "Actual departure time, if it exists, for the route stop in RFC 3339 format.", "example": "2006-01-02T15:04:05+07:00", "format": "date-time", "type": "string"}, "enRouteTime": {"description": "The time the stop became en-route, in RFC 3339 format.", "example": "2006-01-02T15:04:05+07:00", "format": "date-time", "type": "string"}, "eta": {"description": "Estimated time of arrival, if this stop is currently en-route, in RFC 3339 format.", "example": "2006-01-02T15:04:05+07:00", "format": "date-time", "type": "string"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "Unique identifier for the route stop.", "example": "141414", "type": "string"}, "liveSharingUrl": {"description": "The shareable url of the stop's current status.", "example": "https://cloud.samsara.com/fleet/viewer/job/fleet_viewer_token", "type": "string"}, "scheduledArrivalTime": {"description": "Scheduled arrival time, if it exists, for the stop in RFC 3339 format. If it does not exist, and this field was changed in the update, it will be an empty string.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "scheduledDepartureTime": {"description": "Scheduled departure time, if it exists, for the stop in RFC 3339 format. If it does not exist, and this field was changed in the update, it will be an empty string.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "skippedTime": {"description": "Skipped time, if it exists, for the route stop in RFC 3339 format.", "example": "2006-01-02T15:04:05+07:00", "format": "date-time", "type": "string"}, "state": {"description": "The current state of the route stop.  Valid values: `unassigned`, `scheduled`, `en route`, `skipped`, `arrived`, `departed`", "enum": ["unassigned", "scheduled", "en route", "skipped", "arrived", "departed"], "example": "scheduled", "type": "string"}}, "required": ["id"], "type": "object"}, "MinimalRouteStopResponseBody": {"description": "A single route stop for a route.", "properties": {"actualArrivalTime": {"description": "Actual arrival time, if it exists, for the route stop in RFC 3339 format.", "example": "2006-01-02T15:04:05+07:00", "format": "date-time", "type": "string"}, "actualDepartureTime": {"description": "Actual departure time, if it exists, for the route stop in RFC 3339 format.", "example": "2006-01-02T15:04:05+07:00", "format": "date-time", "type": "string"}, "enRouteTime": {"description": "The time the stop became en-route, in RFC 3339 format.", "example": "2006-01-02T15:04:05+07:00", "format": "date-time", "type": "string"}, "eta": {"description": "Estimated time of arrival, if this stop is currently en-route, in RFC 3339 format.", "example": "2006-01-02T15:04:05+07:00", "format": "date-time", "type": "string"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "Unique identifier for the route stop.", "example": "141414", "type": "string"}, "liveSharingUrl": {"description": "The shareable url of the stop's current status.", "example": "https://cloud.samsara.com/fleet/viewer/job/fleet_viewer_token", "type": "string"}, "skippedTime": {"description": "Skipped time, if it exists, for the route stop in RFC 3339 format.", "example": "2006-01-02T15:04:05+07:00", "format": "date-time", "type": "string"}, "state": {"description": "The current state of the route stop.  Valid values: `unassigned`, `scheduled`, `en route`, `skipped`, `arrived`, `departed`", "enum": ["unassigned", "scheduled", "en route", "skipped", "arrived", "departed"], "example": "scheduled", "type": "string"}}, "required": ["id", "state"], "type": "object"}, "OutOfRouteDetailsObjectRequestBody": {"description": "Details specific to Out Of Route", "properties": {"maxOffRouteMeters": {"description": "The minimum distance in meters a vehicle has to be from its active route path to be considered out of its route.", "example": 100, "format": "int64", "type": "integer"}, "minDurationMilliseconds": {"description": "The number of milliseconds the trigger needs to stay active before alerting.", "example": 600000, "format": "int64", "type": "integer"}}, "required": ["maxOffRouteMeters", "minDurationMilliseconds"], "type": "object"}, "OutOfRouteDetailsObjectResponseBody": {"description": "Details specific to Out Of Route", "properties": {"maxOffRouteMeters": {"description": "The minimum distance in meters a vehicle has to be from its active route path to be considered out of its route.", "example": 100, "format": "int64", "type": "integer"}, "minDurationMilliseconds": {"description": "The number of milliseconds the trigger needs to stay active before alerting.", "example": 600000, "format": "int64", "type": "integer"}}, "required": ["maxOffRouteMeters", "minDurationMilliseconds"], "type": "object"}, "OutOfRouteResponseBody": {"description": "Details specific to Out Of Route.", "properties": {"driver": {"$ref": "#/components/schemas/alertObjectDriverResponseBody"}, "trailer": {"$ref": "#/components/schemas/alertObjectTrailerResponseBody"}, "vehicle": {"$ref": "#/components/schemas/alertObjectVehicleResponseBody"}}, "type": "object"}, "RouteChangesResponseBody": {"description": "A diff of the changes for a route update.", "properties": {"after": {"$ref": "#/components/schemas/MinimalRouteAuditLogsResponseBody"}, "before": {"$ref": "#/components/schemas/MinimalRouteAuditLogsResponseBody"}}, "required": ["after", "before"], "type": "object"}, "RouteFeedObjectResponseBody": {"description": "Route feed object.", "properties": {"changes": {"$ref": "#/components/schemas/RouteChangesResponseBody"}, "operation": {"description": "The operation that was performed as part of this route update.  Valid values: `stop scheduled`, `stop en route`, `stop skipped`, `stop arrived`, `stop departed`, `stop ETA updated`, `stop arrival time updated`, `stop completion time updated`, `stop order changed`, `stop arrival prevented`", "enum": ["stop scheduled", "stop en route", "stop skipped", "stop arrived", "stop departed", "stop ETA updated", "stop arrival time updated", "stop completion time updated", "stop order changed", "stop arrival prevented"], "example": "stop skipped", "type": "string"}, "route": {"$ref": "#/components/schemas/baseRouteResponseObjectResponseBody"}, "source": {"description": "The source of this route update. Updates that are triggered by time or by the route being completed are 'automatic'.  Valid values: `automatic`, `driver`, `admin`", "enum": ["automatic", "driver", "admin"], "example": "automatic", "type": "string"}, "time": {"description": "The timestamp of the route in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "format": "date-time", "type": "string"}, "type": {"description": "The type of route update. The route tracking updates occur as a route is completed and stops transition from one state to another. Currently only Route Tracking updates are supported, but this will change in the future when additional types are added.  Valid values: `route tracking`", "enum": ["route tracking"], "example": "route tracking", "type": "string"}}, "required": ["changes", "route", "source", "time", "type"], "type": "object"}, "RouteSettingsRequestBody": {"description": "An optional dictionary, only necessary to override the defaults for route start and end conditions.", "properties": {"routeCompletionCondition": {"default": "arriveLastStop", "description": "Defaults to 'arriveLastStop' which ends the route upon arriving at the final stop. The condition 'departLastStop' \nends the route upon departing the last stop. If 'arriveLastStop' is set, then the departure time of the final stop should not be set.  Valid values: `arriveLastStop`, `departLastStop`", "enum": ["arriveLastStop", "departLastStop"], "example": "arriveLastStop", "type": "string"}, "routeStartingCondition": {"default": "departFirstStop", "description": "Defaults to 'departFirstStop' which starts the route upon departing the first stop in the route.\n The condition 'arriveFirstStop' starts the route upon arriving at the first stop in the route. If 'departFirstStop' is set,\nthe arrival time of the first stop should not be set.  Valid values: `departFirstStop`, `arriveFirstStop`", "enum": ["departFirstStop", "arriveFirstStop"], "example": "departFirstStop", "type": "string"}}, "type": "object"}, "RouteSettingsResponseBody": {"description": "An optional dictionary, only necessary to override the defaults for route start and end conditions.", "properties": {"routeCompletionCondition": {"default": "arriveLastStop", "description": "Defaults to 'arriveLastStop' which ends the route upon arriving at the final stop. The condition 'departLastStop' \nends the route upon departing the last stop. If 'arriveLastStop' is set, then the departure time of the final stop should not be set.  Valid values: `arriveLastStop`, `departLastStop`", "enum": ["arriveLastStop", "departLastStop"], "example": "arriveLastStop", "type": "string"}, "routeStartingCondition": {"default": "departFirstStop", "description": "Defaults to 'departFirstStop' which starts the route upon departing the first stop in the route.\n The condition 'arriveFirstStop' starts the route upon arriving at the first stop in the route. If 'departFirstStop' is set,\nthe arrival time of the first stop should not be set.  Valid values: `departFirstStop`, `arriveFirstStop`", "enum": ["departFirstStop", "arriveFirstStop"], "example": "departFirstStop", "type": "string"}}, "type": "object"}, "RouteStopDetailsObjectResponseBody": {"properties": {"driver": {"$ref": "#/components/schemas/GoaDriverTinyResponseResponseBody"}, "operation": {"description": "The operation that was performed as part of this route update.  Valid values: `stop arrived`, `stop departed`", "enum": ["stop arrived", "stop departed"], "example": "stop arrived", "type": "string"}, "route": {"$ref": "#/components/schemas/WebhookRouteResponseObjectResponseBody"}, "routeStopDetails": {"$ref": "#/components/schemas/MinimalRouteStopResponseBody"}, "time": {"description": "The timestamp of the route in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "format": "date-time", "type": "string"}, "type": {"description": "The type of route update. The route tracking updates occur as a route is completed and stops transition from one state to another. Currently only Route Tracking updates are supported, but this will change in the future when additional types are added.  Valid values: `route tracking`", "enum": ["route tracking"], "example": "route tracking", "type": "string"}, "vehicle": {"$ref": "#/components/schemas/VehicleWithGatewayTinyResponseResponseBody"}}, "required": ["route", "routeStopDetails", "time", "type"], "type": "object"}, "RouteStopETAResponseBody": {"description": "Details specific to Route Stop ETA.", "properties": {"driver": {"$ref": "#/components/schemas/alertObjectDriverResponseBody"}, "trailer": {"$ref": "#/components/schemas/alertObjectTrailerResponseBody"}, "vehicle": {"$ref": "#/components/schemas/alertObjectVehicleResponseBody"}}, "type": "object"}, "RouteStopEstimatedArrivalDetailsObjectRequestBody": {"description": "Details specific to Route Stop Estimated Arrival", "properties": {"alertBeforeArrivalMilliseconds": {"description": "Time threshold for when to send an alert. Sends an alert when the ETA is less than the threshold.", "example": 300000, "format": "int64", "type": "integer"}, "hasLiveShareLink": {"description": "If true, will include a live sharing link in the alert. Defaults to false.", "example": true, "type": "boolean"}, "isAlertOnRouteStopOnly": {"description": "If true, will only alert if the vehicle is en-route to the stop. Defaults to false.", "example": true, "type": "boolean"}, "location": {"$ref": "#/components/schemas/LocationObjectRequestBody"}}, "required": ["alertBeforeArrivalMilliseconds", "location"], "type": "object"}, "RouteStopEstimatedArrivalDetailsObjectResponseBody": {"description": "Details specific to Route Stop Estimated Arrival", "properties": {"alertBeforeArrivalMilliseconds": {"description": "Time threshold for when to send an alert. Sends an alert when the ETA is less than the threshold.", "example": 300000, "format": "int64", "type": "integer"}, "hasLiveShareLink": {"description": "If true, will include a live sharing link in the alert. Defaults to false.", "example": true, "type": "boolean"}, "isAlertOnRouteStopOnly": {"description": "If true, will only alert if the vehicle is en-route to the stop. Defaults to false.", "example": true, "type": "boolean"}, "location": {"$ref": "#/components/schemas/LocationObjectResponseBody"}}, "required": ["alertBeforeArrivalMilliseconds", "location"], "type": "object"}, "RoutesCreateRouteBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesCreateRouteBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesCreateRouteGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesCreateRouteInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesCreateRouteMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesCreateRouteNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesCreateRouteNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesCreateRouteRequestBody": {"properties": {"driverId": {"description": "ID of the driver. Can be either a unique Samsara ID or an [external ID](https://developers.samsara.com/docs/external-ids) for the driver.", "example": "1234", "type": "string"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "name": {"description": "Name for the route", "example": "Bid 123", "type": "string"}, "notes": {"description": "Notes about the route.", "example": "These are my notes", "maxLength": 2000, "type": "string"}, "recomputeScheduledTimes": {"description": "This optional boolean parameter controls whether route schedule arrival and departure times are recalculated. When set to true, the system will automatically recompute the scheduledArrivalTime and scheduledDepartureTime for each stop along the route during route creation. This process overrides any manually provided values, with the exception of the first stop, which retains its user-defined schedule.", "example": true, "type": "boolean"}, "settings": {"$ref": "#/components/schemas/RouteSettingsRequestBody"}, "stops": {"description": "List of stops along the route. For each stop, exactly one of `addressId` and `singleUseLocation` are required. Depending on the `settings` on your route, either a `scheduledArrivalTime` or `scheduledDepartureTime` must be specified for the first job.", "items": {"$ref": "#/components/schemas/CreateRoutesStopRequestObjectRequestBody"}, "minItems": 2, "type": "array"}, "vehicleId": {"description": "ID of the vehicle. Can be either a unique Samsara ID or an [external ID](https://developers.samsara.com/docs/external-ids) for the vehicle.", "example": "453546", "type": "string"}}, "required": ["name", "stops"], "type": "object"}, "RoutesCreateRouteResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/baseRouteResponseObjectResponseBody"}}, "type": "object"}, "RoutesCreateRouteServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesCreateRouteTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesCreateRouteUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesDeleteRouteBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesDeleteRouteBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesDeleteRouteGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesDeleteRouteInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesDeleteRouteMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesDeleteRouteNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesDeleteRouteNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesDeleteRouteServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesDeleteRouteTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesDeleteRouteUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRouteBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRouteBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRouteGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRouteInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRouteMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRouteNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRouteNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRouteResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/baseRouteResponseObjectResponseBody"}}, "type": "object"}, "RoutesFetchRouteServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRouteTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRouteUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRoutesBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRoutesBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRoutesGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRoutesInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRoutesMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRoutesNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRoutesNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRoutesResponseBody": {"properties": {"data": {"description": "An array containing multiple routes.", "items": {"$ref": "#/components/schemas/baseRouteResponseObjectResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "RoutesFetchRoutesServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRoutesTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesFetchRoutesUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesGetRoutesFeedBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesGetRoutesFeedBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesGetRoutesFeedGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesGetRoutesFeedInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesGetRoutesFeedMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesGetRoutesFeedNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesGetRoutesFeedNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesGetRoutesFeedResponseBody": {"properties": {"data": {"description": "Route feed response.", "items": {"$ref": "#/components/schemas/RouteFeedObjectResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "RoutesGetRoutesFeedServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesGetRoutesFeedTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesGetRoutesFeedUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesPatchRouteBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesPatchRouteBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesPatchRouteGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesPatchRouteInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesPatchRouteMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesPatchRouteNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesPatchRouteNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesPatchRouteRequestBody": {"properties": {"driverId": {"description": "ID of the driver. Can be either a unique Samsara ID or an [external ID](https://developers.samsara.com/docs/external-ids) for the driver.", "example": "1234", "type": "string"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "name": {"description": "Name for the route", "example": "Bid 123", "type": "string"}, "notes": {"description": "Notes about the route.", "example": "These are my notes", "maxLength": 2000, "type": "string"}, "recomputeScheduledTimes": {"description": "This optional boolean parameter controls whether route schedule arrival and departure times are recalculated. When set to true, the system will automatically recompute the scheduledArrivalTime and scheduledDepartureTime for each stop along the route during route creation. This process overrides any manually provided values, with the exception of the first stop, which retains its user-defined schedule.", "example": true, "type": "boolean"}, "settings": {"$ref": "#/components/schemas/RouteSettingsRequestBody"}, "stops": {"description": "List of stops along the route. If a valid `id` of a stop is provided, that stop will be updated. If no `id` is provided for a passed in stop, that stop will be created. If `id` value are passed in for some stops and not for others, those with `id` value specified will be retained and updated in the original route, those without `id` value specified in the body will be created, and those without `id` value specified that already existed on the route will be deleted. For each new stop, exactly one of `addressId` and `singleUseLocation` are required. Depending on the `settings` on your route, either a `scheduledArrivalTime` or `scheduledDepartureTime` must be specified for the first job, if a new first job is being added.", "items": {"$ref": "#/components/schemas/UpdateRoutesStopRequestObjectRequestBody"}, "type": "array"}, "vehicleId": {"description": "ID of the vehicle. Can be either a unique Samsara ID or an [external ID](https://developers.samsara.com/docs/external-ids) for the vehicle.", "example": "453546", "type": "string"}}, "type": "object"}, "RoutesPatchRouteResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/baseRouteResponseObjectResponseBody"}}, "type": "object"}, "RoutesPatchRouteServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesPatchRouteTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesPatchRouteUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "RoutesStopResponseObjectResponseBody": {"properties": {"actualArrivalTime": {"description": "Actual arrival time, if it exists, for the route stop in RFC 3339 format.", "example": "2006-01-02T15:04:05+07:00", "format": "date-time", "type": "string"}, "actualDepartureTime": {"description": "Actual departure time, if it exists, for the route stop in RFC 3339 format.", "example": "2006-01-02T15:04:05+07:00", "format": "date-time", "type": "string"}, "address": {"$ref": "#/components/schemas/GoaAddressTinyResponseResponseBody"}, "documents": {"description": "List of documents associated with the stop.", "items": {"$ref": "#/components/schemas/GoaDocumentTinyResponseResponseBody"}, "type": "array"}, "enRouteTime": {"description": "The time the stop became en-route, in RFC 3339 format.", "example": "2006-01-02T15:04:05+07:00", "format": "date-time", "type": "string"}, "eta": {"description": "Estimated time of arrival, if this stop is currently en-route, in RFC 3339 format.", "example": "2006-01-02T15:04:05+07:00", "format": "date-time", "type": "string"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "Id of the stop", "example": "324231", "type": "string"}, "liveSharingUrl": {"description": "The shareable url of the stop's current status.", "example": "https://cloud.samsara.com/fleet/viewer/job/fleet_viewer_token", "type": "string"}, "locationLiveSharingLinks": {"description": "List of shareable, non-expired 'By Location' Live Sharing Links.", "items": {"$ref": "#/components/schemas/LiveSharingLinkResponseObjectResponseBody"}, "type": "array"}, "name": {"description": "Name of the stop", "example": "Stop #1", "type": "string"}, "notes": {"description": "Notes for the stop", "example": "These are my notes", "maxLength": 2000, "type": "string"}, "ontimeWindowAfterArrivalMs": {"description": "Specifies the time window (in milliseconds) after a stop's scheduled arrival time during which the stop is considered 'on-time'.", "example": 300000, "format": "int64", "type": "integer"}, "ontimeWindowBeforeArrivalMs": {"description": "Specifies the time window (in milliseconds) before a stop's scheduled arrival time during which the stop is considered 'on-time'.", "example": 300000, "format": "int64", "type": "integer"}, "scheduledArrivalTime": {"description": "Scheduled arrival time, if it exists, for the stop in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "scheduledDepartureTime": {"description": "Scheduled departure time, if it exists, for the stop in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "singleUseLocation": {"$ref": "#/components/schemas/RoutesSingleUseAddressObjectResponseBody"}, "skippedTime": {"description": "Skipped time, if it exists, for the route stop in RFC 3339 format.", "example": "2006-01-02T15:04:05+07:00", "format": "date-time", "type": "string"}, "state": {"description": "The current state of the route stop.  Valid values: `unassigned`, `scheduled`, `en route`, `skipped`, `arrived`, `departed`", "enum": ["unassigned", "scheduled", "en route", "skipped", "arrived", "departed"], "example": "scheduled", "type": "string"}}, "required": ["id", "name", "state"], "type": "object"}, "TripResponseBody": {"description": "Trip", "properties": {"asset": {"$ref": "#/components/schemas/TripAssetResponseBody"}, "completionStatus": {"description": "Trip completion status  Valid values: `inProgress`, `completed`", "enum": ["inProgress", "completed"], "example": "completed", "type": "string"}, "createdAtTime": {"description": "[RFC 3339] Time the trip was created in Samsara in UTC.", "example": "2024-04-16T19:08:25Z", "type": "string"}, "endLocation": {"$ref": "#/components/schemas/LocationResponseResponseBody"}, "startLocation": {"$ref": "#/components/schemas/LocationResponseResponseBody"}, "tripEndTime": {"description": "[RFC 3339] Time the trip ended in UTC.", "example": "2024-04-16T20:00:00Z", "type": "string"}, "tripStartTime": {"description": "[RFC 3339] Time the trip started in UTC.", "example": "2024-04-16T19:08:25Z", "type": "string"}, "updatedAtTime": {"description": "[RFC 3339] Time the trip was updated in Samsara in UTC. Valid updates are when `endTime` populates or `completionStatus` changes values.", "example": "2024-04-16T19:08:25Z", "type": "string"}}, "required": ["asset", "completionStatus", "createdAtTime", "startLocation", "tripStartTime", "updatedAtTime"], "type": "object"}, "TripSpeedingIntervalsResponseBody": {"description": "TripSpeedingIntervals", "properties": {"asset": {"$ref": "#/components/schemas/TripAssetResponseBody"}, "createdAtTime": {"description": "UTC time the trip was created in Samsara in RFC 3339 format.", "example": "2023-01-27T07:06:25Z", "type": "string"}, "driverId": {"description": "The driver ID assigned to the trip. Only returned if includeDriverId is set to true. 'null' if no driver id.", "example": "12345", "type": "string"}, "intervals": {"description": "List of speeding intervals that belong to the trip. The full list of intervals associated with the trip is always returned, empty if no intervals exist.", "items": {"$ref": "#/components/schemas/SpeedingIntervalResponseBody"}, "type": "array"}, "tripStartTime": {"description": "UTC time the trip started in RFC 3339 format.", "example": "2023-01-27T07:06:25Z", "type": "string"}, "updatedAtTime": {"description": "UTC time the trip was last updated in Samsara in RFC 3339 format. Valid updates are when trip's endTime populates or interval.isDismissed changes value.", "example": "2023-01-27T10:04:20Z", "type": "string"}}, "required": ["asset", "createdAtTime", "intervals", "tripStartTime", "updatedAtTime"], "type": "object"}, "TripsGetTripsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TripsGetTripsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TripsGetTripsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TripsGetTripsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TripsGetTripsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TripsGetTripsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TripsGetTripsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TripsGetTripsResponseBody": {"properties": {"data": {"description": "List of trips", "items": {"$ref": "#/components/schemas/TripResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "TripsGetTripsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TripsGetTripsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "TripsGetTripsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "UpdateRoutesStopRequestObjectRequestBody": {"properties": {"addressId": {"description": "ID of the address. An address [externalId](https://developers.samsara.com/docs/external-ids#using-external-ids) can also be used interchangeably here.", "example": "45934", "type": "string"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "ID of the stop", "example": "4125", "type": "string"}, "name": {"description": "Name of the stop", "example": "Stop #1", "type": "string"}, "notes": {"description": "Notes for the stop", "example": "These are my notes", "maxLength": 2000, "type": "string"}, "ontimeWindowAfterArrivalMs": {"description": "Specifies the time window (in milliseconds) after a stop's scheduled arrival time during which the stop is considered 'on-time'.", "example": 300000, "maximum": 259200000, "minimum": 0, "type": "integer"}, "ontimeWindowBeforeArrivalMs": {"description": "Specifies the time window (in milliseconds) before a stop's scheduled arrival time during which the stop is considered 'on-time'.", "example": 300000, "maximum": 259200000, "minimum": 0, "type": "integer"}, "scheduledArrivalTime": {"description": "This is a required field for all stops EXCEPT the start and end, based on route start and stop settings selected.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "scheduledDepartureTime": {"description": "This is a required field for all stops EXCEPT the start and end, based on route start and stop settings selected.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "singleUseLocation": {"$ref": "#/components/schemas/RoutesSingleUseAddressObjectRequestBody"}}, "type": "object"}, "V1DispatchJob": {"allOf": [{"$ref": "#/components/schemas/V1DispatchJob_allOf"}, {"$ref": "#/components/schemas/V1DispatchJobCreate"}]}, "V1DispatchJobCreate": {"properties": {"destination_address": {"description": "The address of the job destination, as it would be recognized if provided to maps.google.com. Optional if a valid destination address ID is provided.", "example": "123 Main St, Philadelphia, PA 19106", "type": "string"}, "destination_address_id": {"description": "ID of the job destination associated with an address book entry. Optional if valid values are provided for destination address and latitude/longitude. If a valid destination address ID is provided, address/latitude/longitude will be used from the address book entry. Name of the address book entry will only be used if the destination name is not provided.", "example": 67890, "format": "int64", "type": "integer"}, "destination_lat": {"description": "Latitude of the destination in decimal degrees. Optional if a valid destination address ID is provided.", "example": 123.456, "format": "double", "type": "number"}, "destination_lng": {"description": "Longitude of the destination in decimal degrees. Optional if a valid destination address ID is provided.", "example": 37.459, "format": "double", "type": "number"}, "destination_name": {"description": "The name of the job destination. If provided, it will take precedence over the name of the address book entry.", "example": "ACME Inc. Philadelphia HQ", "type": "string"}, "external_ids": {"$ref": "#/components/schemas/V1DispatchRouteJobExternalIds"}, "notes": {"description": "Notes regarding the details of this job, maximum of 2000 characters; newline characters ('\\n')can be used for formatting.", "example": "Ensure crates are stacked no more than 3 high.", "type": "string"}, "scheduled_arrival_time_ms": {"description": "The time at which the assigned driver is scheduled to arrive at the job destination.", "example": 1462881998034, "format": "int64", "type": "integer"}, "scheduled_departure_time_ms": {"description": "The time at which the assigned driver is scheduled to depart from the job destination.", "example": 1462881998034, "format": "int64", "type": "integer"}}, "required": ["scheduled_arrival_time_ms"], "type": "object"}, "V1DispatchJobDocumentInfo": {"properties": {"driverId": {"description": "ID of driver that submitted the document.", "example": 1234, "type": "integer"}, "id": {"description": "ID of document. This can be used to query for the document's info via the /v1/fleet/drivers/{driver_id}/documents/{document_id} endpoint", "example": "2018_42424242", "type": "string"}}, "required": ["driverId", "id"], "type": "object"}, "V1DispatchJobWithoutETA": {"allOf": [{"$ref": "#/components/schemas/V1DispatchJobWithoutETA_allOf"}, {"$ref": "#/components/schemas/V1DispatchJobCreate"}]}, "V1DispatchJobWithoutETA_allOf": {"properties": {"arrived_at_ms": {"description": "The time at which the driver arrived at the job destination.", "example": 1462881998034, "format": "int64", "type": "integer"}, "completed_at_ms": {"description": "The time at which the job was marked complete (e.g. started driving to the next destination).", "example": 1462881998034, "format": "int64", "type": "integer"}, "dispatch_route_id": {"description": "ID of the route that this job belongs to.", "example": 55, "format": "int64", "type": "integer"}, "documents": {"description": "Document submissions associated with this job.", "items": {"$ref": "#/components/schemas/V1DispatchJobDocumentInfo"}, "type": "array"}, "driver_id": {"description": "ID of the driver assigned to the dispatch job.", "example": 444, "format": "int64", "type": "integer"}, "en_route_at_ms": {"description": "The time at which the assigned driver started fulfilling the job (e.g. started driving to the destination).", "example": 1462881998034, "format": "int64", "type": "integer"}, "fleet_viewer_url": {"description": "Fleet viewer url of the dispatch job.", "example": "https://cloud.samsara.com/fleet/viewer/job/fleet_viewer_token", "type": "string"}, "group_id": {"description": "Deprecated.", "example": 101, "format": "int64", "type": "integer"}, "id": {"description": "ID of the Samsara dispatch job.", "example": 773, "format": "int64", "type": "integer"}, "job_state": {"$ref": "#/components/schemas/V1JobStatus"}, "skipped_at_ms": {"description": "The time at which the job was marked skipped.", "example": 1462881998034, "format": "int64", "type": "integer"}, "vehicle_id": {"description": "ID of the vehicle used for the dispatch job.", "example": 112, "format": "int64", "type": "integer"}}, "required": ["dispatch_route_id", "id", "job_state"], "type": "object"}, "V1DispatchJob_allOf": {"properties": {"arrived_at_ms": {"description": "The time at which the driver arrived at the job destination.", "example": 1462881998034, "format": "int64", "type": "integer"}, "completed_at_ms": {"description": "The time at which the job was marked complete (e.g. started driving to the next destination).", "example": 1462881998034, "format": "int64", "type": "integer"}, "dispatch_route_id": {"description": "ID of the route that this job belongs to.", "example": 55, "format": "int64", "type": "integer"}, "documents": {"description": "Document submissions associated with this job.", "items": {"$ref": "#/components/schemas/V1DispatchJobDocumentInfo"}, "type": "array"}, "driver_id": {"description": "ID of the driver assigned to the dispatch job.", "example": 444, "format": "int64", "type": "integer"}, "en_route_at_ms": {"description": "The time at which the assigned driver started fulfilling the job (e.g. started driving to the destination).", "example": 1462881998034, "format": "int64", "type": "integer"}, "estimated_arrival_ms": {"description": "The time at which the assigned driver is estimated to arrive at the job destination. Only valid for en-route jobs.", "example": 1462881998034, "format": "int64", "type": "integer"}, "fleet_viewer_url": {"description": "Fleet viewer url of the dispatch job.", "example": "https://cloud.samsara.com/fleet/viewer/job/fleet_viewer_token", "type": "string"}, "group_id": {"description": "Deprecated.", "example": 101, "format": "int64", "type": "integer"}, "id": {"description": "ID of the Samsara dispatch job.", "example": 773, "format": "int64", "type": "integer"}, "job_state": {"$ref": "#/components/schemas/V1JobStatus"}, "skipped_at_ms": {"description": "The time at which the job was marked skipped.", "example": 1462881998034, "format": "int64", "type": "integer"}, "vehicle_id": {"description": "ID of the vehicle used for the dispatch job.", "example": 112, "format": "int64", "type": "integer"}}, "required": ["dispatch_route_id", "id", "job_state"], "type": "object"}, "V1DispatchRoute": {"allOf": [{"$ref": "#/components/schemas/V1DispatchRoute_allOf"}, {"$ref": "#/components/schemas/V1DispatchRouteBase"}]}, "V1DispatchRouteBase": {"properties": {"actual_end_ms": {"description": "The time in Unix epoch milliseconds that the route actually ended.", "example": 1462882101000, "format": "int64", "type": "integer"}, "actual_start_ms": {"description": "The time in Unix epoch milliseconds that the route actually started.", "example": 1462882098000, "format": "int64", "type": "integer"}, "complete_last_stop_on_arrival": {"default": true, "description": "When set to true (default), this causes the Route to complete on arrival at the final stop. When set to false, the last stop will capture arrival and departure separately as with other stops.", "type": "boolean"}, "driver_id": {"description": "ID of the driver assigned to the dispatch route. Note that driver_id and vehicle_id are mutually exclusive. If neither is specified, then the route is unassigned.", "example": 555, "format": "int64", "type": "integer"}, "externalIds": {"$ref": "#/components/schemas/V1DispatchRouteExternalIds"}, "group_id": {"description": "Deprecated.", "example": 101, "format": "int64", "type": "integer"}, "name": {"description": "Descriptive name of this route.", "example": "Bid #123", "type": "string"}, "notes": {"description": "Notes regarding the details of this route; maximum of 2000 characters; newline characters ('\\n')can be used for formatting.", "example": "Please make sure to confirm crate count at each stop on this route.\nTotal number of crates for route: 23.", "type": "string"}, "odometer_end_meters": {"description": "Odometer reading at the end of the route. Will not be returned if Route is not completed or if Odometer information is not available for the relevant vehicle.", "example": 2000000, "format": "int64", "type": "integer"}, "odometer_start_meters": {"description": "Odometer reading at the start of the route. Will not be returned if Route has not started or if Odometer information is not available for the relevant vehicle.", "example": 1000000, "format": "int64", "type": "integer"}, "scheduled_end_ms": {"description": "The time in Unix epoch milliseconds that the last job in the route is scheduled to end.", "example": 1462881998034, "format": "int64", "type": "integer"}, "scheduled_meters": {"description": "The distance expected to be traveled for this route in meters.", "example": 10000, "format": "int64", "type": "integer"}, "scheduled_start_ms": {"description": "The time in Unix epoch milliseconds that the route is scheduled to start.", "example": 1462881998034, "format": "int64", "type": "integer"}, "start_location_address": {"description": "The address of the route's starting location, as it would be recognized if provided to maps.google.com. Optional if a valid start location address ID is provided.", "example": "123 Main St, Philadelphia, PA 19106", "type": "string"}, "start_location_address_id": {"description": "ID of the start location associated with an address book entry. Optional if valid values are provided for start location address and latitude/longitude. If a valid start location address ID is provided, address/latitude/longitude will be used from the address book entry. Name of the address book entry will only be used if the start location name is not provided.", "example": 67890, "format": "int64", "type": "integer"}, "start_location_lat": {"description": "Latitude of the start location in decimal degrees. Optional if a valid start location address ID is provided.", "example": 123.456, "format": "double", "type": "number"}, "start_location_lng": {"description": "Longitude of the start location in decimal degrees. Optional if a valid start location address ID is provided.", "example": 37.459, "format": "double", "type": "number"}, "start_location_name": {"description": "The name of the route's starting location. If provided, it will take precedence over the name of the address book entry.", "example": "ACME Inc. Philadelphia HQ", "type": "string"}, "trailer_id": {"description": "ID of the trailer assigned to the dispatch route. Note that trailers can only be assigned to routes that have a Vehicle or Driver assigned to them.", "example": 666, "format": "int64", "type": "integer"}, "vehicle_id": {"description": "ID of the vehicle assigned to the dispatch route. Note that vehicle_id and driver_id are mutually exclusive. If neither is specified, then the route is unassigned.", "example": 444, "format": "int64", "type": "integer"}}, "type": "object"}, "V1DispatchRouteExternalIds": {"description": "The [external IDs](https://developers.samsara.com/docs/external-ids) for the given object.", "example": {"maintenanceId": "250020", "payrollId": "ABFS18600"}, "type": "object"}, "V1DispatchRouteHistoricalEntry": {"properties": {"changed_at_ms": {"description": "Timestamp that the route was updated, represented as Unix milliseconds since epoch.", "example": 1499411220000, "format": "int64", "type": "integer"}, "route": {"$ref": "#/components/schemas/V1DispatchRoute"}}, "type": "object"}, "V1DispatchRouteHistory": {"properties": {"history": {"description": "History of the route's state changes.", "items": {"$ref": "#/components/schemas/V1DispatchRouteHistoricalEntry"}, "type": "array"}}, "type": "object"}, "V1DispatchRouteJobExternalIds": {"description": "The [external IDs](https://developers.samsara.com/docs/external-ids) for the given object.", "example": {"maintenanceId": "250020", "payrollId": "ABFS18600"}, "type": "object"}, "V1DispatchRouteWithoutETA": {"allOf": [{"$ref": "#/components/schemas/V1DispatchRouteWithoutETA_allOf"}, {"$ref": "#/components/schemas/V1DispatchRouteBase"}]}, "V1DispatchRouteWithoutETA_allOf": {"properties": {"dispatch_jobs": {"description": "The dispatch jobs associated with this route.", "items": {"$ref": "#/components/schemas/V1DispatchJobWithoutETA"}, "type": "array"}, "id": {"description": "ID of the Samsara dispatch route.", "example": 556, "format": "int64", "type": "integer"}}, "type": "object"}, "V1DispatchRoute_allOf": {"properties": {"dispatch_jobs": {"description": "The dispatch jobs associated with this route.", "items": {"$ref": "#/components/schemas/V1DispatchJob"}, "type": "array"}, "id": {"description": "ID of the Samsara dispatch route.", "example": 556, "format": "int64", "type": "integer"}}, "type": "object"}, "V1DispatchRoutes": {"items": {"$ref": "#/components/schemas/V1DispatchRouteWithoutETA"}, "type": "array"}, "V1TripResponse": {"description": "Contains the trips for the vehicle in the requested timeframe. A trip is represented as an object that contains startMs, startLocation, startAddress, startCoordinates, endMs, endLocation, endAddress and endCoordinates. Ongoing trips will be returned with 9223372036854775807 as their endMs.", "properties": {"trips": {"items": {"$ref": "#/components/schemas/V1TripResponse_trips"}, "type": "array"}}, "type": "object"}, "V1TripResponse_endCoordinates": {"description": "End (latitude, longitude) in decimal degrees.", "properties": {"latitude": {"example": 23.413702345, "format": "double", "type": "number"}, "longitude": {"example": -91.502888123, "format": "double", "type": "number"}}, "type": "object"}, "V1TripResponse_startCoordinates": {"description": "Start (latitude, longitude) in decimal degrees.", "properties": {"latitude": {"example": 29.443702345, "format": "double", "type": "number"}, "longitude": {"example": -98.502888123, "format": "double", "type": "number"}}, "type": "object"}, "V1TripResponse_trips": {"properties": {"assetIds": {"description": "List of associated asset IDs", "example": [122], "items": {"format": "int64", "type": "integer"}, "type": "array"}, "codriverIds": {"description": "List of codriver IDs", "example": [122], "items": {"format": "int64", "type": "integer"}, "type": "array"}, "distanceMeters": {"description": "Length of the trip in meters. This value is calculated from the GPS data collected by the Samsara Vehicle Gateway.", "example": 2500, "type": "integer"}, "driverId": {"description": "ID of the driver.", "example": 719, "type": "integer"}, "endAddress": {"$ref": "#/components/schemas/V1TripResponse_endAddress"}, "endCoordinates": {"$ref": "#/components/schemas/V1TripResponse_endCoordinates"}, "endLocation": {"description": "Geocoded street address of start (latitude, longitude) coordinates.", "example": "571 S Lake Ave, Pasadena, CA 91101", "type": "string"}, "endMs": {"description": "End of the trip in UNIX milliseconds. Ongoing trips are indicated by an endMs value of 9223372036854775807.", "example": 1462881998034, "type": "integer"}, "endOdometer": {"description": "Odometer reading (in meters) at the end of the trip. This is read from the vehicle's on-board diagnostics. If <PERSON><PERSON><PERSON> cannot read the vehicle's odometer values from on-board diagnostics, this value will be 0.", "example": 210430500, "type": "integer"}, "fuelConsumedMl": {"description": "Amount in milliliters of fuel consumed on this trip.", "example": 75700, "type": "integer"}, "startAddress": {"$ref": "#/components/schemas/V1TripResponse_startAddress"}, "startCoordinates": {"$ref": "#/components/schemas/V1TripResponse_startCoordinates"}, "startLocation": {"description": "Geocoded street address of start (latitude, longitude) coordinates.", "example": "16 N Fair Oaks Ave, Pasadena, CA 91103", "type": "string"}, "startMs": {"description": "Beginning of the trip in UNIX milliseconds.", "example": 1462878398034, "type": "integer"}, "startOdometer": {"description": "Odometer reading (in meters) at the beginning of the trip. This is read from the vehicle's on-board diagnostics. If <PERSON><PERSON><PERSON> cannot read the vehicle's odometer values from on-board diagnostics, this value will be 0.", "example": 210430450, "type": "integer"}, "tollMeters": {"description": "Length in meters trip spent on toll roads.", "example": 32000, "type": "integer"}}, "type": "object"}, "WebhookRouteResponseObjectResponseBody": {"properties": {"actualRouteEndTime": {"description": "Actual end time, if it exists, for the route in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "actualRouteStartTime": {"description": "Actual start time, if it exists, for the route in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "ID of the route", "example": "342341", "type": "string"}, "name": {"description": "Route name", "example": "Bid 123", "type": "string"}, "notes": {"description": "Notes for the route", "example": "These are my notes", "type": "string"}, "scheduledRouteEndTime": {"description": "Scheduled end time, if it exists, for the route in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "scheduledRouteStartTime": {"description": "Scheduled start time, if it exists, for the route in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "settings": {"$ref": "#/components/schemas/RouteSettingsResponseBody"}, "stops": {"description": "List of stops along the route", "items": {"$ref": "#/components/schemas/RoutesStopResponseObjectResponseBody"}, "minItems": 2, "type": "array"}}, "required": ["id"], "type": "object"}, "baseRouteResponseObjectResponseBody": {"properties": {"actualRouteEndTime": {"description": "Actual end time, if it exists, for the route in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "actualRouteStartTime": {"description": "Actual start time, if it exists, for the route in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "driver": {"$ref": "#/components/schemas/GoaDriverTinyResponseResponseBody"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "ID of the route", "example": "342341", "type": "string"}, "name": {"description": "Route name", "example": "Bid 123", "type": "string"}, "notes": {"description": "Notes for the route", "example": "These are my notes", "type": "string"}, "orgLocalTimezone": {"description": "The local timezone, as defined in Settings for your organization. Timezones use [IANA timezone database](https://www.iana.org/time-zones) keys (e.g. `America/Los_Angeles`, `America/New_York`, `Europe/London`, etc.). You can find a mapping of common timezone formats to IANA timezone keys [here](https://unicode.org/cldr/charts/latest/supplemental/zone_tzid.html).", "example": "America/Los_Angeles", "type": "string"}, "recurringRouteLiveSharingLinks": {"description": "List of shareable, non-expired 'By recurring Route' Live Sharing Links.", "items": {"$ref": "#/components/schemas/LiveSharingLinkResponseObjectResponseBody"}, "type": "array"}, "scheduledRouteEndTime": {"description": "Scheduled end time, if it exists, for the route in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "scheduledRouteStartTime": {"description": "Scheduled start time, if it exists, for the route in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "settings": {"$ref": "#/components/schemas/RouteSettingsResponseBody"}, "stops": {"description": "List of stops along the route", "items": {"$ref": "#/components/schemas/RoutesStopResponseObjectResponseBody"}, "minItems": 2, "type": "array"}, "vehicle": {"$ref": "#/components/schemas/GoaVehicleTinyResponseResponseBody"}}, "required": ["id"], "type": "object"}}, "securitySchemes": {"AccessTokenHeader": {"type": "http", "scheme": "bearer"}}}, "paths": {}, "tags": [{"name": "Routes"}]}