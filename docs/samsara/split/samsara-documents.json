{"openapi": "3.0.1", "info": {"description": "Samsara API specification for documents related endpoints and schemas.", "title": "Samsara API - Documents", "version": "2025-05-12"}, "servers": [{"url": "https://api.samsara.com/"}, {"url": "https://api.eu.samsara.com/"}], "security": [{"AccessTokenHeader": []}], "components": {"parameters": {"V1documentQueryByParam": {"description": "Retrieve most recent documents based on either driver creation time or driver update time. If no value is provided, the default is `\"created\"`.", "in": "query", "name": "queryBy", "schema": {"enum": ["created", "updated"], "format": "string", "type": "string"}}, "V1documentsDurationMsParam": {"description": "Time in milliseconds that represents the duration before endMs to query. Defaults to 24 hours.", "in": "query", "name": "durationMs", "schema": {"format": "int64", "type": "integer"}}, "V1documentsEndMsParam": {"description": "Time in unix milliseconds that represents the oldest documents to return. Used in combination with durationMs. Defaults to now.", "in": "query", "name": "endMs", "schema": {"format": "int64", "type": "integer"}}}, "requestBodies": {"V1createDvirParam": {"content": {"application/json": {"schema": {"properties": {"inspectionType": {"description": "Only type 'mechanic' is currently accepted.", "enum": ["mechanic"], "example": "mechanic", "type": "string"}, "mechanicNotes": {"description": "Any notes from the mechanic.", "example": "Replaced headlight on passenger side.", "type": "string"}, "odometerMiles": {"description": "The current odometer of the vehicle.", "example": 38426, "type": "integer"}, "previousDefectsCorrected": {"description": "Whether any previous defects were corrected. If this vehicle or trailer was previously marked unsafe, and this DVIR marks it as safe, either previousDefectsCorrected or previousDefectsIgnored must be true.", "example": true, "type": "boolean"}, "previousDefectsIgnored": {"description": "Whether any previous defects were ignored. If this vehicle or trailer was previously marked unsafe, and this DVIR marks it as safe, either previousDefectsCorrected or previousDefectsIgnored must be true.", "example": false, "type": "boolean"}, "resolvedDefectIds": {"description": "List of defect IDs to resolve.  The defects must be associated with the provided vehicle or trailer.", "example": [18, 19], "items": {"format": "int64", "type": "integer"}, "type": "array"}, "safe": {"description": "Whether or not this vehicle or trailer is safe to drive.", "enum": ["safe", "unsafe"], "example": "safe", "type": "string"}, "trailerId": {"description": "Id of trailer being inspected. Either vehicleId or trailerId must be provided.", "example": 11, "type": "integer"}, "userEmail": {"description": "The Samsara login email for the person creating the DVIR. The email must correspond to a Samsara user's email.", "example": "<EMAIL>", "type": "string"}, "vehicleId": {"description": "Id of vehicle being inspected. Either vehicleId or trailerId must be provided.", "example": 10, "type": "integer"}}, "required": ["inspectionType", "safe", "userEmail"], "type": "object"}}}, "description": "DVIR creation body", "required": true}, "V1documentCreateParam": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1DocumentCreate"}}}, "description": "To create a document for a given document type, provide the `documentTypeUuid` of the type of document you'd like to create. Then, pass in the `fields` of the document in the same order that they show up in the given document type.", "required": true}}, "schemas": {"CreateDvirRequest": {"description": "DVIR creation body", "properties": {"authorId": {"description": "Samsara user ID of the mechanic creating the DVIR.", "example": "11", "type": "string"}, "licensePlate": {"description": "The license plate of this vehicle.", "example": "XHK1234", "maxLength": 12, "type": "string"}, "location": {"description": "Optional string if your jurisdiction requires a location of the DVIR.", "example": "350 Rhode Island St Ste. 400S, San Francisco, CA 94103", "type": "string"}, "mechanicNotes": {"description": "The mechanics notes on the DVIR.", "example": "Replaced headlight on passenger side.", "type": "string"}, "odometerMeters": {"description": "The odometer reading in meters.", "example": 14010293, "type": "integer"}, "resolvedDefectIds": {"description": "Array of ids for defects being resolved with this DVIR.", "items": {"type": "string"}, "type": "array"}, "safetyStatus": {"description": "Whether or not this vehicle or trailer is safe to drive.", "enum": ["safe", "unsafe"], "example": "safe", "type": "string"}, "trailerId": {"description": "Id of trailer being inspected. Either vehicleId or trailerId must be provided.", "example": "11", "type": "string"}, "type": {"description": "Only type 'mechanic' is currently accepted.", "enum": ["mechanic"], "example": "mechanic", "type": "string"}, "vehicleId": {"description": "Id of vehicle being inspected. Either vehicleId or trailerId must be provided.", "example": "10", "type": "string"}}, "required": ["authorId", "safetyStatus", "type"], "type": "object"}, "DVIRSubmittedDeviceTriggerDetailsObjectRequestBody": {"description": "Details specific to DVIR Submitted by Device", "properties": {"dvirMinDurationMilliseconds": {"description": "The trigger will only fire if the selected DVIR types are submitted within the duration.", "example": 600000, "format": "int64", "type": "integer"}, "dvirSubmissionTypes": {"description": "Filter to these types of DVIR submissions.", "example": ["SAFE_WITH_DEFECTS", "SAFE_WITH_DEFECTS"], "items": {"description": "DVIR submission type  Valid values: `SAFE_NO_DEFECTS`, `SAFE_WITH_DEFECTS`, `UNSAFE`", "enum": ["SAFE_NO_DEFECTS", "SAFE_WITH_DEFECTS", "UNSAFE"], "example": "SAFE_WITH_DEFECTS", "type": "string"}, "type": "array"}}, "type": "object"}, "DVIRSubmittedDeviceTriggerDetailsObjectResponseBody": {"description": "Details specific to DVIR Submitted by Device", "properties": {"dvirMinDurationMilliseconds": {"description": "The trigger will only fire if the selected DVIR types are submitted within the duration.", "example": 600000, "format": "int64", "type": "integer"}, "dvirSubmissionTypes": {"description": "Filter to these types of DVIR submissions.", "example": ["UNSAFE", "SAFE_WITH_DEFECTS", "SAFE_WITH_DEFECTS", "UNSAFE"], "items": {"description": "DVIR submission type  Valid values: `SAFE_NO_DEFECTS`, `SAFE_WITH_DEFECTS`, `UNSAFE`", "enum": ["SAFE_NO_DEFECTS", "SAFE_WITH_DEFECTS", "UNSAFE"], "example": "SAFE_NO_DEFECTS", "type": "string"}, "type": "array"}}, "type": "object"}, "DocumentPdfGenerationRequest": {"description": "Identifies a document for PDF generation.", "properties": {"documentId": {"description": "ID of the document.", "example": "6c8c0c01-206a-41a4-9d21-15b9978d04cb", "type": "string"}}, "required": ["documentId"], "type": "object"}, "DocumentPdfGenerationResponse": {"description": "Identifies a PDF generation job.", "properties": {"data": {"$ref": "#/components/schemas/DocumentPdfGenerationResponse_data"}}, "type": "object"}, "DocumentPdfGenerationResponse_data": {"properties": {"documentId": {"description": "ID of the document.", "example": "6c8c0c01-206a-41a4-9d21-15b9978d04cb", "type": "string"}, "id": {"description": "ID of the PDF file generated or being generated for the document.", "example": "5c8c0c01-206a-41a4-9d21-15b9978d04cb", "type": "string"}}, "type": "object"}, "DocumentPdfQueryResponse": {"description": "A PDF of a document", "properties": {"data": {"$ref": "#/components/schemas/DocumentPdfQueryResponse_data"}}, "type": "object"}, "DocumentPdfQueryResponse_data": {"properties": {"completedAtTime": {"description": "Time that PDF generation was completed, in RFC 3339 format.", "example": "2020-01-02T15:04:06+07:00", "type": "string"}, "documentId": {"description": "ID of the document.", "example": "6c8c0c01-206a-41a4-9d21-15b9978d04cb", "type": "string"}, "downloadDocumentPdfUrl": {"description": "S3 pre-signed URL to download PDF file.", "example": "https://samsara-driver-document-pdfs.s3.us-west-2.amazonaws.com/org/38487/42a4cffc-409d-4ddf-ba1c-5e3bbb961cba?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASI...&X-Amz-Date=20200423T162507Z&X-Amz-Expires=86400&X-Amz-Security-Token=IQoJ...-Amz-SignedHeaders=host&response-expires=2020-04-24T16%3A25%3A07Z&X-Amz-Signature=1c6fe87...", "type": "string"}, "id": {"description": "ID of the PDF file generated or being generated for the document", "example": "5c8c0c01-206a-41a4-9d21-15b9978d04cb", "type": "string"}, "jobStatus": {"description": "Describes status of the PDF generation job. Valid values: `requested`, `processing`, `completed`. ", "enum": ["requested", "processing", "completed"], "example": "Completed", "type": "string"}, "requestedAtTime": {"description": "Time that PDF generation was requested, in RFC 3339 format.", "example": "2020-01-02T15:04:05+07:00", "type": "string"}}, "type": "object"}, "DocumentTypesGetDocumentTypesBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentTypesGetDocumentTypesBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentTypesGetDocumentTypesGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentTypesGetDocumentTypesInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentTypesGetDocumentTypesMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentTypesGetDocumentTypesNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentTypesGetDocumentTypesNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentTypesGetDocumentTypesResponseBody": {"properties": {"data": {"description": "List of all document types for the organization", "example": [{"conditionalFieldSections": [{"conditionalFieldFirstIndex": 1, "conditionalFieldLastIndex": 2, "triggeringFieldIndex": 0, "triggeringFieldValue": "Yes"}], "fieldTypes": [{"fieldType": "multipleChoice", "label": "Was there damage?", "multipleChoiceFieldTypeMetaData": [{"label": "Yes"}, {"label": "No"}], "requiredField": true}, {"fieldType": "photo", "label": "Damage Photos", "requiredField": false}, {"fieldType": "number", "label": "Number of vehicles impacted", "numberFieldTypeMetaData": {"numberOfDecimalPlaces": 2}, "requiredField": false}, {"fieldType": "dateTime", "label": "Date and time", "requiredField": true}, {"fieldType": "signature", "label": "Sign", "requiredField": true, "signatureFieldTypeMetaData": {"legalText": "Legal Text"}}], "id": "4aff772c-a7bb-45e6-8e41-6a53e34feb83", "name": "Accident Report", "orgId": 12345}], "items": {"$ref": "#/components/schemas/getDocumentTypeResponseObjectResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "DocumentTypesGetDocumentTypesServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentTypesGetDocumentTypesTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentTypesGetDocumentTypesUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsDeleteDocumentBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsDeleteDocumentBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsDeleteDocumentGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsDeleteDocumentInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsDeleteDocumentMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsDeleteDocumentNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsDeleteDocumentNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsDeleteDocumentServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsDeleteDocumentTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsDeleteDocumentUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/documentResponseObjectResponseBody"}}, "type": "object"}, "DocumentsGetDocumentServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentsResponseBody": {"properties": {"data": {"description": "Multiple documents.", "items": {"$ref": "#/components/schemas/documentResponseObjectResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "DocumentsGetDocumentsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsGetDocumentsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsPostDocumentBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsPostDocumentBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsPostDocumentGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsPostDocumentInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsPostDocumentMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsPostDocumentNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsPostDocumentNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsPostDocumentRequestBody": {"properties": {"documentTypeId": {"description": "ID for the document type.", "example": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac7", "type": "string"}, "driverId": {"description": "ID of the driver. Can be either unique Samsara ID or an [external ID](https://developers.samsara.com/docs/external-ids) for the driver.", "example": "45646", "type": "string"}, "fields": {"description": "The fields associated with this document.", "items": {"$ref": "#/components/schemas/fieldObjectPostRequestBody"}, "type": "array"}, "name": {"description": "Name of the document.", "example": "Dropoff Slip 123", "type": "string"}, "notes": {"description": "Notes on the document.", "example": "Missing a crate", "maxLength": 2000, "type": "string"}, "routeStopId": {"description": "ID of the route stop. Can be either unique Samsara ID or an [external ID](https://developers.samsara.com/docs/external-ids) for the route stop.", "example": "45646", "type": "string"}, "state": {"default": "required", "description": "The condition of the document created for the driver. Can be either `required` or `submitted`, if no value is specified, `state` defaults to `required`. `required` documents are pre-populated documents for the Driver to fill out in the Driver App.  Valid values: `submitted`, `required`", "enum": ["submitted", "required"], "example": "submitted", "type": "string"}, "vehicleId": {"description": "ID of the vehicle. Can be either unique Samsara ID or an [external ID](https://developers.samsara.com/docs/external-ids) for the vehicle.", "example": "45646", "type": "string"}}, "required": ["documentTypeId", "driverId"], "type": "object"}, "DocumentsPostDocumentResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/documentResponseObjectResponseBody"}}, "type": "object"}, "DocumentsPostDocumentServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsPostDocumentTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DocumentsPostDocumentUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "Dvir": {"description": "Information about a DVIR.", "properties": {"authorSignature": {"$ref": "#/components/schemas/DvirAuthorSignature"}, "endTime": {"description": "Time when driver signed and completed this DVIR. UTC timestamp in RFC 3339 format. Example: `2020-01-27T07:06:25Z`.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "id": {"description": "Unique Samsara ID for the DVIR.", "example": "7107471", "type": "string"}, "licensePlate": {"$ref": "#/components/schemas/DvirLicensePlate"}, "location": {"$ref": "#/components/schemas/DvirLocation"}, "mechanicNotes": {"$ref": "#/components/schemas/DvirMechanicNotes"}, "odometerMeters": {"$ref": "#/components/schemas/DvirOdometerMeters"}, "safetyStatus": {"default": "unsafe", "description": "The condition of vehicle on which DVIR was done. Valid values: `safe`, `unsafe`, `resolved`.", "enum": ["safe", "unsafe", "resolved"], "example": "unsafe", "type": "string"}, "secondSignature": {"$ref": "#/components/schemas/DvirSecondSignature"}, "startTime": {"description": "Time when driver began filling out this DVIR. UTC timestamp in RFC 3339 format. Example: `2020-01-27T07:06:25Z`.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "thirdSignature": {"$ref": "#/components/schemas/DvirThirdSignature"}, "trailer": {"$ref": "#/components/schemas/DvirTrailer"}, "trailerDefects": {"$ref": "#/components/schemas/DvirTrailerDefects"}, "trailerName": {"description": "The name of the trailer the DVIR was submitted for.  Only included for tractor+trailer DVIRs.", "example": "Midwest Trailer #5", "type": "string"}, "type": {"default": "unspecified", "description": "Inspection type of the DVIR. Valid values: `preTrip`, `postTrip`, `mechanic`, `unspecified`.", "enum": ["preTrip", "postTrip", "mechanic", "unspecified"], "example": "preTrip", "type": "string"}, "vehicle": {"$ref": "#/components/schemas/DvirVehicle"}, "vehicleDefects": {"$ref": "#/components/schemas/DvirVehicleDefects"}}, "required": ["id"], "type": "object"}, "DvirAuthorSignature": {"allOf": [{"description": "An author signature object which has a signatory mini object with the signed time. Author Signature.", "type": "object"}, {"$ref": "#/components/schemas/DvirSignature"}]}, "DvirDefectGetDefectBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectGetDefectBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectGetDefectGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectGetDefectInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectGetDefectMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectGetDefectNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectGetDefectNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectGetDefectResponseBody": {"properties": {"comment": {"description": "Comment on the defect.", "example": "Engine failure.", "type": "string"}, "createdAtTime": {"description": "Time when defect was created in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "defectPhotos": {"description": "List of DVIR defect's photos", "items": {"$ref": "#/components/schemas/DefectPhotoResponseResponseBody"}, "type": "array"}, "defectTypeId": {"description": "The unique ID of the defect type.", "example": "25d6151e-29b5-453e-875a-7c5425332e09", "type": "string"}, "dvirId": {"description": "The unique ID of the defect's DVIR.", "example": "292371177", "type": "string"}, "id": {"description": "The unique ID of the DVIR defect.", "example": "9700544", "type": "string"}, "isResolved": {"description": "Signifies if this defect is resolved.", "example": true, "type": "boolean"}, "mechanicNotes": {"description": "The mechanics notes on the defect.", "example": "Broken passenger side window.", "type": "string"}, "resolvedAtTime": {"description": "Time when this defect was resolved in RFC 3339 format. Will not be returned if the defect is unresolved.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "resolvedBy": {"$ref": "#/components/schemas/DvirResolvedByObjectResponseBody"}, "trailer": {"$ref": "#/components/schemas/DefectTrailerResponseResponseBody"}, "updatedAtTime": {"description": "Time when defect was last updated in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "vehicle": {"$ref": "#/components/schemas/DefectVehicleResponseResponseBody"}}, "required": ["comment", "dvirId", "id", "isResolved"], "type": "object"}, "DvirDefectGetDefectServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectGetDefectTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectGetDefectUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectStreamDefectsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectStreamDefectsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectStreamDefectsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectStreamDefectsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectStreamDefectsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectStreamDefectsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectStreamDefectsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectStreamDefectsResponseBody": {"properties": {"data": {"description": "List of DVIR defects.", "items": {"$ref": "#/components/schemas/DefectsResponseDataResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "DvirDefectStreamDefectsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectStreamDefectsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectStreamDefectsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectTypeGetDefectTypesBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectTypeGetDefectTypesBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectTypeGetDefectTypesGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectTypeGetDefectTypesInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectTypeGetDefectTypesMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectTypeGetDefectTypesNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectTypeGetDefectTypesNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectTypeGetDefectTypesResponseBody": {"properties": {"data": {"description": "List of defect types.", "items": {"$ref": "#/components/schemas/DefectTypesResponseDataResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "DvirDefectTypeGetDefectTypesServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectTypeGetDefectTypesTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectTypeGetDefectTypesUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirDefectsObject_v2022_09_13ResponseBody": {"description": "A description of a DVIR defect", "properties": {"comment": {"description": "Comment on the defect.", "example": "Air compressor not working", "type": "string"}, "createdAtTime": {"description": "Time when the defect was created. UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "defectType": {"description": "The type of DVIR defect.", "example": "Air Compressor", "type": "string"}, "id": {"description": "The ID of the defect.", "example": "18", "type": "string"}, "isResolved": {"description": "Signifies if this defect is resolved.", "example": false, "type": "boolean"}, "mechanicNotes": {"description": "The mechanic notes on this defect.", "example": "Extremely large oddly shaped hole in passenger side window.", "type": "string"}, "mechanicNotesUpdatedAtTime": {"description": "Time when mechanic notes were last updated. UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "resolvedAtTime": {"description": "Time when this defect was resolved. Will not be returned if the defect is unresolved. UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "resolvedBy": {"$ref": "#/components/schemas/DvirResolvedByObjectResponseBody"}, "trailer": {"$ref": "#/components/schemas/GoaTrailerTinyResponseResponseBody"}, "vehicle": {"$ref": "#/components/schemas/VehicleWithGatewayTinyResponseResponseBody"}}, "required": ["createdAtTime", "defectType", "id", "isResolved"], "type": "object"}, "DvirGetDvirBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirGetDvirBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirGetDvirGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirGetDvirInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirGetDvirMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirGetDvirNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirGetDvirNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirGetDvirResponseBody": {"properties": {"authorSignature": {"$ref": "#/components/schemas/AuthorSignatureObjectResponseBody"}, "defectIds": {"description": "IDs of defects registered for the DVIR.", "example": ["8d218e6c-7a16-4f9f-90f7-cc1d93b9e596", "25d6151e-29b5-453e-875a-7c5425332e09"], "items": {"example": "Magni facere dolores aut blanditiis.", "type": "string"}, "type": "array"}, "dvirSubmissionBeginTime": {"description": "Time when driver created DVIR. UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "dvirSubmissionTime": {"description": "Time when driver submitted the DVIR. UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "formattedAddress": {"example": "350 Rhode Island St Ste. 400S, San Francisco, CA 94103", "type": "string"}, "id": {"description": "The unique id of the DVIR", "example": "12345", "type": "string"}, "mechanicNotes": {"description": "The mechanics notes on the DVIR.", "example": "Replaced headlight on passenger side.", "type": "string"}, "odometerMeters": {"description": "The odometer reading in meters.", "example": 91823, "format": "int64", "type": "integer"}, "safetyStatus": {"description": "The condition of vehicle on which DVIR was done.  Valid values: `unknown`, `safe`, `unsafe`, `resolved`", "enum": ["unknown", "safe", "unsafe", "resolved"], "example": "unsafe", "type": "string"}, "secondSignature": {"$ref": "#/components/schemas/AuthorSignatureObjectResponseBody"}, "thirdSignature": {"$ref": "#/components/schemas/AuthorSignatureObjectResponseBody"}, "trailer": {"$ref": "#/components/schemas/TrailerDvirObjectResponseBody"}, "type": {"description": "Inspection type of the DVIR.  Valid values: `preTrip`, `postTrip`, `mechanic`, `unspecified`", "enum": ["preTrip", "postTrip", "mechanic", "unspecified"], "example": "mechanic", "type": "string"}, "updatedAtTime": {"description": "Time of any DVIR updates. UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "vehicle": {"$ref": "#/components/schemas/VehicleDvirObjectResponseBody"}, "walkaroundPhotos": {"description": "List of walkaround photos", "items": {"$ref": "#/components/schemas/WalkaroundPhotoObjectResponseBody"}, "type": "array"}}, "required": ["authorSignature", "dvirSubmissionBeginTime", "dvirSubmissionTime", "id", "type", "updatedAtTime"], "type": "object"}, "DvirGetDvirServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirGetDvirTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirGetDvirUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirGetDvirsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirGetDvirsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirGetDvirsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirGetDvirsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirGetDvirsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirGetDvirsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirGetDvirsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirGetDvirsResponseBody": {"properties": {"data": {"items": {"$ref": "#/components/schemas/DvirStreamResponseDataResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "DvirGetDvirsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirGetDvirsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirGetDvirsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "DvirLicensePlate": {"description": "The license plate of this vehicle.", "example": "XHK1234", "maxLength": 12, "type": "string"}, "DvirMechanicNotes": {"description": "The mechanics notes on the DVIR.", "example": "Replaced headlight on passenger side.", "type": "string"}, "DvirOdometerMeters": {"description": "The odometer reading in meters.", "example": 14010293, "type": "integer"}, "DvirResolvedByObjectResponseBody": {"description": "The person who resolved this defect.", "properties": {"id": {"description": "ID of the entity that resolved this defect. If the defect was resolved by a driver, this will be a Samsara Driver ID. If the defect was resolved by a mechanic, this will be the Samsara Dashboard User ID of the mechanic.", "example": "8172", "type": "string"}, "name": {"description": "Name of the person who resolved this defect.", "example": "<PERSON>", "type": "string"}, "type": {"description": "Indicates whether this defect was resolved by a driver or a mechanic.  Valid values: `driver`, `mechanic`", "enum": ["driver", "mechanic"], "example": "mechanic", "type": "string"}}, "required": ["id", "name", "type"], "type": "object"}, "DvirResponse": {"description": "The DVIR response.", "properties": {"data": {"$ref": "#/components/schemas/Dvir"}}, "type": "object"}, "DvirSecondSignature": {"allOf": [{"description": "An author signature object which has a signatory mini object with the signed time. Second Signature.", "type": "object"}, {"$ref": "#/components/schemas/DvirSignature"}]}, "DvirSignature": {"description": "DVIR Signure.", "properties": {"signatoryUser": {"allOf": [{"description": "The user who signed the DVIR.", "type": "object"}, {"$ref": "#/components/schemas/userTinyResponse"}], "type": "object"}, "signedAtTime": {"description": "The time when the DVIR was signed. UTC timestamp in RFC 3339 format. Example: `2020-01-27T07:06:25Z`.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "type": {"default": "driver", "description": "Whether the DVIR was submitted by a `driver` or `mechanic`. Valid values: `driver`, `mechanic`.", "enum": ["driver", "mechanic"], "example": "driver", "type": "string"}}, "type": "object"}, "DvirStreamResponseDataResponseBody": {"properties": {"authorSignature": {"$ref": "#/components/schemas/AuthorSignatureObjectResponseBody"}, "defectIds": {"description": "IDs of defects registered for the DVIR.", "example": ["8d218e6c-7a16-4f9f-90f7-cc1d93b9e596", "25d6151e-29b5-453e-875a-7c5425332e09"], "items": {"example": "Vero minima quibusdam beatae.", "type": "string"}, "type": "array"}, "dvirSubmissionBeginTime": {"description": "Time when driver created DVIR. UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "dvirSubmissionTime": {"description": "Time when driver submitted the DVIR. UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "formattedAddress": {"example": "350 Rhode Island St Ste. 400S, San Francisco, CA 94103", "type": "string"}, "id": {"description": "The unique id of the DVIR", "example": "12345", "type": "string"}, "mechanicNotes": {"description": "The mechanics notes on the DVIR.", "example": "Replaced headlight on passenger side.", "type": "string"}, "odometerMeters": {"description": "The odometer reading in meters.", "example": 91823, "format": "int64", "type": "integer"}, "safetyStatus": {"description": "The condition of vehicle on which DVIR was done.  Valid values: `unknown`, `safe`, `unsafe`, `resolved`", "enum": ["unknown", "safe", "unsafe", "resolved"], "example": "unsafe", "type": "string"}, "secondSignature": {"$ref": "#/components/schemas/AuthorSignatureObjectResponseBody"}, "thirdSignature": {"$ref": "#/components/schemas/AuthorSignatureObjectResponseBody"}, "trailer": {"$ref": "#/components/schemas/TrailerDvirObjectResponseBody"}, "type": {"description": "Inspection type of the DVIR.  Valid values: `preTrip`, `postTrip`, `mechanic`, `unspecified`", "enum": ["preTrip", "postTrip", "mechanic", "unspecified"], "example": "mechanic", "type": "string"}, "updatedAtTime": {"description": "Time of any DVIR updates. UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "vehicle": {"$ref": "#/components/schemas/VehicleDvirObjectResponseBody"}, "walkaroundPhotos": {"description": "List of walkaround photos", "items": {"$ref": "#/components/schemas/WalkaroundPhotoObjectResponseBody"}, "type": "array"}}, "required": ["authorSignature", "dvirSubmissionBeginTime", "dvirSubmissionTime", "id", "type", "updatedAtTime"], "type": "object"}, "DvirThirdSignature": {"allOf": [{"description": "An author signature object which has a signatory mini object with the signed time. Third Signature.", "type": "object"}, {"$ref": "#/components/schemas/DvirSignature"}]}, "DvirTrailer": {"allOf": [{"description": "The trailer the DVIR was submitted for. Only included for trailer only DVIRs.", "type": "object"}, {"$ref": "#/components/schemas/trailerTinyResponse"}]}, "DvirTrailerDefects": {"description": "Defects registered for the trailer which was part of the DVIR.", "items": {"$ref": "#/components/schemas/dvirTrailerDefectsItems"}, "type": "array"}, "DvirsListResponse": {"description": "A list of DVIRs and pagination information.", "properties": {"data": {"description": "A list of DVIRs.", "items": {"$ref": "#/components/schemas/Dvir"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/paginationResponse"}}, "required": ["data", "pagination"], "type": "object"}, "FormSubmissionPdfExportResponseObjectResponseBody": {"description": "Form Submission PDF export response object.", "properties": {"completedAtTime": {"description": "Time when the PDF export job was completed. Included if 'jobStatus' is 'done'. UTC timestamp in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "errorMessage": {"description": "An error message for failed PDF export jobs. Included if 'jobStatus' is 'failed'.", "example": "PDF export timed out.", "type": "string"}, "expiresAtTime": {"description": "Time when the PDF export job expires. After expiration, GET requests for this job will fail and clients must create a new one with another POST request. UTC timestamp in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "id": {"description": "ID of the form submission being exported.", "example": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac7", "type": "string"}, "jobStatus": {"description": "Status of the PDF export job.  Valid values: `unknown`, `pending`, `done`, `failed`", "enum": ["unknown", "pending", "done", "failed"], "example": "pending", "type": "string"}, "pdfId": {"description": "Unique ID for the PDF export that is created.", "example": "300af62b-5aea-43a9-b4cf-a59667e817ed", "type": "string"}, "pdfUrl": {"description": "URL to download the PDF file. Expires at time specified in 'pdfUrlExpiresAtTime'. Included if 'jobStatus' is 'done'.", "example": "https://samsara-pdf-exports.s3.us-west-2.amazonaws.com/123456", "type": "string"}, "pdfUrlExpiresAtTime": {"description": "Time when the PDF export's 'pdfUrl' expires. After expiration, clients can retrieve a fresh url with another GET request. UTC timestamp in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "requestedAtTime": {"description": "Time when the PDF export POST request was made. UTC timestamp in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}}, "required": ["expiresAtTime", "id", "jobStatus", "pdfId", "requestedAtTime"], "type": "object"}, "FormSubmissionRequestApprovalDetailsRequestBody": {"description": "The details of a form approval. Only valid for forms that require approvals.", "properties": {"comment": {"description": "Comment for the approval decision. Sometimes required when updating status to 'changesRequested'. Only valid when requesting changes or approving a form submission.", "example": "Approving this submission", "type": "string"}}, "type": "object"}, "FormSubmissionRequestAssignedToRequestBody": {"description": "Form submission assignee update object", "properties": {"id": {"description": "ID of the form submission assignee.", "example": "938172", "type": "string"}, "type": {"description": "Type of the form submission assignee.  Valid values: `driver`, `user`", "enum": ["driver", "user"], "example": "driver", "type": "string"}}, "required": ["id", "type"], "type": "object"}, "FormSubmissionRequestCheckBoxesValueObjectRequestBody": {"description": "The value of a check boxes form input field. Only valid for check boxes form input fields.", "properties": {"valueIds": {"example": ["9814a1fa-f0c6-408b-bf85-51dc3bc71ac7", "1214a1fa-f0c6-408b-bf85-51dc3bc71ac7", "2214a1fa-f0c6-408b-bf85-51dc3bc71ac7", "3214a1fa-f0c6-408b-bf85-51dc3bc71ac7"], "items": {"example": "233374f1-297f-8711-5fc3-4f6b3731ad63", "format": "uuid", "type": "string"}, "type": "array"}}, "required": ["valueIds"], "type": "object"}, "FormSubmissionRequestDateTimeValueObjectRequestBody": {"description": "The value of a datetime form input field. Only valid for datetime form input fields.", "properties": {"value": {"description": "The value of the user generated date/time field response. UTC timestamp in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}}, "required": ["value"], "type": "object"}, "FormSubmissionRequestMultipleChoiceValueObjectRequestBody": {"description": "The value of a multiple choice form input field. Only valid for multiple choice form input fields.", "properties": {"valueId": {"example": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac7", "format": "uuid", "type": "string"}}, "required": ["valueId"], "type": "object"}, "FormSubmissionRequestNumberValueObjectRequestBody": {"description": "The value of a number form input field. Only valid for number form input fields.", "properties": {"value": {"example": 123.456, "format": "double", "type": "number"}}, "required": ["value"], "type": "object"}, "FormSubmissionRequestPersonObjectRequestBody": {"description": "Person object.", "properties": {"polymorphicUserId": {"description": "<PERSON><PERSON>a polymorphicUserID of the person.", "example": "user-12345", "type": "string"}}, "required": ["polymorphicUserId"], "type": "object"}, "FormSubmissionRequestPersonValueObjectRequestBody": {"description": "The value of an person form input field. Only valid for person form input fields.", "properties": {"person": {"$ref": "#/components/schemas/FormSubmissionRequestPersonObjectRequestBody"}}, "required": ["person"], "type": "object"}, "FormSubmissionRequestTableCellObjectRequestBody": {"description": "The value of a cell in a table row.", "properties": {"checkBoxesValue": {"$ref": "#/components/schemas/FormSubmissionRequestCheckBoxesValueObjectRequestBody"}, "dateTimeValue": {"$ref": "#/components/schemas/FormSubmissionRequestDateTimeValueObjectRequestBody"}, "id": {"description": "Unique identifier for the cell.", "example": "9fac4466-9d85-4768-9f1f-ff8f757f70c4", "format": "uuid", "type": "string"}, "multipleChoiceValue": {"$ref": "#/components/schemas/FormSubmissionRequestMultipleChoiceValueObjectRequestBody"}, "numberValue": {"$ref": "#/components/schemas/FormSubmissionRequestNumberValueObjectRequestBody"}, "personValue": {"$ref": "#/components/schemas/FormSubmissionRequestPersonValueObjectRequestBody"}, "textValue": {"$ref": "#/components/schemas/FormSubmissionRequestTextValueObjectRequestBody"}, "type": {"description": "Type of the cell field.  Valid values: `number`, `text`, `multiple_choice`, `check_boxes`, `datetime`, `person`", "enum": ["number", "text", "multiple_choice", "check_boxes", "datetime", "person"], "example": "number", "type": "string"}}, "required": ["id", "type"], "type": "object"}, "FormSubmissionRequestTableRowObjectRequestBody": {"description": "The value of a row in a table form input field.", "properties": {"cells": {"description": "List of cells in the row.", "items": {"$ref": "#/components/schemas/FormSubmissionRequestTableCellObjectRequestBody"}, "type": "array"}, "id": {"description": "Unique identifier for the row.", "example": "ee62df83-16e8-46ae-94d6-4933848f5e66", "format": "uuid", "type": "string"}}, "required": ["cells", "id"], "type": "object"}, "FormSubmissionRequestTableValueObjectRequestBody": {"description": "The value of a table form input field. Only valid for table form input fields.", "properties": {"rows": {"description": "List of rows in the table.", "items": {"$ref": "#/components/schemas/FormSubmissionRequestTableRowObjectRequestBody"}, "type": "array"}}, "required": ["rows"], "type": "object"}, "FormSubmissionRequestTextValueObjectRequestBody": {"description": "The value of a text form input field. Only valid for text form input fields.", "properties": {"value": {"example": "Exposed wires", "type": "string"}}, "required": ["value"], "type": "object"}, "FormSubmissionResponseObjectResponseBody": {"description": "Form Submission response object.", "properties": {"approvalDetails": {"$ref": "#/components/schemas/FormsProductSubmissionApprovalDetailsObjectResponseBody"}, "asset": {"$ref": "#/components/schemas/FormsAssetObjectResponseBody"}, "assignedAtTime": {"description": "Assignment time of the form submission. Sometimes returned if the submission was assigned to a user or driver. UTC timestamp in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "assignedTo": {"$ref": "#/components/schemas/FormsPolymorphicUserObjectResponseBody"}, "createdAtTime": {"description": "Creation time of the form submission. UTC timestamp in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "dueAtTime": {"description": "Time of when the submission is due. Sometimes returned, if the submission has a due date. UTC timestamp in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "fields": {"description": "List of field inputs in a form submission.", "items": {"$ref": "#/components/schemas/FormsFieldInputObjectResponseBody"}, "type": "array"}, "formTemplate": {"$ref": "#/components/schemas/FormTemplateReferenceObjectResponseBody"}, "geofence": {"$ref": "#/components/schemas/FormsGeofenceObjectResponseBody"}, "id": {"description": "ID of the form submission.", "example": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac7", "type": "string"}, "isRequired": {"description": "Indicates whether the worker is required to complete this form or not. Always returned.", "example": true, "type": "boolean"}, "location": {"$ref": "#/components/schemas/FormsLocationObjectResponseBody"}, "routeId": {"description": "ID of the route. Sometimes returned if the submission was assigned to a route stop.", "example": "123456789", "type": "string"}, "routeStopId": {"description": "ID of the route stop. Sometimes returned if the submission was assigned to a route stop.", "example": "987654321", "type": "string"}, "score": {"$ref": "#/components/schemas/FormsScoreObjectResponseBody"}, "status": {"description": "State for the Form Submission. Always returned.  Valid values: `notStarted`, `completed`, `archived`, `inProgress`, `needsReview`, `changesRequested`, `approved`", "enum": ["notStarted", "completed", "archived", "inProgress", "needsReview", "changesRequested", "approved"], "example": "notStarted", "type": "string"}, "submittedAtTime": {"description": "Submission time of the form submission. UTC timestamp in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "submittedBy": {"$ref": "#/components/schemas/FormsPolymorphicUserObjectResponseBody"}, "title": {"description": "Title of the form submission. Sometimes returned if the submission has a title.", "example": "Form Submission Title", "type": "string"}, "updatedAtTime": {"description": "Update time of the form submission. UTC timestamp in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}}, "required": ["createdAtTime", "fields", "formTemplate", "id", "isRequired", "status", "submittedAtTime", "submittedBy", "updatedAtTime"], "type": "object"}, "FormSubmissionsGetFormSubmissionsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsPdfExportsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsPdfExportsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsPdfExportsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsPdfExportsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsPdfExportsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsPdfExportsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsPdfExportsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsPdfExportsResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/FormSubmissionPdfExportResponseObjectResponseBody"}}, "required": ["data"], "type": "object"}, "FormSubmissionsGetFormSubmissionsPdfExportsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsPdfExportsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsPdfExportsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsResponseBody": {"properties": {"data": {"description": "List of form submissions.", "items": {"$ref": "#/components/schemas/FormSubmissionResponseObjectResponseBody"}, "type": "array"}}, "required": ["data"], "type": "object"}, "FormSubmissionsGetFormSubmissionsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsStreamBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsStreamBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsStreamGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsStreamInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsStreamMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsStreamNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsStreamNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsStreamResponseBody": {"properties": {"data": {"description": "List of form submissions.", "items": {"$ref": "#/components/schemas/FormSubmissionResponseObjectResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "FormSubmissionsGetFormSubmissionsStreamServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsStreamTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsStreamUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsGetFormSubmissionsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPatchFormSubmissionBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPatchFormSubmissionBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPatchFormSubmissionGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPatchFormSubmissionInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPatchFormSubmissionMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPatchFormSubmissionNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPatchFormSubmissionNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPatchFormSubmissionRequestBody": {"description": "Form submission fields to update.", "example": {"id": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac8", "status": "notStarted"}, "properties": {"approvalDetails": {"$ref": "#/components/schemas/FormSubmissionRequestApprovalDetailsRequestBody"}, "assignedTo": {"$ref": "#/components/schemas/FormSubmissionRequestAssignedToRequestBody"}, "dueAtTime": {"description": "Due date of the form submission. UTC timestamp in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "id": {"description": "ID of the form submission.", "example": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac7", "type": "string"}, "isRequired": {"description": "Indicates whether the worker is required to complete this form or not at a specific route stop. Defaults to `true` if the form is assigned to a user or driver. When true, the worker cannot depart the route stop until this form submission is `submitted`.", "example": true, "type": "boolean"}, "routeStopId": {"description": "ID of the route stop the form submission is assigned to. Must be a unique Samsara ID.", "example": "4070621712", "type": "string"}, "status": {"description": "Status of the form submission.  Valid values: `notStarted`, `archived`, `inProgress`, `changesRequested`, `approved`", "enum": ["notStarted", "archived", "inProgress", "changesRequested", "approved"], "example": "notStarted", "type": "string"}, "title": {"description": "Title of the form submission.", "example": "Job - J999", "maxLength": 255, "type": "string"}}, "required": ["id"], "type": "object"}, "FormSubmissionsPatchFormSubmissionResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/FormSubmissionResponseObjectResponseBody"}}, "required": ["data"], "type": "object"}, "FormSubmissionsPatchFormSubmissionServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPatchFormSubmissionTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPatchFormSubmissionUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionRequestBody": {"description": "Form submission fields to create.", "example": {"formTemplate": {"id": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac7", "revisionId": "1214a1fa-f0c6-408b-bf85-51dc3bc71ac7"}, "status": "notStarted"}, "properties": {"assignedTo": {"$ref": "#/components/schemas/FormSubmissionRequestAssignedToRequestBody"}, "dueAtTime": {"description": "Due date of the form submission. UTC timestamp in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "fields": {"description": "List of field inputs in a form submission.", "items": {"$ref": "#/components/schemas/FormSubmissionRequestFieldInputObjectRequestBody"}, "type": "array"}, "formTemplate": {"$ref": "#/components/schemas/FormTemplateRequestObjectRequestBody"}, "isRequired": {"description": "Indicates whether the worker is required to complete this form or not at a specific route stop. Defaults to `true` if the form is assigned to a user or driver. When true, the worker cannot depart the route stop until this form submission is `submitted`.", "example": true, "type": "boolean"}, "routeStopId": {"description": "ID of the route stop the form submission is assigned to. Must be a unique Samsara ID.", "example": "4070621712", "type": "string"}, "status": {"description": "Status of the form submission.  Valid values: `notStarted`", "enum": ["notStarted"], "example": "notStarted", "type": "string"}, "title": {"description": "Title of the form submission.", "example": "Job - J999", "maxLength": 255, "type": "string"}}, "required": ["formTemplate", "status"], "type": "object"}, "FormSubmissionsPostFormSubmissionResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/FormSubmissionResponseObjectResponseBody"}}, "required": ["data"], "type": "object"}, "FormSubmissionsPostFormSubmissionServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionsPdfExportsBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionsPdfExportsBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionsPdfExportsGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionsPdfExportsInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionsPdfExportsMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionsPdfExportsNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionsPdfExportsNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionsPdfExportsResponseBody": {"properties": {"data": {"$ref": "#/components/schemas/FormSubmissionPdfExportResponseObjectResponseBody"}}, "required": ["data"], "type": "object"}, "FormSubmissionsPostFormSubmissionsPdfExportsServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionsPdfExportsTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmissionsPostFormSubmissionsPdfExportsUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormSubmittedResponseBody": {"description": "Details specific to Form Submitted.", "properties": {"form": {"$ref": "#/components/schemas/FormSubmissionResponseObjectResponseBody"}}, "required": ["form"], "type": "object"}, "FormTemplateReferenceObjectResponseBody": {"description": "Form template reference object.", "properties": {"id": {"description": "ID of the form template.", "example": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac7", "format": "uuid", "type": "string"}, "revisionId": {"description": "ID of the form template revision.", "example": "1214a1fa-f0c6-408b-bf85-51dc3bc71ac7", "format": "uuid", "type": "string"}}, "required": ["id", "revisionId"], "type": "object"}, "FormTemplateRequestObjectRequestBody": {"description": "Form template request object.", "properties": {"id": {"description": "ID of the form template.", "example": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac7", "format": "uuid", "type": "string"}, "revisionId": {"description": "ID of the form template revision. Defaults to the latest template revision if not provided", "example": "1214a1fa-f0c6-408b-bf85-51dc3bc71ac7", "format": "uuid", "type": "string"}}, "required": ["id"], "type": "object"}, "FormTemplateResponseObjectResponseBody": {"description": "Form Template response object.", "properties": {"approvalConfig": {"$ref": "#/components/schemas/FormsApprovalConfigObjectResponseBody"}, "createdAtTime": {"description": "Creation time of the form template. UTC timestamp in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "createdBy": {"$ref": "#/components/schemas/FormsPolymorphicUserObjectResponseBody"}, "description": {"description": "Description of the form template. Sometimes returned if the template has a description.", "example": "This is a form template description.", "type": "string"}, "fields": {"description": "List of fields in the form template.", "items": {"$ref": "#/components/schemas/FormsFieldDefinitionObjectResponseBody"}, "type": "array"}, "id": {"description": "Unique identifier of the form template.", "example": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac7", "format": "uuid", "type": "string"}, "revisionId": {"description": "Unique identifier of the form template revision.", "example": "1214a1fa-f0c6-408b-bf85-51dc3bc71ac7", "format": "uuid", "type": "string"}, "sections": {"description": "List of sections in the form template.", "items": {"$ref": "#/components/schemas/FormTemplateSectionObjectResponseBody"}, "type": "array"}, "title": {"description": "Title of the form template.", "example": "Form Template Title", "type": "string"}, "updatedAtTime": {"description": "Update time of the form template. UTC timestamp in RFC 3339 format.", "example": "2019-06-13T19:08:25Z", "format": "date-time", "type": "string"}, "updatedBy": {"$ref": "#/components/schemas/FormsPolymorphicUserObjectResponseBody"}}, "required": ["createdAtTime", "created<PERSON>y", "fields", "id", "revisionId", "sections", "title", "updatedAtTime", "updatedBy"], "type": "object"}, "FormTemplateSectionObjectResponseBody": {"description": "Form Template section object.", "properties": {"fieldIndexFirstInclusive": {"description": "The index of the first field from the fields array that is in this section. Index 0 represents the first field definition of the fields array.", "example": 0, "format": "int64", "type": "integer"}, "fieldIndexLastInclusive": {"description": "The index of the last field from the fields array that is in this section.", "example": 9, "format": "int64", "type": "integer"}, "id": {"description": "Identifier of the section.", "example": "dbab5bc8-e41c-4b52-bfa5-fd2c7e809b00", "format": "uuid", "type": "string"}, "label": {"description": "Label of the section.", "example": "Engine Hours", "type": "string"}}, "required": ["fieldIndexFirstInclusive", "fieldIndexLastInclusive", "id", "label"], "type": "object"}, "FormTemplatesGetFormTemplatesBadGatewayErrorResponseBody": {"description": "Bad Gateway", "properties": {"message": {"description": "Message of error", "example": "rpc error: code = Unknown desc = connection refused", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormTemplatesGetFormTemplatesBadRequestErrorResponseBody": {"description": "Bad Request parameters", "properties": {"message": {"description": "Message of error", "example": "Invalid value for parameter.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormTemplatesGetFormTemplatesGatewayTimeoutErrorResponseBody": {"description": "Gateway timeout", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormTemplatesGetFormTemplatesInternalServerErrorResponseBody": {"description": "An internal server error occurred", "properties": {"message": {"description": "Message of error", "example": "Failed to execute GraphQL query.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormTemplatesGetFormTemplatesMethodNotAllowedErrorResponseBody": {"description": "Method not allowed", "properties": {"message": {"description": "Message of error", "example": "DELETE not allowed on /endpoint.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormTemplatesGetFormTemplatesNotFoundErrorResponseBody": {"description": "Resource not found", "properties": {"message": {"description": "Message of error", "example": "Object not found.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormTemplatesGetFormTemplatesNotImplementedErrorResponseBody": {"description": "Requested endpoint is not yet implemented", "properties": {"message": {"description": "Message of error", "example": "Not implemented.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormTemplatesGetFormTemplatesResponseBody": {"properties": {"data": {"description": "List of form templates.", "items": {"$ref": "#/components/schemas/FormTemplateResponseObjectResponseBody"}, "type": "array"}, "pagination": {"$ref": "#/components/schemas/GoaPaginationResponseResponseBody"}}, "required": ["data", "pagination"], "type": "object"}, "FormTemplatesGetFormTemplatesServiceUnavailableErrorResponseBody": {"description": "Service unavailable", "properties": {"message": {"description": "Message of error", "example": "context deadline exceeded", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormTemplatesGetFormTemplatesTooManyRequestsErrorResponseBody": {"description": "Too many requests", "properties": {"message": {"description": "Message of error", "example": "Exceeded rate limit.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormTemplatesGetFormTemplatesUnauthorizedErrorResponseBody": {"description": "Unauthorized", "properties": {"message": {"description": "Message of error", "example": "Invalid token.", "type": "string"}, "requestId": {"description": "The request ID; used when reaching out to support for issues with requests.", "example": "8916e1c1", "type": "string"}}, "required": ["message", "requestId"], "type": "object"}, "FormsActionObjectResponseBody": {"description": "Action object.", "properties": {"fieldId": {"description": "Identifier of the followup question that will be shown if the condition is met. Only returned when the action type is `askFollowupQuestion`.", "example": "bd673690-f09b-4f3d-93bd-99c4b2971554", "type": "string"}, "sectionId": {"description": "Identifier of the conditional section that will be shown if the condition is met. Only returned when the action type is `showSection`.", "example": "bd673690-f09b-4f3d-93bd-99c4b2971554", "type": "string"}, "type": {"description": "Type of action to take if corresponding condition is met.  Valid values: `askFollowupQuestion`, `showSection`, `requirePhoto`, `requireNote`, `createIssue`", "enum": ["askFollowupQuestion", "showSection", "requirePhoto", "requireNote", "createIssue"], "example": "askFollowupQuestion", "type": "string"}}, "required": ["type"], "type": "object"}, "FormsApprovalConfigObjectResponseBody": {"description": "Form Template approval configuration object.", "properties": {"singleApprovalConfig": {"$ref": "#/components/schemas/FormsSingleApprovalConfigObjectResponseBody"}, "type": {"description": "Type of approval.  Valid values: `singleApproval`", "enum": ["singleApproval"], "example": "singleApproval", "type": "string"}}, "required": ["type"], "type": "object"}, "FormsCheckBoxesValueObjectResponseBody": {"description": "The value of a check boxes form input field.", "properties": {"value": {"description": "List of selected options.", "example": ["Option A", "Option B"], "items": {"description": "Selected option", "example": "Voluptatem aut.", "type": "string"}, "type": "array"}, "valueIds": {"description": "List of selected option IDs.", "example": ["0cbbddb3-2541-4889-b4eb-92171cbfc142", "d33198cb-369f-4278-8120-d92d3ebf74bf"], "items": {"description": "Selected option ID", "example": "Dolor numquam architecto.", "type": "string"}, "type": "array"}}, "required": ["value", "valueIds"], "type": "object"}, "FormsConditionObjectResponseBody": {"description": "Forms condition object.", "properties": {"selectedOptionIds": {"description": "List of option IDs that will satisfy the condition if selected. For check boxes fields, the condition will be met if any of these option IDs are selected. Only returned for multiple choice or check boxes fields when the condition type is `multipleChoiceValueCondition` or `checkBoxesValueCondition`.", "example": ["9814a1fa-f0c6-408b-bf85-51dc3bc71ac7"], "items": {"example": "Ut ea.", "type": "string"}, "type": "array"}, "type": {"description": "Type of condition that must be met for actions to be taken.  Valid values: `multipleChoiceValueCondition`, `checkBoxesValueCondition`", "enum": ["multipleChoiceValueCondition", "checkBoxesValueCondition"], "example": "multipleChoiceValueCondition", "type": "string"}}, "required": ["type"], "type": "object"}, "FormsConditionalActionObjectResponseBody": {"description": "Conditional action object.", "properties": {"actions": {"description": "List of actions to take if the condition is met.", "items": {"$ref": "#/components/schemas/FormsActionObjectResponseBody"}, "type": "array"}, "condition": {"$ref": "#/components/schemas/FormsConditionObjectResponseBody"}}, "required": ["actions", "condition"], "type": "object"}, "FormsDateTimeValueObjectResponseBody": {"description": "The value of a datetime form input field.", "properties": {"type": {"description": "The type of datetime format.  Valid values: `datetime`, `date`, `time`", "enum": ["datetime", "date", "time"], "example": "datetime", "type": "string"}, "value": {"description": "UTC timestamp in RFC 3339 format.", "example": "2024-08-08T18:53:23Z", "format": "date-time", "type": "string"}}, "required": ["type", "value"], "type": "object"}, "FormsMultipleChoiceValueObjectResponseBody": {"description": "The value of a multiple choice form input field.", "properties": {"value": {"description": "Selected option.", "example": "Yes", "type": "string"}, "valueId": {"description": "ID of the selected option.", "example": "94096370-7228-4d83-ae5d-b20f3e45c0fc", "type": "string"}}, "required": ["value", "valueId"], "type": "object"}, "FormsNumberValueObjectResponseBody": {"description": "The value of a number form input field.", "properties": {"value": {"description": "Number value.", "example": 123.456, "format": "double", "type": "number"}}, "required": ["value"], "type": "object"}, "FormsPersonObjectResponseBody": {"description": "Tracked or untracked (i.e. manually entered) person object.", "properties": {"entryType": {"description": "The type of entry for the person.  Valid values: `tracked`, `untracked`", "enum": ["tracked", "untracked"], "example": "tracked", "type": "string"}, "name": {"description": "Name of an untracked (i.e. manually entered) person.", "example": "<PERSON>", "type": "string"}, "polymorphicUserId": {"$ref": "#/components/schemas/FormsPolymorphicUserObjectResponseBody"}}, "required": ["entryType"], "type": "object"}, "FormsPersonValueObjectResponseBody": {"description": "The value of a person form input field.", "properties": {"person": {"$ref": "#/components/schemas/FormsPersonObjectResponseBody"}}, "required": ["person"], "type": "object"}, "FormsPolymorphicUserObjectResponseBody": {"description": "User or driver object.", "properties": {"id": {"description": "ID of the polymorphic user.", "example": "938172", "type": "string"}, "type": {"description": "The type of the polymorphic user.  Valid values: `driver`, `user`", "enum": ["driver", "user"], "example": "driver", "type": "string"}}, "required": ["id", "type"], "type": "object"}, "FormsProductSubmissionApprovalDetailsObjectResponseBody": {"description": "The value of the approval details for a forms product submission.", "properties": {"comment": {"description": "Comment from the approver when requesting changes or approving the submission.", "example": "Requesting some changes to the submission.", "type": "string"}}, "required": ["comment"], "type": "object"}, "FormsSelectOptionObjectResponseBody": {"description": "Multiple choice or checkbox value option object.", "properties": {"id": {"description": "Identifier of the option.", "example": "e879028d-bce5-0238-ffec-11cd9236bcda", "type": "string"}, "ignoreQuestionFromScoreIfSelected": {"description": "Indicates whether the question should be ignored from the total score if this option is selected. Returns true if a score weight was not given to this option. Only present when the select form field has scoring.", "example": false, "type": "boolean"}, "label": {"description": "Label of the option.", "example": "Yes", "type": "string"}, "optionScoreWeight": {"description": "Score weight of the option, indicates number of score points received if this option is selected. Only present if the select form field has scoring.", "example": 5, "format": "int64", "type": "integer"}}, "required": ["id", "label"], "type": "object"}, "FormsSignatureValueObjectResponseBody": {"description": "The value of a signature form input field.", "properties": {"media": {"$ref": "#/components/schemas/FormsMediaRecordObjectResponseBody"}}, "required": ["media"], "type": "object"}, "FormsSingleApprovalConfigObjectResponseBody": {"description": "Single approval configuration object.", "properties": {"allowManualApproverSelection": {"description": "Indicates whether approver can be manually selected. True by default.", "example": true, "type": "boolean"}, "requirements": {"$ref": "#/components/schemas/SingleApprovalRequirementsObjectResponseBody"}}, "required": ["allowManualApproverSelection", "requirements"], "type": "object"}, "FormsTableCellObjectResponseBody": {"description": "Defines a cell in a table row.", "properties": {"checkBoxesValue": {"$ref": "#/components/schemas/FormsCheckBoxesValueObjectResponseBody"}, "dateTimeValue": {"$ref": "#/components/schemas/FormsDateTimeValueObjectResponseBody"}, "id": {"description": "Unique identifier for the cell.", "format": "uuid", "type": "string"}, "mediaValue": {"$ref": "#/components/schemas/FormsMediaValueObjectResponseBody"}, "multipleChoiceValue": {"$ref": "#/components/schemas/FormsMultipleChoiceValueObjectResponseBody"}, "numberValue": {"$ref": "#/components/schemas/FormsNumberValueObjectResponseBody"}, "personValue": {"$ref": "#/components/schemas/FormsPersonValueObjectResponseBody"}, "signatureValue": {"$ref": "#/components/schemas/FormsSignatureValueObjectResponseBody"}, "textValue": {"$ref": "#/components/schemas/FormsTextValueObjectResponseBody"}, "type": {"description": "Type of the cell field.  Valid values: `number`, `text`, `multiple_choice`, `check_boxes`, `datetime`, `signature`, `media`, `person`", "enum": ["number", "text", "multiple_choice", "check_boxes", "datetime", "signature", "media", "person"], "example": "number", "type": "string"}}, "required": ["id", "type"], "type": "object"}, "FormsTableColumnObjectResponseBody": {"description": "Defines a column in a table form input field.", "properties": {"id": {"description": "Unique identifier for the column.", "format": "uuid", "type": "string"}, "label": {"description": "Label of the column.", "example": "Store Number", "type": "string"}, "type": {"description": "Type of the column field.  Valid values: `text`, `number`, `datetime`, `check_boxes`, `multiple_choice`, `signature`, `media`, `person`", "enum": ["text", "number", "datetime", "check_boxes", "multiple_choice", "signature", "media", "person"], "example": "number", "type": "string"}}, "required": ["id", "label", "type"], "type": "object"}, "FormsTableRowObjectResponseBody": {"description": "Defines a row in a table form input field.", "properties": {"cells": {"description": "List of cells in the row.", "items": {"$ref": "#/components/schemas/FormsTableCellObjectResponseBody"}, "type": "array"}, "id": {"description": "Unique identifier for the row.", "format": "uuid", "type": "string"}}, "required": ["cells", "id"], "type": "object"}, "FormsTableValueObjectResponseBody": {"description": "The value of a table form input field.", "properties": {"columns": {"description": "List of table columns.", "items": {"$ref": "#/components/schemas/FormsTableColumnObjectResponseBody"}, "type": "array"}, "rows": {"description": "List of table rows.", "items": {"$ref": "#/components/schemas/FormsTableRowObjectResponseBody"}, "type": "array"}}, "required": ["columns", "rows"], "type": "object"}, "FormsTextValueObjectResponseBody": {"description": "The value of a text form input field.", "properties": {"value": {"description": "Text value.", "example": "Exposed wires", "type": "string"}}, "required": ["value"], "type": "object"}, "GoaDocumentTinyResponseResponseBody": {"description": "A minified Document object", "properties": {"id": {"description": "Id of the document", "example": "494123", "type": "string"}, "name": {"description": "Name of the document", "example": "Fuel Receipt Wichita", "type": "string"}}, "required": ["id"], "type": "object"}, "GoaDocumentTypeTinyResponseResponseBody": {"description": "A minified document type object", "properties": {"id": {"description": "ID of the document type.", "example": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac7", "type": "string"}, "name": {"description": "Name of the document type.", "example": "Fleet Truck List", "type": "string"}}, "type": "object"}, "TrailerDvirObjectResponseBody": {"description": "A trailer object", "properties": {"externalIds": {"additionalProperties": {"type": "string"}, "description": "A map of external ids", "type": "object"}, "id": {"description": "ID of the trailer", "example": "494123", "type": "string"}}, "type": "object"}, "UpdateDvirRequest": {"description": "Information about resolving a DVIR.", "properties": {"authorId": {"description": "The user who is resolving the dvir.", "example": "11", "type": "string"}, "isResolved": {"description": "Resolves the DVIR. Must be `true`.", "type": "boolean"}, "mechanicNotes": {"description": "The mechanics notes on the DVIR.", "example": "Replaced headlight on passenger side.", "type": "string"}, "signedAtTime": {"description": "Time when user signed this DVIR. Defaults to now. UTC timestamp in RFC 3339 format. Example: `2020-01-27T07:06:25Z`.", "example": "2020-01-27T07:06:25Z", "type": "string"}}, "required": ["authorId", "isResolved"], "type": "object"}, "V1Document": {"allOf": [{"$ref": "#/components/schemas/V1Document_allOf"}, {"$ref": "#/components/schemas/V1DocumentBase"}]}, "V1DocumentBase": {"properties": {"dispatchJobId": {"description": "ID of the Samsara dispatch job for which the document is submitted.", "example": 773, "format": "int64", "type": "integer"}, "name": {"description": "Custom name of the document. If no custom name is given to the document, the admin dashboard and driver app will display the template name as the default document name.", "example": "Fuel Receipt Wichita", "maxLength": 255, "minLength": 1, "type": "string"}, "notes": {"description": "Notes submitted with this document.", "example": "Fueled up before delivery.", "type": "string"}, "state": {"default": "Required", "description": "The condition of the document created for the driver. Can be either `Required` or `Submitted`. If no value is specified, `state` defaults to `Required`. `Required` documents are pre-populated documents for the Driver to fill out in the Driver App and have not yet been submitted. `Submitted` documents have been submitted by the driver in the Driver App. `Archived` documents have been archived by the admin in the cloud dashboard.", "enum": ["Required", "Submitted", "Archived"], "example": "Submitted", "type": "string"}}, "required": ["dispatchJobId", "notes"], "type": "object"}, "V1DocumentCreate": {"allOf": [{"$ref": "#/components/schemas/V1DocumentCreate_allOf"}, {"$ref": "#/components/schemas/V1DocumentCreateBase"}]}, "V1DocumentCreateBase": {"properties": {"dispatchJobId": {"description": "ID of the Samsara dispatch job for which the document is submitted.", "example": 773, "format": "int64", "type": "integer"}, "name": {"description": "Custom name of the document. If no custom name is given to the document, the admin dashboard and driver app will display the template name as the default document name.", "example": "Fuel Receipt Wichita", "maxLength": 255, "minLength": 1, "type": "string"}, "notes": {"description": "Notes submitted with this document.", "example": "Fueled up before delivery.", "type": "string"}, "state": {"default": "Required", "description": "The condition of the document created for the driver. Can be either `Required` or `Submitted`. If no value is specified, `state` defaults to `Required`. `Required` documents are pre-populated documents for the Driver to fill out in the Driver App and have not yet been submitted. `Submitted` documents will show up as submitted by the driver through the driver app.", "enum": ["Required", "Submitted"], "example": "Required", "type": "string"}}, "type": "object"}, "V1DocumentCreate_allOf": {"description": "Arguments to create a document.", "properties": {"documentTypeUuid": {"description": "Universally unique identifier for the document type that this document is being created for.", "example": "4aff772c-a7bb-45e6-8e41-6a53e34feb83", "type": "string"}, "fields": {"$ref": "#/components/schemas/V1DocumentCreateFields"}}, "required": ["documentTypeUuid", "fields"], "type": "object"}, "V1DocumentType": {"properties": {"fieldTypes": {"description": "The fields associated with this document type.", "items": {"$ref": "#/components/schemas/V1DocumentFieldType"}, "type": "array"}, "name": {"description": "Name of the document type.", "example": "Fuel Receipt", "type": "string"}, "orgId": {"description": "ID for the organization this document belongs to.", "example": 773, "format": "int64", "type": "integer"}, "uuid": {"description": "Universally unique identifier for the document type. Can be passed in as the `documentTypeUuid` when creating a document for this document type.", "example": "4aff772c-a7bb-45e6-8e41-6a53e34feb83", "type": "string"}}, "required": ["fieldTypes", "orgId", "uuid"], "type": "object"}, "V1DocumentTypes": {"items": {"$ref": "#/components/schemas/V1DocumentType"}, "type": "array"}, "V1Document_allOf": {"properties": {"conditionalFieldSections": {"$ref": "#/components/schemas/V1DocumentConditionalFields"}, "documentType": {"description": "Name of the document type.", "example": "Accident Report", "type": "string"}, "driverCreatedAtMs": {"description": "The time in Unix epoch milliseconds that the document was created in the driver app.", "example": 1462881998034, "format": "int64", "type": "integer"}, "driverId": {"description": "ID of the driver for whom the document is submitted.", "example": 555, "format": "int64", "type": "integer"}, "fields": {"$ref": "#/components/schemas/V1DocumentFields"}, "id": {"description": "ID of the document.", "example": "2018_42424242", "type": "string"}, "orgId": {"description": "Organization ID that the document belongs to.", "example": 555, "format": "int64", "type": "integer"}, "serverCreatedAtMs": {"description": "The time in Unix epoch milliseconds that the document was received by the server.", "example": 1462881998034, "format": "int64", "type": "integer"}, "serverUpdatedAtMs": {"description": "The time in Unix epoch milliseconds that the document was updated on the server.", "example": 1462881998034, "format": "int64", "type": "integer"}, "vehicleId": {"description": "ID of the vehicle the driver was signed into when the document was submitted. Will be `null` if the document `state` is `Required`.", "example": 222, "format": "int64", "type": "integer"}}, "required": ["documentType", "driverCreatedAtMs", "driverId", "fields", "id", "orgId", "serverCreatedAtMs", "serverUpdatedAtMs", "vehicleId"], "type": "object"}, "V1Documents": {"description": "List of documents.", "properties": {"driverDocuments": {"description": "List of documents.", "items": {"$ref": "#/components/schemas/V1Document"}, "type": "array"}}, "type": "object"}, "V1DvirBase": {"properties": {"authorSignature": {"$ref": "#/components/schemas/V1DvirBase_authorSignature"}, "defectsCorrected": {"description": "Signifies if the defects on the vehicle corrected after the DVIR is done.", "example": true, "type": "boolean"}, "defectsNeedNotBeCorrected": {"description": "Signifies if the defects on this vehicle can be ignored.", "example": false, "type": "boolean"}, "id": {"description": "The id of this DVIR record.", "example": 19, "format": "int64", "type": "integer"}, "inspectionType": {"description": "Inspection type of the DVIR. Valid values: `preTrip`, `postTrip`, `mechanic`, `unspecified`.", "example": "pre trip", "type": "string"}, "mechanicNotes": {"description": "The mechanics notes on the DVIR.", "example": "The vehicle is now safe.", "type": "string"}, "mechanicOrAgentSignature": {"$ref": "#/components/schemas/V1DvirBase_mechanicOrAgentSignature"}, "nextDriverSignature": {"$ref": "#/components/schemas/V1DvirBase_nextDriverSignature"}, "odometerMiles": {"description": "The odometer reading in miles for the vehicle when the DVIR was done.", "example": 49912, "format": "int64", "type": "integer"}, "startedAtMs": {"description": "Timestamp when driver began filling out this DVIR, in UNIX milliseconds.", "example": 1453449599999, "format": "int64", "type": "integer"}, "timeMs": {"description": "Timestamp of when this DVIR was signed & completed, in UNIX milliseconds.", "example": 1453449599999, "format": "int64", "type": "integer"}, "trailerDefects": {"description": "Defects registered for the trailer which was part of the DVIR.", "items": {"$ref": "#/components/schemas/V1DvirDefectBase"}, "type": "array"}, "trailerId": {"description": "The id of the trailer which was part of the DVIR.", "example": 19, "type": "integer"}, "trailerName": {"description": "The name of the trailer which was part of the DVIR.", "example": "Storer's Trailer 19", "type": "string"}, "vehicle": {"$ref": "#/components/schemas/V1DvirBase_vehicle"}, "vehicleCondition": {"description": "The condition of vechile on which DVIR was done.", "example": "SATISFACTORY", "type": "string"}, "vehicleDefects": {"description": "Defects registered for the vehicle which was part of the DVIR.", "items": {"$ref": "#/components/schemas/V1DvirDefectBase"}, "type": "array"}}, "type": "object"}, "V1DvirBase_authorSignature": {"description": "The authors signature for the DVIR.", "properties": {"driverId": {"description": "ID of the driver who signed the DVIR. Will not be returned if mechanic<PERSON><PERSON><PERSON><PERSON> is returned.", "example": 2581, "format": "int64", "type": "integer"}, "email": {"description": "Email of the  driver|mechanic who signed the DVIR.", "example": "<EMAIL>", "type": "string"}, "mechanicUserId": {"description": "ID of the mechanic who signed the DVIR. Will not be returned if driverId is returned.", "example": 14849, "format": "int64", "type": "integer"}, "name": {"description": "The name of the driver or mechanic who signed the DVIR.", "example": "<PERSON>", "type": "string"}, "signedAt": {"description": "The time in millis when the DVIR was signed", "example": 12535500000, "format": "int64", "type": "integer"}, "type": {"description": "Type corresponds to whether the signature corresponds to driver|mechanic.", "example": "driver", "type": "string"}, "username": {"description": "Userna<PERSON> of the  driver|mechanic who signed the DVIR.", "example": "jsmith", "type": "string"}}, "type": "object"}, "V1DvirBase_mechanicOrAgentSignature": {"description": "The mechanic's or agent's signature for the DVIR.", "properties": {"driverId": {"description": "ID of the driver who signed the DVIR. Will not be returned if mechanic<PERSON><PERSON><PERSON><PERSON> is returned.", "example": 2581, "format": "int64", "type": "integer"}, "email": {"description": "Email of the  agent|mechanic who signed the DVIR.", "example": "<EMAIL>", "type": "string"}, "mechanicUserId": {"description": "ID of the mechanic who signed the DVIR. Will not be returned if driverId is returned.", "example": 14849, "format": "int64", "type": "integer"}, "name": {"description": "The name of the agent or mechanic who signed the DVIR.", "example": "<PERSON>", "type": "string"}, "signedAt": {"description": "The time in millis when the DVIR was signed", "example": 12535500000, "format": "int64", "type": "integer"}, "type": {"description": "Type corresponds to whether the signature corresponds to driver|mechanic.", "example": "driver", "type": "string"}, "username": {"description": "Userna<PERSON> of the  agent|mechanic who signed the DVIR.", "example": "jsmith", "type": "string"}}, "type": "object"}, "V1DvirDefectBase": {"properties": {"comment": {"description": "The comment describing the type of DVIR defect.", "example": "Air Compressor not working", "type": "string"}, "defectType": {"description": "The type of DVIR defect. Possible values: [`AIR_COMPRESSOR`, `AIR_CONDITIONER`, `AIR_LINES`, `BATTERY`, `BELTS_HOSES`, `BRAKE_ACCESSORIES`, `BRAKE_CHECK`, `<PERSON>AKE_CONNECTIONS`, `<PERSON>AKE<PERSON>`, `<PERSON><PERSON>UTCH`, `COUPLING_DEVICES`, `DEFROSTER_HEATER`, `DOORS`, `DRIVE_LINE`, `EMERGENCY_DOOR_AND_BUZZER`, `ENGINE`, `ENTRANCE_STEPS`, `EXHAUST`, `FIFTH_WHEEL`, `FIRST_AID_KIT`, `FLUID_LEVELS`, `FRAME_ASSEMBLY`, `FRONT_AXLE`, `FUEL_TANKS`, `HORN`, `INTERIOR_AND_FLOOR`, `LANDING_GEAR`, `LIGHTS`, `MIRRORS`, `M<PERSON><PERSON><PERSON><PERSON>`, `O<PERSON>_PRESSURE`, `<PERSON><PERSON>ER`, `RA<PERSON>ATOR`, `REAR_END`, `REFLECTORS`, `ROOF`, `SAFETY_EQUIPMENT`, `STARTER`, `STEERING`, `STOP_ARM_CONTROL`, `STOP_ARM`, `SUSPENSION`, `TIRE_CHAINS`, `TIRES`, `TRANSMISSION`, `TRIP_RECORDER`, `WHEELS_RIMS`, `WINDOWS`, `WINDSHIELD_WIPERS`, `UNSET`]", "example": "AIR_COMPRESSOR", "type": "string"}, "id": {"description": "The id of this defect.", "example": 18, "format": "int64", "type": "integer"}, "resolved": {"description": "Signifies if this defect is resolved.", "example": true, "type": "boolean"}, "resolvedAt": {"description": "Timestamp when this defect was resolved, in UNIX milliseconds.  Will not be returned if the defect is unresolved.", "example": 1453449599999, "format": "int64", "type": "integer"}, "resolvedByDriverId": {"description": "ID of the driver who resolved this defect. Will not be returned if the defect is unresolved or resolvedByMechanicId is returned.", "example": 2581, "format": "int64", "type": "integer"}, "resolvedByMechanicId": {"description": "ID of the mechanic who resolved this defect. Will not be returned if the defect is unresolved or resolvedByDriverId is returned.", "example": 14849, "format": "int64", "type": "integer"}}, "type": "object"}, "V1DvirListResponse": {"properties": {"dvirs": {"items": {"$ref": "#/components/schemas/V1DvirBase"}, "type": "array"}}, "type": "object"}, "WorkflowDvirObjectResponseBody": {"description": "A DVIR description", "properties": {"authorSignature": {"$ref": "#/components/schemas/WorkflowAuthorSignatureObjectResponseBody"}, "defects": {"description": "Defects registered for the DVIR.", "items": {"$ref": "#/components/schemas/DvirDefectsObject_v2022_09_13ResponseBody"}, "type": "array"}, "endTime": {"description": "Time when the driver signed and completed this DVIR. UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "formattedLocation": {"description": "Optional string if your jurisdiction requires a location of the DVIR.", "example": "350 Rhode Island St Ste. 400S, San Francisco, CA 94103", "type": "string"}, "id": {"description": "The unique id of the DVIR", "example": "12345", "type": "string"}, "mechanicNotes": {"description": "The mechanics notes on the DVIR.", "example": "Replaced headlight on passenger side.", "type": "string"}, "needsCorrection": {"description": "Indicates if a defect needs correction.", "example": false, "type": "boolean"}, "odometerMeters": {"description": "The odometer reading in meters.", "example": 91823, "format": "int64", "type": "integer"}, "safetyStatus": {"description": "The condition of vehicle on which DVIR was done.  Valid values: `safe`, `unsafe`, `resolved`", "enum": ["safe", "unsafe", "resolved"], "example": "unsafe", "type": "string"}, "secondSignature": {"$ref": "#/components/schemas/WorkflowAuthorSignatureObjectResponseBody"}, "startTime": {"description": "Time when driver began filling out this DVIR in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "thirdSignature": {"$ref": "#/components/schemas/WorkflowAuthorSignatureObjectResponseBody"}, "trailer": {"$ref": "#/components/schemas/GoaTrailerTinyResponseResponseBody"}, "type": {"description": "Inspection type of the DVIR.  Valid values: `preTrip`, `postTrip`, `mechanic`, `unspecified`", "enum": ["preTrip", "postTrip", "mechanic", "unspecified"], "example": "mechanic", "type": "string"}}, "required": ["authorSignature", "endTime", "id", "needsCorrection", "safetyStatus", "startTime", "type"], "type": "object"}, "WorkflowDvirSubmittedResponseObjectResponseBody": {"description": "Details specific to DVIR Submitted.", "properties": {"driver": {"$ref": "#/components/schemas/GoaDriverTinyResponseResponseBody"}, "dvir": {"$ref": "#/components/schemas/WorkflowDvirObjectResponseBody"}, "vehicle": {"$ref": "#/components/schemas/VehicleWithGatewayTinyResponseResponseBody"}}, "type": "object"}, "documentResponseObjectResponseBody": {"description": "A single document.", "properties": {"conditionalFieldSections": {"description": "List of the document conditional field sections.", "items": {"$ref": "#/components/schemas/conditionalFieldSectionObjectResponseBody"}, "type": "array"}, "createdAtTime": {"description": "Time the document was created in RFC 3339 format.", "example": "1971-05-04T11:43:21Z", "format": "date-time", "type": "string"}, "documentType": {"$ref": "#/components/schemas/GoaDocumentTypeTinyResponseResponseBody"}, "driver": {"$ref": "#/components/schemas/GoaDriverTinyResponseResponseBody"}, "fields": {"description": "The fields associated with this document.", "items": {"$ref": "#/components/schemas/fieldObjectResponseBody"}, "type": "array"}, "id": {"description": "Universally unique identifier for the document.", "example": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac7", "type": "string"}, "name": {"description": "Name of the document.", "example": "Dropoff Slip 123", "type": "string"}, "notes": {"description": "Notes on the document.", "example": "Missing a crate", "type": "string"}, "route": {"$ref": "#/components/schemas/GoaRouteTinyResponseResponseBody"}, "routeStop": {"$ref": "#/components/schemas/GoaRouteStopTinyResponseResponseBody"}, "state": {"description": "The condition of the document created for the driver. Can be either Required or Submitted. Required documents are pre-populated documents for the Driver to fill out in the Driver App and have not yet been submitted. Submitted documents have been submitted by the driver in the Driver App. Archived documents have been archived by the admin in the cloud dashboard.  Valid values: `submitted`, `required`, `archived`", "enum": ["submitted", "required", "archived"], "example": "submitted", "type": "string"}, "updatedAtTime": {"description": "Time the document was updated in RFC 3339 format.", "example": "1979-12-10T00:51:01Z", "format": "date-time", "type": "string"}, "vehicle": {"$ref": "#/components/schemas/GoaVehicleTinyResponseResponseBody"}}, "required": ["createdAtTime", "documentType", "driver", "fields", "id", "state"], "type": "object"}, "dvirTrailerDefectsItems": {"properties": {"comment": {"description": "Comment on the defect.", "example": "Air Compressor not working", "type": "string"}, "createdAtTime": {"description": "Time when the defect was created. UTC timestamp in RFC 3339 format. Example: `2020-01-27T07:06:25Z`.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "defectType": {"description": "The type of DVIR defect.", "example": "Air Compressor", "type": "string"}, "id": {"description": "ID of the defect.", "example": "18", "type": "string"}, "isResolved": {"description": "Signifies if this defect is resolved.", "example": true, "type": "boolean"}, "mechanicNotes": {"description": "The mechanics notes on the defect.", "example": "Extremely large oddly shaped hole in passenger side window.", "type": "string"}, "mechanicNotesUpdatedAtTime": {"description": "Time when mechanic notes were last updated. UTC timestamp in RFC 3339 format. Example: `2020-01-27T07:06:25Z`.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "resolvedAtTime": {"description": "Time when this defect was resolved. Will not be returned if the defect is unresolved. UTC timestamp in RFC 3339 format. Example: `2020-01-27T07:06:25Z`.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "resolvedBy": {"$ref": "#/components/schemas/Defect_resolvedBy"}, "trailer": {"allOf": [{"description": "The trailer this defect was submitted for.", "type": "object"}, {"description": "A minified trailer object.", "properties": {"id": {"description": "ID of the trailer.", "example": "123456789", "type": "string"}, "name": {"description": "Name of the trailer.", "example": "Midwest Trailer #5", "type": "string"}}, "type": "object"}], "type": "object"}, "vehicle": {"allOf": [{"description": "The vehicle this defect was submitted for.", "type": "object"}, {"description": "A minified vehicle object.", "properties": {"ExternalIds": {"additionalProperties": {"type": "string"}, "description": "The [external IDs](https://developers.samsara.com/docs/external-ids) for the given object.", "example": {"maintenanceId": "250020", "payrollId": "ABFS18600"}, "type": "object"}, "id": {"description": "ID of the vehicle.", "example": "123456789", "type": "string"}, "name": {"description": "Name of the vehicle.", "example": "Midwest Truck #4", "type": "string"}}, "type": "object"}], "type": "object"}}, "required": ["id", "isResolved"], "type": "object", "x-go-gen-location": "models"}, "getDocumentTypeResponseObjectResponseBody": {"properties": {"conditionalFieldSections": {"description": "List of the document type conditional field sections.", "items": {"$ref": "#/components/schemas/conditionalFieldSectionObjectResponseBody"}, "type": "array"}, "fieldTypes": {"description": "The fields associated with this document type.", "items": {"$ref": "#/components/schemas/fieldTypesObjectResponseBody"}, "type": "array"}, "id": {"description": "Universally unique identifier for the document type. This value can be passed in as the documentTypeId when creating a document.", "example": "9814a1fa-f0c6-408b-bf85-51dc3bc71ac7", "type": "string"}, "name": {"description": "Name of the document type.", "example": "Bill's Fuel Receipts", "type": "string"}, "orgId": {"description": "ID for the organization this document type belongs to.", "example": 3771310580452555000, "format": "int64", "type": "integer"}}, "type": "object"}, "scannedDocumentValueObjectRequestBody": {"properties": {"id": {"description": "Id of the scanned document.", "example": "f5271458-21f9-4a9f-a290-780c6d8840ff", "type": "string"}, "url": {"description": "Url of the scanned document.", "example": "https://samsara-driver-media-upload.s3.us-west-2.amazonaws.com/123456", "type": "string"}}, "type": "object"}, "scannedDocumentValueObjectResponseBody": {"properties": {"id": {"description": "Id of the scanned document.", "example": "f5271458-21f9-4a9f-a290-780c6d8840ff", "type": "string"}, "url": {"description": "Url of the scanned document.", "example": "https://samsara-driver-media-upload.s3.us-west-2.amazonaws.com/123456", "type": "string"}}, "type": "object"}}, "securitySchemes": {"AccessTokenHeader": {"type": "http", "scheme": "bearer"}}}, "paths": {"/dvirs/stream": {"get": {"description": "Returns a history/feed of changed DVIRs by updatedAtTime between startTime and endTime parameters. In case of missing `endTime` parameter it will return a never ending stream of data.\n\n <b>Rate limit:</b> 5 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Read DVIRs** under the Maintenance category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "get<PERSON><PERSON><PERSON>", "parameters": [{"description": " If specified, this should be the endCursor value from the previous page of results. When present, this request will return the next page of results that occur immediately after the previous page of results.", "in": "query", "name": "after", "schema": {"type": "string"}}, {"description": "The limit for how many objects will be in the response. Default and max for this value is 200 objects.", "in": "query", "name": "limit", "schema": {"default": 200, "maximum": 200, "minimum": 1, "type": "integer"}}, {"description": "Optional boolean indicating whether to return external IDs on supported entities", "in": "query", "name": "includeExternalIds", "schema": {"type": "boolean"}}, {"description": "Required RFC 3339 timestamp to begin the feed or history by `updatedAtTime` at `startTime`.", "in": "query", "name": "startTime", "required": true, "schema": {"type": "string"}}, {"description": "Optional RFC 3339 timestamp. If not provided then the endpoint behaves as an unending feed of changes.", "in": "query", "name": "endTime", "schema": {"type": "string"}}, {"description": "Optional list of safety statuses. Valid values: [safe, unsafe, resolved]", "explode": true, "in": "query", "name": "safetyStatus", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirsResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirsUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirsNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirsMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirsTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirsInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirsNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirsBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirsServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirsGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirsBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "Stream DVIRs", "tags": ["Maintenance"]}}, "/dvirs/{id}": {"get": {"description": "Get a single DVIR by ID.\n\n <b>Rate limit:</b> 10 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>).\n\nTo use this endpoint, select **Read DVIRs** under the Maintenance category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>\n \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.", "operationId": "getDvir", "parameters": [{"description": "Id of the DVIR.", "in": "path", "name": "id", "required": true, "schema": {"type": "string"}}, {"description": "Optional boolean indicating whether to return external IDs on supported entities", "in": "query", "name": "includeExternalIds", "schema": {"type": "boolean"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirResponseBody"}}}, "description": "OK response."}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirUnauthorizedErrorResponseBody"}}}, "description": "Unauthorized response."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirNotFoundErrorResponseBody"}}}, "description": "Not Found response."}, "405": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirMethodNotAllowedErrorResponseBody"}}}, "description": "Method Not Allowed response."}, "429": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirTooManyRequestsErrorResponseBody"}}}, "description": "Too Many Requests response."}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirInternalServerErrorResponseBody"}}}, "description": "Internal Server Error response."}, "501": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirNotImplementedErrorResponseBody"}}}, "description": "Not Implemented response."}, "502": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirBadGatewayErrorResponseBody"}}}, "description": "Bad Gateway response."}, "503": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirServiceUnavailableErrorResponseBody"}}}, "description": "Service Unavailable response."}, "504": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirGatewayTimeoutErrorResponseBody"}}}, "description": "Gateway Timeout response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DvirGetDvirBadRequestErrorResponseBody"}}}, "description": "Bad Request response."}}, "summary": "[beta] Get a single DVIR by ID.", "tags": ["Beta APIs"]}}}, "tags": [{"name": "Documents"}]}