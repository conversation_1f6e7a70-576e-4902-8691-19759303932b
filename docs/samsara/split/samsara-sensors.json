{"openapi": "3.0.1", "info": {"description": "Samsara API specification for sensors related endpoints and schemas.", "title": "Samsara API - Sensors", "version": "2025-05-12"}, "servers": [{"url": "https://api.samsara.com/"}, {"url": "https://api.eu.samsara.com/"}], "security": [{"AccessTokenHeader": []}], "components": {"parameters": {}, "requestBodies": {"V1sensorParam": {"content": {"application/json": {"schema": {"properties": {"sensors": {"description": "List of sensor IDs to query.", "example": [122], "items": {"format": "int64", "type": "integer"}, "type": "array"}}, "required": ["sensors"], "type": "object"}}}, "description": "List of sensor IDs to query.", "required": true}}, "schemas": {"AmbientTemperatureDetailsObjectRequestBody": {"description": "Details specific to Ambient Temperature.", "properties": {"cargoIsFull": {"description": "Whether the cargo is full.", "example": true, "type": "boolean"}, "doorsAreClosed": {"description": "Whether the doors are closed.", "example": true, "type": "boolean"}, "minDurationMilliseconds": {"description": "The number of milliseconds the trigger needs to stay active before alerting.", "example": 600000, "format": "int64", "type": "integer"}, "operation": {"description": "How to evaluate the threshold.  Valid values: `GREATER`, `INSIDE_RANGE`, `LESS`, `OUTSIDE_RANGE`", "enum": ["GREATER", "INSIDE_RANGE", "LESS", "OUTSIDE_RANGE"], "example": "GREATER", "type": "string"}, "temperatureCelcius": {"description": "The temperature in Celcius threshold value.", "example": 60, "format": "int64", "type": "integer"}}, "required": ["minDurationMilliseconds", "operation", "temperatureCelcius"], "type": "object"}, "AmbientTemperatureDetailsObjectResponseBody": {"description": "Details specific to Ambient Temperature.", "properties": {"cargoIsFull": {"description": "Whether the cargo is full.", "example": true, "type": "boolean"}, "doorsAreClosed": {"description": "Whether the doors are closed.", "example": true, "type": "boolean"}, "minDurationMilliseconds": {"description": "The number of milliseconds the trigger needs to stay active before alerting.", "example": 600000, "format": "int64", "type": "integer"}, "operation": {"description": "How to evaluate the threshold.  Valid values: `GREATER`, `INSIDE_RANGE`, `LESS`, `OUTSIDE_RANGE`", "enum": ["GREATER", "INSIDE_RANGE", "LESS", "OUTSIDE_RANGE"], "example": "GREATER", "type": "string"}, "temperatureCelcius": {"description": "The temperature in Celcius threshold value.", "example": 60, "format": "int64", "type": "integer"}}, "required": ["minDurationMilliseconds", "operation", "temperatureCelcius"], "type": "object"}, "AmbientTemperatureResponseBody": {"description": "Details specific to Ambient Temperature.", "properties": {"sensor": {"$ref": "#/components/schemas/alertObjectSensorResponseBody"}}, "type": "object"}, "ReeferTemperatureResponseBody": {"description": "Details specific to Reefer Temperature.", "properties": {"driver": {"$ref": "#/components/schemas/alertObjectDriverResponseBody"}, "trailer": {"$ref": "#/components/schemas/alertObjectTrailerResponseBody"}, "vehicle": {"$ref": "#/components/schemas/alertObjectVehicleResponseBody"}}, "type": "object"}, "Sensor": {"properties": {"id": {"description": "ID of the sensor", "type": "string"}, "mac": {"description": "MAC address of the sensor", "type": "string"}, "name": {"description": "Name of the sensor", "type": "string"}}, "required": ["id", "mac", "name"], "type": "object"}, "TrailerStatReeferAmbientAirTemperatureMilliCTypeResponseBody": {"description": "Reefer ambient air temperature reading.", "properties": {"time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The ambient air temperature reading of the reefer in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "TrailerStatReeferAmbientAirTemperatureWithDecorationsTypeResponseBody": {"description": "Reefer ambient air temperature reading.", "properties": {"decorations": {"$ref": "#/components/schemas/TrailerStatDecorationResponseBody"}, "time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The ambient air temperature reading of the reefer in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "TrailerStatReeferReturnAirTemperatureMilliCZone1TypeResponseBody": {"description": "Return air temperature of zone 1 of the reefer. This is the temperature of the air as it enters the cooling unit.", "properties": {"time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The return air temperature reading in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "TrailerStatReeferReturnAirTemperatureMilliCZone1WithDecorationsTypeResponseBody": {"description": "Return air temperature of zone 1 of the reefer. This is the temperature of the air as it enters the cooling unit.", "properties": {"decorations": {"$ref": "#/components/schemas/TrailerStatDecorationResponseBody"}, "time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The return air temperature reading in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "TrailerStatReeferReturnAirTemperatureMilliCZone2TypeResponseBody": {"description": "Return air temperature of zone 2 of the reefer. This is the temperature of the air as it enters the cooling unit.", "properties": {"time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The return air temperature reading in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "TrailerStatReeferReturnAirTemperatureMilliCZone2WithDecorationsTypeResponseBody": {"description": "Return air temperature of zone 2 of the reefer. This is the temperature of the air as it enters the cooling unit.", "properties": {"decorations": {"$ref": "#/components/schemas/TrailerStatDecorationResponseBody"}, "time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The return air temperature reading in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "TrailerStatReeferReturnAirTemperatureMilliCZone3TypeResponseBody": {"description": "Return air temperature of zone 3 of the reefer. This is the temperature of the air as it enters the cooling unit.", "properties": {"time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The return air temperature reading in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "TrailerStatReeferReturnAirTemperatureMilliCZone3WithDecorationsTypeResponseBody": {"description": "Return air temperature of zone 3 of the reefer. This is the temperature of the air as it enters the cooling unit.", "properties": {"decorations": {"$ref": "#/components/schemas/TrailerStatDecorationResponseBody"}, "time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The return air temperature reading in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "TrailerStatReeferSetPointTemperatureMilliCZone1TypeResponseBody": {"description": "Set point temperature of zone 1 of the reefer.", "properties": {"time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The set point temperature reading in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "TrailerStatReeferSetPointTemperatureMilliCZone1WithDecorationsTypeResponseBody": {"description": "Set point temperature of zone 1 of the reefer.", "properties": {"decorations": {"$ref": "#/components/schemas/TrailerStatDecorationResponseBody"}, "time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The set point temperature reading in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "TrailerStatReeferSetPointTemperatureMilliCZone2TypeResponseBody": {"description": "Set point temperature of zone 2 of the reefer.", "properties": {"time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The set point temperature reading in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "TrailerStatReeferSetPointTemperatureMilliCZone2WithDecorationsTypeResponseBody": {"description": "Set point temperature of zone 2 of the reefer.", "properties": {"decorations": {"$ref": "#/components/schemas/TrailerStatDecorationResponseBody"}, "time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The set point temperature reading in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "TrailerStatReeferSetPointTemperatureMilliCZone3TypeResponseBody": {"description": "Set point temperature of zone 3 of the reefer.", "properties": {"time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The set point temperature reading in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "TrailerStatReeferSetPointTemperatureMilliCZone3WithDecorationsTypeResponseBody": {"description": "Set point temperature of zone 3 of the reefer.", "properties": {"decorations": {"$ref": "#/components/schemas/TrailerStatDecorationResponseBody"}, "time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The set point temperature reading in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "TrailerStatReeferSupplyAirTemperatureMilliCZone1TypeResponseBody": {"description": "Supply or discharge air temperature of zone 2 of the reefer. This is the temperature of the air as it leaves the cooling unit.", "properties": {"time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The supply or discharge air temperature reading in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "TrailerStatReeferSupplyAirTemperatureMilliCZone1WithDecorationsTypeResponseBody": {"description": "Supply or discharge air temperature of zone 2 of the reefer. This is the temperature of the air as it leaves the cooling unit.", "properties": {"decorations": {"$ref": "#/components/schemas/TrailerStatDecorationResponseBody"}, "time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The supply or discharge air temperature reading in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "TrailerStatReeferSupplyAirTemperatureMilliCZone2TypeResponseBody": {"description": "Supply or discharge air temperature of zone 2 of the reefer. This is the temperature of the air as it leaves the cooling unit.", "properties": {"time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The supply or discharge air temperature reading in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "TrailerStatReeferSupplyAirTemperatureMilliCZone2WithDecorationsTypeResponseBody": {"description": "Supply or discharge air temperature of zone 2 of the reefer. This is the temperature of the air as it leaves the cooling unit.", "properties": {"decorations": {"$ref": "#/components/schemas/TrailerStatDecorationResponseBody"}, "time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The supply or discharge air temperature reading in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "TrailerStatReeferSupplyAirTemperatureMilliCZone3TypeResponseBody": {"description": "Supply or discharge air temperature of zone 2 of the reefer. This is the temperature of the air as it leaves the cooling unit.", "properties": {"time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The supply or discharge air temperature reading in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "TrailerStatReeferSupplyAirTemperatureMilliCZone3WithDecorationsTypeResponseBody": {"description": "Supply or discharge air temperature of zone 2 of the reefer. This is the temperature of the air as it leaves the cooling unit.", "properties": {"decorations": {"$ref": "#/components/schemas/TrailerStatDecorationResponseBody"}, "time": {"description": "UTC timestamp in RFC 3339 format.", "example": "2020-01-27T07:06:25Z", "type": "string"}, "value": {"description": "The supply or discharge air temperature reading in millidegree Celsius.", "example": 50, "format": "int64", "type": "integer"}}, "required": ["time", "value"], "type": "object"}, "V1CargoResponse_sensors": {"properties": {"cargoEmpty": {"description": "Flag indicating whether the current cargo is empty or loaded.", "example": true, "type": "boolean"}, "cargoStatusTime": {"description": "The timestamp of reported cargo status, specified in RFC 3339 time.", "example": "2019-04-17T16:42:55Z", "type": "string"}, "id": {"description": "ID of the sensor.", "example": 122, "format": "int64", "type": "integer"}, "name": {"description": "Name of the sensor.", "example": "Trailer Cargo V1Sensor", "type": "string"}, "redEyeDistance": {"description": "The distance between red eye detector and the closest object in cm.", "example": 175, "type": "integer"}, "trailerId": {"description": "ID of the trailer associated with the sensor for the data point. If no trailer is connected, this parameter will not be reported.", "example": 123, "type": "integer"}, "vehicleId": {"description": "ID of the vehicle associated with the sensor for the data point. If no vehicle is connected, this parameter will not be reported.", "example": 124, "type": "integer"}}, "type": "object"}, "V1DoorResponse_sensors": {"properties": {"doorClosed": {"description": "Flag indicating whether the current door is closed or open.", "example": true, "type": "boolean"}, "doorStatusTime": {"description": "The timestamp of reported door status, specified in RFC 3339 time.", "example": "2019-04-17T16:42:55Z", "type": "string"}, "id": {"description": "ID of the sensor.", "example": 122, "format": "int64", "type": "integer"}, "name": {"description": "Name of the sensor.", "example": "Trailer Door V1Sensor", "type": "string"}, "trailerId": {"description": "ID of the trailer associated with the sensor for the data point. If no trailer is connected, this parameter will not be reported.", "example": 123, "type": "integer"}, "vehicleId": {"description": "ID of the vehicle associated with the sensor for the data point. If no vehicle is connected, this parameter will not be reported.", "example": 124, "type": "integer"}}, "type": "object"}, "V1HumidityResponse": {"description": "Contains the current humidity of a sensor.", "properties": {"groupId": {"description": "Deprecated.", "example": 101, "format": "int64", "type": "integer"}, "sensors": {"items": {"$ref": "#/components/schemas/V1HumidityResponse_sensors"}, "type": "array"}}, "type": "object"}, "V1HumidityResponse_sensors": {"properties": {"humidity": {"description": "Currently reported relative humidity in percent, from 0-100.", "example": 53, "type": "integer"}, "humidityTime": {"description": "The timestamp of reported relative humidity, specified in RFC 3339 time.", "example": "2019-04-17T16:42:55Z", "type": "string"}, "id": {"description": "ID of the sensor.", "example": 122, "format": "int64", "type": "integer"}, "name": {"description": "Name of the sensor.", "example": "Freezer Humidity V1Sensor", "type": "string"}, "trailerId": {"description": "ID of the trailer associated with the sensor for the data point. If no trailer is connected, this parameter will not be reported.", "example": 123, "type": "integer"}, "vehicleId": {"description": "ID of the vehicle associated with the sensor for the data point. If no vehicle is connected, this parameter will not be reported.", "example": 124, "type": "integer"}}, "type": "object"}, "V1Sensor": {"description": "Contains information about a sensor.", "properties": {"id": {"description": "ID of the sensor.", "example": 123, "format": "int64", "type": "integer"}, "macAddress": {"description": "MAC address of the sensor.", "example": "11:11:11:11:11:11", "type": "string"}, "name": {"description": "Name of the sensor.", "example": "Freezer ABC", "type": "string"}}, "required": ["id"], "type": "object"}, "V1SensorHistoryResponse": {"description": "Contains the results for a sensor history request. Each result contains a timestamp and datapoint for each requested (sensor, field) pair.", "properties": {"results": {"items": {"$ref": "#/components/schemas/V1SensorHistoryResponse_results"}, "type": "array"}}, "type": "object"}, "V1SensorHistoryResponse_results": {"properties": {"series": {"description": "List of datapoints, one for each requested (sensor, field) pair.", "items": {"example": 1, "format": "int64", "type": "integer"}, "type": "array"}, "timeMs": {"description": "Timestamp in UNIX milliseconds.", "example": 1453449599999, "type": "integer"}}, "type": "object"}, "V1TemperatureResponse": {"description": "Contains the current temperatures of a sensor.", "properties": {"groupId": {"description": "Deprecated.", "example": 101, "format": "int64", "type": "integer"}, "sensors": {"items": {"$ref": "#/components/schemas/V1TemperatureResponse_sensors"}, "type": "array"}}, "type": "object"}, "V1TemperatureResponse_sensors": {"properties": {"ambientTemperature": {"description": "Currently reported ambient temperature in millidegrees celsius.", "example": 11057, "type": "integer"}, "ambientTemperatureTime": {"description": "The timestamp of reported ambient temperature, specified in RFC 3339 time.", "example": "2019-04-17T16:42:55Z", "type": "string"}, "id": {"description": "ID of the sensor.", "example": 122, "format": "int64", "type": "integer"}, "name": {"description": "Name of the sensor.", "example": "Freezer Temp V1Sensor", "type": "string"}, "probeTemperature": {"description": "Currently reported probe temperature in millidegrees celsius. If no probe is connected, this parameter will not be reported.", "example": -20145, "type": "integer"}, "probeTemperatureTime": {"description": "The timestamp of reported probe temperature, specified in RFC 3339 time.", "example": "2019-04-17T16:42:55Z", "type": "string"}, "trailerId": {"description": "ID of the trailer associated with the sensor for the data point. If no trailer is connected, this parameter will not be reported.", "example": 123, "type": "integer"}, "vehicleId": {"description": "ID of the vehicle associated with the sensor for the data point. If no vehicle is connected, this parameter will not be reported.", "example": 124, "type": "integer"}}, "type": "object"}, "_v1_sensors_history_series": {"description": "V1Sensor ID and field to query.", "properties": {"field": {"description": "Field to query.", "enum": ["ambientTemperature", "cargoPercent", "currentLoop1Raw", "currentLoop1Mapped", "currentLoop2Raw", "currentLoop2Mapped", "doorClosed", "humidity", "pmPowerTotal", "pmPhase1Power", "pmPhase2Power", "pmPhase3Power", "pmPhase1PowerFactor", "pmPhase2PowerFactor", "pmPhase3PowerFactor", "probeTemperature"], "example": "ambientTemperature", "type": "string"}, "widgetId": {"description": "V1Sensor ID to query.", "example": 1, "format": "int64", "type": "integer"}}, "required": ["field", "widgetId"], "type": "object"}, "alertObjectSensorResponseBody": {"description": "A sensor associated with the alert.", "properties": {"id": {"description": "Thye ID of the sensor associated with the alert", "example": "22222", "type": "string"}, "name": {"description": "The name of the sensor.", "example": "Sensor-123", "type": "string"}, "pinnedDeviceId": {"description": "The Pinned Device ID associated with the alert", "example": "22222", "type": "string"}, "product": {"$ref": "#/components/schemas/alertObjectProductResponseBody"}, "tags": {"description": "The list of [tags](https://kb.samsara.com/hc/en-us/articles/360026674631-Using-Tags-and-Tag-Nesting) associated with the sensor.", "items": {"$ref": "#/components/schemas/GoaTagTinyResponseResponseBody"}, "type": "array"}}, "required": ["id"], "type": "object"}, "sensorResponseBody": {"description": "A sensor", "properties": {"id": {"description": "ID of the sensor", "example": "12345", "type": "string"}, "mac": {"description": "The MAC address of the sensor", "example": "00:00:5e:00:53:af", "type": "string"}, "name": {"description": "The name of the sensor", "example": "Rear temperature sensor", "type": "string"}}, "required": ["id", "mac", "name"], "type": "object"}}, "securitySchemes": {"AccessTokenHeader": {"type": "http", "scheme": "bearer"}}}, "paths": {"/v1/sensors/cargo": {"post": {"description": "<n class=\"warning\">\n<nh>\n<i class=\"fa fa-exclamation-circle\"></i>\nThis endpoint is still on our legacy API.\n</nh>\n</n>\n\nGet cargo monitor status (empty / full) for requested sensors. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Sensors** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "V1getSensorsCargo", "requestBody": {"$ref": "#/components/requestBodies/inline_object_4", "content": {"application/json": {"schema": {"properties": {"sensors": {"description": "List of sensor IDs to query.", "example": [122], "items": {"format": "int64", "type": "integer"}, "type": "array"}}, "required": ["sensors"], "type": "object"}}}, "description": "List of sensor IDs to query.", "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1CargoResponse"}}}, "description": "List of sensor objects containing the current cargo status reported by each sensor."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "Get cargo status", "tags": ["Sensors"], "x-codegen-request-body-name": "V1sensorParam"}}, "/v1/sensors/door": {"post": {"description": "<n class=\"warning\">\n<nh>\n<i class=\"fa fa-exclamation-circle\"></i>\nThis endpoint is still on our legacy API.\n</nh>\n</n>\n\nGet door monitor status (closed / open) for requested sensors. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Sensors** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "V1getSensorsDoor", "requestBody": {"$ref": "#/components/requestBodies/inline_object_5", "content": {"application/json": {"schema": {"properties": {"sensors": {"description": "List of sensor IDs to query.", "example": [122], "items": {"format": "int64", "type": "integer"}, "type": "array"}}, "required": ["sensors"], "type": "object"}}}, "description": "List of sensor IDs to query.", "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1DoorResponse"}}}, "description": "List of sensor objects containing the current door status reported by each sensor."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "Get door status", "tags": ["Sensors"], "x-codegen-request-body-name": "V1sensorParam"}}, "/v1/sensors/history": {"post": {"description": "<n class=\"warning\">\n<nh>\n<i class=\"fa fa-exclamation-circle\"></i>\nThis endpoint is still on our legacy API.\n</nh>\n</n>\n\nGet historical data for specified sensors. This method returns a set of historical data for the specified sensors in the specified time range and at the specified time resolution. \n\n <b>Rate limit:</b> 100 requests/sec (learn more about rate limits <a href=\"https://developers.samsara.com/docs/rate-limits\" target=\"_blank\">here</a>). \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Sensors** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "V1getSensorsHistory", "requestBody": {"$ref": "#/components/requestBodies/inline_object_6", "content": {"application/json": {"schema": {"properties": {"endMs": {"description": "End of the time range, specified in milliseconds UNIX time.", "example": 1462881998034, "type": "integer"}, "fillMissing": {"default": "<PERSON><PERSON><PERSON>", "enum": ["<PERSON><PERSON><PERSON>", "with<PERSON><PERSON><PERSON>"], "type": "string"}, "series": {"items": {"$ref": "#/components/schemas/_v1_sensors_history_series"}, "maxItems": 40, "type": "array"}, "startMs": {"description": "Beginning of the time range, specified in milliseconds UNIX time.", "example": 1462878398034, "type": "integer"}, "stepMs": {"description": "Time resolution for which data should be returned, in milliseconds. Specifying 3600000 will return data at hour intervals.", "example": 3600000, "type": "integer"}}, "required": ["endMs", "series", "startMs", "stepMs"], "type": "object"}}}, "description": "Time range and resolution, and list of sensor ID, field pairs to query.", "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1SensorHistoryResponse"}}}, "description": "List of results objects, each containing a time and a datapoint for each requested sensor/field pair."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "Get sensor history", "tags": ["Sensors"], "x-codegen-request-body-name": "historyParam"}}, "/v1/sensors/humidity": {"post": {"description": "<n class=\"warning\">\n<nh>\n<i class=\"fa fa-exclamation-circle\"></i>\nThis endpoint is still on our legacy API.\n</nh>\n</n>\n\nGet humidity for requested sensors. This method returns the current relative humidity for the requested sensors. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Sensors** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "V1getSensorsHumidity", "requestBody": {"$ref": "#/components/requestBodies/inline_object_7", "content": {"application/json": {"schema": {"properties": {"sensors": {"description": "List of sensor IDs to query.", "example": [122], "items": {"format": "int64", "type": "integer"}, "type": "array"}}, "required": ["sensors"], "type": "object"}}}, "description": "List of sensor IDs to query.", "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1HumidityResponse"}}}, "description": "List of sensor objects containing the current humidity reported by each sensor."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "Get humidity", "tags": ["Sensors"], "x-codegen-request-body-name": "V1sensorParam"}}, "/v1/sensors/list": {"post": {"description": "<n class=\"warning\">\n<nh>\n<i class=\"fa fa-exclamation-circle\"></i>\nThis endpoint is still on our legacy API.\n</nh>\n</n>\n\nGet sensor objects. This method returns a list of the sensor objects in the Samsara Cloud and information about them. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Sensors** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "V1getSensors", "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/inline_response_200_9"}}}, "description": "List of sensor objects."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "Get all sensors", "tags": ["Sensors"]}}, "/v1/sensors/temperature": {"post": {"description": "<n class=\"warning\">\n<nh>\n<i class=\"fa fa-exclamation-circle\"></i>\nThis endpoint is still on our legacy API.\n</nh>\n</n>\n\nGet temperature for requested sensors. This method returns the current ambient temperature (and probe temperature if applicable) for the requested sensors. \n\n **Submit Feedback**: Likes, dislikes, and API feature requests should be filed as feedback in our <a href=\"https://forms.gle/zkD4NCH7HjKb7mm69\" target=\"_blank\">API feedback form</a>. If you encountered an issue or noticed inaccuracies in the API documentation, please <a href=\"https://www.samsara.com/help\" target=\"_blank\">submit a case</a> to our support team.\n\nTo use this endpoint, select **Write Sensors** under the Equipment category when creating or editing an API token. <a href=\"https://developers.samsara.com/docs/authentication#scopes-for-api-tokens\" target=\"_blank\">Learn More.</a>", "operationId": "V1getSensorsTemperature", "requestBody": {"$ref": "#/components/requestBodies/inline_object_8", "content": {"application/json": {"schema": {"properties": {"sensors": {"description": "List of sensor IDs to query.", "example": [122], "items": {"format": "int64", "type": "integer"}, "type": "array"}}, "required": ["sensors"], "type": "object"}}}, "description": "List of sensor IDs to query.", "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1TemperatureResponse"}}}, "description": "List of sensor objects containing the current temperature reported by each sensor."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/V1ErrorResponse"}}}, "description": "Unexpected error."}}, "summary": "Get temperature", "tags": ["Sensors"], "x-codegen-request-body-name": "V1sensorParam"}}}, "tags": [{"name": "Sensors"}]}