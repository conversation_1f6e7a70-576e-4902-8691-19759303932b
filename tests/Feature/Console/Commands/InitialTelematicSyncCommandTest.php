<?php

namespace Tests\Feature\Console\Commands;

use App\Models\Truck;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

uses(RefreshDatabase::class);

beforeEach(function () {
    DB::table('settings')->where('group', 'integrations_telematics_samsara')->delete();

    DB::table('settings')->insert([
        [
            'group' => 'integrations_telematics_samsara',
            'name' => 'enabled',
            'payload' => 'true',
            'created_at' => now(),
            'updated_at' => now(),
        ],
        [
            'group' => 'integrations_telematics_samsara',
            'name' => 'credentials',
            'payload' => json_encode(['api_key' => 'test-api-key']),
            'created_at' => now(),
            'updated_at' => now(),
        ],
    ]);
});

function fakeSuccessfulSamsaraResponse(array $vehicles = []): void
{
    Http::fake([
        'https://api.samsara.com/*' => Http::response([
            'data' => $vehicles,
            'pagination' => ['endCursor' => null, 'hasNextPage' => false],
        ]),
    ]);
}

function fakeErrorSamsaraResponse(int $statusCode = 401, array $error = ['error' => 'Unauthorized']): void
{
    Http::fake([
        'https://api.samsara.com/*' => Http::response($error, $statusCode),
    ]);
}

test('command executes without throwing exceptions', function () {
    Truck::factory()->create(['vin' => '1FUJGHDV8DLBZ1234']);

    fakeSuccessfulSamsaraResponse([
        [
            'id' => 'samsara-vehicle-001',
            'name' => 'Test Vehicle',
            'vin' => '1FUJGHDV8DLBZ1234',
            'make' => 'Freightliner',
            'createdAtTime' => '2023-01-01T00:00:00Z',
        ],
    ]);

    $this->artisan('sync:telematics:initial')->run();

    expect(true)->toBeTrue();
});

test('command handles empty response', function () {
    fakeSuccessfulSamsaraResponse();

    $this->artisan('sync:telematics:initial')->run();

    expect(true)->toBeTrue();
});

test('command handles API errors', function () {
    fakeErrorSamsaraResponse();

    $this->artisan('sync:telematics:initial')->run();

    expect(true)->toBeTrue();
})->group('error-handling');

test('command is registered and available', function () {
    $this->artisan('sync:telematics:initial --help')
        ->assertExitCode(0);
});
