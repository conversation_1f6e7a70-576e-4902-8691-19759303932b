<?php

namespace Tests\Feature\Settings;

use App\Models\PoiCategory;
use App\Models\PoiItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia as Assert;

uses(RefreshDatabase::class);

test('authenticated users can view POI items for a category', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $category = PoiCategory::factory()->create();
    PoiItem::factory()->count(3)->create(['poi_category_id' => $category->id]);

    $response = $this->get(route('poi.categories.items.index', ['category' => $category->id]));

    $response->assertOk();
    $response->assertInertia(fn (Assert $page) => $page
        ->component('settings/poi/items/index')
        ->has('category', fn (Assert $categoryData) => $categoryData
            ->hasAll(['id', 'name', 'description', 'icon', 'created_at', 'updated_at'])
            ->where('id', $category->id)
        )
        ->has('items', 3)
        ->has('items.0', fn (Assert $item) => $item
            ->hasAll([
                'id', 'poi_category_id', 'name', 'address_line1', 'address_line2',
                'city', 'state', 'postal_code', 'country', 'latitude', 'longitude',
                'phone', 'email', 'website', 'opening_hours', 'metadata', 'created_at', 'updated_at',
            ])
        )
    );
});

test('POI items only show items for the specific category', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $category1 = PoiCategory::factory()->create();
    $category2 = PoiCategory::factory()->create();

    $item1 = PoiItem::factory()->create(['poi_category_id' => $category1->id, 'name' => 'Item 1']);
    $item2 = PoiItem::factory()->create(['poi_category_id' => $category2->id, 'name' => 'Item 2']);

    $response = $this->get(route('poi.categories.items.index', ['category' => $category1->id]));

    $response->assertInertia(fn (Assert $page) => $page
        ->component('settings/poi/items/index')
        ->has('items', 1)
        ->where('items.0.name', 'Item 1')
        ->where('items.0.poi_category_id', $category1->id)
    );
});

test('guests cannot view POI items', function () {
    $category = PoiCategory::factory()->create();

    $response = $this->get(route('poi.categories.items.index', ['category' => $category->id]));

    $response->assertRedirect(route('login'));
});

test('returns 404 when category does not exist', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $response = $this->get(route('poi.categories.items.index', ['category' => 999]));

    $response->assertStatus(404);
});
