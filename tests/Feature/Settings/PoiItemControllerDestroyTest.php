<?php

namespace Tests\Feature\Settings;

use App\Models\PoiCategory;
use App\Models\PoiItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('authenticated users can delete POI item', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $category = PoiCategory::factory()->create();
    $item = PoiItem::factory()->create(['poi_category_id' => $category->id]);

    $response = $this->delete(route('poi.items.destroy', ['item' => $item->id]));

    $response->assertRedirect(route('poi.categories.items.index', $category));
    $response->assertSessionHas('success', 'POI Item deleted successfully.');

    $this->assertDatabaseMissing('poi_items', [
        'id' => $item->id,
    ]);
});

test('deleting POI item does not affect other items or category', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $category = PoiCategory::factory()->create();
    $item1 = PoiItem::factory()->create(['poi_category_id' => $category->id]);
    $item2 = PoiItem::factory()->create(['poi_category_id' => $category->id]);

    $response = $this->delete(route('poi.items.destroy', ['item' => $item1->id]));

    $response->assertRedirect(route('poi.categories.items.index', $category));

    // Only the deleted item should be missing
    $this->assertDatabaseMissing('poi_items', [
        'id' => $item1->id,
    ]);

    // Other item and category should remain
    $this->assertDatabaseHas('poi_items', [
        'id' => $item2->id,
    ]);
    $this->assertDatabaseHas('poi_categories', [
        'id' => $category->id,
    ]);
});

test('guests cannot delete POI item', function () {
    $category = PoiCategory::factory()->create();
    $item = PoiItem::factory()->create(['poi_category_id' => $category->id]);

    $response = $this->delete(route('poi.items.destroy', ['item' => $item->id]));

    $response->assertRedirect(route('login'));

    $this->assertDatabaseHas('poi_items', [
        'id' => $item->id,
    ]);
});

test('returns 404 when item does not exist for delete', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $response = $this->delete(route('poi.items.destroy', ['item' => 999]));

    $response->assertStatus(404);
});
