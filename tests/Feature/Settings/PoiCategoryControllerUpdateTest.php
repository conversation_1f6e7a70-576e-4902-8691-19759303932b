<?php

namespace Tests\Feature\Settings;

use App\Data\PoiCategoryData;
use App\Models\PoiCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('authenticated users can update POI category', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $category = PoiCategory::factory()->create([
        'name' => 'Original Name',
        'description' => 'Original description',
        'icon' => 'original-icon',
    ]);

    $updatedData = PoiCategoryData::from([
        'name' => 'Updated Name',
        'description' => 'Updated description',
        'icon' => 'updated-icon',
    ]);

    $response = $this->patch(
        route('poi.categories.update', ['category' => $category->id]),
        $updatedData->toArray()
    );

    $response->assertRedirect(route('poi.index'));
    $response->assertSessionHas('success', 'POI Category updated successfully.');

    $this->assertDatabaseHas('poi_categories', [
        'id' => $category->id,
        'name' => 'Updated Name',
        'description' => 'Updated description',
        'icon' => 'updated-icon',
    ]);
});

test('can update POI category with null values', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $category = PoiCategory::factory()->create([
        'name' => 'Test Category',
        'description' => 'Test description',
        'icon' => 'test-icon',
    ]);

    $updatedData = PoiCategoryData::from([
        'name' => 'Updated Category',
        'description' => null,
        'icon' => null,
    ]);

    $response = $this->patch(
        route('poi.categories.update', ['category' => $category->id]),
        $updatedData->toArray()
    );

    $response->assertRedirect(route('poi.index'));

    $this->assertDatabaseHas('poi_categories', [
        'id' => $category->id,
        'name' => 'Updated Category',
        'description' => null,
        'icon' => null,
    ]);
});

test('guests cannot update POI category', function () {
    $category = PoiCategory::factory()->create([
        'name' => 'Original Name',
    ]);

    $response = $this->patch(
        route('poi.categories.update', ['category' => $category->id]),
        ['name' => 'Hacked Name']
    );

    $response->assertRedirect(route('login'));

    $this->assertDatabaseHas('poi_categories', [
        'id' => $category->id,
        'name' => 'Original Name',
    ]);
});

test('returns 404 when category does not exist for update', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $response = $this->patch(
        route('poi.categories.update', ['category' => 999]),
        ['name' => 'Non-existent Category']
    );

    $response->assertStatus(404);
});

test('validation fails with empty name on update', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $category = PoiCategory::factory()->create([
        'name' => 'Original Name',
    ]);

    $response = $this->patch(
        route('poi.categories.update', ['category' => $category->id]),
        ['name' => '']
    );

    $response->assertSessionHasErrors(['name']);

    $this->assertDatabaseHas('poi_categories', [
        'id' => $category->id,
        'name' => 'Original Name',
    ]);
});
