<?php

namespace Tests\Feature\Settings;

use App\Models\PoiCategory;
use App\Models\PoiItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('authenticated users can delete POI category', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $category = PoiCategory::factory()->create();

    $response = $this->delete(route('poi.categories.destroy', ['category' => $category->id]));

    $response->assertRedirect(route('poi.index'));
    $response->assertSessionHas('success', 'POI Category deleted successfully.');

    $this->assertDatabaseMissing('poi_categories', [
        'id' => $category->id,
    ]);
});

test('deleting POI category cascades to delete associated items', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $category = PoiCategory::factory()->create();
    $item1 = PoiItem::factory()->create(['poi_category_id' => $category->id]);
    $item2 = PoiItem::factory()->create(['poi_category_id' => $category->id]);

    $response = $this->delete(route('poi.categories.destroy', ['category' => $category->id]));

    $response->assertRedirect(route('poi.index'));

    // Category should be deleted
    $this->assertDatabaseMissing('poi_categories', [
        'id' => $category->id,
    ]);

    // Associated items should be deleted due to cascade
    $this->assertDatabaseMissing('poi_items', [
        'id' => $item1->id,
    ]);
    $this->assertDatabaseMissing('poi_items', [
        'id' => $item2->id,
    ]);
});

test('guests cannot delete POI category', function () {
    $category = PoiCategory::factory()->create();

    $response = $this->delete(route('poi.categories.destroy', ['category' => $category->id]));

    $response->assertRedirect(route('login'));

    $this->assertDatabaseHas('poi_categories', [
        'id' => $category->id,
    ]);
});

test('returns 404 when category does not exist for delete', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $response = $this->delete(route('poi.categories.destroy', ['category' => 999]));

    $response->assertStatus(404);
});
