<?php

namespace Tests\Feature\Settings;

use App\Data\PoiItemData;
use App\Models\PoiCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('authenticated users can create POI item with full data', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $category = PoiCategory::factory()->create();

    $itemData = PoiItemData::from([
        'name' => "Joe's Diner",
        'address_line1' => '123 Main Street',
        'address_line2' => 'Suite 100',
        'city' => 'Springfield',
        'state' => 'IL',
        'postal_code' => '62701',
        'country' => 'USA',
        'latitude' => 39.7817,
        'longitude' => -89.6501,
        'phone' => '******-123-4567',
        'email' => '<EMAIL>',
        'website' => 'https://joesdiner.com',
        'metadata' => ['hours' => '6am-10pm', 'rating' => 4.5],
    ]);

    $response = $this->post(
        route('poi.items.store', ['category' => $category->id]),
        $itemData->toArray()
    );

    $response->assertRedirect(route('poi.categories.items.index', $category));
    $response->assertSessionHas('success', 'POI Item created successfully.');

    $this->assertDatabaseHas('poi_items', [
        'poi_category_id' => $category->id,
        'name' => "Joe's Diner",
        'address_line1' => '123 Main Street',
        'address_line2' => 'Suite 100',
        'city' => 'Springfield',
        'state' => 'IL',
        'postal_code' => '62701',
        'country' => 'USA',
        'latitude' => 39.7817,
        'longitude' => -89.6501,
        'phone' => '******-123-4567',
        'email' => '<EMAIL>',
        'website' => 'https://joesdiner.com',
    ]);
});

test('can create POI item with minimal data', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $category = PoiCategory::factory()->create();

    $itemData = PoiItemData::from([
        'name' => 'Simple Location',
    ]);

    $response = $this->post(
        route('poi.items.store', ['category' => $category->id]),
        $itemData->toArray()
    );

    $response->assertRedirect(route('poi.categories.items.index', $category));
    $response->assertSessionHas('success', 'POI Item created successfully.');

    $this->assertDatabaseHas('poi_items', [
        'poi_category_id' => $category->id,
        'name' => 'Simple Location',
        'address_line1' => null,
        'latitude' => null,
        'longitude' => null,
    ]);
});

test('guests cannot create POI item', function () {
    $category = PoiCategory::factory()->create();

    $itemData = [
        'name' => 'Unauthorized Location',
    ];

    $response = $this->post(route('poi.items.store', ['category' => $category->id]), $itemData);

    $response->assertRedirect(route('login'));
    $this->assertDatabaseMissing('poi_items', $itemData);
});

test('validation fails with empty name', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $category = PoiCategory::factory()->create();

    $response = $this->post(route('poi.items.store', ['category' => $category->id]), [
        'name' => '',
        'city' => 'Test City',
    ]);

    $response->assertSessionHasErrors(['name']);
    $this->assertDatabaseMissing('poi_items', [
        'city' => 'Test City',
    ]);
});

test('validation fails with invalid latitude', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $category = PoiCategory::factory()->create();

    $response = $this->post(route('poi.items.store', ['category' => $category->id]), [
        'name' => 'Test Location',
        'latitude' => 91, // Invalid: must be between -90 and 90
        'longitude' => 0,
    ]);

    $response->assertSessionHasErrors(['latitude']);
    $this->assertDatabaseMissing('poi_items', [
        'name' => 'Test Location',
    ]);
});

test('validation fails with invalid longitude', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $category = PoiCategory::factory()->create();

    $response = $this->post(route('poi.items.store', ['category' => $category->id]), [
        'name' => 'Test Location',
        'latitude' => 0,
        'longitude' => 181, // Invalid: must be between -180 and 180
    ]);

    $response->assertSessionHasErrors(['longitude']);
    $this->assertDatabaseMissing('poi_items', [
        'name' => 'Test Location',
    ]);
});

test('validation fails with invalid email format', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $category = PoiCategory::factory()->create();

    $response = $this->post(route('poi.items.store', ['category' => $category->id]), [
        'name' => 'Test Location',
        'email' => 'invalid-email-format',
    ]);

    $response->assertSessionHasErrors(['email']);
    $this->assertDatabaseMissing('poi_items', [
        'name' => 'Test Location',
    ]);
});

test('returns 404 when category does not exist for creating item', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $response = $this->post(route('poi.items.store', ['category' => 999]), [
        'name' => 'Test Location',
    ]);

    $response->assertStatus(404);
});
