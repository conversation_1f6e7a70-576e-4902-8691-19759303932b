<?php

namespace Tests\Feature\Settings;

use App\Models\PoiCategory;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia as Assert;

uses(RefreshDatabase::class);

test('authenticated users can view POI categories index', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    PoiCategory::factory()->count(3)->create();

    $response = $this->get(route('poi.index'));

    $response->assertOk();
    $response->assertInertia(fn (Assert $page) => $page
        ->component('settings/poi')
        ->has('categories', 3)
        ->has('categories.0', fn (Assert $category) => $category
            ->hasAll(['id', 'name', 'description', 'icon', 'created_at', 'updated_at'])
        )
    );
});

test('guests cannot view POI categories index', function () {
    $response = $this->get(route('poi.index'));

    $response->assertRedirect(route('login'));
});

test('POI categories are ordered by name', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    PoiCategory::factory()->create(['name' => 'Zulu Category']);
    PoiCategory::factory()->create(['name' => 'Alpha Category']);
    PoiCategory::factory()->create(['name' => 'Beta Category']);

    $response = $this->get(route('poi.index'));

    $response->assertInertia(fn (Assert $page) => $page
        ->component('settings/poi')
        ->where('categories.0.name', 'Alpha Category')
        ->where('categories.1.name', 'Beta Category')
        ->where('categories.2.name', 'Zulu Category')
    );
});
