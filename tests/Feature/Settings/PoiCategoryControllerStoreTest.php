<?php

namespace Tests\Feature\Settings;

use App\Data\PoiCategoryData;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('authenticated users can create POI category with valid data', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $categoryData = PoiCategoryData::from([
        'name' => 'Restaurants',
        'description' => 'Food and dining establishments',
        'icon' => 'utensils',
    ]);

    $response = $this->post(
        route('poi.categories.store'),
        $categoryData->toArray()
    );

    $response->assertRedirect(route('poi.index'));
    $response->assertSessionHas('success', 'POI Category created successfully.');

    $this->assertDatabaseHas('poi_categories', [
        'name' => 'Restaurants',
        'description' => 'Food and dining establishments',
        'icon' => 'utensils',
    ]);
});

test('can create POI category with minimal data', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $categoryData = PoiCategoryData::from([
        'name' => 'Gas Stations',
    ]);

    $response = $this->post(
        route('poi.categories.store'),
        $categoryData->toArray()
    );

    $response->assertRedirect(route('poi.index'));
    $response->assertSessionHas('success', 'POI Category created successfully.');

    $this->assertDatabaseHas('poi_categories', [
        'name' => 'Gas Stations',
        'description' => null,
        'icon' => null,
    ]);
});

test('guests cannot create POI category', function () {
    $categoryData = [
        'name' => 'Hotels',
        'description' => 'Accommodation services',
    ];

    $response = $this->post(route('poi.categories.store'), $categoryData);

    $response->assertRedirect(route('login'));
    $this->assertDatabaseMissing('poi_categories', $categoryData);
});

test('validation fails with empty name', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $response = $this->post(route('poi.categories.store'), [
        'name' => '',
        'description' => 'Test description',
    ]);

    $response->assertSessionHasErrors(['name']);
    $this->assertDatabaseMissing('poi_categories', [
        'description' => 'Test description',
    ]);
});

test('validation fails with name too long', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $longName = str_repeat('a', 256); // Max length is 255

    $response = $this->post(route('poi.categories.store'), [
        'name' => $longName,
        'description' => 'Test description',
    ]);

    $response->assertSessionHasErrors(['name']);
    $this->assertDatabaseMissing('poi_categories', [
        'description' => 'Test description',
    ]);
});
