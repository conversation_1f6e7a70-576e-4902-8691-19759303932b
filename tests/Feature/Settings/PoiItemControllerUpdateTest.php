<?php

namespace Tests\Feature\Settings;

use App\Data\PoiItemData;
use App\Models\PoiCategory;
use App\Models\PoiItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('authenticated users can update POI item', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $category = PoiCategory::factory()->create();
    $item = PoiItem::factory()->create([
        'poi_category_id' => $category->id,
        'name' => 'Original Name',
        'address_line1' => 'Original Address',
        'latitude' => 40.0000,
        'longitude' => -80.0000,
    ]);

    $updatedData = PoiItemData::from([
        'name' => 'Updated Name',
        'address_line1' => 'Updated Address',
        'address_line2' => 'Suite 200',
        'city' => 'Updated City',
        'state' => 'PA',
        'postal_code' => '15222',
        'country' => 'USA',
        'latitude' => 41.0000,
        'longitude' => -81.0000,
        'phone' => '******-987-6543',
        'email' => '<EMAIL>',
        'website' => 'https://updated.com',
        'metadata' => ['updated' => true],
    ]);

    $response = $this->patch(
        route('poi.items.update', ['item' => $item->id]),
        $updatedData->toArray()
    );

    $response->assertRedirect(route('poi.categories.items.index', $category));
    $response->assertSessionHas('success', 'POI Item updated successfully.');

    $this->assertDatabaseHas('poi_items', [
        'id' => $item->id,
        'poi_category_id' => $category->id,
        'name' => 'Updated Name',
        'address_line1' => 'Updated Address',
        'address_line2' => 'Suite 200',
        'city' => 'Updated City',
        'state' => 'PA',
        'postal_code' => '15222',
        'country' => 'USA',
        'latitude' => 41.0000,
        'longitude' => -81.0000,
        'phone' => '******-987-6543',
        'email' => '<EMAIL>',
        'website' => 'https://updated.com',
    ]);
});

test('can update POI item with null values', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $category = PoiCategory::factory()->create();
    $item = PoiItem::factory()->create([
        'poi_category_id' => $category->id,
        'name' => 'Test Item',
        'address_line1' => 'Test Address',
        'phone' => '******-123-4567',
        'email' => '<EMAIL>',
    ]);

    $updatedData = PoiItemData::from([
        'name' => 'Updated Item',
        'address_line1' => null,
        'phone' => null,
        'email' => null,
    ]);

    $response = $this->patch(
        route('poi.items.update', ['item' => $item->id]),
        $updatedData->toArray()
    );

    $response->assertRedirect(route('poi.categories.items.index', $category));

    $this->assertDatabaseHas('poi_items', [
        'id' => $item->id,
        'name' => 'Updated Item',
        'address_line1' => null,
        'phone' => null,
        'email' => null,
    ]);
});

test('guests cannot update POI item', function () {
    $category = PoiCategory::factory()->create();
    $item = PoiItem::factory()->create([
        'poi_category_id' => $category->id,
        'name' => 'Original Name',
    ]);

    $response = $this->patch(
        route('poi.items.update', ['item' => $item->id]),
        ['name' => 'Hacked Name']
    );

    $response->assertRedirect(route('login'));

    $this->assertDatabaseHas('poi_items', [
        'id' => $item->id,
        'name' => 'Original Name',
    ]);
});

test('returns 404 when item does not exist for update', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $response = $this->patch(
        route('poi.items.update', ['item' => 999]),
        ['name' => 'Non-existent Item']
    );

    $response->assertStatus(404);
});

test('validation fails with empty name on update', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $category = PoiCategory::factory()->create();
    $item = PoiItem::factory()->create([
        'poi_category_id' => $category->id,
        'name' => 'Original Name',
    ]);

    $response = $this->patch(
        route('poi.items.update', ['item' => $item->id]),
        ['name' => '']
    );

    $response->assertSessionHasErrors(['name']);

    $this->assertDatabaseHas('poi_items', [
        'id' => $item->id,
        'name' => 'Original Name',
    ]);
});

test('validation fails with invalid coordinates on update', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $category = PoiCategory::factory()->create();
    $item = PoiItem::factory()->create([
        'poi_category_id' => $category->id,
        'name' => 'Test Item',
        'latitude' => 40.0000,
        'longitude' => -80.0000,
    ]);

    $response = $this->patch(
        route('poi.items.update', ['item' => $item->id]),
        [
            'name' => 'Test Item',
            'latitude' => 91,  // Invalid
            'longitude' => 181, // Invalid
        ]
    );

    $response->assertSessionHasErrors(['latitude', 'longitude']);

    $this->assertDatabaseHas('poi_items', [
        'id' => $item->id,
        'latitude' => 40.0000,
        'longitude' => -80.0000,
    ]);
});
