<?php

use App\Data\TruckData;
use App\Enums\TruckStatusEnum;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

it('can create truck with valid data', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $truckData = TruckData::from([
        'unit_number' => '123456',
        'status' => TruckStatusEnum::IN_PIT_STOP,
    ]);

    $response = $this->post(
        route('trucks.store'),
        $truckData->toArray(),
    );

    $response->assertRedirect(route('trucks.index'));
    $response->assertSessionHas('success', 'Truck created successfully');

    expect(DB::table('trucks')->where($truckData->toArray())->exists())
        ->toBeTrue();
});

it('requires deactivation reason when deactivation date is present', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $truckData = TruckData::from([
        'unit_number' => '123456',
        'deactivation_date' => '2025-05-26',
        'status' => TruckStatusEnum::IN_PIT_STOP,
    ]);

    $response = $this->post(
        route('trucks.store'),
        $truckData->toArray(),
    );

    $response->assertSessionHasErrors(['deactivation_reason']);
    expect(DB::table('trucks')->where($truckData->toArray())->exists())->tobeFalse();
});
