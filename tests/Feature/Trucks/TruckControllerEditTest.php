<?php

use App\Enums\FleetStatusEnum;
use App\Enums\OwnershipTypeEnum;
use App\Enums\TruckStatusEnum;
use App\Models\Truck;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia as Assert;

uses(RefreshDatabase::class);

test('authenticated users can access truck edit page', function () {
    $user = User::factory()->create();
    $truck = Truck::create([
        'unit_number' => 'TR-123',
        'make' => 'Volvo',
        'model' => 'VNL 860',
        'year' => 2022,
        'vin' => 'ABC123XYZ456789',
        'plate' => 'ABC123',
        'ownership_type' => OwnershipTypeEnum::OWNER,
        'status' => TruckStatusEnum::OTR_REPAIRS_NEEDED,
    ]);

    $response = $this
        ->actingAs($user)
        ->get(route('trucks.edit', ['truck' => $truck->id]));

    $response
        ->assertStatus(200)
        ->assertInertia(fn (Assert $page) => $page
            ->component('trucks/edit')
            ->has('truck', fn (Assert $prop) => $prop
                ->where('id', $truck->id)
                ->where('unit_number', 'TR-123')
                ->where('make', 'Volvo')
                ->where('model', 'VNL 860')
                ->where('year', 2022)
                ->where('vin', 'ABC123XYZ456789')
                ->where('plate', 'ABC123')
                ->etc()
            )
            ->has('fleetStatuses', fn (Assert $prop) => $prop
                ->each(fn (Assert $value) => $value
                    ->has('value')
                    ->has('label')
                )
                ->count(count(FleetStatusEnum::cases()))
            )
            ->has('ownershipType', fn (Assert $prop) => $prop
                ->each(fn (Assert $value) => $value
                    ->has('value')
                    ->has('label')
                )
                ->count(count(OwnershipTypeEnum::cases()))
            )
        );
});

test('guests cannot access truck edit page', function () {
    $truck = Truck::create([
        'unit_number' => 'TR-123',
        'status' => TruckStatusEnum::OTR_REPAIRS_NEEDED,
    ]);

    $response = $this->get(route('trucks.edit', ['truck' => $truck->id]));

    $response->assertRedirect(route('login'));
});

test('returns 404 when truck does not exist', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->get(route('trucks.edit', ['truck' => 999]));

    $response->assertStatus(404);
});
