<?php

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia as Assert;

uses(RefreshDatabase::class);

test('authenticated users can access truck import page', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $response = $this->get(route('trucks.import.index'));

    $response
        ->assertStatus(200)
        ->assertInertia(fn (Assert $page) => $page
            ->component('trucks/import')
            ->has('recentImports')
        );
});

test('guests cannot access truck import page', function () {
    $response = $this->get(route('trucks.import.index'));

    $response->assertRedirect(route('login'));
});
