<?php

use App\Enums\FleetStatusEnum;
use App\Enums\OwnershipTypeEnum;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia as Assert;

uses(RefreshDatabase::class);

test('authenticated users can access truck create page', function () {

    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->get(route('trucks.create'));

    $response
        ->assertStatus(200)
        ->assertInertia(fn (Assert $page) => $page
            ->component('trucks/create')
            ->has('fleetStatuses', fn (Assert $prop) => $prop
                ->each(fn (Assert $value) => $value
                    ->etc()
                )
                ->count(count(FleetStatusEnum::cases()))
            )
            ->has('ownershipType', fn (Assert $prop) => $prop
                ->each(fn (Assert $value) => $value
                    ->has('value')
                    ->has('label')
                )
                ->count(count(OwnershipTypeEnum::cases()))
            )
        );
});

test('guests cannot access truck create page', function () {
    $response = $this->get(route('trucks.create'));

    $response->assertRedirect(route('login'));
});
