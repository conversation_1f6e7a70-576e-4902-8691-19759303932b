<?php

use App\Enums\OwnershipTypeEnum;
use App\Enums\TruckStatusEnum;
use App\Models\Truck;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('authenticated users can delete a truck', function () {
    $user = User::factory()->create();

    $truck = Truck::create([
        'unit_number' => 'TR-123',
        'make' => 'Volvo',
        'model' => 'VNL 860',
        'year' => 2022,
        'vin' => 'ABC123XYZ456789',
        'plate' => 'ABC123',
        'ownership_type' => OwnershipTypeEnum::OWNER,
        'status' => TruckStatusEnum::OTR_REPAIRS_NEEDED,
    ]);

    $this->actingAs($user);

    $response = $this->delete(route('trucks.destroy', ['truck' => $truck->id]));

    $response->assertRedirect(route('trucks.index'));
    $response->assertSessionHas('success', 'Truck deleted successfully');

    $this->assertDatabaseMissing('trucks', ['id' => $truck->id]);
});

test('guests cannot delete a truck', function () {
    $truck = Truck::create([
        'unit_number' => 'TR-123',
        'make' => 'Volvo',
        'model' => 'VNL 860',
        'year' => 2022,
        'vin' => 'ABC123XYZ456789',
        'plate' => 'ABC123',
        'ownership_type' => OwnershipTypeEnum::OWNER,
        'status' => TruckStatusEnum::OTR_REPAIRS_NEEDED,
    ]);

    $response = $this->delete(route('trucks.destroy', ['truck' => $truck->id]));

    $response->assertRedirect(route('login'));

    $this->assertDatabaseHas('trucks', ['id' => $truck->id]);
});

test('returns 404 when truck does not exist', function () {
    $user = User::factory()->create();

    $this->actingAs($user);

    $response = $this->delete(route('trucks.destroy', ['truck' => 999]));

    $response->assertStatus(404);
});
