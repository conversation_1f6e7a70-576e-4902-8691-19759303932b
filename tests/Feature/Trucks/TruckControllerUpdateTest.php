<?php

use App\Enums\OwnershipTypeEnum;
use App\Enums\TruckStatusEnum;
use App\Models\Truck;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('authenticated users can update a truck', function () {
    $user = User::factory()->create();
    $truck = Truck::create([
        'unit_number' => 'TR-123',
        'make' => 'Volvo',
        'model' => 'VNL 860',
        'year' => 2022,
        'status' => TruckStatusEnum::ACCIDENT_INSPECTION_NEEDED,
        'vin' => 'ABC123XYZ456789',
        'plate' => 'ABC123',
        'ownership_type' => OwnershipTypeEnum::OWNER,
    ]);

    $updatedData = [
        'unit_number' => 'TR-456',
        'make' => 'Peterbilt',
        'model' => '579',
        'year' => 2023,
        'vin' => 'XYZ987ABC123456',
        'status' => TruckStatusEnum::BODY_SHOP->value,
        'plate' => 'XYZ456',
        'ownership_type' => OwnershipTypeEnum::LEASE_TO_OWN->value,
        'deer_guards' => true,
        'dash_camera' => true,
        'has_headrack' => true,
        'gateway_serial' => '1234567890',
    ];

    $response = $this
        ->actingAs($user)
        ->patch(route('trucks.update', ['truck' => $truck->id]), $updatedData);

    $response->assertRedirect(route('trucks.index'));
    $response->assertSessionHas('success', 'Truck updated successfully');

    $this->assertDatabaseHas('trucks', [
        'id' => $truck->id,
        'unit_number' => 'TR-456',
        'make' => 'Peterbilt',
        'model' => '579',
        'year' => 2023,
        'vin' => 'XYZ987ABC123456',
        'status' => TruckStatusEnum::BODY_SHOP->value,
        'plate' => 'XYZ456',
        'ownership_type' => OwnershipTypeEnum::LEASE_TO_OWN->value,
        'deer_guards' => true,
        'dash_camera' => true,
        'has_headrack' => true,
        'gateway_serial' => '1234567890',
    ]);
});

test('guests cannot update a truck', function () {
    $truck = Truck::create([
        'unit_number' => 'TR-123',
        'status' => TruckStatusEnum::OTR_REPAIRS_NEEDED,
    ]);

    $response = $this->patch(route('trucks.update', ['truck' => $truck->id]), [
        'unit_number' => 'TR-456',
    ]);

    $response->assertRedirect(route('login'));

    $this->assertDatabaseHas('trucks', [
        'id' => $truck->id,
        'unit_number' => 'TR-123',
    ]);
});

test('returns 404 when truck does not exist for update', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->patch(route('trucks.update', ['truck' => 999]), [
            'unit_number' => 'TR-456',
        ]);

    $response->assertStatus(404);
});

test('validation fails with empty unit_number', function () {
    $user = User::factory()->create();
    $truck = Truck::create([
        'unit_number' => 'TR-123',
        'status' => TruckStatusEnum::OTR_REPAIRS_NEEDED,
    ]);

    $response = $this
        ->actingAs($user)
        ->patch(route('trucks.update', ['truck' => $truck->id]), [
            'unit_number' => '',
        ]);

    $response->assertSessionHasErrors(['unit_number']);

    $this->assertDatabaseHas('trucks', [
        'id' => $truck->id,
        'unit_number' => 'TR-123',
    ]);
});
