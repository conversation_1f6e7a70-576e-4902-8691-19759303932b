<?php

use App\Enums\TruckStatusEnum;
use App\Models\Truck;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia as Assert;

uses(RefreshDatabase::class);

test('can show list of trucks', function () {
    $this->actingAs(User::factory()->create());

    $this
        ->get(route('trucks.index'))
        ->assertInertia(fn (Assert $page) => $page->component('trucks/index'));
});

test('can filter trucks by status', function () {
    $this->actingAs(User::factory()->create());

    Truck::factory()->create([
        'unit_number' => '123',
        'status' => TruckStatusEnum::IN_PIT_STOP,
    ]);
    Truck::factory()->create([
        'unit_number' => '456',
        'status' => TruckStatusEnum::ACCIDENT_INSPECTION_NEEDED,
    ]);

    $response = $this->get(route('trucks.index',
        [
            'filter' => [
                'status' => TruckStatusEnum::ACCIDENT_INSPECTION_NEEDED->value,
            ],
        ],
    ));

    $response
        ->assertInertia(fn (Assert $page) => $page
            ->component('trucks/index')
            ->has('trucks.data', 1)
            ->where('trucks.data.0.status', TruckStatusEnum::ACCIDENT_INSPECTION_NEEDED->value)
        );
});

test('can filter trucks by multiple status values', function () {
    $this->actingAs(User::factory()->create());

    Truck::factory()->create([
        'unit_number' => '123',
        'status' => TruckStatusEnum::IN_PIT_STOP,
    ]);
    Truck::factory()->create([
        'unit_number' => '456',
        'status' => TruckStatusEnum::ACCIDENT_INSPECTION_NEEDED,
    ]);
    Truck::factory()->create([
        'unit_number' => '789',
        'status' => TruckStatusEnum::ON_THE_ROAD,
    ]);

    $response = $this->get(route('trucks.index',
        [
            'filter' => [
                'status' => TruckStatusEnum::IN_PIT_STOP->value.','.TruckStatusEnum::ACCIDENT_INSPECTION_NEEDED->value,
            ],
        ],
    ));

    $response
        ->assertInertia(fn (Assert $page) => $page
            ->component('trucks/index')
            ->has('trucks.data', 2)
            ->where('trucks.data.0.status', TruckStatusEnum::IN_PIT_STOP->value)
            ->where('trucks.data.1.status', TruckStatusEnum::ACCIDENT_INSPECTION_NEEDED->value)
        );
});

test('can filter trucks by partial unit number', function () {
    $this->actingAs(User::factory()->create());

    Truck::factory()->create([
        'unit_number' => 'ABC123',
        'status' => TruckStatusEnum::IN_PIT_STOP,
    ]);
    Truck::factory()->create([
        'unit_number' => 'DEF456',
        'status' => TruckStatusEnum::ACCIDENT_INSPECTION_NEEDED,
    ]);

    $response = $this->get(route('trucks.index',
        [
            'filter' => [
                'unit_number' => 'ABC',
            ],
        ],
    ));

    $response
        ->assertInertia(fn (Assert $page) => $page
            ->component('trucks/index')
            ->has('trucks.data', 1)
            ->where('trucks.data.0.unit_number', 'ABC123')
        );
});

test('can sort trucks by status', function () {
    $this->actingAs(User::factory()->create());

    Truck::factory()->create([
        'unit_number' => '123',
        'status' => TruckStatusEnum::IN_PIT_STOP,
    ]);

    Truck::factory()->create([
        'unit_number' => '456',
        'status' => TruckStatusEnum::ACCIDENT_INSPECTION_NEEDED,
    ]);

    $response = $this->get(route('trucks.index',
        [
            'sort' => 'status',
        ],
    ));

    $response
        ->assertInertia(fn (Assert $page) => $page
            ->component('trucks/index')
            ->has('trucks.data', 2)
            ->where('trucks.data.0.status', TruckStatusEnum::ACCIDENT_INSPECTION_NEEDED->value)
            ->where('trucks.data.1.status', TruckStatusEnum::IN_PIT_STOP->value)
        );
});

test('can paginate trucks', function () {
    $this->actingAs(User::factory()->create());

    // Create enough trucks to test pagination (more than 30)
    Truck::factory()->count(35)->create();

    // Test first page
    $response = $this->get(route('trucks.index'));
    $response
        ->assertInertia(fn (Assert $page) => $page
            ->component('trucks/index')
            ->has('trucks.data', 30) // Should have 30 items per page
            ->where('trucks.current_page', 1)
            ->where('trucks.last_page', 2)
        );

    // Test second page
    $response = $this->get(route('trucks.index', ['page' => 2]));
    $response
        ->assertInertia(fn (Assert $page) => $page
            ->component('trucks/index')
            ->has('trucks.data', 5) // Should have remaining 5 items
            ->where('trucks.current_page', 2)
            ->where('trucks.last_page', 2)
        );
});

test('returns filter configurations in response', function () {
    $this->actingAs(User::factory()->create());

    // Create some trucks to ensure we have filter options with counts
    Truck::factory()->create(['status' => TruckStatusEnum::IN_PIT_STOP, 'make' => 'Volvo']);
    Truck::factory()->create(['status' => TruckStatusEnum::IN_PIT_STOP, 'make' => 'Kenworth']);

    $response = $this->get(route('trucks.index'));

    $response
        ->assertInertia(fn (Assert $page) => $page
            ->component('trucks/index')
            ->has('filters')
            ->has('filters.0.columnId')
            ->has('filters.0.title')
            ->has('filters.0.type')
            ->has('filters.0.options')
            ->where('filters.0.columnId', 'status')
            ->where('filters.0.type', 'faceted')
            ->has('filters.0.options.0.count') // Verify counts are included
        );
});
