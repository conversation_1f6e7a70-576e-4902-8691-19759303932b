<?php

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia as Assert;

uses(RefreshDatabase::class);

test('authenticated users can access trailer import page', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $response = $this->get(route('trailers.import.index'));

    $response
        ->assertStatus(200)
        ->assertInertia(fn (Assert $page) => $page
            ->component('trailers/import')
            ->has('recentImports')
        );
});

test('guests cannot access trailer import page', function () {
    $response = $this->get(route('trailers.import.index'));

    $response->assertRedirect(route('login'));
});
