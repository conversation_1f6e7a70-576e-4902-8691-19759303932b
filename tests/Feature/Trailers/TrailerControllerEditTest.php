<?php

use App\Enums\FleetStatusEnum;
use App\Enums\TrailerStatusEnum;
use App\Models\Trailer;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia as Assert;

uses(RefreshDatabase::class);

test('authenticated users can access trailer edit page', function () {
    $user = User::factory()->create();
    $trailer = Trailer::create([
        'unit_number' => 'TRLR-123',
        'make' => 'Great Dane',
        'model' => 'Everest',
        'year' => 2021,
        'vin' => 'TRL123VIN456789',
        'plate_number' => 'TRL123',
        'status' => TrailerStatusEnum::READY_TO_GO,
    ]);

    $response = $this
        ->actingAs($user)
        ->get(route('trailers.edit', ['trailer' => $trailer->id]));

    $response
        ->assertStatus(200)
        ->assertInertia(fn (Assert $page) => $page
            ->component('trailers/edit')
            ->has('trailer', fn (Assert $prop) => $prop
                ->where('id', $trailer->id)
                ->where('unit_number', 'TRLR-123')
                ->where('make', 'Great Dane')
                ->where('model', 'Everest')
                ->where('year', 2021)
                ->where('vin', 'TRL123VIN456789')
                ->where('plate_number', 'TRL123')
                ->etc()
            )
            ->has('fleetStatuses', fn (Assert $prop) => $prop
                ->each(fn (Assert $value) => $value
                    ->has('value')
                    ->has('label')
                )
                ->count(count(FleetStatusEnum::cases()))
            )
        );
});

test('guests cannot access trailer edit page', function () {
    $trailer = Trailer::create([
        'unit_number' => 'TRLR-123',
        'status' => TrailerStatusEnum::READY_TO_GO,
    ]);

    $response = $this->get(route('trailers.edit', ['trailer' => $trailer->id]));

    $response->assertRedirect(route('login'));
});

test('returns 404 when trailer does not exist', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->get(route('trailers.edit', ['trailer' => 999]));

    $response->assertStatus(404);
});
