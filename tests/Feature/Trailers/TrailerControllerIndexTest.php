<?php

namespace Tests\Feature\Trailers;

use App\Enums\TrailerStatusEnum;
use App\Models\Trailer;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia as Assert;

uses(RefreshDatabase::class);

test('list all trailers', function () {
    $this->actingAs(User::factory()->create());

    $this
        ->get(route('trailers.index'))
        ->assertInertia(fn (Assert $page) => $page->component('trailers/index'));
});

test('can sort trailers by unit number', function () {
    $this->actingAs(User::factory()->create());

    Trailer::create([
        'unit_number' => 'DEF',
        'status' => TrailerStatusEnum::READY_TO_GO,
    ]);

    Trailer::create([
        'unit_number' => 'ABC',
        'status' => TrailerStatusEnum::FOR_RENT,
    ]);

    $response = $this->get(route('trailers.index',
        [
            'sort' => 'unit_number',
        ],
    ));

    $response
        ->assertInertia(fn (Assert $page) => $page
            ->component('trailers/index')
            ->has('trailers.data', 2)
            ->where('trailers.data.0.unit_number', 'ABC')
            ->where('trailers.data.1.unit_number', 'DEF')
        );
});

test('can sort trailers by unit number with natural sorting', function () {
    $this->actingAs(User::factory()->create());

    Trailer::create([
        'unit_number' => 'T10',
        'status' => TrailerStatusEnum::READY_TO_GO,
    ]);

    Trailer::create([
        'unit_number' => 'T2',
        'status' => TrailerStatusEnum::FOR_RENT,
    ]);

    Trailer::create([
        'unit_number' => 'T1',
        'status' => TrailerStatusEnum::OTR_REPAIRS_NEEDED,
    ]);

    $response = $this->get(route('trailers.index',
        [
            'sort' => 'unit_number',
        ],
    ));

    $response
        ->assertInertia(fn (Assert $page) => $page
            ->component('trailers/index')
            ->has('trailers.data', 3)
            ->where('trailers.data.0.unit_number', 'T1')
            ->where('trailers.data.1.unit_number', 'T10')
            ->where('trailers.data.2.unit_number', 'T2')
        );
});

test('can sort trailers by make', function () {
    $this->actingAs(User::factory()->create());

    Trailer::create([
        'unit_number' => 'T1',
        'make' => 'Vanguard',
        'status' => TrailerStatusEnum::READY_TO_GO,
    ]);

    Trailer::create([
        'unit_number' => 'T2',
        'make' => 'Great Dane',
        'status' => TrailerStatusEnum::ACCIDENT_INSPECTION_NEEDED,
    ]);

    $response = $this->get(route('trailers.index',
        [
            'sort' => 'make',
        ],
    ));

    $response
        ->assertInertia(fn (Assert $page) => $page
            ->component('trailers/index')
            ->has('trailers.data', 2)
            ->where('trailers.data.0.make', 'Great Dane')
            ->where('trailers.data.1.make', 'Vanguard')
        );
});

test('can sort trailers by plate number with natural sorting', function () {
    $this->actingAs(User::factory()->create());

    Trailer::create([
        'unit_number' => 'T1',
        'plate_number' => 'ABC10',
        'status' => TrailerStatusEnum::READY_TO_GO,
    ]);

    Trailer::create([
        'unit_number' => 'T2',
        'plate_number' => 'ABC2',
        'status' => TrailerStatusEnum::FOR_RENT,
    ]);

    $response = $this->get(route('trailers.index',
        [
            'sort' => 'plate_number',
        ],
    ));

    $response
        ->assertInertia(fn (Assert $page) => $page
            ->component('trailers/index')
            ->has('trailers.data', 2)
            ->where('trailers.data.0.plate_number', 'ABC10')
            ->where('trailers.data.1.plate_number', 'ABC2')
        );
});
