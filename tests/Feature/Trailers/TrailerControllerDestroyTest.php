<?php

use App\Models\Trailer;
use App\Models\User;

test('authenticated user can delete a trailer', function () {
    $user = User::factory()->create();
    $trailer = Trailer::factory()->create();

    $this->actingAs($user);

    $response = $this->delete(route('trailers.destroy', ['trailer' => $trailer->id]));

    $response->assertRedirect(route('trailers.index'));
    $response->assertSessionHas('success', 'Trailer deleted successfully');

    $this->assertDatabaseMissing('trailers', ['id' => $trailer->id]);
});

test('guests cannot delete a trailer', function () {
    $trailer = Trailer::factory()->create();

    $response = $this->delete(route('trailers.destroy', ['trailer' => $trailer->id]));

    $response->assertRedirect(route('login'));

    $this->assertDatabaseHas('trailers', ['id' => $trailer->id]);
});

test('returns 404 when trailer does not exist', function () {
    $user = User::factory()->create();

    $this->actingAs($user);

    $response = $this->delete(route('trailers.destroy', ['trailer' => 111]));

    $response->assertStatus(404);
});
