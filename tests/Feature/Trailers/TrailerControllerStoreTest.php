<?php

namespace Tests\Feature\Trailers;

use App\Data\TrailerData;
use App\Enums\TrailerStatusEnum;
use App\Enums\UsaRegionEnum;
use App\Models\TrailerType;
use App\Models\User;
use DB;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

it('can create trailer with valid data', function () {
    $this->actingAs(User::factory()->create());

    $trailerData = TrailerData::from([
        'unit_number' => '123456',
        'status' => TrailerStatusEnum::IN_PIT_STOP,
    ]);

    $response = $this->post(
        route('trailers.store'),
        $trailerData->toArray()
    );

    $response->assertRedirect(route('trailers.index'));
    $response->assertSessionHas('success', 'Trailer created successfully');

    expect(DB::table('trailers')->where($trailerData->toArray())->exists())->toBeTrue();
});

it('can create trailer with full data', function () {
    $this->actingAs(User::factory()->create());
    $trailerType = TrailerType::factory()->create();

    $creationPayload = [
        'unit_number' => 'UNIT-NUMBER-1234',
        'make' => 'Vanguard',
        'model' => 'Dry Van',
        'year' => 2022,
        'vin' => 'ABC123DEF456',
        'dot_expiry' => '2027-05-21',
        'plate_number' => 'PLATE-NUMBER-1234',
        'plate_region' => UsaRegionEnum::ILLINOIS->value,
        'owner_company' => 'Company name',
        'activation_date' => '2025-05-21',
        'deactivation_date' => '2026-05-21',
        'location' => '1234 W Madison St, Chicago, IL 60607',
        'gps_note' => 'Important GPS note',
        'type_id' => $trailerType->id,
        'status' => TrailerStatusEnum::IN_PIT_STOP,
    ];

    $trailerData = TrailerData::from($creationPayload);

    $response = $this->post(
        route('trailers.store'),
        $trailerData->toArray()
    );

    $response->assertRedirect(route('trailers.index'));
    $response->assertSessionHas('success', 'Trailer created successfully');

    $this->assertDatabaseHas('trailers', $creationPayload);
});
