<?php

use App\Enums\TrailerStatusEnum;
use App\Enums\TrailerTypeEnum;
use App\Enums\UsaRegionEnum;
use App\Models\Trailer;
use App\Models\TrailerType;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('authenticated users can update a trailer', function () {
    $this->actingAs(User::factory()->create());

    $oldTrailerType = TrailerType::factory()->create([
        'name' => TrailerTypeEnum::REEFER->value,
    ]);

    $trailer = Trailer::factory()->create([
        'type_id' => $oldTrailerType->id,
        'status' => TrailerStatusEnum::READY_TO_GO->value,
    ]);

    $newTrailerType = TrailerType::factory()->create([
        'name' => TrailerTypeEnum::DRY_VAN->value,
    ]);

    $updatedData = [
        'unit_number' => 'UNIT-NUMBER-1234',
        'make' => 'Vanguard',
        'model' => 'Dry Van',
        'year' => 2022,
        'vin' => 'ABC123DEF456',
        'dot_expiry' => '2027-05-21',
        'plate_region' => UsaRegionEnum::ILLINOIS->value,
        'owner_company' => 'Company name',
        'activation_date' => '2025-05-21',
        'deactivation_date' => '2026-05-21',
        'location' => '1234 W Madison St, Chicago, IL 60607',
        'gps_note' => 'Important GPS note',
        'type_id' => $newTrailerType->id,
        'status' => TrailerStatusEnum::IN_PIT_STOP->value,
    ];

    $response = $this->patch(route('trailers.update', ['trailer' => $trailer->id]), $updatedData);

    $response->assertRedirect(route('trailers.index'));
    $response->assertSessionHas('success', 'Trailer updated successfully');

    $this->assertDatabaseHas('trailers', [
        'id' => $trailer->id,
        ...$updatedData,
    ]);
});

test('guests cannot update a trailer', function () {
    $trailer = Trailer::factory()->create([
        'unit_number' => 'UNIT-NUMBER-1234',
    ]);

    $updatedData = [
        'unit_number' => 'UNIT-NUMBER-4567',
    ];

    $response = $this->patch(route('trailers.update', ['trailer' => $trailer->id]), $updatedData);

    $response->assertRedirect(route('login'));
    $this->assertDatabaseHas('trailers', [
        'id' => $trailer->id,
        'unit_number' => 'UNIT-NUMBER-1234',
    ]);
});

test('404 is returned when trailer does not exist', function () {
    $this->actingAs(User::factory()->create());

    $updateData = [
        'unit_number' => 'UNIT-NUMBER-1234',
    ];

    $response = $this->patch(route('trailers.update', ['trailer' => 111]), $updateData);

    $response->assertStatus(404);
});

test('validation fails with empty unit_number', function () {
    $this->actingAs(User::factory()->create());
    $trailer = Trailer::factory()->create([
        'unit_number' => 'UNIT-NUMBER-1234',
    ]);

    $updatedData = [
        'unit_number' => '',
    ];

    $response = $this->patch(route('trailers.update', ['trailer' => $trailer->id]), $updatedData);

    $response->assertSessionHasErrors(['unit_number']);

    $this->assertDatabaseHas('trailers', [
        'id' => $trailer->id,
        'unit_number' => 'UNIT-NUMBER-1234',
    ]);
});
