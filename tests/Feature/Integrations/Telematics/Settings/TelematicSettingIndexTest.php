<?php

namespace Tests\Feature\Integrations\Telematics;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia as Assert;

uses(RefreshDatabase::class);

test('authenticated users can view telematics settings', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $response = $this->get(route('integrations.telematics.index'));

    $response->assertOk();
    $response->assertInertia(fn (Assert $page) => $page
        ->component('integrations/telematics/index')
        ->has('settings.0', fn (Assert $setting) => $setting
            ->hasAll(['id', 'name', 'enabled', 'fields'])
            ->where('id', 'samsara')
            ->where('name', 'Samsara')
        )
        ->has('settings.0.fields.0', fn (Assert $field) => $field
            ->hasAll(['key', 'value', 'label', 'type', 'required', 'placeholder', 'description'])
        )
    );
});
