<?php

namespace Tests\Feature\Integrations\Telematics\Settings;

use App\Models\User;
use App\Settings\SamsaraSettings;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('authenticated users can update telematics settings', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $updatedData = [
        'enabled' => true,
        'credentials' => [
            'api_key' => 'samsara_api_key',
        ],
    ];

    $response = $this->put(
        route('integrations.telematics.update', ['provider' => SamsaraSettings::PROVIDER_KEY]),
        $updatedData
    );

    $response->assertRedirect();
    $response->assertSessionHas('success', 'Settings updated successfully.');

    $this->assertDatabaseHas('settings', [
        'group' => 'integrations_telematics_samsara',
        'name' => 'enabled',
        'payload' => 'true',
    ]);

    $this->assertDatabaseHas('settings', [
        'group' => 'integrations_telematics_samsara',
        'name' => 'credentials',
        'payload' => json_encode(['api_key' => 'samsara_api_key']),
    ]);
});

test('returns 404 for invalid provider key', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $updatedData = [
        'credentials' => [
            'api_key' => 'samsara_api_key',
        ],
    ];

    $response = $this->put(route('integrations.telematics.update', ['provider' => 'invalid_provider']), $updatedData);

    $response->assertNotFound();
});

test('guests cannot update telematics settings', function () {
    $response = $this->put(route('integrations.telematics.update', ['provider' => SamsaraSettings::PROVIDER_KEY]), [
        'credentials' => [
            'api_key' => 'samsara_api_key',
        ],
        'enabled' => true,
    ]);

    $response->assertRedirect(route('login'));
});

test('validation fails when enabled field is missing', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $response = $this->put(route('integrations.telematics.update', ['provider' => SamsaraSettings::PROVIDER_KEY]), [
        'credentials' => [
            'api_key' => 'samsara_api_key',
        ],
    ]);

    $response->assertSessionHasErrors(['enabled']);
});

test('validation fails when credentials field is missing', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $response = $this->put(route('integrations.telematics.update', ['provider' => SamsaraSettings::PROVIDER_KEY]), [
        'enabled' => true,
    ]);

    $response->assertSessionHasErrors(['credentials']);
});
