<?php

use App\Models\Driver;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('authenticated users can delete a driver', function () {
    $user = User::factory()->create();
    $driver = Driver::create([
        'first_name' => 'John',
        'last_name' => 'Doe',
    ]);

    $this->actingAs($user);

    $response = $this->delete(route('drivers.destroy', ['driver' => $driver->id]));

    $response->assertRedirect(route('drivers.index'));
    $response->assertSessionHas('success', 'Driver deleted successfully');

    $this->assertDatabaseMissing('drivers', ['id' => $driver->id]);
});

test('guests cannot delete a driver', function () {
    $driver = Driver::create([
        'first_name' => 'John',
        'last_name' => 'Doe',
    ]);

    $response = $this->delete(route('drivers.destroy', ['driver' => $driver->id]));

    $response->assertRedirect(route('login'));

    $this->assertDatabaseHas('drivers', ['id' => $driver->id]);
});

test('returns 404 when driver does not exist', function () {
    $user = User::factory()->create();

    $this->actingAs($user);

    $response = $this->delete(route('drivers.destroy', ['driver' => 999]));

    $response->assertStatus(404);
});
