<?php

use App\Models\Driver;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia as Assert;

uses(RefreshDatabase::class);

test('can show list of drivers', function () {
    $this->actingAs(User::factory()->create());

    $this
        ->get(route('drivers.index'))
        ->assertInertia(fn (Assert $page) => $page->component('drivers/index'));
});

test('can sort drivers by first name', function () {
    $this->actingAs(User::factory()->create());

    Driver::factory()->create([
        'first_name' => '<PERSON>',
        'last_name' => '<PERSON>',
    ]);

    Driver::factory()->create([
        'first_name' => 'John',
        'last_name' => 'Doe',
    ]);

    $response = $this->get(route('drivers.index',
        [
            'sort' => '-first_name',
        ],
    ));

    $response
        ->assertInertia(fn (Assert $page) => $page
            ->component('drivers/index')
            ->has('drivers.data', 2)
            ->where('drivers.data.0.first_name', 'John')
            ->where('drivers.data.1.first_name', 'Andrew')
        );
});

test('can sort drivers by last name', function () {
    $this->actingAs(User::factory()->create());

    Driver::factory()->create([
        'first_name' => 'John',
        'last_name' => 'Smith',
    ]);

    Driver::factory()->create([
        'first_name' => 'Jane',
        'last_name' => 'Doe',
    ]);

    $response = $this->get(route('drivers.index',
        [
            'sort' => 'last_name',
        ],
    ));

    $response
        ->assertInertia(fn (Assert $page) => $page
            ->component('drivers/index')
            ->has('drivers.data', 2)
            ->where('drivers.data.0.last_name', 'Doe')
            ->where('drivers.data.1.last_name', 'Smith')
        );
});

test('can sort drivers by contract type', function () {
    $this->actingAs(User::factory()->create());

    Driver::factory()->create([
        'first_name' => 'John',
        'last_name' => 'Smith',
        'contract_type' => 'owner',
    ]);

    Driver::factory()->create([
        'first_name' => 'Jane',
        'last_name' => 'Doe',
        'contract_type' => 'lease',
    ]);

    $response = $this->get(route('drivers.index',
        [
            'sort' => 'contract_type',
        ],
    ));

    $response
        ->assertInertia(fn (Assert $page) => $page
            ->component('drivers/index')
            ->has('drivers.data', 2)
            ->where('drivers.data.0.contract_type', 'lease')
            ->where('drivers.data.1.contract_type', 'owner')
        );
});
