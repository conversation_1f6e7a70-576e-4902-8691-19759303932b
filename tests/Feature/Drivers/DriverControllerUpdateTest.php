<?php

use App\Models\Driver;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('authenticated users can update a driver', function () {
    $user = User::factory()->create();
    $driver = Driver::create([
        'first_name' => 'John',
        'last_name' => 'Doe',
        'phone_number' => '1234567890',
        'contract_type' => 'owner',
        'email' => '<EMAIL>',
    ]);

    $updatedData = [
        'first_name' => '<PERSON>',
        'last_name' => 'Smith',
        'phone_number' => '0987654321',
        'contract_type' => 'lease',
        'email' => '<EMAIL>',
    ];

    $response = $this
        ->actingAs($user)
        ->patch(route('drivers.update', ['driver' => $driver->id]), $updatedData);

    $response->assertRedirect(route('drivers.index'));
    $response->assertSessionHas('success', 'Driver updated successfully');

    $this->assertDatabaseHas('drivers', [
        'id' => $driver->id,
        'first_name' => 'Jane',
        'last_name' => 'Smith',
        'phone_number' => '0987654321',
        'contract_type' => 'lease',
        'email' => '<EMAIL>',
    ]);
});

test('guests cannot update a driver', function () {
    $driver = Driver::create([
        'first_name' => 'John',
        'last_name' => 'Doe',
    ]);

    $response = $this->patch(route('drivers.update', ['driver' => $driver->id]), [
        'first_name' => 'Jane',
    ]);

    $response->assertRedirect(route('login'));

    $this->assertDatabaseHas('drivers', [
        'id' => $driver->id,
        'first_name' => 'John',
    ]);
});

test('returns 404 when driver does not exist for update', function () {
    $user = User::factory()->create();

    $response = $this
        ->actingAs($user)
        ->patch(route('drivers.update', ['driver' => 999]), [
            'first_name' => 'Jane',
        ]);

    $response->assertStatus(404);
});

test('validation fails with empty first_name', function () {
    $user = User::factory()->create();
    $driver = Driver::create([
        'first_name' => 'John',
        'last_name' => 'Doe',
    ]);

    $response = $this
        ->actingAs($user)
        ->patch(route('drivers.update', ['driver' => $driver->id]), [
            'first_name' => '',
        ]);

    $response->assertSessionHasErrors(['first_name']);

    $this->assertDatabaseHas('drivers', [
        'id' => $driver->id,
        'first_name' => 'John',
    ]);
});
