<?php

use App\Data\DriverData;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;

uses(RefreshDatabase::class);

it('can create driver with valid data', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    $driverData = DriverData::from([
        'first_name' => '<PERSON>',
        'last_name' => 'Doe',
        'phone_number' => '1234567890',
        'contract_type' => 'owner',
    ]);

    $response = $this->post(
        route('drivers.store'),
        $driverData->toArray(),
    );

    $response->assertRedirect(route('drivers.index'));
    $response->assertSessionHas('success', 'Driver created successfully');

    expect(DB::table('drivers')->where($driverData->toArray())->exists())
        ->toBeTrue();
});
