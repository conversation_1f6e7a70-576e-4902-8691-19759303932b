<?php

namespace Tests\Unit\Services;

use App\Enums\TruckStatusEnum;
use App\Models\Truck;
use App\Services\FilterConfigurations\TruckFilterConfiguration;
use App\Services\FilterConfigurationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FilterConfigurationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected FilterConfigurationService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new FilterConfigurationService;
    }

    public function test_generates_truck_filters_with_correct_structure()
    {
        // Create some test data
        Truck::factory()->create(['status' => TruckStatusEnum::IN_PIT_STOP, 'make' => 'Volvo', 'year' => 2020]);
        Truck::factory()->create(['status' => TruckStatusEnum::IN_PIT_STOP, 'make' => 'Kenworth', 'year' => 2021]);

        $filters = $this->service->generateFilters(
            TruckFilterConfiguration::getModelClass(),
            TruckFilterConfiguration::getConfiguration()
        );

        $this->assertIsArray($filters);
        $this->assertCount(3, $filters); // status, make, year

        // Check status filter structure
        $statusFilter = collect($filters)->firstWhere('columnId', 'status');
        $this->assertNotNull($statusFilter);
        $this->assertEquals('Truck Status', $statusFilter['title']);
        $this->assertEquals('faceted', $statusFilter['type']);
        $this->assertTrue($statusFilter['multiple']);
        $this->assertIsArray($statusFilter['options']);

        // Check make filter structure
        $makeFilter = collect($filters)->firstWhere('columnId', 'make');
        $this->assertNotNull($makeFilter);
        $this->assertEquals('Make', $makeFilter['title']);
        $this->assertEquals('faceted', $makeFilter['type']);
        $this->assertIsArray($makeFilter['options']);

        // Check year filter structure
        $yearFilter = collect($filters)->firstWhere('columnId', 'year');
        $this->assertNotNull($yearFilter);
        $this->assertEquals('Year', $yearFilter['title']);
        $this->assertEquals('faceted', $yearFilter['type']);
        $this->assertIsArray($yearFilter['options']);
    }

    public function test_filter_options_include_counts()
    {
        // Create test data with known counts
        Truck::factory()->create(['status' => TruckStatusEnum::IN_PIT_STOP, 'make' => 'Volvo']);
        Truck::factory()->create(['status' => TruckStatusEnum::IN_PIT_STOP, 'make' => 'Volvo']);
        Truck::factory()->create(['status' => TruckStatusEnum::ON_THE_ROAD, 'make' => 'Kenworth']);

        $filters = $this->service->generateFilters(
            TruckFilterConfiguration::getModelClass(),
            TruckFilterConfiguration::getConfiguration()
        );

        $statusFilter = collect($filters)->firstWhere('columnId', 'status');
        $statusOptions = collect($statusFilter['options']);

        // Check that options have counts
        foreach ($statusOptions as $option) {
            $this->assertArrayHasKey('count', $option);
            $this->assertArrayHasKey('value', $option);
            $this->assertArrayHasKey('label', $option);
            $this->assertGreaterThan(0, $option['count']);
        }
    }

    public function test_generates_custom_filters_with_configuration()
    {
        // Create test data
        Truck::factory()->create(['make' => 'Volvo']);
        Truck::factory()->create(['make' => 'Kenworth']);

        $customConfig = [
            [
                'type' => 'faceted',
                'field' => 'make',
                'title' => 'Truck Make',
                'column_id' => 'truck_make',
                'sort_by' => 'label',
            ],
        ];

        $filters = $this->service->generateFilters(Truck::class, $customConfig);

        $this->assertCount(1, $filters);
        $this->assertEquals('truck_make', $filters[0]['columnId']);
        $this->assertEquals('Truck Make', $filters[0]['title']);
        $this->assertIsArray($filters[0]['options']);
    }
}
