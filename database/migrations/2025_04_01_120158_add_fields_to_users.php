<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone')->nullable()->after('email');
            $table->string('alias')->nullable()->after('phone');
            $table->string('status')->after('alias')->default(\App\Enums\UserStatus::ACTIVE);
        });
    }
};
