<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('trucks', function (Blueprint $table) {
            $table->unsignedBigInteger('driver_1_id')->nullable()->after('id');
            $table->unsignedBigInteger('driver_2_id')->nullable()->after('driver_1_id');
            $table->foreign('driver_1_id')->references('id')->on('drivers')->nullOnDelete();
            $table->foreign('driver_2_id')->references('id')->on('drivers')->nullOnDelete();
        });
    }
};
