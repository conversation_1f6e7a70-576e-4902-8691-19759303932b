<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('devices', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('device_type');
            $table->string('status');
            $table->string('ip_address')->nullable();
            $table->string('mac_address')->nullable();
            $table->timestamp('last_communication_at')->nullable();
            $table->timestamps();
        });
    }
};
