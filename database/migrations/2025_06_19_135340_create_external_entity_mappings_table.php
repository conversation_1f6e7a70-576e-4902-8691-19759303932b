<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('external_entity_mappings', function (Blueprint $table) {
            $table->id();
            $table->string('service_type', 50);
            $table->string('provider', 50);
            $table->morphs('entity');
            $table->string('external_id');
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->unique([
                'service_type',
                'provider',
                'entity_type',
                'external_id',
            ], 'unique_service_provider_external_id');
        });
    }
};
