<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        // Create the main table structure first without partitioning
        Schema::create('telematic_readings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('external_entity_mapping_id')
                  ->constrained('external_entity_mappings')
                  ->onDelete('cascade');
            $table->string('reading_type', 50); // odometer, engine_hours, fuel_level, etc.
            $table->decimal('value', 15, 6); // High precision for various measurements
            $table->string('unit', 20); // meters, hours, percent, etc.
            $table->timestamp('recorded_at', 6); // High precision timestamp from provider
            $table->timestamp('synced_at', 6); // When synced to our system
            $table->jsonb('metadata')->nullable(); // Provider-specific data (PostgreSQL JSONB)
            $table->timestamps();

            // Indexes for performance
            $table->index(['external_entity_mapping_id', 'reading_type', 'recorded_at']);
            $table->index(['reading_type', 'recorded_at']);
            $table->index('recorded_at');
            $table->index('synced_at');

            // Unique constraint to prevent duplicates
            $table->unique([
                'external_entity_mapping_id',
                'reading_type',
                'recorded_at'
            ], 'telematic_readings_unique');
        });
    }

    public function down(): void
    {
        // Drop all partitions first
        $partitions = DB::select("
            SELECT schemaname, tablename
            FROM pg_tables
            WHERE tablename LIKE 'telematic_readings_%'
        ");

        foreach ($partitions as $partition) {
            DB::statement("DROP TABLE IF EXISTS {$partition->tablename} CASCADE");
        }

        Schema::dropIfExists('telematic_readings');
    }


};
