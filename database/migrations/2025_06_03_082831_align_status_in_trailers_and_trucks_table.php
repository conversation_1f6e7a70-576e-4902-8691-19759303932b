<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('trailers', function (Blueprint $table) {
            $table->renameColumn('fleet_status', 'status');
            $table->string('status')->nullable(false)->change();
        });

        Schema::table('trucks', function (Blueprint $table) {
            $table->dropColumn('fleet_status');
            $table->string('status')->nullable(false)->change();
        });
    }
};
