<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('trucks', function (Blueprint $table) {
            $table->string('plate_region')->nullable()->after('plate')->comment('State/Province/Region where the license plate is registered');
            $table->renameColumn('dot_expiry', 'regulatory_compliance_expiry');
        });
    }
};
