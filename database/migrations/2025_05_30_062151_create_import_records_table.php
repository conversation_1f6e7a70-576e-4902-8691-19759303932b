<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('import_records', function (Blueprint $table) {
            $table->id();
            $table->string('type');
            $table->string('filename');
            $table->string('file_path');
            $table->string('report_file_path')->nullable();
            $table->string('status');
            $table->integer('total_records')->default(0);
            $table->integer('imported_records')->default(0);
            $table->integer('updated_records')->default(0);
            $table->integer('failed_records')->default(0);
            $table->json('errors')->nullable();
            $table->timestamps();

            $table->index(['type', 'created_at']);
            $table->index('status');
        });
    }
};
