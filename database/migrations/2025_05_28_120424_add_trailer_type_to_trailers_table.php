<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('trailers', function (Blueprint $table) {
            $table->foreignId('type_id')->nullable()->after('vin')->constrained('trailer_types')->nullOnDelete();
        });
    }
};
