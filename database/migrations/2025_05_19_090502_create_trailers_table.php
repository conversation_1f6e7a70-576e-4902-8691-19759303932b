<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('trailers', function (Blueprint $table) {
            $table->id();
            $table->string('unit_number');
            $table->string('make')->nullable();
            $table->string('model')->nullable();
            $table->smallInteger('year')->nullable();
            $table->string('vin')->nullable();
            $table->date('dot_expiry')->nullable();
            $table->string('plate_number')->nullable();
            $table->string('plate_region')->nullable();
            $table->string('owner_company')->nullable();
            $table->string('ownership')->nullable();
            $table->string('fleet_status')->nullable();
            $table->date('activation_date')->nullable();
            $table->date('deactivation_date')->nullable();
            $table->string('location')->nullable();
            $table->text('gps_note')->nullable();
            $table->timestamps();
        });
    }
};
