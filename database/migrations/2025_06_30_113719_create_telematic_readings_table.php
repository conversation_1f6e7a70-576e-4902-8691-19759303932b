<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        DB::statement('
            CREATE TABLE telematic_readings (
                id BIGSERIAL NOT NULL,
                external_entity_mapping_id BIGINT NOT NULL,
                reading_type VARCHAR(50) NOT NULL,
                value DECIMAL(15,6) NOT NULL,
                unit VARCHAR(20) NOT NULL,
                recorded_at TIMESTAMP(6) NOT NULL,
                synced_at TIMESTAMP(6) NOT NULL,
                metadata JSONB,
                created_at TIMESTAMP,
                updated_at TIMESTAMP,

                PRIMARY KEY (id, recorded_at),

                CONSTRAINT fk_telematic_readings_external_entity_mapping
                    FOREIGN KEY (external_entity_mapping_id)
                    REFERENCES external_entity_mappings(id)
                    ON DELETE CASCADE
            ) PARTITION BY RANGE (recorded_at)
        ');

        DB::statement('CREATE INDEX idx_telematic_readings_entity_type_recorded ON telematic_readings (external_entity_mapping_id, reading_type, recorded_at)');
        DB::statement('CREATE INDEX idx_telematic_readings_type_recorded ON telematic_readings (reading_type, recorded_at)');
        DB::statement('CREATE INDEX idx_telematic_readings_recorded_at ON telematic_readings (recorded_at)');
        DB::statement('CREATE INDEX idx_telematic_readings_synced_at ON telematic_readings (synced_at)');

        DB::statement('
            CREATE UNIQUE INDEX telematic_readings_unique
            ON telematic_readings (external_entity_mapping_id, reading_type, recorded_at)
        ');

        $this->createInitialPartitions();
    }

    public function down(): void
    {
        $partitions = DB::select("
            SELECT schemaname, tablename
            FROM pg_tables
            WHERE tablename LIKE 'telematic_readings_%'
        ");

        foreach ($partitions as $partition) {
            DB::statement("DROP TABLE IF EXISTS {$partition->tablename} CASCADE");
        }

        Schema::dropIfExists('telematic_readings');
    }

    private function createInitialPartitions(): void
    {
        $startDate = now()->startOfMonth();

        for ($i = 0; $i < 4; $i++) {
            $partitionStart = $startDate->copy()->addMonths($i);
            $partitionEnd = $partitionStart->copy()->addMonth();
            $partitionName = 'telematic_readings_'.$partitionStart->format('Y_m');

            DB::statement("
                CREATE TABLE {$partitionName} PARTITION OF telematic_readings
                FOR VALUES FROM ('{$partitionStart->toDateString()}')
                TO ('{$partitionEnd->toDateString()}')
            ");
        }
    }
};
