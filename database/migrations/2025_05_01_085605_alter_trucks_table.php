<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('trucks', function (Blueprint $table) {
            $table->boolean('has_headrack')->default(false)->after('fleet_status')->comment('Whether the truck has a headrack');
            $table->string('ownership_type')->nullable()->after('fleet_status')->comment('Ownership/contract category');
            $table->string('plate')->nullable()->after('fleet_status')->comment('License plate number');
        });
    }
};
