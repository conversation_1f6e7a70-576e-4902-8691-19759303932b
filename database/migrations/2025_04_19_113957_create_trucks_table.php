<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('trucks', function (Blueprint $table) {
            $table->id();
            $table->string('unit_number')->comment('A unique identifier for the truck within the fleet.');
            $table->string('make')->nullable()->comment('The manufacturer of the truck.');
            $table->string('model')->nullable()->comment('The specific model of the truck.');
            $table->smallInteger('year')->nullable()->comment('The year the truck was manufactured.');
            $table->string('vin')->nullable()->comment('A unique code used to identify each vehicle globally.');
            $table->date('dot_expiry')->nullable()->comment('The expiration date of the truck\'s current regulatory compliance.');
            $table->string('owner_company')->nullable()->comment('The name of the company that owns or is currently operating the truck.');
            $table->string('fleet_status')->nullable()->comment('Current operational status of the truck (e.g., On the Road, In Repair).');
            $table->date('activation_date')->nullable()->comment('Date when the truck was activated for fleet use');
            $table->date('deactivation_date')->nullable()->comment('Date when the truck was deactivated or removed from fleet use');
            $table->date('lease_termination_date')->nullable()->comment('End date of the current lease');
            $table->string('rent_lease_type')->nullable()->comment('Type of rent or lease agreement');
            $table->text('gps_note')->nullable()->comment('Notes related to GPS equipment or tracking info');
            $table->boolean('deer_guards')->default(false)->comment('Whether the truck is equipped with deer guards');
            $table->boolean('dash_camera')->default(false)->comment('Whether the truck is equipped with a dash camera');
            $table->timestamps();
        });
    }
};
