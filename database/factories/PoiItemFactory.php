<?php

namespace Database\Factories;

use App\Models\PoiCategory;
use App\Models\PoiItem;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class PoiItemFactory extends Factory
{
    protected $model = PoiItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'poi_category_id' => PoiCategory::factory(),
            'name' => $this->faker->company.' '.$this->faker->word,
            'address_line1' => $this->faker->streetAddress,
            'address_line2' => $this->faker->optional()->secondaryAddress,
            'city' => $this->faker->city,
            'state' => $this->faker->stateAbbr,
            'postal_code' => $this->faker->postcode,
            'country' => $this->faker->countryCode,
            'latitude' => $this->faker->latitude,
            'longitude' => $this->faker->longitude,
            'phone' => $this->faker->optional()->phoneNumber,
            'email' => $this->faker->optional()->companyEmail,
            'website' => $this->faker->optional()->url,
            'metadata' => $this->faker->optional()->randomElements([
                'note' => $this->faker->sentence,
                'tags' => $this->faker->words(3),
            ]),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }
}
