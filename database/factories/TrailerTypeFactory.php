<?php

namespace Database\Factories;

use App\Enums\TrailerTypeEnum;
use App\Models\TrailerType;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class TrailerTypeFactory extends Factory
{
    protected $model = TrailerType::class;

    public function definition(): array
    {
        $nameEnum = $this->faker->randomElement(TrailerTypeEnum::cases());

        return [
            'name' => $nameEnum->value,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }
}
