<?php

namespace Database\Factories;

use App\Models\PoiCategory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class PoiCategoryFactory extends Factory
{
    protected $model = PoiCategory::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = [
            'Dealerships',
            'Service Centers',
            'Parts Suppliers',
            'Rest Areas',
            'Fuel Stations',
            'Repair Shops',
            'Warehouses',
            'Distribution Centers',
            'Corporate Offices',
            'Training Facilities',
        ];

        return [
            'name' => $this->faker->randomElement($categories).'-'.Str::random(5),
            'description' => $this->faker->sentence(10),
            'icon' => 'icon-'.$this->faker->word.'.png',
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }
}
