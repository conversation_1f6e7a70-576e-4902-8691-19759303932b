<?php

namespace Database\Factories;

use App\Models\Role;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends Factory<Role>
 */
class RoleFactory extends Factory
{
    protected $model = Role::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'workos_id' => 'fake-'.Str::random(10),
            'name' => $this->faker->jobTitle(),
            'slug' => Str::slug($this->faker->jobTitle().'-'.Str::random(8)),
            'description' => $this->faker->sentence(8),
            'type' => $this->faker->randomElement(['EnvironmentRole', 'OrganizationRole']),
            'permissions' => $this->faker->randomElements(['read', 'write', 'delete', 'update'], $this->faker->numberBetween(1, 4)),
            'priority' => $this->faker->numberBetween(1, 10),
            'is_default' => $this->faker->boolean(20),
        ];
    }
}
