<?php

namespace Database\Factories;

use App\Enums\DriverContractTypeEnum;
use App\Models\Driver;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class DriverFactory extends Factory
{
    protected $model = Driver::class;

    public function definition(): array
    {
        $contractTypes = [
            DriverContractTypeEnum::OWNER,
            DriverContractTypeEnum::OWNER_O,
            DriverContractTypeEnum::LEASE,
            DriverContractTypeEnum::CT_MILE,
            DriverContractTypeEnum::PERCENT_MILE,
        ];

        return [
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'phone_number' => $this->faker->phoneNumber(),
            'contract_type' => $this->faker->randomElement($contractTypes),
            'email' => $this->faker->unique()->safeEmail(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }
}
