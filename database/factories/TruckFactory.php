<?php

namespace Database\Factories;

use App\Enums\FleetStatusEnum;
use App\Enums\OwnershipTypeEnum;
use App\Enums\TruckStatusEnum;
use App\Models\Truck;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class TruckFactory extends Factory
{
    protected $model = Truck::class;

    public function definition(): array
    {
        $truckMakes = ['Volvo', 'Peterbilt', 'Kenworth', 'Freightliner', 'Mack', 'International'];
        $truckModels = [
            'Volvo' => ['VNL 860', 'VNL 760', 'VNR 640'],
            'Peterbilt' => ['579', '389', '567'],
            'Kenworth' => ['T680', 'W900', 'T880'],
            'Freightliner' => ['Cascadia', 'Coronado', 'Columbia'],
            'Mack' => ['Anthem', 'Pinnacle', 'Granite'],
            'International' => ['LT Series', 'RH Series', 'HX Series'],
        ];

        $make = $this->faker->randomElement($truckMakes);
        $model = $this->faker->randomElement($truckModels[$make]);
        $year = $this->faker->numberBetween(2015, 2024);

        $activationDate = $this->faker->dateTimeBetween('-5 years', 'now');

        // 30% chance of having a deactivation date
        $deactivationDate = $this->faker->boolean(30)
            ? $this->faker->dateTimeBetween($activationDate, '+2 years')
            : null;

        // 40% chance of having a lease termination date if ownership type is lease-related
        $leaseTerminationDate = null;

        // Random ownership type
        $ownershipType = $this->faker->randomElement([
            OwnershipTypeEnum::OWNER,
            OwnershipTypeEnum::LEASE_TO_OWN,
            OwnershipTypeEnum::COMPANY_LEASE,
            OwnershipTypeEnum::LOCAL,
            OwnershipTypeEnum::PITSTOP_SERVICE_TRUCK,
        ]);

        $possibleStatuses = array_column(TruckStatusEnum::cases(), 'value');
        $status = $this->faker->randomElement($possibleStatuses);

        // Set lease termination date if ownership type is lease-related
        if (in_array($ownershipType, [OwnershipTypeEnum::LEASE_TO_OWN, OwnershipTypeEnum::COMPANY_LEASE]) && $this->faker->boolean(40)) {
            $leaseTerminationDate = $this->faker->dateTimeBetween('+6 months', '+5 years');
        }

        return [
            'unit_number' => 'TR-'.$this->faker->unique()->numberBetween(1000, 9999),
            'make' => $make,
            'model' => $model,
            'year' => $year,
            'vin' => strtoupper($this->faker->regexify('[A-HJ-NPR-Z0-9]{17}')),
            'status' => $status,
            'regulatory_compliance_expiry' => $this->faker->dateTimeBetween('now', '+2 years'),
            'owner_company' => $this->faker->company(),
            'activation_date' => $activationDate,
            'deactivation_date' => $deactivationDate,
            'lease_termination_date' => $leaseTerminationDate,
            'rent_lease_type' => $ownershipType === OwnershipTypeEnum::COMPANY_LEASE ? $this->faker->randomElement(['Monthly', 'Yearly', 'Contract']) : null,
            'gps_note' => $this->faker->boolean(30) ? $this->faker->sentence() : null,
            'deer_guards' => $this->faker->boolean(40),
            'dash_camera' => $this->faker->boolean(70),
            'has_headrack' => $this->faker->boolean(50),
            'ownership_type' => $ownershipType,
            'plate' => strtoupper($this->faker->regexify('[A-Z]{3}[0-9]{3}')),
            'plate_expiration_date' => $this->faker->dateTimeBetween('now', '+5 years'),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }

    /**
     * Configure the model factory to create an active truck.
     */
    public function active(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'fleet_status' => FleetStatusEnum::ACTIVE,
                'deactivation_date' => null,
            ];
        });
    }

    /**
     * Configure the model factory to create an inactive truck.
     */
    public function inactive(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'fleet_status' => FleetStatusEnum::INACTIVE,
                'deactivation_date' => $this->faker->dateTimeBetween('-1 year', 'now'),
                'deactivation_reason' => $this->faker->sentence(),
            ];
        });
    }

    /**
     * Configure the model factory to create a truck with owner ownership type.
     */
    public function owner(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'ownership_type' => OwnershipTypeEnum::OWNER,
                'lease_termination_date' => null,
            ];
        });
    }

    /**
     * Configure the model factory to create a truck with lease ownership type.
     */
    public function leased(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'ownership_type' => OwnershipTypeEnum::COMPANY_LEASE,
                'lease_termination_date' => $this->faker->dateTimeBetween('+6 months', '+3 years'),
                'rent_lease_type' => $this->faker->randomElement(['Monthly', 'Yearly', 'Contract']),
            ];
        });
    }
}
