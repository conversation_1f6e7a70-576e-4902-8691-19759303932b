<?php

namespace Database\Factories;

use App\Enums\TrailerStatusEnum;
use App\Enums\UsaRegionEnum;
use App\Models\Trailer;
use App\Models\TrailerType;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class TrailerFactory extends Factory
{
    protected $model = Trailer::class;

    public function definition(): array
    {
        $trailerMakes = ['Great Dane', 'Hyundai Translead', 'Vanguard', 'Vanguard National'];
        $trailerModels = [
            'Great Dane' => ['Dry Van', 'Reefer', 'Flatbed'],
            'Hyundai Translead' => ['Dry Van', 'Reefer'],
            'Vanguard' => ['Dry Van', 'Reefer'],
            'Vanguard National' => ['Dry Van', 'Reefer'],
        ];

        $make = $this->faker->randomElement($trailerMakes);
        $model = $this->faker->randomElement($trailerModels[$make]);

        $possiblePlateRegions = array_column(UsaRegionEnum::cases(), 'value');
        $generatedPlateRegion = strtoupper($this->faker->randomElement($possiblePlateRegions));

        $possibleStatuses = array_column(TrailerStatusEnum::cases(), 'value');
        $status = $this->faker->randomElement($possibleStatuses);

        $activationDate = $this->faker->dateTimeBetween('-5 years');
        $deactivationDate = $this->faker->boolean(30)
            ? $this->faker->dateTimeBetween($activationDate, '+2 years')
            : null;

        return [
            'unit_number' => $this->faker->unique()->numberBetween(1000, 9999),
            'make' => $make,
            'model' => $model,
            'year' => $this->faker->numberBetween(2015, 2024),
            'vin' => strtoupper($this->faker->regexify('[A-HJ-NPR-Z0-9]{17}')),
            'type_id' => TrailerType::factory(),
            'dot_expiry' => $this->faker->dateTimeBetween('now', '+2 years'),
            'plate_number' => strtoupper($this->faker->bothify('###### ??')),
            'plate_region' => $generatedPlateRegion,
            'owner_company' => $this->faker->company(),
            'status' => $status,
            'activation_date' => $activationDate,
            'deactivation_date' => $deactivationDate,
            'location' => $this->faker->address(),
            'gps_note' => $this->faker->boolean(30) ? $this->faker->sentence() : null,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }
}
