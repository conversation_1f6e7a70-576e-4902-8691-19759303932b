<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Models\Role;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        Role::factory(10)->create();
        $this->call([
            OrganizationSeeder::class,
            RoleSeeder::class,
            PoiSeeder::class,
            TrailerTypeSeeder::class,
        ]);
    }
}
