<?php

namespace Database\Seeders;

use App\Models\Organization;
use Illuminate\Database\Seeder;
use WorkOS\Organizations;

class OrganizationSeeder extends Seeder
{
    public function run(): void
    {
        $organizations = new Organizations;
        $organizationId = config('services.workos.organization_id');

        try {
            // Get only the current organization from WorkOS
            $workOSOrg = $organizations->getOrganization($organizationId);

            Organization::updateOrCreate(
                ['workos_id' => $workOSOrg->id],
                [
                    'name' => $workOSOrg->name,
                    'slug' => $this->generateSlug($workOSOrg->name),
                    'status' => 'active',
                ]
            );
        } catch (\Exception $e) {
            $this->command->error("Failed to sync organization {$organizationId}: {$e->getMessage()}");
        }
    }

    private function generateSlug(string $name): string
    {
        return strtolower(preg_replace('/[^A-Za-z0-9-]+/', '-', $name));
    }
}
