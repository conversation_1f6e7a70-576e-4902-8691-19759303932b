<?php

namespace Database\Seeders;

use App\Enums\TrailerTypeEnum;
use App\Models\TrailerType;
use Illuminate\Database\Seeder;

class TrailerTypeSeeder extends Seeder
{
    public function run(): void
    {
        $trailerTypes = [
            [
                'name' => TrailerTypeEnum::DRY_VAN->value,
            ],
            [
                'name' => TrailerTypeEnum::STEP_DECK->value,
            ],
            [
                'name' => TrailerTypeEnum::FLAT_BED->value,
            ],
            [
                'name' => TrailerTypeEnum::REEFER->value,
            ],
            [
                'name' => TrailerTypeEnum::RGN->value,
            ],
        ];

        TrailerType::upsert($trailerTypes, ['name']);
    }
}
