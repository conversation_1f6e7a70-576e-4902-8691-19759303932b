<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    public function run(): void
    {
        $roles = [
            [
                'workos_id' => 'role_01JQPHGFX22YR5GHP3SS11D7PM',
                'name' => 'Member',
                'slug' => 'member',
                'description' => 'The default user role',
                'type' => 'EnvironmentRole',
                'permissions' => [],
                'priority' => 0,
                'is_default' => false,
                'created_at' => '2025-04-13 15:23:26',
                'updated_at' => '2025-04-13 15:23:26',
            ],
            [
                'workos_id' => 'role_01JQR13HP226HGYKVYP6MNXD9Z',
                'name' => 'Admin',
                'slug' => 'admin',
                'description' => 'Access to manage all available resources',
                'type' => 'EnvironmentRole',
                'permissions' => ['widgets:users-table:manage'],
                'priority' => 0,
                'is_default' => false,
                'created_at' => '2025-04-13 15:23:26',
                'updated_at' => '2025-04-13 15:23:26',
            ],
            [
                'workos_id' => 'role_01JRQABQRDT6NV2EBD34FHHQQF',
                'name' => 'Dispatcher',
                'slug' => 'org-dispatcher',
                'description' => 'Manages load assignments and monitors fleet operations.',
                'type' => 'OrganizationRole',
                'permissions' => [],
                'priority' => 0,
                'is_default' => false,
                'created_at' => '2025-04-13 15:23:26',
                'updated_at' => '2025-04-13 15:23:26',
            ],
            [
                'workos_id' => 'role_01JRQACRR30QRWF3QPQS5XZN7V',
                'name' => 'Accountant/Billing Manager',
                'slug' => 'org-accountantbilling-manager',
                'description' => 'Handles payments, invoicing, and financial reports.',
                'type' => 'OrganizationRole',
                'permissions' => [],
                'priority' => 0,
                'is_default' => false,
                'created_at' => '2025-04-13 15:23:26',
                'updated_at' => '2025-04-13 15:23:26',
            ],
            [
                'workos_id' => 'role_01JRQAD835Z1BF67KCA26Z2KJ8',
                'name' => 'Fleet Manager',
                'slug' => 'org-fleet-manager',
                'description' => 'Oversees maintenance and compliance of fleet equipment.',
                'type' => 'OrganizationRole',
                'permissions' => [],
                'priority' => 0,
                'is_default' => false,
                'created_at' => '2025-04-13 15:23:26',
                'updated_at' => '2025-04-13 15:23:26',
            ],
            [
                'workos_id' => 'role_01JRQADQ3JVCB2ZYJATET7XSEN',
                'name' => 'Safety/Compliance Manager',
                'slug' => 'org-safetycompliance-manager',
                'description' => 'nsures regulatory compliance and driver safety.',
                'type' => 'OrganizationRole',
                'permissions' => [],
                'priority' => 0,
                'is_default' => false,
                'created_at' => '2025-04-13 15:23:26',
                'updated_at' => '2025-04-13 15:23:26',
            ],
            [
                'workos_id' => 'role_01JRQAE31967WEFETWZR83YJK1',
                'name' => 'Driver',
                'slug' => 'org-driver',
                'description' => 'Accesses shipment details and updates load statuses.',
                'type' => 'OrganizationRole',
                'permissions' => [],
                'priority' => 0,
                'is_default' => false,
                'created_at' => '2025-04-13 15:23:26',
                'updated_at' => '2025-04-13 15:23:26',
            ],
            [
                'workos_id' => 'role_01JRQAEJDJKZF5M2N2EN1CABHY',
                'name' => 'Recruiter/HR Specialist',
                'slug' => 'org-recruiterhr-specialist',
                'description' => 'Manages driver recruitment and onboarding processes.',
                'type' => 'OrganizationRole',
                'permissions' => [],
                'priority' => 0,
                'is_default' => false,
                'created_at' => '2025-04-13 15:23:26',
                'updated_at' => '2025-04-13 15:23:26',
            ],
        ];

        foreach ($roles as $role) {
            Role::updateOrCreate(['workos_id' => $role['workos_id']], $role);
        }
    }
}
