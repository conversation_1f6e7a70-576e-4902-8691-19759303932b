<?php

use App\Enums\TrailerTypeEnum;
use App\Models\TrailerType;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\LaravelOneTimeOperations\OneTimeOperation;

return new class extends OneTimeOperation
{
    /**
     * Determine if the operation is being processed asynchronously.
     */
    protected bool $async = true;

    /**
     * The queue that the job will be dispatched to.
     */
    protected string $queue = 'operations';

    /**
     * A tag name, that this operation can be filtered by.
     */
    protected ?string $tag = null;

    /**
     * Process the operation.
     */
    public function process(): void
    {
        $trailerTypes = [
            [
                'name' => TrailerTypeEnum::DRY_VAN->value,
            ],
            [
                'name' => TrailerTypeEnum::STEP_DECK->value,
            ],
            [
                'name' => TrailerTypeEnum::FLAT_BED->value,
            ],
            [
                'name' => TrailerTypeEnum::REEFER->value,
            ],
            [
                'name' => TrailerTypeEnum::RGN->value,
            ],
        ];

        foreach ($trailerTypes as $type) {
            TrailerType::updateOrCreate(['name' => $type['name']], $type);
        }
    }
};
