image: lorisleiva/laravel-docker:8.4

definitions:
  caches:
    composer: ~/.composer/cache

pipelines:
  pull-requests:
    '**':
      - step:
          name: PHP Build
          caches:
            - composer
          script:
            - echo 'memory_limit = 512M' >> /usr/local/etc/php/conf.d/docker-php-memory-limit.ini
            - composer install --optimize-autoloader --no-interaction --prefer-dist
          artifacts:
            - vendor/**
            - .env.example

      - parallel:
          - step:
              name: Pest Tests
              script:
                - echo 'memory_limit = 512M' >> /usr/local/etc/php/conf.d/docker-php-memory-limit.ini
                - ln -f -s .env.example .env
                - php artisan key:generate
                - vendor/bin/pest
          - step:
              name: Pint Code Style Check
              script:
                - echo 'memory_limit = 512M' >> /usr/local/etc/php/conf.d/docker-php-memory-limit.ini
                - vendor/bin/pint --test
          - step:
              name: PHPStan Analysis
              script:
                - echo 'memory_limit = 512M' >> /usr/local/etc/php/conf.d/docker-php-memory-limit.ini
                - vendor/bin/phpstan analyze

  branches:
    staging:
      - step:
          name: Deploy to Staging
          deployment: staging
          trigger: automatic
          script:
            - curl -X POST "https://cloud.laravel.com/deploy/9e96c367-399f-4280-b6e3-384a6e9413b1/Fu2mqUYf4ZT5rvFD"

    master:
      - step:
          name: Deploy to Production
          deployment: production
          trigger: manual
          script:
            - curl -X POST "https://cloud.laravel.com/deploy/9e96c367-399f-4280-b6e3-384a6e9413b1/Fu2mqUYf4ZT5rvFD"
