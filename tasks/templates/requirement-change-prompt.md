# Requirement Change Prompt Template

Task TMS-XXX has new requirements. Please help me:

1. Update the description.md file in tasks/TMS-XXX/
2. Add an entry to the Requirements Log section with:
    - What changed: [specific changes]
    - Reason: [why it changed]
    - Impact: [how this affects implementation]
3. Update the global changelog.md
4. Check if this affects any related tasks and note dependencies

Current requirement changes: [describe the changes]

## Usage Instructions
1. Replace TMS-XXX with actual task number
2. Replace [specific changes] with detailed change description
3. Replace [describe the changes] with the actual requirement changes
4. Copy this prompt and use with your AI agent
