# TMS-193: Implement UI for Quick Maintenance View in Trucks Table

## Task Description
Implement expandable rows in the Trucks table where inside these rows should be visible fictional (hardcoded) information about current truck maintenance status, like mileage, next service due and other common metrics for fleet maintenance management.

## Requirements
- **Expandable Rows**: Implement expandable table rows using TanStack Table's expanding functionality
- **Maintenance Data Display**: Show hardcoded maintenance information including:
  - Current mileage
  - Next service due date/mileage
  - Last service date
  - Maintenance status/alerts
  - Oil change due
  - Tire rotation due
  - Inspection due dates
  - Any other relevant fleet maintenance metrics
- **UI Design**: Create a nice concise UI for the expanded content
- **Data Source**: Use hardcoded data in the Page component (no backend integration yet)
- **Future Integration**: Data will likely be sourced from Samsara TMS in future iterations

## Technical Approach
- Utilize TanStack Table's expanding feature: https://tanstack.com/table/latest/docs/guide/expanding
- Implement in the existing Trucks table component
- Follow existing project patterns and component structure
- Use shadcn/ui components for consistent styling

## Files to Modify
- `resources/js/pages/trucks/index.tsx` - Main trucks page
- `resources/js/pages/trucks/columns.tsx` - Table column definitions
- Potentially create new components for maintenance details display

## Acceptance Criteria
- [x] Trucks table has expandable rows functionality
- [x] Expanded rows show comprehensive maintenance information
- [x] UI is clean, intuitive and matches project design patterns
- [x] Data is hardcoded but structured for future API integration
- [x] Implementation follows TanStack Table best practices
- [x] Code follows project conventions and TypeScript standards

## Implementation Status
- **Status**: ✅ COMPLETED
- **Completion Date**: 2025-06-25
- **Build Status**: ✅ Successfully builds without errors
- **TypeScript**: ✅ Type-safe implementation

## Related Tasks
- Future: Integration with Samsara TMS for real maintenance data
- Future: Backend API endpoints for maintenance data

## Notes
- This is a UI-focused task with hardcoded data
- Focus on creating a good user experience for fleet managers
- Consider mobile responsiveness for the expanded content
- Maintenance data should be realistic and comprehensive for fleet management needs