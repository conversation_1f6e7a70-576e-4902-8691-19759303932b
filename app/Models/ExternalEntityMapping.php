<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property string $service_type
 * @property string $provider
 * @property string $entity_type
 * @property int $entity_id
 * @property string $external_id
 * @property array|null $metadata
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class ExternalEntityMapping extends Model
{
    protected $guarded = [];

    protected $casts = [
        'metadata' => 'array',
    ];

    public function entity(): MorphTo
    {
        return $this->morphTo();
    }

    public function telematicReadings(): HasMany
    {
        return $this->hasMany(TelematicReading::class);
    }
}
