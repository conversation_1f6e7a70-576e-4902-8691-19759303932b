<?php

namespace App\Models;

use App\Enums\OwnershipTypeEnum;
use App\Enums\TruckStatusEnum;
use App\Settings\SamsaraSettings;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Truck extends Model
{
    use HasFactory;
    use LogsActivity;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'deer_guards' => 'boolean',
            'dash_camera' => 'boolean',
            'activation_date' => 'date',
            'deactivation_date' => 'date',
            'lease_termination_date' => 'date',
            'has_headrack' => 'boolean',
            'ownership_type' => OwnershipTypeEnum::class,
            'status' => TruckStatusEnum::class,
        ];
    }

    public function driver1(): BelongsTo
    {
        return $this->belongsTo(Driver::class, 'driver_1_id');
    }

    public function driver2(): BelongsTo
    {
        return $this->belongsTo(Driver::class, 'driver_2_id');
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logUnguarded()
            ->logOnlyDirty()
            ->useLogName('Truck')
            ->dontSubmitEmptyLogs();
    }

    public function externalMappings(): MorphMany
    {
        return $this->morphMany(ExternalEntityMapping::class, 'entity');
    }

    public function samsaraMappings(): MorphMany
    {
        return $this->externalMappings()->where('provider', SamsaraSettings::PROVIDER_KEY);
    }
}
