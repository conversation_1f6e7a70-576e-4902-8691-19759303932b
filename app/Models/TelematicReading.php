<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $external_entity_mapping_id
 * @property string $reading_type
 * @property float $value
 * @property string $unit
 * @property Carbon $recorded_at
 * @property Carbon $synced_at
 * @property array|null $metadata
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 *
 * @property-read ExternalEntityMapping $externalEntityMapping
 * @property-read Model $entity
 */
class TelematicReading extends Model
{
    protected $guarded = [];

    protected $casts = [
        'value' => 'decimal:6',
        'recorded_at' => 'datetime:Y-m-d H:i:s.u',
        'synced_at' => 'datetime:Y-m-d H:i:s.u',
        'metadata' => 'array',
    ];

    // Reading type constants
    public const TYPE_ODOMETER = 'odometer';
    public const TYPE_ENGINE_HOURS = 'engine_hours';
    public const TYPE_FUEL_LEVEL = 'fuel_level';
    public const TYPE_SPEED = 'speed';
    public const TYPE_LOCATION = 'location';
    public const TYPE_DIAGNOSTICS = 'diagnostics';

    // Unit constants
    public const UNIT_METERS = 'meters';
    public const UNIT_KILOMETERS = 'kilometers';
    public const UNIT_MILES = 'miles';
    public const UNIT_HOURS = 'hours';
    public const UNIT_PERCENT = 'percent';
    public const UNIT_KMH = 'kmh';
    public const UNIT_MPH = 'mph';

    /**
     * Relationship to external entity mapping
     */
    public function externalEntityMapping(): BelongsTo
    {
        return $this->belongsTo(ExternalEntityMapping::class);
    }

    /**
     * Get the entity (Truck/Trailer) through the external mapping
     */
    public function entity(): Model
    {
        return $this->externalEntityMapping->entity;
    }

    /**
     * Scope to filter by reading type
     */
    public function scopeOfType(Builder $query, string $type): Builder
    {
        return $query->where('reading_type', $type);
    }

    /**
     * Scope to filter by external entity mapping
     */
    public function scopeForEntity(Builder $query, int $mappingId): Builder
    {
        return $query->where('external_entity_mapping_id', $mappingId);
    }

    /**
     * Scope to filter by time range
     */
    public function scopeRecordedBetween(Builder $query, Carbon $start, Carbon $end): Builder
    {
        return $query->whereBetween('recorded_at', [$start, $end]);
    }

    /**
     * Scope to get latest readings first
     */
    public function scopeLatest(Builder $query): Builder
    {
        return $query->orderBy('recorded_at', 'desc');
    }

    /**
     * Scope to get readings for a specific provider
     */
    public function scopeForProvider(Builder $query, string $provider): Builder
    {
        return $query->whereHas('externalEntityMapping', function (Builder $q) use ($provider) {
            $q->where('provider', $provider);
        });
    }

    /**
     * Convert value to different units
     */
    public function convertValue(string $targetUnit): float
    {
        return match ([$this->unit, $targetUnit]) {
            ['meters', 'kilometers'] => $this->value / 1000,
            ['meters', 'miles'] => $this->value / 1609.34,
            ['kilometers', 'meters'] => $this->value * 1000,
            ['kilometers', 'miles'] => $this->value / 1.60934,
            ['miles', 'meters'] => $this->value * 1609.34,
            ['miles', 'kilometers'] => $this->value * 1.60934,
            ['kmh', 'mph'] => $this->value / 1.60934,
            ['mph', 'kmh'] => $this->value * 1.60934,
            default => $this->value, // Same unit or unsupported conversion
        };
    }

    /**
     * Check if this reading is an odometer reading
     */
    public function isOdometer(): bool
    {
        return $this->reading_type === self::TYPE_ODOMETER;
    }

    /**
     * Check if this reading is an engine hours reading
     */
    public function isEngineHours(): bool
    {
        return $this->reading_type === self::TYPE_ENGINE_HOURS;
    }

    /**
     * Check if this reading is a fuel level reading
     */
    public function isFuelLevel(): bool
    {
        return $this->reading_type === self::TYPE_FUEL_LEVEL;
    }

    /**
     * Get the provider name from the external mapping
     */
    public function getProviderAttribute(): string
    {
        return $this->externalEntityMapping->provider;
    }

    /**
     * Get the entity type from the external mapping
     */
    public function getEntityTypeAttribute(): string
    {
        return $this->externalEntityMapping->entity_type;
    }
}
