<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $external_entity_mapping_id
 * @property string $reading_type
 * @property float $value
 * @property string $unit
 * @property Carbon $recorded_at
 * @property Carbon $synced_at
 * @property array|null $metadata
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read ExternalEntityMapping $externalEntityMapping
 */
class TelematicReading extends Model
{
    protected $guarded = [];

    protected $casts = [
        'value' => 'decimal:6',
        'recorded_at' => 'datetime:Y-m-d H:i:s.u',
        'synced_at' => 'datetime:Y-m-d H:i:s.u',
        'metadata' => 'array',
    ];

    public function externalEntityMapping(): BelongsTo
    {
        return $this->belongsTo(ExternalEntityMapping::class);
    }
}
