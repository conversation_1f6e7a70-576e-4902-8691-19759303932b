<?php

namespace App\Models;

use App\Enums\TrailerStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Trailer extends Model
{
    use HasFactory;
    use LogsActivity;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'status' => TrailerStatusEnum::class,
        ];
    }

    public function type(): BelongsTo
    {
        return $this->belongsTo(TrailerType::class, 'type_id');
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logUnguarded()
            ->logOnlyDirty()
            ->useLogName('Trailer')
            ->dontSubmitEmptyLogs();
    }

    public function externalMappings(): MorphMany
    {
        return $this->morphMany(ExternalEntityMapping::class, 'entity');
    }
}
