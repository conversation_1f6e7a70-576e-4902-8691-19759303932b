<?php

namespace App\Services\Telematics\Sync\Strategies;

use App\Data\Samsara\SamsaraVehicleData;
use App\Exceptions\Telematics\TelematicApiException;
use App\Exceptions\Telematics\TelematicConfigurationException;
use App\Exceptions\Telematics\TelematicSyncException;
use App\Models\ExternalEntityMapping;
use App\Models\Truck;
use App\Services\Telematics\Clients\Samsara\SamsaraClient;
use App\Services\Telematics\Sync\TelematicSyncInterface;
use App\Settings\SamsaraSettings;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

readonly class SamsaraStrategy implements TelematicSyncInterface
{
    private const string SERVICE_TYPE = 'telematic';

    public function __construct(
        private readonly SamsaraClient $client,
    ) {}

    /**
     * @throws TelematicConfigurationException
     * @throws TelematicApiException
     * @throws TelematicSyncException
     */
    public function syncInitial(): array
    {
        $endCursor = null;
        $totalTrucksSynced = 0;
        $totalErrors = 0;
        $errors = [];

        try {
            do {
                $params = [
                    'after' => $endCursor,
                ];

                Log::info('Fetching vehicles from Samsara', [
                    'provider' => SamsaraSettings::PROVIDER_KEY,
                    'params' => $params,
                ]);

                $response = $this->client->getAllVehicles($params);

                $endCursor = $response->pagination->endCursor;
                $hasNextPage = $response->pagination->hasNextPage;

                $syncResult = $this->syncVehicles($response->data);
                $totalTrucksSynced += $syncResult['synced'];
                $totalErrors += $syncResult['errors'];

                if (! empty($syncResult['error_details'])) {
                    $errors = [...$errors, ...$syncResult['error_details']];
                }

                Log::info('Batch sync completed', [
                    'provider' => SamsaraSettings::PROVIDER_KEY,
                    'batch_synced' => $syncResult['synced'],
                    'batch_errors' => $syncResult['errors'],
                    'total_synced' => $totalTrucksSynced,
                    'has_next_page' => $hasNextPage,
                ]);

            } while ($hasNextPage && $endCursor !== '');

            if ($totalErrors > 0) {
                throw TelematicSyncException::partialSyncFailure(
                    SamsaraSettings::PROVIDER_KEY,
                    $totalTrucksSynced,
                    $totalErrors,
                    $errors
                );
            }

            Log::info('Truck sync completed successfully.', [
                'provider' => SamsaraSettings::PROVIDER_KEY,
                'total_synced' => $totalTrucksSynced,
            ]);

            return [
                'provider' => SamsaraSettings::PROVIDER_KEY,
                'total_synced' => $totalTrucksSynced,
                'total_errors' => $totalErrors,
            ];
        } catch (TelematicApiException|TelematicConfigurationException $e) {
            Log::error('API/Configuration error during sync', [
                'provider' => SamsaraSettings::PROVIDER_KEY,
                'error' => $e->getMessage(),
                'context' => $e->getContext(),
            ]);

            throw $e;
        } catch (Exception $e) {
            Log::error('Unexpected error during sync', [
                'provider' => SamsaraSettings::PROVIDER_KEY,
                'error' => $e->getMessage(),
                'total_synced_before_error' => $totalTrucksSynced,
            ]);

            throw TelematicSyncException::syncFailed(
                SamsaraSettings::PROVIDER_KEY,
                $e->getMessage(),
                ['total_synced_before_error' => $totalTrucksSynced]
            );
        }
    }

    private function syncVehicles(Collection $vehicles): array
    {
        $syncedCount = 0;
        $errorCount = 0;
        $errorDetails = [];

        if ($vehicles->isEmpty()) {
            Log::info('No vehicles to sync in this batch');

            return ['synced' => 0, 'errors' => 0, 'error_details' => []];
        }

        $samsaraVinNumbers = $vehicles->pluck('vin')->filter()->unique()->all();

        if (empty($samsaraVinNumbers)) {
            Log::warning('No valid VIN numbers found in vehicle data', [
                'vehicle_count' => $vehicles->count(),
            ]);

            return ['synced' => 0, 'errors' => $vehicles->count(), 'error_details' => ['No valid VIN numbers found']];
        }

        $trucks = Truck::whereIn('vin', $samsaraVinNumbers)->with('samsaraMappings')->get();
        $vehiclesByVin = $vehicles->keyBy('vin');

        Log::info('Processing vehicles for sync', [
            'total_vehicles' => $vehicles->count(),
            'vehicles_with_vin' => count($samsaraVinNumbers),
            'matching_trucks' => $trucks->count(),
        ]);

        foreach ($trucks as $truck) {
            try {
                $vehicleData = $vehiclesByVin->get($truck->vin);

                if (! $vehicleData) {
                    $errorCount++;
                    $errorDetails[] = "Vehicle data not found for truck VIN: {$truck->vin}";

                    continue;
                }

                if ($truck->samsaraMappings->isNotEmpty()) {
                    /** @var ExternalEntityMapping $existingMapping */
                    $existingMapping = $truck->samsaraMappings->first();
                    $existingCreatedAt = data_get($existingMapping->metadata, 'createdAt');

                    if ($existingCreatedAt && $existingCreatedAt > $vehicleData->createdAtTime) {
                        Log::debug('Skipping vehicle sync - existing mapping is newer', [
                            'truck_id' => $truck->id,
                            'vin' => $truck->vin,
                            'existing_created_at' => $existingCreatedAt,
                            'vehicle_created_at' => $vehicleData->createdAtTime,
                        ]);

                        continue;
                    }
                }

                ExternalEntityMapping::updateOrCreate([
                    'service_type' => self::SERVICE_TYPE,
                    'provider' => SamsaraSettings::PROVIDER_KEY,
                    'entity_type' => Truck::class,
                    'entity_id' => $truck->id,
                ], [
                    'external_id' => $vehicleData->id,
                    'metadata' => $this->getMetadata($vehicleData),
                ]);

                $syncedCount++;

                Log::debug('Vehicle synced successfully', [
                    'truck_id' => $truck->id,
                    'vin' => $truck->vin,
                    'external_id' => $vehicleData->id,
                ]);
            } catch (Exception $e) {
                $errorCount++;
                $errorDetails[] = "Failed to sync truck {$truck->id} (VIN: {$truck->vin}): {$e->getMessage()}";

                Log::error('Failed to sync individual vehicle', [
                    'truck_id' => $truck->id,
                    'vin' => $truck->vin,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return [
            'synced' => $syncedCount,
            'errors' => $errorCount,
            'error_details' => $errorDetails,
        ];
    }

    private function getMetadata(SamsaraVehicleData $vehicleData): array
    {
        return [
            'createdAt' => $vehicleData->createdAtTime,
        ];
    }
}
