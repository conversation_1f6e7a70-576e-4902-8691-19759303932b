<?php

namespace App\Services\Telematics\Sync;

use App\Exceptions\Telematics\TelematicSyncException;
use App\Services\Telematics\Settings\TelematicSettingService;

readonly class TelematicSyncService
{
    public function __construct(
        private TelematicSettingService $telematicSettingService,
        /** @var array<string, TelematicSyncInterface> */
        private array $telematicSyncStrategyMap,
    ) {}

    /**
     * @throws TelematicSyncException
     */
    public function syncInitial(): array
    {
        $activeProvider = $this->telematicSettingService->getActiveProvider();

        if ($activeProvider === null) {
            throw TelematicSyncException::noActiveProvider();
        }

        $providerKey = $activeProvider->getProviderKey();

        if (! isset($this->telematicSyncStrategyMap[$providerKey])) {
            throw TelematicSyncException::syncFailed(
                $providerKey,
                "Sync strategy not found for provider: {$providerKey}"
            );
        }

        return $this->telematicSyncStrategyMap[$providerKey]->syncInitial();
    }
}
