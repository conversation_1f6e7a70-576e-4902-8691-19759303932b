<?php

namespace App\Services\Telematics\Clients\Samsara;

use App\Data\Samsara\SamsaraVehicleResponseData;
use App\Exceptions\Telematics\TelematicApiException;
use App\Exceptions\Telematics\TelematicConfigurationException;
use App\Settings\SamsaraSettings;
use Exception;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SamsaraClient
{
    private const string VEHICLES_ENDPOINT = 'fleet/vehicles';

    private bool $configured = false;

    public function __construct(
        private PendingRequest $client,
        private readonly SamsaraSettings $settings,
    ) {}

    /**
     * @throws TelematicConfigurationException
     * @throws TelematicApiException
     */
    public function getAllVehicles(array $params = []): SamsaraVehicleResponseData
    {
        $this->ensureConfigured();

        try {
            $response = $this->client->get(self::VEHICLES_ENDPOINT, $params);

            if ($response->failed()) {
                $this->handleFailedResponse($response, $params);
            }

            $data = $response->json();

            if (! is_array($data)) {
                throw TelematicApiException::invalidResponse(
                    SamsaraSettings::PROVIDER_KEY,
                    'Response is not valid JSON',
                    $params
                );
            }

            return SamsaraVehicleResponseData::fromApiResponse($data);
        } catch (ConnectionException $e) {
            throw TelematicApiException::connectionFailed(
                SamsaraSettings::PROVIDER_KEY,
                $e->getMessage(),
                $params
            );
        } catch (Exception $e) {
            if ($e instanceof TelematicApiException || $e instanceof TelematicConfigurationException) {
                throw $e;
            }

            throw TelematicApiException::invalidResponse(
                SamsaraSettings::PROVIDER_KEY,
                $e->getMessage(),
                $params
            );
        }
    }

    /**
     * @throws TelematicConfigurationException
     */
    private function ensureConfigured(): void
    {
        if ($this->configured) {
            return;
        }

        $this->validateConfiguration();

        $baseUrl = config('services.samsara.base_url');
        $apiKey = $this->settings->credentials['api_key'] ?? null;

        $this->client = Http::withHeaders([
            'Authorization' => "Bearer {$apiKey}",
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ])->baseUrl($baseUrl);

        $this->configured = true;
    }

    /**
     * @throws TelematicConfigurationException
     */
    private function validateConfiguration(): void
    {
        $baseUrl = config('services.samsara.base_url');
        $apiKey = $this->settings->credentials['api_key'] ?? null;

        if (empty($baseUrl)) {
            throw TelematicConfigurationException::missingBaseUrl(SamsaraSettings::PROVIDER_KEY);
        }

        if (empty($apiKey)) {
            throw TelematicConfigurationException::missingApiKey(SamsaraSettings::PROVIDER_KEY);
        }
    }

    /**
     * @throws TelematicConfigurationException
     * @throws TelematicApiException
     */
    private function handleFailedResponse($response, array $params): void
    {
        $statusCode = $response->status();
        $responseBody = $response->body();

        Log::error('Samsara API request failed', [
            'status' => $statusCode,
            'body' => $responseBody,
            'params' => $params,
        ]);

        if ($statusCode === 429) {
            $retryAfter = $response->header('Retry-After');
            throw TelematicApiException::rateLimitExceeded(SamsaraSettings::PROVIDER_KEY, $retryAfter);
        }

        if ($statusCode === 401) {
            throw TelematicConfigurationException::invalidApiKey(SamsaraSettings::PROVIDER_KEY);
        }

        throw TelematicApiException::requestFailed(
            SamsaraSettings::PROVIDER_KEY,
            $statusCode,
            $responseBody,
            $params
        );
    }
}
