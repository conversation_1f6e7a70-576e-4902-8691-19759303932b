<?php

namespace App\Console\Commands;

use App\Exceptions\Telematics\TelematicConfigurationException;
use App\Exceptions\Telematics\TelematicException;
use App\Services\Telematics\Sync\TelematicSyncService;
use Exception;
use Illuminate\Console\Command;

class InitialTelematicSyncCommand extends Command
{
    public function __construct(
        private readonly TelematicSyncService $telematicSyncService,
    ) {
        parent::__construct();
    }

    /**
     * {@inheritDoc}
     */
    protected $signature = 'sync:telematics:initial
                            {--cron : Run in cron mode with minimal output}';

    /**
     * {@inheritDoc}
     */
    protected $description = 'Sync initial entities from enabled telematics provider';

    /**
     * Handle the command execution.
     *
     * @return int Exit code (0 for success, 1 for failure)
     */
    public function handle(): int
    {
        $isCronMode = $this->option('cron');
        $isVerbose = $this->output->isVerbose();

        if (! $isCronMode) {
            $this->info('Starting initial entity sync from enabled provider...');
        }

        try {
            $result = $this->telematicSyncService->syncInitial();

            if ($isCronMode) {
                $totalSynced = $result['total_synced'] ?? 0;
                $totalErrors = $result['total_errors'] ?? 0;
                $provider = $result['provider'] ?? 'unknown';

                if ($totalErrors > 0) {
                    $this->info("TELEMATIC_SYNC_SUCCESS: {$totalSynced} vehicles synced, {$totalErrors} errors from {$provider}");
                } else {
                    $this->info("TELEMATIC_SYNC_SUCCESS: {$totalSynced} vehicles synced from {$provider}");
                }

                if ($isVerbose) {
                    $this->line("PROVIDER: {$provider}");
                    $this->line("TOTAL_SYNCED: {$totalSynced}");
                    $this->line("TOTAL_ERRORS: {$totalErrors}");
                }
            } else {
                $this->info('✅ Initial entities were synced successfully.');
            }

            return self::SUCCESS;
        } catch (TelematicConfigurationException $e) {
            return $this->handleConfigurationError($e, $isVerbose, $isCronMode);
        } catch (TelematicException $e) {
            return $this->handleTelematicError($e, $isVerbose, $isCronMode);
        } catch (Exception $e) {
            return $this->handleUnexpectedError($e, $isVerbose, $isCronMode);
        }
    }

    private function handleConfigurationError(TelematicConfigurationException $e, bool $isVerbose, bool $isCronMode): int
    {
        if ($isCronMode) {
            $this->error("TELEMATIC_SYNC_FAILED: Configuration error - {$e->getMessage()}");
            if ($isVerbose) {
                $this->line("PROVIDER: {$e->getProvider()}");
                $this->line("ACTIONABLE_MESSAGE: {$e->getActionableMessage()}");
                foreach ($e->getContext() as $key => $value) {
                    $this->line("CONTEXT_{$key}: ".(is_array($value) ? json_encode($value) : $value));
                }
            }
        } else {
            $this->error('❌ Configuration Error: '.$e->getMessage());
            $this->newLine();
            $this->warn('💡 '.$e->getActionableMessage());
            $this->newLine();
            $this->line('<comment>To fix this issue:</comment>');
            $this->line('1. Go to your application settings');
            $this->line('2. Navigate to Integrations > Telematics');
            $this->line('3. Configure the correct API credentials');

            if ($isVerbose) {
                $this->newLine();
                $this->line('<comment>Additional context:</comment>');
                foreach ($e->getContext() as $key => $value) {
                    $this->line("  {$key}: ".(is_array($value) ? json_encode($value) : $value));
                }
            }
        }

        return self::FAILURE;
    }

    private function handleTelematicError(TelematicException $e, bool $isVerbose, bool $isCronMode): int
    {
        if ($isCronMode) {
            $this->error("TELEMATIC_SYNC_FAILED: {$e->getMessage()}");
            if ($isVerbose) {
                $this->line("PROVIDER: {$e->getProvider()}");
                $this->line("ACTIONABLE_MESSAGE: {$e->getActionableMessage()}");
                foreach ($e->getContext() as $key => $value) {
                    $this->line("CONTEXT_{$key}: ".(is_array($value) ? json_encode($value) : $value));
                }
            }
        } else {
            $this->error('❌ Sync failed: '.$e->getMessage());
            $this->newLine();
            $this->warn('💡 '.$e->getActionableMessage());

            if ($isVerbose) {
                $this->newLine();
                $this->line('<comment>Additional context:</comment>');
                foreach ($e->getContext() as $key => $value) {
                    $this->line("  {$key}: ".(is_array($value) ? json_encode($value) : $value));
                }
            }
        }

        return self::FAILURE;
    }

    private function handleUnexpectedError(Exception $e, bool $isVerbose, bool $isCronMode): int
    {
        if ($isCronMode) {
            $this->error("TELEMATIC_SYNC_FAILED: Unexpected error - {$e->getMessage()}");
            if ($isVerbose) {
                $this->line('EXCEPTION_CLASS: '.get_class($e));
                $this->line('STACK_TRACE: '.str_replace("\n", ' | ', $e->getTraceAsString()));
            }
        } else {
            $this->error('❌ An unexpected error occurred: '.$e->getMessage());
            $this->newLine();
            $this->warn('💡 Please check your configuration and try again. If the problem persists, contact support.');

            if ($isVerbose) {
                $this->newLine();
                $this->line('<comment>Stack trace:</comment>');
                $this->line($e->getTraceAsString());
            }
        }

        return self::FAILURE;
    }
}
