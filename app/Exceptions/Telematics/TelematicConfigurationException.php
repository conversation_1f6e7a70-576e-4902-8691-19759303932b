<?php

namespace App\Exceptions\Telematics;

class TelematicConfigurationException extends TelematicException
{
    public static function missingApi<PERSON>ey(string $provider): self
    {
        return new self(
            "API key is missing for {$provider} provider",
            $provider,
            ['issue' => 'missing_api_key']
        );
    }

    public static function invalidApi<PERSON>ey(string $provider): self
    {
        return new self(
            "API key is invalid for {$provider} provider",
            $provider,
            ['issue' => 'invalid_api_key']
        );
    }

    public static function missingBaseUrl(string $provider): self
    {
        return new self(
            "Base URL is missing for {$provider} provider",
            $provider,
            ['issue' => 'missing_base_url']
        );
    }

    public function getActionableMessage(): string
    {
        $issue = $this->context['issue'] ?? 'unknown';

        return match ($issue) {
            'missing_api_key' => "Please configure the API key for {$this->provider} in the telematic settings or check your environment variables.",
            'invalid_api_key' => "The API key for {$this->provider} appears to be invalid. Please verify the key in your telematic settings.",
            'missing_base_url' => "The base URL for {$this->provider} is not configured. Please check your services configuration.",
            default => "There is a configuration issue with the {$this->provider} provider. Please check your settings.",
        };
    }
}
