<?php

namespace App\Exceptions\Telematics;

use Exception;

abstract class TelematicException extends Exception
{
    public function __construct(
        string $message,
        protected readonly string $provider,
        protected readonly array $context = [],
        ?Exception $previous = null,
    ) {
        parent::__construct($message, 0, $previous);
    }

    public function getProvider(): string
    {
        return $this->provider;
    }

    public function getContext(): array
    {
        return $this->context;
    }

    abstract public function getActionableMessage(): string;
}
