<?php

namespace App\Exceptions\Telematics;

class TelematicApiException extends TelematicException
{
    public static function requestFailed(string $provider, int $statusCode, string $responseBody, array $requestParams = []): self
    {
        return new self(
            "API request to {$provider} failed with status {$statusCode}",
            $provider,
            [
                'status_code' => $statusCode,
                'response_body' => $responseBody,
                'request_params' => $requestParams,
                'issue' => 'request_failed',
            ]
        );
    }

    public static function connectionFailed(string $provider, string $error, array $requestParams = []): self
    {
        return new self(
            "Failed to connect to {$provider} API: {$error}",
            $provider,
            [
                'connection_error' => $error,
                'request_params' => $requestParams,
                'issue' => 'connection_failed',
            ]
        );
    }

    public static function invalidResponse(string $provider, string $error, array $requestParams = []): self
    {
        return new self(
            "Invalid response from {$provider} API: {$error}",
            $provider,
            [
                'parse_error' => $error,
                'request_params' => $requestParams,
                'issue' => 'invalid_response',
            ]
        );
    }

    public static function rateLimitExceeded(string $provider, ?string $retryAfter = null): self
    {
        return new self(
            "Rate limit exceeded for {$provider} API",
            $provider,
            [
                'retry_after' => $retryAfter,
                'issue' => 'rate_limit_exceeded',
            ]
        );
    }

    public function getActionableMessage(): string
    {
        $issue = $this->context['issue'] ?? 'unknown';
        $statusCode = $this->context['status_code'] ?? null;

        return match ($issue) {
            'request_failed' => $this->getRequestFailedMessage($statusCode),
            'connection_failed' => "Unable to connect to {$this->provider} API. Please check your internet connection and try again. If the problem persists, {$this->provider} services may be temporarily unavailable.",
            'invalid_response' => "Received an unexpected response from {$this->provider} API. This may indicate a temporary service issue. Please try again later.",
            'rate_limit_exceeded' => $this->getRateLimitMessage(),
            default => "An API error occurred with {$this->provider}. Please try again later or contact support if the issue persists.",
        };
    }

    private function getRequestFailedMessage(?int $statusCode): string
    {
        return match ($statusCode) {
            401 => "Authentication failed with {$this->provider}. Please check your API key in the telematic settings.",
            403 => "Access forbidden by {$this->provider}. Your API key may not have the required permissions.",
            404 => "The requested resource was not found on {$this->provider}. Please check your configuration.",
            429 => "Too many requests to {$this->provider}. Please wait before trying again.",
            500, 502, 503, 504 => "{$this->provider} is experiencing server issues. Please try again later.",
            default => "Request to {$this->provider} failed with status {$statusCode}. Please check your configuration and try again.",
        };
    }

    private function getRateLimitMessage(): string
    {
        $retryAfter = $this->context['retry_after'] ?? null;
        $message = "Rate limit exceeded for {$this->provider} API.";

        if ($retryAfter) {
            $message .= " Please wait {$retryAfter} seconds before trying again.";
        } else {
            $message .= ' Please wait a few minutes before trying again.';
        }

        return $message;
    }
}
