<?php

namespace App\Exceptions\Telematics;

class TelematicSyncException extends TelematicException
{
    public static function noActiveProvider(): self
    {
        return new self(
            'No active telematic provider found',
            'none',
            ['issue' => 'no_active_provider']
        );
    }

    public static function syncFailed(string $provider, string $reason, array $context = []): self
    {
        return new self(
            "Sync failed for {$provider}: {$reason}",
            $provider,
            [...$context, 'issue' => 'sync_failed', 'reason' => $reason]
        );
    }

    public static function partialSyncFailure(string $provider, int $successful, int $failed, array $errors = []): self
    {
        return new self(
            "Partial sync failure for {$provider}: {$successful} successful, {$failed} failed",
            $provider,
            [
                'issue' => 'partial_sync_failure',
                'successful_count' => $successful,
                'failed_count' => $failed,
                'errors' => $errors,
            ]
        );
    }

    public function getActionableMessage(): string
    {
        $issue = $this->context['issue'] ?? 'unknown';

        return match ($issue) {
            'no_active_provider' => 'No telematic provider is currently enabled. Please go to Settings > Integrations > Telematics and enable a provider.',
            'sync_failed' => "Sync failed: {$this->context['reason']}. Please check your provider configuration and try again.",
            'partial_sync_failure' => $this->getPartialSyncMessage(),
            default => 'Sync operation failed. Please check your configuration and try again.',
        };
    }

    private function getPartialSyncMessage(): string
    {
        $successful = $this->context['successful_count'] ?? 0;
        $failed = $this->context['failed_count'] ?? 0;

        $message = "Sync completed with some failures: {$successful} vehicles synced successfully, {$failed} failed.";

        if (! empty($this->context['errors'])) {
            $message .= ' Check the logs for detailed error information.';
        }

        return $message;
    }
}
