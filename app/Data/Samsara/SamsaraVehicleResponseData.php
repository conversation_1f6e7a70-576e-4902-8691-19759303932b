<?php

namespace App\Data\Samsara;

use Illuminate\Support\Collection;
use Spatie\LaravelData\Data;

class SamsaraVehicleResponseData extends Data
{
    public function __construct(
        public Collection $data,
        public SamsaraPaginationData $pagination,
    ) {}

    public static function fromApiResponse(array $response): self
    {
        $vehicles = collect($response['data'] ?? [])
            ->map(fn (array $vehicleData) => SamsaraVehicleData::from($vehicleData));

        return new self(
            data: $vehicles,
            pagination: SamsaraPaginationData::from($response['pagination'] ?? []),
        );
    }
}
