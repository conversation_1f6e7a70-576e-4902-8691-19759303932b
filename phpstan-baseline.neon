parameters:
	ignoreErrors:
		-
			message: '#^Access to an undefined property WorkOS\\Resource\\User\:\:\$id\.$#'
			identifier: property.notFound
			count: 3
			path: app/Http/Controllers/Users/<USER>

		-
			message: '#^Access to an undefined property WorkOS\\Resource\\User\:\:\$email\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Controllers/Users/<USER>

		-
			message: '#^Access to an undefined property WorkOS\\Resource\\User\:\:\$firstName\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Controllers/Users/<USER>

		-
			message: '#^Access to an undefined property WorkOS\\Resource\\User\:\:\$id\.$#'
			identifier: property.notFound
			count: 4
			path: app/Http/Controllers/Users/<USER>

		-
			message: '#^Access to an undefined property WorkOS\\Resource\\User\:\:\$lastName\.$#'
			identifier: property.notFound
			count: 1
			path: app/Http/Controllers/Users/<USER>
